{"version": 3, "file": "tlds.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/resources/radar/email/security/top/tlds/tlds.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAEzD,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAC/C,OAAO,KAAK,YAAY,MAAM,aAAa,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAClF,OAAO,KAAK,OAAO,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,QAAQ,CAAC;AAC9D,OAAO,KAAK,QAAQ,MAAM,SAAS,CAAC;AACpC,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAElE,qBAAa,IAAK,SAAQ,WAAW;IACnC,SAAS,EAAE,YAAY,CAAC,SAAS,CAA4C;IAC7E,IAAI,EAAE,OAAO,CAAC,IAAI,CAAkC;IACpD,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAoC;IAEzD;;;;;;;;OAQG;IACH,GAAG,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IACzF,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;CAcpE;AAED,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC;IAE1B,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;CACnC;AAED,yBAAiB,cAAc,CAAC;IAC9B;;OAEG;IACH,UAAiB,IAAI;QACnB,cAAc,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3C,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC;;WAEG;QACH,WAAW,EAAE,MAAM,CAAC;QAEpB;;;WAGG;QACH,aAAa,EACT,YAAY,GACZ,UAAU,GACV,SAAS,GACT,YAAY,GACZ,mBAAmB,GACnB,iBAAiB,GACjB,uBAAuB,GACvB,OAAO,CAAC;QAEZ;;WAEG;QACH,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzB;IAED,UAAiB,IAAI,CAAC;QACpB,UAAiB,cAAc;YAC7B,WAAW,EAAE,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAE9C;;eAEG;YACH,KAAK,EAAE,MAAM,CAAC;SACf;QAED,UAAiB,cAAc,CAAC;YAC9B;;eAEG;YACH,UAAiB,UAAU;gBACzB,UAAU,EAAE,MAAM,CAAC;gBAEnB,WAAW,EAAE,MAAM,CAAC;gBAEpB,OAAO,EAAE,MAAM,CAAC;gBAEhB,SAAS,EAAE,MAAM,CAAC;gBAElB;;mBAEG;gBACH,eAAe,EAAE,OAAO,CAAC;gBAEzB,SAAS,EAAE,MAAM,CAAC;gBAElB,SAAS,EAAE,MAAM,CAAC;aACnB;SACF;QAED,UAAiB,SAAS;YACxB;;eAEG;YACH,OAAO,EAAE,MAAM,CAAC;YAEhB;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;SACnB;QAED,UAAiB,IAAI;YACnB,IAAI,EAAE,MAAM,CAAC;YAEb,KAAK,EAAE,MAAM,CAAC;SACf;KACF;IAED,UAAiB,IAAI;QACnB,IAAI,EAAE,MAAM,CAAC;QAEb;;WAEG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAExB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IAEvC;;;OAGG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IAExC;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAExB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAErB;;OAEG;IACH,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,WAAW,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IAEpC;;OAEG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;CACnE;AAMD,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,OAAO,EAAE,KAAK,cAAc,IAAI,cAAc,EAAE,KAAK,YAAY,IAAI,YAAY,EAAE,CAAC;IAEpF,OAAO,EACL,SAAS,IAAI,SAAS,EACtB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;IAEF,OAAO,EAAE,IAAI,IAAI,IAAI,EAAE,KAAK,eAAe,IAAI,eAAe,EAAE,KAAK,aAAa,IAAI,aAAa,EAAE,CAAC;IAEtG,OAAO,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,gBAAgB,IAAI,gBAAgB,EAAE,KAAK,cAAc,IAAI,cAAc,EAAE,CAAC;CAC7G"}