"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tlds = exports.Spoof = exports.Spam = exports.Malicious = void 0;
var malicious_1 = require("./malicious.js");
Object.defineProperty(exports, "Malicious", { enumerable: true, get: function () { return malicious_1.Malicious; } });
var spam_1 = require("./spam.js");
Object.defineProperty(exports, "Spam", { enumerable: true, get: function () { return spam_1.Spam; } });
var spoof_1 = require("./spoof.js");
Object.defineProperty(exports, "Spoof", { enumerable: true, get: function () { return spoof_1.Spoof; } });
var tlds_1 = require("./tlds.js");
Object.defineProperty(exports, "Tlds", { enumerable: true, get: function () { return tlds_1.Tlds; } });
//# sourceMappingURL=index.js.map