// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../../resource.mjs";
import * as TldsAPI from "./tlds/tlds.mjs";
import { Tlds } from "./tlds/tlds.mjs";
export class Top extends APIResource {
    constructor() {
        super(...arguments);
        this.tlds = new TldsAPI.Tlds(this._client);
    }
}
Top.Tlds = Tlds;
//# sourceMappingURL=top.mjs.map