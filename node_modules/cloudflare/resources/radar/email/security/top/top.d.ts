import { APIResource } from "../../../../../resource.js";
import * as TldsAPI from "./tlds/tlds.js";
import { TldGetParams, TldGetResponse, Tlds } from "./tlds/tlds.js";
export declare class Top extends APIResource {
    tlds: TldsAPI.Tlds;
}
export declare namespace Top {
    export { Tlds as Tlds, type TldGetResponse as TldGetResponse, type TldGetParams as TldGetParams };
}
//# sourceMappingURL=top.d.ts.map