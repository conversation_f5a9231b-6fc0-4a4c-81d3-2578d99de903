{"version": 3, "file": "top.d.ts", "sourceRoot": "", "sources": ["../../../../../src/resources/radar/attacks/layer7/top/top.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAEtD,OAAO,KAAK,IAAI,MAAM,qBAAqB,CAAC;AAC5C,OAAO,KAAK,OAAO,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAClE,OAAO,KAAK,YAAY,MAAM,aAAa,CAAC;AAC5C,OAAO,EACL,oBAAoB,EACpB,sBAAsB,EACtB,oBAAoB,EACpB,sBAAsB,EACtB,SAAS,EACV,MAAM,aAAa,CAAC;AAErB,qBAAa,GAAI,SAAQ,WAAW;IAClC,SAAS,EAAE,YAAY,CAAC,SAAS,CAA4C;IAC7E,IAAI,EAAE,OAAO,CAAC,IAAI,CAAkC;IAEpD;;;;;;;;;;;;OAYG;IACH,OAAO,CAAC,KAAK,CAAC,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;IACrG,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;IAe3E;;;;;OAKG;IACH,QAAQ,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IACxG,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IAe7E;;;;;OAKG;IACH,QAAQ,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IACxG,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;CAc9E;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,IAAI,EAAE,kBAAkB,CAAC,IAAI,CAAC;IAE9B,KAAK,EAAE,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;CACvC;AAED,yBAAiB,kBAAkB,CAAC;IAClC;;OAEG;IACH,UAAiB,IAAI;QACnB,cAAc,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3C,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC;;WAEG;QACH,WAAW,EAAE,MAAM,CAAC;QAEpB;;;WAGG;QACH,aAAa,EACT,YAAY,GACZ,UAAU,GACV,SAAS,GACT,YAAY,GACZ,mBAAmB,GACnB,iBAAiB,GACjB,uBAAuB,GACvB,OAAO,CAAC;QAEZ;;WAEG;QACH,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzB;IAED,UAAiB,IAAI,CAAC;QACpB,UAAiB,cAAc;YAC7B,WAAW,EAAE,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAE9C;;eAEG;YACH,KAAK,EAAE,MAAM,CAAC;SACf;QAED,UAAiB,cAAc,CAAC;YAC9B;;eAEG;YACH,UAAiB,UAAU;gBACzB,UAAU,EAAE,MAAM,CAAC;gBAEnB,WAAW,EAAE,MAAM,CAAC;gBAEpB,OAAO,EAAE,MAAM,CAAC;gBAEhB,SAAS,EAAE,MAAM,CAAC;gBAElB;;mBAEG;gBACH,eAAe,EAAE,OAAO,CAAC;gBAEzB,SAAS,EAAE,MAAM,CAAC;gBAElB,SAAS,EAAE,MAAM,CAAC;aACnB;SACF;QAED,UAAiB,SAAS;YACxB;;eAEG;YACH,OAAO,EAAE,MAAM,CAAC;YAEhB;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;SACnB;QAED,UAAiB,IAAI;YACnB,IAAI,EAAE,MAAM,CAAC;YAEb,KAAK,EAAE,MAAM,CAAC;SACf;KACF;IAED,UAAiB,IAAI;QACnB,mBAAmB,EAAE,MAAM,CAAC;QAE5B,iBAAiB,EAAE,MAAM,CAAC;QAE1B,mBAAmB,EAAE,MAAM,CAAC;QAE5B,iBAAiB,EAAE,MAAM,CAAC;QAE1B,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,IAAI,EAAE,mBAAmB,CAAC,IAAI,CAAC;IAE/B,KAAK,EAAE,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;CACxC;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,IAAI;QACnB,cAAc,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3C,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC;;WAEG;QACH,WAAW,EAAE,MAAM,CAAC;QAEpB;;;WAGG;QACH,aAAa,EACT,YAAY,GACZ,UAAU,GACV,SAAS,GACT,YAAY,GACZ,mBAAmB,GACnB,iBAAiB,GACjB,uBAAuB,GACvB,OAAO,CAAC;QAEZ;;WAEG;QACH,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzB;IAED,UAAiB,IAAI,CAAC;QACpB,UAAiB,cAAc;YAC7B,WAAW,EAAE,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAE9C;;eAEG;YACH,KAAK,EAAE,MAAM,CAAC;SACf;QAED,UAAiB,cAAc,CAAC;YAC9B;;eAEG;YACH,UAAiB,UAAU;gBACzB,UAAU,EAAE,MAAM,CAAC;gBAEnB,WAAW,EAAE,MAAM,CAAC;gBAEpB,OAAO,EAAE,MAAM,CAAC;gBAEhB,SAAS,EAAE,MAAM,CAAC;gBAElB;;mBAEG;gBACH,eAAe,EAAE,OAAO,CAAC;gBAEzB,SAAS,EAAE,MAAM,CAAC;gBAElB,SAAS,EAAE,MAAM,CAAC;aACnB;SACF;QAED,UAAiB,SAAS;YACxB;;eAEG;YACH,OAAO,EAAE,MAAM,CAAC;YAEhB;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;SACnB;QAED,UAAiB,IAAI;YACnB,IAAI,EAAE,MAAM,CAAC;YAEb,KAAK,EAAE,MAAM,CAAC;SACf;KACF;IAED,UAAiB,IAAI;QACnB,IAAI,EAAE,MAAM,CAAC;QAEb,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,IAAI,EAAE,mBAAmB,CAAC,IAAI,CAAC;IAE/B,KAAK,EAAE,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;CACxC;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,IAAI;QACnB,cAAc,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3C,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC;;WAEG;QACH,WAAW,EAAE,MAAM,CAAC;QAEpB;;;WAGG;QACH,aAAa,EACT,YAAY,GACZ,UAAU,GACV,SAAS,GACT,YAAY,GACZ,mBAAmB,GACnB,iBAAiB,GACjB,uBAAuB,GACvB,OAAO,CAAC;QAEZ;;WAEG;QACH,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzB;IAED,UAAiB,IAAI,CAAC;QACpB,UAAiB,cAAc;YAC7B,WAAW,EAAE,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAE9C;;eAEG;YACH,KAAK,EAAE,MAAM,CAAC;SACf;QAED,UAAiB,cAAc,CAAC;YAC9B;;eAEG;YACH,UAAiB,UAAU;gBACzB,UAAU,EAAE,MAAM,CAAC;gBAEnB,WAAW,EAAE,MAAM,CAAC;gBAEpB,OAAO,EAAE,MAAM,CAAC;gBAEhB,SAAS,EAAE,MAAM,CAAC;gBAElB;;mBAEG;gBACH,eAAe,EAAE,OAAO,CAAC;gBAEzB,SAAS,EAAE,MAAM,CAAC;gBAElB,SAAS,EAAE,MAAM,CAAC;aACnB;SACF;QAED,UAAiB,SAAS;YACxB;;eAEG;YACH,OAAO,EAAE,MAAM,CAAC;YAEhB;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;SACnB;QAED,UAAiB,IAAI;YACnB,IAAI,EAAE,MAAM,CAAC;YAEb,KAAK,EAAE,MAAM,CAAC;SACf;KACF;IAED,UAAiB,IAAI;QACnB,IAAI,EAAE,MAAM,CAAC;QAEb,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,gBAAgB;IAC/B;;;;;OAKG;IACH,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAExB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAExB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;OAGG;IACH,cAAc,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAErC;;;OAGG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;;;OAIG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEzB;;;OAGG;IACH,SAAS,CAAC,EAAE,gBAAgB,GAAG,oBAAoB,CAAC;IAEpD;;OAEG;IACH,iBAAiB,CAAC,EAAE,KAAK,CACrB,MAAM,GACN,KAAK,GACL,gBAAgB,GAChB,cAAc,GACd,eAAe,GACf,YAAY,GACZ,sBAAsB,CACzB,CAAC;IAEF;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAErB;;;OAGG;IACH,aAAa,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;CAC1C;AAED,MAAM,WAAW,iBAAiB;IAChC;;;;;OAKG;IACH,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAExB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAExB;;OAEG;IACH,UAAU,CAAC,EAAE,KAAK,CACd,KAAK,GACL,MAAM,GACN,QAAQ,GACR,KAAK,GACL,MAAM,GACN,OAAO,GACP,SAAS,GACT,UAAU,GACV,OAAO,GACP,OAAO,GACP,KAAK,GACL,OAAO,GACP,SAAS,GACT,OAAO,GACP,WAAW,GACX,YAAY,GACZ,SAAS,GACT,UAAU,GACV,SAAS,GACT,MAAM,GACN,OAAO,GACP,MAAM,GACN,OAAO,GACP,YAAY,GACZ,aAAa,GACb,MAAM,GACN,QAAQ,GACR,YAAY,GACZ,MAAM,GACN,WAAW,GACX,QAAQ,GACR,QAAQ,GACR,WAAW,GACX,OAAO,GACP,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,QAAQ,GACR,gBAAgB,GAChB,iBAAiB,GACjB,aAAa,GACb,cAAc,GACd,aAAa,GACb,MAAM,GACN,MAAM,GACN,OAAO,CACV,CAAC;IAEF;;OAEG;IACH,WAAW,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAEpD;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAEnC;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;OAIG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEzB;;OAEG;IACH,iBAAiB,CAAC,EAAE,KAAK,CACrB,MAAM,GACN,KAAK,GACL,gBAAgB,GAChB,cAAc,GACd,eAAe,GACf,YAAY,GACZ,sBAAsB,CACzB,CAAC;IAEF;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACtB;AAED,MAAM,WAAW,iBAAiB;IAChC;;;;;OAKG;IACH,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAExB;;;;OAIG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAExB;;OAEG;IACH,UAAU,CAAC,EAAE,KAAK,CACd,KAAK,GACL,MAAM,GACN,QAAQ,GACR,KAAK,GACL,MAAM,GACN,OAAO,GACP,SAAS,GACT,UAAU,GACV,OAAO,GACP,OAAO,GACP,KAAK,GACL,OAAO,GACP,SAAS,GACT,OAAO,GACP,WAAW,GACX,YAAY,GACZ,SAAS,GACT,UAAU,GACV,SAAS,GACT,MAAM,GACN,OAAO,GACP,MAAM,GACN,OAAO,GACP,YAAY,GACZ,aAAa,GACb,MAAM,GACN,QAAQ,GACR,YAAY,GACZ,MAAM,GACN,WAAW,GACX,QAAQ,GACR,QAAQ,GACR,WAAW,GACX,OAAO,GACP,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,QAAQ,GACR,gBAAgB,GAChB,iBAAiB,GACjB,aAAa,GACb,cAAc,GACd,aAAa,GACb,MAAM,GACN,MAAM,GACN,OAAO,CACV,CAAC;IAEF;;OAEG;IACH,WAAW,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAEpD;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAEnC;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;OAIG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEzB;;OAEG;IACH,iBAAiB,CAAC,EAAE,KAAK,CACrB,MAAM,GACN,KAAK,GACL,gBAAgB,GAChB,cAAc,GACd,eAAe,GACf,YAAY,GACZ,sBAAsB,CACzB,CAAC;IAEF;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACtB;AAKD,MAAM,CAAC,OAAO,WAAW,GAAG,CAAC;IAC3B,OAAO,EACL,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,iBAAiB,IAAI,iBAAiB,GAC5C,CAAC;IAEF,OAAO,EACL,SAAS,IAAI,SAAS,EACtB,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,oBAAoB,IAAI,oBAAoB,GAClD,CAAC;IAEF,OAAO,EACL,IAAI,IAAI,IAAI,EACZ,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,eAAe,IAAI,eAAe,GACxC,CAAC;CACH"}