// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../../resource.mjs";
import * as SummaryAPI from "./summary.mjs";
import { Summary, } from "./summary.mjs";
export class TimeseriesGroups extends APIResource {
    constructor() {
        super(...arguments);
        this.summary = new SummaryAPI.Summary(this._client);
    }
}
TimeseriesGroups.Summary = Summary;
//# sourceMappingURL=timeseries-groups.mjs.map