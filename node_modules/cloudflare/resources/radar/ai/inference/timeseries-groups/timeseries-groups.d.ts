import { APIResource } from "../../../../../resource.js";
import * as SummaryAPI from "./summary.js";
import { Summary, SummaryModelParams, SummaryModelResponse, SummaryTaskParams, SummaryTaskResponse } from "./summary.js";
export declare class TimeseriesGroups extends APIResource {
    summary: SummaryAPI.Summary;
}
export declare namespace TimeseriesGroups {
    export { Summary as Summary, type SummaryModelResponse as SummaryModelResponse, type SummaryTaskResponse as SummaryTaskResponse, type SummaryModelParams as SummaryModelParams, type SummaryTaskParams as SummaryTaskParams, };
}
//# sourceMappingURL=timeseries-groups.d.ts.map