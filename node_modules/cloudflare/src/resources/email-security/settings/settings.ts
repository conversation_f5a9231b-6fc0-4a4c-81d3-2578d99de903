// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

import { APIResource } from '../../../resource';
import * as AllowPoliciesAPI from './allow-policies';
import {
  AllowPolicies,
  AllowPolicyCreateParams,
  AllowPolicyCreateResponse,
  AllowPolicyDeleteParams,
  AllowPolicyDeleteResponse,
  AllowPolicyEditParams,
  AllowPolicyEditResponse,
  AllowPolicyGetParams,
  AllowPolicyGetResponse,
  AllowPolicyListParams,
  AllowPolicyListResponse,
  AllowPolicyListResponsesV4PagePaginationArray,
} from './allow-policies';
import * as BlockSendersAPI from './block-senders';
import {
  BlockSenderCreateParams,
  BlockSenderCreateResponse,
  BlockSenderDeleteParams,
  BlockSenderDeleteResponse,
  BlockSenderEditParams,
  BlockSenderEditResponse,
  BlockSenderGetParams,
  BlockSenderGetResponse,
  BlockSenderListParams,
  BlockSenderListResponse,
  BlockSenderListResponsesV4PagePaginationArray,
  BlockSenders,
} from './block-senders';
import * as DomainsAPI from './domains';
import {
  DomainBulkDeleteParams,
  DomainBulkDeleteResponse,
  DomainBulkDeleteResponsesSinglePage,
  DomainDeleteParams,
  DomainDeleteResponse,
  DomainEditParams,
  DomainEditResponse,
  DomainGetParams,
  DomainGetResponse,
  DomainListParams,
  DomainListResponse,
  DomainListResponsesV4PagePaginationArray,
  Domains,
} from './domains';
import * as ImpersonationRegistryAPI from './impersonation-registry';
import {
  ImpersonationRegistry,
  ImpersonationRegistryCreateParams,
  ImpersonationRegistryCreateResponse,
  ImpersonationRegistryDeleteParams,
  ImpersonationRegistryDeleteResponse,
  ImpersonationRegistryEditParams,
  ImpersonationRegistryEditResponse,
  ImpersonationRegistryGetParams,
  ImpersonationRegistryGetResponse,
  ImpersonationRegistryListParams,
  ImpersonationRegistryListResponse,
  ImpersonationRegistryListResponsesV4PagePaginationArray,
} from './impersonation-registry';
import * as TrustedDomainsAPI from './trusted-domains';
import {
  TrustedDomainCreateParams,
  TrustedDomainCreateResponse,
  TrustedDomainDeleteParams,
  TrustedDomainDeleteResponse,
  TrustedDomainEditParams,
  TrustedDomainEditResponse,
  TrustedDomainGetParams,
  TrustedDomainGetResponse,
  TrustedDomainListParams,
  TrustedDomainListResponse,
  TrustedDomainListResponsesV4PagePaginationArray,
  TrustedDomains,
} from './trusted-domains';

export class Settings extends APIResource {
  allowPolicies: AllowPoliciesAPI.AllowPolicies = new AllowPoliciesAPI.AllowPolicies(this._client);
  blockSenders: BlockSendersAPI.BlockSenders = new BlockSendersAPI.BlockSenders(this._client);
  domains: DomainsAPI.Domains = new DomainsAPI.Domains(this._client);
  impersonationRegistry: ImpersonationRegistryAPI.ImpersonationRegistry =
    new ImpersonationRegistryAPI.ImpersonationRegistry(this._client);
  trustedDomains: TrustedDomainsAPI.TrustedDomains = new TrustedDomainsAPI.TrustedDomains(this._client);
}

Settings.AllowPolicies = AllowPolicies;
Settings.AllowPolicyListResponsesV4PagePaginationArray = AllowPolicyListResponsesV4PagePaginationArray;
Settings.BlockSenders = BlockSenders;
Settings.BlockSenderListResponsesV4PagePaginationArray = BlockSenderListResponsesV4PagePaginationArray;
Settings.Domains = Domains;
Settings.DomainListResponsesV4PagePaginationArray = DomainListResponsesV4PagePaginationArray;
Settings.DomainBulkDeleteResponsesSinglePage = DomainBulkDeleteResponsesSinglePage;
Settings.ImpersonationRegistry = ImpersonationRegistry;
Settings.ImpersonationRegistryListResponsesV4PagePaginationArray =
  ImpersonationRegistryListResponsesV4PagePaginationArray;
Settings.TrustedDomains = TrustedDomains;
Settings.TrustedDomainListResponsesV4PagePaginationArray = TrustedDomainListResponsesV4PagePaginationArray;

export declare namespace Settings {
  export {
    AllowPolicies as AllowPolicies,
    type AllowPolicyCreateResponse as AllowPolicyCreateResponse,
    type AllowPolicyListResponse as AllowPolicyListResponse,
    type AllowPolicyDeleteResponse as AllowPolicyDeleteResponse,
    type AllowPolicyEditResponse as AllowPolicyEditResponse,
    type AllowPolicyGetResponse as AllowPolicyGetResponse,
    AllowPolicyListResponsesV4PagePaginationArray as AllowPolicyListResponsesV4PagePaginationArray,
    type AllowPolicyCreateParams as AllowPolicyCreateParams,
    type AllowPolicyListParams as AllowPolicyListParams,
    type AllowPolicyDeleteParams as AllowPolicyDeleteParams,
    type AllowPolicyEditParams as AllowPolicyEditParams,
    type AllowPolicyGetParams as AllowPolicyGetParams,
  };

  export {
    BlockSenders as BlockSenders,
    type BlockSenderCreateResponse as BlockSenderCreateResponse,
    type BlockSenderListResponse as BlockSenderListResponse,
    type BlockSenderDeleteResponse as BlockSenderDeleteResponse,
    type BlockSenderEditResponse as BlockSenderEditResponse,
    type BlockSenderGetResponse as BlockSenderGetResponse,
    BlockSenderListResponsesV4PagePaginationArray as BlockSenderListResponsesV4PagePaginationArray,
    type BlockSenderCreateParams as BlockSenderCreateParams,
    type BlockSenderListParams as BlockSenderListParams,
    type BlockSenderDeleteParams as BlockSenderDeleteParams,
    type BlockSenderEditParams as BlockSenderEditParams,
    type BlockSenderGetParams as BlockSenderGetParams,
  };

  export {
    Domains as Domains,
    type DomainListResponse as DomainListResponse,
    type DomainDeleteResponse as DomainDeleteResponse,
    type DomainBulkDeleteResponse as DomainBulkDeleteResponse,
    type DomainEditResponse as DomainEditResponse,
    type DomainGetResponse as DomainGetResponse,
    DomainListResponsesV4PagePaginationArray as DomainListResponsesV4PagePaginationArray,
    DomainBulkDeleteResponsesSinglePage as DomainBulkDeleteResponsesSinglePage,
    type DomainListParams as DomainListParams,
    type DomainDeleteParams as DomainDeleteParams,
    type DomainBulkDeleteParams as DomainBulkDeleteParams,
    type DomainEditParams as DomainEditParams,
    type DomainGetParams as DomainGetParams,
  };

  export {
    ImpersonationRegistry as ImpersonationRegistry,
    type ImpersonationRegistryCreateResponse as ImpersonationRegistryCreateResponse,
    type ImpersonationRegistryListResponse as ImpersonationRegistryListResponse,
    type ImpersonationRegistryDeleteResponse as ImpersonationRegistryDeleteResponse,
    type ImpersonationRegistryEditResponse as ImpersonationRegistryEditResponse,
    type ImpersonationRegistryGetResponse as ImpersonationRegistryGetResponse,
    ImpersonationRegistryListResponsesV4PagePaginationArray as ImpersonationRegistryListResponsesV4PagePaginationArray,
    type ImpersonationRegistryCreateParams as ImpersonationRegistryCreateParams,
    type ImpersonationRegistryListParams as ImpersonationRegistryListParams,
    type ImpersonationRegistryDeleteParams as ImpersonationRegistryDeleteParams,
    type ImpersonationRegistryEditParams as ImpersonationRegistryEditParams,
    type ImpersonationRegistryGetParams as ImpersonationRegistryGetParams,
  };

  export {
    TrustedDomains as TrustedDomains,
    type TrustedDomainCreateResponse as TrustedDomainCreateResponse,
    type TrustedDomainListResponse as TrustedDomainListResponse,
    type TrustedDomainDeleteResponse as TrustedDomainDeleteResponse,
    type TrustedDomainEditResponse as TrustedDomainEditResponse,
    type TrustedDomainGetResponse as TrustedDomainGetResponse,
    TrustedDomainListResponsesV4PagePaginationArray as TrustedDomainListResponsesV4PagePaginationArray,
    type TrustedDomainCreateParams as TrustedDomainCreateParams,
    type TrustedDomainListParams as TrustedDomainListParams,
    type TrustedDomainDeleteParams as TrustedDomainDeleteParams,
    type TrustedDomainEditParams as TrustedDomainEditParams,
    type TrustedDomainGetParams as TrustedDomainGetParams,
  };
}
