// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

import { APIResource } from '../../../resource';
import * as TokensAPI from './tokens';
import { TokenCreateParams, TokenCreateResponse, Tokens } from './tokens';

export class VPCFlows extends APIResource {
  tokens: TokensAPI.Tokens = new TokensAPI.Tokens(this._client);
}

VPCFlows.Tokens = Tokens;

export declare namespace VPCFlows {
  export {
    Tokens as Tokens,
    type TokenCreateResponse as TokenCreateResponse,
    type TokenCreateParams as TokenCreateParams,
  };
}
