// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

import { APIResource } from '../../../resource';
import * as ReportsAPI from './reports/reports';
import { ReportGetParams, Reports } from './reports/reports';

export class Analytics extends APIResource {
  reports: ReportsAPI.Reports = new ReportsAPI.Reports(this._client);
}

Analytics.Reports = Reports;

export declare namespace Analytics {
  export { Reports as Reports, type ReportGetParams as ReportGetParams };
}
