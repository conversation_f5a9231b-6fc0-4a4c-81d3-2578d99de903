// <PERSON><PERSON>t to create VA<PERSON> assistant with webhook integration
// Run this with: node setup-vapi-assistant.js

const VAPI_API_URL = 'https://api.vapi.ai/assistant';

async function createVapiAssistant() {
  const assistantConfig = {
    name: "Aven AI Customer Support Assistant",
    model: {
      provider: "openai",
      model: "gpt-4o-mini", 
      temperature: 0.7,
      systemMessage: "You are Aven's AI voice assistant. For ANY question about Aven services, products, rates, features, or policies, you MUST use the searchKnowledge function to get accurate information. Do not answer from general knowledge - always search the knowledge base first."
    },
    voice: {
      provider: "11labs",
      voiceId: "21m00Tcm4TlvDq8ikWAM"
    },
    transcriber: {
      provider: "deepgram",
      model: "nova-2",
      language: "en-US"
    },
    functions: [
      {
        name: "searchKnowledge",
        description: "Search Aven's knowledge base for information about products, services, rates, features, or any company-specific details. ALWAYS use this for Aven-related questions.",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "Search query for the knowledge base"
            }
          },
          required: ["query"]
        }
      }
    ],
    serverUrl: "YOUR_DOMAIN/api/voice/webhook",
    endCallMessage: "Thank you for contacting Aven!",
    maxDurationSeconds: 1800
  };

  console.log('Creating VAPI assistant...');
  console.log('Config:', JSON.stringify(assistantConfig, null, 2));
  
  try {
    const response = await fetch(VAPI_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP ${response.status}: ${error}`);
    }

    const assistant = await response.json();
    console.log('✅ Assistant created successfully!');
    console.log('Assistant ID:', assistant.id);
    console.log('Add this to your .env.local:');
    console.log(`NEXT_PUBLIC_VAPI_ASSISTANT_ID=${assistant.id}`);
    
    return assistant;
  } catch (error) {
    console.error('❌ Error creating assistant:', error);
    throw error;
  }
}

// Check if running directly
if (require.main === module) {
  // Get API key from environment or command line
  const apiKey = process.env.VAPI_PRIVATE_KEY || process.argv[2];
  const domain = process.env.WEBHOOK_DOMAIN || process.argv[3];
  
  if (!apiKey) {
    console.error('Please provide VAPI_PRIVATE_KEY:');
    console.error('node setup-vapi-assistant.js YOUR_VAPI_PRIVATE_KEY YOUR_DOMAIN');
    console.error('Or set VAPI_PRIVATE_KEY environment variable');
    process.exit(1);
  }
  
  if (!domain) {
    console.error('Please provide your domain:');
    console.error('node setup-vapi-assistant.js YOUR_VAPI_PRIVATE_KEY YOUR_DOMAIN');
    console.error('Example: node setup-vapi-assistant.js vapi_abc123 https://myapp.vercel.app');
    process.exit(1);
  }
  
  process.env.VAPI_PRIVATE_KEY = apiKey;
  process.env.WEBHOOK_DOMAIN = domain;
  
  createVapiAssistant().catch(console.error);
}

module.exports = { createVapiAssistant };