The team at Priceline discussed evaluations (evals) primarily in the context of their Penny Generative AI agent, particularly concerning its content output moderation.
Here's what they talked about and the evals they used:
•
Content Output Moderation Evals:
◦
They implemented an eval process for content output moderation. This was chosen over input moderation because the messages move too quickly for effective input moderation and was a recommendation from OpenAI.
◦
The process involves checking if <PERSON>'s output content requires moderation.
◦
If moderation is required, they take the transcript of <PERSON>'s output and use a separate Large Language Model (LLM) call to assess its validity or compliance. This helps them determine if <PERSON> "did a good job".
•
Telemetry for Running Evals:
◦
While their initial real-time voice telemetry setup was not ideal (a single span for each session rather than each transaction), it still provided "so much valuable data" and made development "a lot easier".
◦
They are currently working on an update to their telemetry system to be transaction-level based, which was suggested by Arize AI and makes it "so much easier to run evals and stuff on". This indicates that effective data collection is crucial for their evaluation processes.
•
Future Enhancements for Evals:
◦
A significant upcoming enhancement involves the ability to link to recorded audio in the user interface. This will allow them to "write evals against things that we just wouldn't be able to write evals about" if they didn't have access to the actual audio.

---
hi this is <PERSON> heluk from root signals uh presenting agent evaluations finally with the map so the agent evaluation is rather Art and Science and ultimately it is nonetheless required to actually ensure that your agents do what you expect them to do especially when you're launching them in production so without further Ado let's just uh Dive Right There into the map and start to get an understanding of what are all the things involved if you want to evaluate all the aspects of your agents uh so the agent evaluation can be actually neatly divided to the semantic and behavioral parts of what the agents do the semantic part is all about how do the representations uh of the reality that the agent has actually relate to the reality whereas the behavioral parts are all about how do the actions and the tools that the agent is using actually contributing to the achievement of the agent's goals in its environment and ultimately what kind of effects it will have on its environment as well the semantic part is uh further divided into what could be call the single step or single turn uh items which are like coherence consistency Etc so various sematic virtues and what can be called the multi- turn aspect of the same so this Rel to chatter reasoning whereas in the behavioral part you also have this distinction whether you're looking at the task progression and planning or then individual selection and usage of tools so let's uh dive into each of these and paying also attention to the fact that truthfulness is ultimately achiev by grounding uh the representations in your data often with track whereas uh the goal achievement is what actually uh is achieved by grounding to the tools that the agent has available and the this is sort of a symmetry that is not accidental because in a sense there's similarity and analogy between uh representations and behaviors because representing the world is a kind of activity so you can see that uh that the representations are in a sense a special case of tools special case of uh uh of behaviors and uh let's then dive into these individual parts of the map so first looking at the semantic quality for the single turn case so here we have these Universal virtues there's a long list of these that you can look at uh and these are actually non agenic in a sense and we are only covering them here because of completeness so that you can sort of understand these agent Parts through the contrast so these are things like uh uh is the uh the reply that the agent is giving to the user is this consistent is the is the content actually safe and so on and so on and then there's the inter interesting and important part of whether whatever the agent is actually saying if it's uh aligning uh with the values of uh of the organizations or people uh who are the stakeholders and also whether it aderes to the policies of the same rag or what could be more generally called yet attention management uh is then something that you need to measure through specific evaluators such as those looking at whether the retreat context uh was correct whether all of it was comprehensively recalled uh and so on and so on and ultimately relating the answers uh to the external reality through what is often called the Fai fulness which is then separate from uh from the both the answer question relevance and also from the notion of factfulness in general which relates to the reality Beyond just the reference data that the rag is using so now moving forward uh we should also pay attention to the fact that the rag evaluations actually come uh in many forms and they they also have certain symmetries uh that are not that difficult to understand when you look at them uh through uh a map like this so they are all just essentially looking at different relationships between the parts of the rag pipeline but this is not the the main topic of the presentation so you can look look up more information through the uh background material links that people provide so then moving to the multi-turn case the multi-turn in the semantic quality sense means essentially chat conversation histories how do these develop what sort of things we want to look for in this consistency Etc adherence and sticking to the topics when that is actually necessary sometimes we want to allow changing topics if the for example a chatbot users user wants to change the topic or sometimes we don't but we need to be able to uh be aware of this then super important uh and almost groundbreaking progress has been achieved by uh by uh investments in reasoning so you can also uh actually evaluate reasoning traces the reasoning uh Chain of Thought if if allowed to use that term here and that's sort of another way of looking at the kind of sequential or M well multi- turn and sequential kinds of activities that the agent is just doing in the course of its reasoning and representations of the world before we even taking any kind of actions and now we move to the side so here we have uh things to evaluate such as where the agent is actually following instructions is it extracting the the tool characteristics correctly is it selecting the right is the right tool being selected is the output quality of the tools correct are the error situations handled correctly are the structures interation tool format are they uh correct so these are all all the things that relate to uh to the behaviors even before you have a chain of uh behaviors and when you move to this chain of behaviors or multistep or uh multi-turn uh cases so then you actually start to look at like are the actions that the ACT agent is taking converging towards actually achieving its goal and uh is the plan that the agent might have actually consistent and is it actually uh high quality in whatever sense you want to measure that uh so then both of these are actually grounded uh on external reality so uh like mentioned the representations are grounded on truthfulness and uh the activities and behaviors are ultimate grounded by goal achievement and utility uh and this is uh what sort of uh is the ultimate uh metric to everything else and know uh that the agent is trying to do and all these other things along the way are sort of just proxy metrics so to speak uh moving to other practical considerations we can only scratch the surface here so I'm just sort of listing not to leave this out but they don't needly fall into this general map that I presented but should also be taken into account on the background so most important of which is the cost and latency optimization so generally you want to the agent to progress towards its goal as quickly as possible and as cheaply as possible so cost and latency optimization the number of steps optimization and then moving to the what you ofer are going to need which is the tracing and debugging so being able to actually see where the agent go wrong error management here refers to to dealing with the with the errors of the tool usage so not the actual like semantic errors that that the agent is doing in the course of its inference and thinking process uh very important distinction to keep in mind is the offline versus online testing so what are the things that uh that you can actually uh try out and evaluate during development and what are the things that you can do and must do or should do uh during the actual online activities of the agent and these are actually going to become like a two very distinct Dimensions that could be also included on the map but uh but that would make the map map more complicated so that's why I'm just mentioning them here now separately and then we have various special cases that I don't have time to cover some of these uh are uh more refined and more advanced than others and and some of these things are sort of uh more researchy and there's a lot of papers on each of these topics and uh uh depending on what you're doing tool specific metrics might actually be useful to add to the mix even uh uh and they might even be rather simple to implement because the tools are often something as easy as straightforward as like API calls and uh and such things that can actually be just measured separately uh using more traditional software testing methodologies uh one important part to mention here is that a lot of these measurements are going to to be implemented with llm as charge techniques and now the caveat uh behind this is that the quite often when people start implementing these evaluation methodologies they are looking at this kind of a single tier approach single tier here means that you're focusing on optimizing this operative element flow meaning that you're optimizing your agent which makes sense but if you only are concerned of uh with getting some scores on what the agent is doing then you're sort of forgetting that what about all the cost latencies uncertainty related to the charge itself the charge that is on the background so you should be actually uh taking into account as early as possible that you are going to need to also optimize the charge itself and this is what the what we could call Double tier so you need to optimize both the operative LM flow that powers your agent and then this chargement flow that actually Powers your evalu ations and uh this is a rather complex situation in general and we are calling this eval Ops because uh this seems like a like a separate kind of thing that involves uh evaluations that themselves are so complicated uh so expensive and slow that they sort of earn their own uh category of of activities and this is uh something that uh that we have written about and uh and you can also find more about this on the source materials uh the general general tast of it is that that eval Ops is a kind of a special case of llm Ops and and actually operates on different kinds of entities that that the llm Ops uh uh in general and uh and then it also requires different ways of thinking different kind of software implementations and also essentially a different kind of resourcing to to get those evaluations right so thank you very much this was only a brief Glimpse on the on the general landscape I hope the map is helpful to you and uh please take a look at the source materials which will give you more depth to each of these uh these topics and uh happy to discuss any of these things and let me uh know if I'm forgetting something crucial and uh this uh presentation will of course be obsolete by the time it it goes out so uh probably when when you're watching this there's already some major developments have happen but that's uh that's how it is on this General Life of uh of uh AI Engineers uh so let's go out there and make our agents measurable controllable and let's make sure they are actually doing our biding and not not rebelling against our uh ultimate intentions thank you very much 

-----
 
Me: By spirit  
Them: My experience building an agent for SuiteBench verified is kind of our case study. SuiteBench verified for those that aren't particular is kind of the canonical AI coding evaluation. We'll we'll get into a little bit more what that is as a refresher a few slides in. Here. Let me set my timer, actually, just to make sure we're on time. K. So okay. AI coding. Let's take a kind of zoom out and and think about what's been going on in the space for the past couple of years. So 2023 was completion models. So this is, like, GitHub Copilot. 2024 was when people started using chatbots to help write code. This is in particular when people found that, like, Cloud, it was really helpful when it could, like, look at your code base or look at files. 2025 is really about agents. Now if we look under the hood and think about the underlying retrieval tasks that are relevant to each of these products, the retrieval complexity is only getting higher. So for completions, the kind of deciderata for retrieval system is like okay. It needs to be super low latency, so that's hard. But the retrieval is not super difficult for code at least. So it's like, I'm trying to complete a function. Let's say it's a implementation of a method that lives on the interface. Well, I probably wanna go retrieve other, implementations of that interface, for inspiration. And so you kinda just need to, like, copy words, like, look for, like, other locations where that function name is present. And that's clearly not that hard of retrieval task. The only thing that's annoying is how latency sensitive it is. Then we go to chat box in 2024, And with chatbots, things are getting a little bit harder. I may have a question like, okay. Let's say I'm building kind of like an LLM power application. I may have a question from my chatbot, like, how, do I abstract away the LLM SDK that I'm using from my application. So what that means is, you know, maybe I wanna use OpenAI model sometime, some I wanna use Gemini models, and I wanna have, like, one consistent API for all these and not have to use the Gemini SDK sometimes and the OpenAI SDK and other times. And so that probably means, you know, creating some kind of interface on top of on top of the, OpenAI client and on top of the Gemini client. And then the question is, is you know, okay. That were that that chat model, can it find the locations of the Gemini SDK and the OpenEye SDK in your code base, can it kinda figure out, like, okay. How do I abstract these? And can I figure out maybe where should I put that abstraction? And this is a bit more of a challenging retrieval problem. And so here was when we really found, like, training embedding models becomes a little bit more important. Now with agents, it's getting a lot more complicated. Now the agent has to understand many different parts of your code base. It has to through different files. It say, like, looks through a 100 k tokens worth of files, and then it needs to truncate those from the prompt and go look at another 100 k worth of tokens. So things are only getting more complicated. A quick refresher on on what exactly do we mean by retrieval here. So, you know, everyone thinks about embedding models, but there's really a lot of different techniques that could be used. There's good old BM 25, which, by the way, for completions, we found was extremely good. There's embedding models, which have a little bit more flexibility or good for less structured content. Good when you're not doing just similarity, but you're doing some kind of more complex mapping you're trying to model in the retrieval. And then there's, like, other tools like GREP and PageRank. Quick plug on, you know, this work. So as I mentioned, we were making this agent that could perform on this eval called a SWE bench. So SuiteBench is essentially, like, I give, the agent a description of a coding problem, that has access to a code base. There are some held out tests and some not held out tests, and then the agent has to go implement the that PR description in that code base, and then we run all the tests at the end and see if it passes. It's able to do things like you know, use whatever tools we we provide it, whether it's iterating through files or or running tests. So we provide we created this kind of open source repo for this, and it has all this simple stuff you may need. It has a bash, bash tool. File viewing tools. It has this notion of, like, a sequential thinking tool. And then everything else, like Docker harnesses so that the agent can run code and so forth. We ended up getting to the top of this eval, So you see that's that's us there up top, Augment agent v zero. One of the most interesting facts about this work was that we just gonna read this quote from our blog. We explored adding various embedding based retrieval tools but found that for Sweebench tasks, this, was not the bottleneck. GREP and find were sufficient. So this is really interesting, and frankly, the first time that I saw this result, I was, like, surprised. I I thought maybe there there's, like, a bug in our evals or something because, like, clearly embedding models should be so much more powerful than grep and find. And so we'll we'll kind of look at some examples and try to figure out. You know, does this data point indicate that, you know, traditional reg as we know it is is obsolete. So here we're looking at, like, a very specific SWE bench problem. So I'm just gonna read through it so we can get some context. So this is pulled from an actual PR in GitHub. An actual issue on GitHub. Modeling separability matrix does not compute separability correctly for nested compound models. Consider the following model, and there's some code. Acceptability matrix as you might expect, is diagonal. It's a more code. I make the model more complex and more code. And then user starts talking about how in a particular situation, these inputs, outputs are no longer separable. And it's not really important here is the separability matrix, but, like, just you know, it's good to kinda get a sense of what is the problem. So there's this code base called Astropy. There's this function called separability matrix in this file. And there's some property of it that seems buggy. So now let's look at what the agent does to try to solve this problem. So here, I actually took all of our agents' tool calls and LLM responses and, and and kinda just paste to them here. We can kinda click through these and and and kinda get a sense of of what it's doing under the hood. So first, you know, we we have this initial prompt, which which obviously contains all this, and we ask the agent to to figure out how to fix it. Then then the agent responds, I'll help you implement the necessary changes to fix the issue with the separability matrix function for nested compound models. Let's start by exploring the repository structure to get familiar with it. Then it calls a bash tool using find and it looks for all files that have the prefix in the name set so it's like separability, separable, and then it finds this separable dot py file, and then it uses a file reading tool to look at that file. And then it says, now let's also look at the compound model class to understand how it's structured. It looks in this file core dot py. And it greps for the word modeling. Ben doesn't quite find anything, so now uses a file reading tool. And it reads lines one to 50 for port dot pi You'll notice here it's just reading 50 lines instead of reading all the file which you did separable for separable dot py. One, because this is an option. It can optionally just look at a few lines. But also, and it's prompted to try to, you know, as little token budget as possible, but, also, this is a much larger file. It doesn't find what it's looking for, and so it's like, let's look at the compound model class. It does a grep. It still doesn't find what it's looking for. It does another file read. Still doesn't find what it's looking for and does another grep. Still doesn't find what or actually, it finds it then. And then and then now it's, like, looking for the function, so now it does another grep. And so, you know, and then and then it kinda goes on and it does implement the the correct solution. And so my takeaway looking at this was, well, agents are kinda dumb. But they're persistent. So they eventually find their way around with GREP and FIND. In other words, the agent's persistence is compensating for worse tools. So there are some benefits of this agentic retrieval with GREP and FIND. One, iterative retrieval is super easy. So if we if any of you have seen what, like, an old school iterative retrieval setup might look like, it's something like in this bottom right. You have a prompt one, which is your query. You put that in embedding model. You dot product against the knowledge corpus. Get some top k documents. Now you have new prompt that's query plus those top k documents. You put that into a new a second embedding model, which you've trained differently, You dot product that against the  
Me: Your  
Them: to  
Me: Okay.  
Them: Talk it. And then you have to decide how to balance your resultant prompt between the set of top k documents and this set of top k documents. Prompt token budget management is a lot easier. So this is some code from our code base and, basically, what happens is, you know, the agent's iteratively doing this this crazy and grabbing all these different files. And anytime we hit our token budget limit, which is I think it was a 150 k tokens, we just truncate all the old tool calls. So here, we replace all the sequential tool calls with truncated. We run tool if you need to see output input output again, and then we do the same for other for other tools. And so as a result, the agent, you know, can use up all of its prompt space. It never, hits any any kind of errors with that. And if it needs to go look at the output of some old tool call, like some old grep, for example, can just rerun that grep. It's super low effort to build and maintain. So with vector databases, you obviously have this whole new dependency and this whole new database you have to deal with. You have, say, a sync you have to manage. So, you know, with completions, we have, say, like, fifty millisecond service level agreement for how quickly we update the documents on the client. So in that Versus Code editor or whatever your editor is, and and our servers, None of that you have to worry about anymore. And finally, there's this really cool course correction property. Where if the first call to say the bachelor tool with find or or with grep doesn't work, then it tries again, and maybe this is a view tool. Then it tries again. It uses it uses grep. But the challenges with grep and find are they're hard to scale, So imagine doing GREP and FINE against 10,000,000 files. What about a 100,000,000 files? You definitely can't replace PageRank on Google with with Grep and Find. And they also don't work particularly well with low structure natural language. So if we look at the bottom right here, it's just some random code snippet I picked out There are all these high signal words in it. So there's text prompt, text results, token counter, count tokens, and these make it really easy to figure out how to grep things, like again, that go back to this there's, like, sepra star. Like, that works because code is super is super structured. That's not necessarily true for, like, other kinds of content. Or or information you you might wanna retrieve over. So what's the best of both worlds? You can imagine this agentic loop, but with access to great embedding models is as tools. In order to build this you still build your underlying embedding system per usual, and you just surface it to your agent as a tool similarly how we to how we surface GREP and find these tools. There are gonna be some potholes when applying this to other domains. One of my hypotheses that I haven't you know, really confirmed but I think maybe true is that anthropic models are post trained to be really good at agentic code search specifically, but I'm not sure how well they that generalizes to other domains. As I just mentioned, code has a lot of high signal keywords. And also all of this is is pretty slow and expensive. So when we are trying to decide then, okay, how do we architect this retrieval system? We may have some rag. So the traditional RAG may have this agentic retrieval. The embedding tool is one option. The Grep and find tools are other options. There are different axes we can consider, and each of these axes is different weight depending on what you're building. So there's quality, so how good is the the final output, There's latency, so it's you know, the embedding models, you know, as I mentioned, are, like, fifty millisecond SLA. Agents can take many minutes. Cost, so the agents are much more agentic, which was much more expensive. Reliability, agentic retrieval has that course correction. But embeddings do not. Their size of index, so rep and find work totally great for, you know, medium sized indices, but for truly large small to medium sized indices, but for truly large indices, it's just too slow. And then there's the effort to build and maintain the your retrieval system Do you have the the engineering resources to deal with maintaining some index and updating it and whatnot? So we look at some examples of some different kind of configurations of systems, so, again, traditional retrieval, it's okay quality, it's really fast, it's really cheap, The reliability isn't great because there's no course correction. It's super scalable, and it kinda depends on the effort to build and maintain it. The main factor here is, I think, whether or not you're training your own embedding models. This kind of agent plus grep find tool, the quality is really great, but it may degrade in certain situations like really large indices, or when you're retrieving or searching over content that's super low structure where like, keyword searches don't work well. It's slow. It's expensive. The reliability is great because agents are persistent. They're course correcting. And this also is gets even much better when the agent has access to some kind of signal that it's in the right direction. So for coding tools, you can the agent has access to being able to run tests. And so if it gets kinda stuck in some direction and has some hypothesis for you know, kind of a task it's trying to complete, it can spin up an ephemeral file, run some code, see the output, and then use that to kind of inform how it continues to do the the code search. The size of the index, well, it's it's not that scalable as we talked about. But the effort to build and maintain it is is super easy. And then finally, this kind of third option I mentioned, which is the agent plus an embeddings based tool. The quality is great. It's still slow and expensive, but we get everything else. So  
Me: Issus. Hello?  
Them: get the reliability, persistent course correcting,  
Me: Okay. Okay.  
Them: It now handles large indices, and the effort to build and maintain the retrieval system. It's mostly easy because you have less of a need to train embedding models because agent can just be persistent. I know, you know, that depends. Maybe you wanna optimize things further. I have agentic loop and also a really good embedding model that can help. I wanna go through a couple, like, preemptive q and a's and then we can kind of talk through questions. So one, I already built a retrieval system with custom trained and Bing models. Do I check it and build agentic retrieval? It should be clear right now. No. Build agentic retrieval on top of it. Just take your existing system and add it as a tool to an agent loop. How do I eval agentic retrieval? End to end, vibe eval first. So five to 10 examples. Then consider quantitative end to end eval or eval just of the tool. Note that improving embedding tool won't necessarily help because the agent is persistent. So I'll give some anecdotes here from our experimentation. So were, like, working on an embedding based tool for SuiteBench in particular, And we had some evals that were using the kind of hill climb just on that embedding model. And we improved the embedding model, but we found that the end to end result didn't improve because the was super persistent. So you always wanna start with that end to end eval first and then kind of go from there depending on where where the system is struggling. I'm starting from scratch. Do I start? I'd suggest building the dumbest retrieval tools possible. Put an agent loop on top, iterate from here on whichever qualities of the system cause the most pain for customers or for you, and follow the previous eval instructions. And then the last question or last topic in traditional reg, I can improve my system by training better embedding models. How do I improve my system with AgenTeq retrieval? So in general, I would just say build better tools. So this is a little anecdote. Agents are getting radically smarter, but even Einstein preferred writing on paper instead of a stone tablet. So, yes, these agents are persistent. Whatever. You can give them to improve the odds that they find what they're looking for. So that could mean adding a re ranker to your embedding tool. So one benefit here is you spend less tokens, so it's cheaper. Maybe it's also faster because the agent messes up less because you have precision on that embedding tool is higher. You can try training different embedding models for different tasks. Maybe you have one embedding model that's really good at answering questions about messages in Slack. Another one that's really good at looking over the code base. You can provide these as separate tools. You also wanna prompt tune your tool schemas to avoid pitfalls. So for example, if we look at our bash tool, which as you as we saw previously, our SuiteBench agent was using to call grep and find and whatnot. We had to prompt tune a little to tell it, like, this is how you use grep. And, like, this is how you use find. And, like, by the way, like, don't do x and y regexes because those will be really slow. Yeah. So that's kinda that's kinda everything. I'll pause now, and we can kinda talk through any questions. Hopefully, that was all stimulating. This is great. I have a bunch of questions. In my mind, but let's go over some of the questions on the Slido. Marion just shared the link back in the chat. So feel free to just go on there, look at the questions you want to ask, upload the ones you you care about. I think the first question on everyone's mind is actually gonna be around the frameworks that you use. So how did you decide what tools to build? Did you build the framework yourself? You talk a little bit more about that? Definitely. You know, I so I built the framework myself. Let's go all the way back here. I've shared a link to the GitHub on chat. So I built I built the framework myself. I found you need something very simple. Like, it's very hard to you know, I looked at some of the existing frameworks out there, and they were nice. But I I just I wanted something super simple. Like, I can yeah. Let's see. I will can you still see my code base Yep. Or my screen? K. I'm just gonna, like, walk you through a few things in this code base just to give you an example. So we have this tools directory. There's this very simple notion of an LLM tool. Let's see. LLM tools in a UTills common. Utils common dot pi. Yeah. So there's this very simple abstraction. It's LLM tool. It has a name, a description, and input tool schema. There's, like, a few commands on it. Then you have this notion of a dialogue messages, which is where we keep track of, like, the the dialogue history for the agent. This needs to have options to add user prompts and then also agent prompts and tool calls. And then finally, need some kind of truncation strategy. And this is one thing that might be interesting to talk about is, like, how to think about truncation strategies. But once you have those components, you have that dialogue messages object, you have this LLM tool abstraction, then you're like, pretty much good to go. You yeah. That's pretty much it. Then you just call the the agent a loop. I for at the top level agent, I actually just implement the l this LLM tool extraction. It just has, like, separate LM tools. It's, like, the the dependencies. So given those are the only things that are necessary, like, I didn't really use any frameworks. Yeah. That makes sense. I think a lot of people sort of at least the high level practitioners have generally leaned on things yourself just because you want fine control over the prompts and how things are called and and whatnot. Those are just so simple too. Yeah. Is this I'm working on several new projects right now. And because I I I I left Augman recently to experiment on some different projects, and I built several agents, and I literally and for total things totally unrelated to code, and I literally just forked to this code base. And, like, made a handful of changes, and it, like, pretty much just works. Alright. So there's gonna be a new agent framework that you're gonna release sometime in the near future. Hold on. I'd love for you to comment on that, like, one conversation we had earlier like, last month where it was just, you know, do I give my agent, like, access of control, like, command line and have it do everything, or do you build out these specific, like, search tools like grab find and all that? You mentioned it sort of really changed the distribution of how these things behaved. Could you talk a little bit more about that too? Yeah. I mean, I guess like, I mean, one interesting thing here is, like, we don't really give it a grep find tool. We just give access to a bash tool, and it can kinda do whatever it wants And so you do wanna give it a a kind of this maximum flexibility And this works because it's super persistent. So, like, when it messes up, when it, like, runs the wrong command, then it will, like, figure out how to run the right command and and go from there. But but, yeah, again, like, if you if you already have a good embedding system, I wouldn't, like, throw it away. It's, like, probably gonna be better than than bash. Yeah. Does that does that kind of answer your question? I think so. I think it could have answers the question also of, like, do we want sort of a few very good tools or many tools in the beginning? And here, it really feels like Oh, right. Yes. Embeddings, and you're probably gonna be good to go. Yes. Okay. So, yeah, one interesting thing here is, like, people have talked a lot about tuning your system prompt and tuning your prompts. There's a whole new access to this now. With agents, which is, like, tuning the set of tools. Bash, we could have instead had, like, 10 different sub goals. And we actually did start with this, which was, like, having the file reading tool, file editing tool, the GREP tool, the find tool, and so forth. And then, you know, the agent call these tools whenever, I'm like, it kinda it didn't help. It made the code base more complex. Yeah. It it didn't seem like a very fruitful direction. I'd say. Interesting. It it almost feels like you kind of bitter lessened away. A lot of those things. And so you know what? Turns out if you just give a bash, it'll work today. Maybe in the future, it's just keyboard and mouse inputs. But Yeah. I I also think the more tools you get, the more you more stuff you have to prompt tune, and so that takes that just takes some more time. Got it. It's almost like fewer tools, fewer tools, better tools, and use tools that are very generalizable in in the hopes that future models will just be able to use them better. Yeah. Exactly. Makes sense. One question I'm really curious about is, like, what do you feel like we can learn as sort of practitioners not in cogeneration, but maybe, you know, what does the AI for medical scribes or the AI for, like, construction documentation or AI for legal like, for lawyers. Like, what can they learn from the things that the coding community has been, picking up on? Well, I mean, the main thing is to do AgenTik for Trickful. So that's, like, you give the agent access to tools that that are, like, search tools. The I'd say the next thing is, like, well, okay. How do I build those? How do I build that system? How do I build those tools? And the simple answer is, like, whatever you already have turn those into tools. And iterate from there. I think that's kind of the sim that's kind of the simple thing, which is, like, very unin maybe to some people because they want to go architect some whenever there's some big technological innovation, they wanna go architect some whole new system. Train models, and whatnot. But this is it's really just, like, a layer on top of your existing search tool existing search system. And does cost in money and it does cost in latency. It just kinda makes things better. So I would just start from there. And then, you know, there are all these as we kind of discussed a little bit, there are all these different directions you can go from there, whether it's, like, realizing that, okay, now these this agent is so persistent. I I wanna give it access to a a new kind of embedding model that, like, maybe it was really hard to fit into my previous search system. Maybe I discovered that, like, actually, my embedding models don't help at all. And doing some more basic search or doing, a Glean integration or something like that is just as effective and and way less total cost total cost of ownership for the team. So I just I just go to that and deprecate my embedding embedding service. You kinda need to make those decisions you know, once you kinda get a sense of of of what's moving the needle on on top of the basic agentic retrieval system. That makes sense. I think yeah, cost of ownership is something that people are not really thinking about these days, but really should as especially because the future, more and more things will be abstracted away. I think maybe last year, everyone was asking me how to fine tune language models. But now very little. Is going in that direction. But I think a lot more folks are thinking about doing fine tuning embedding models I mean, that's an interesting topic. I still think no one really knows how to fine tune embedding models. Like, very few people do. So that's one thing that's exciting about this kind of new technology is I can't tell you how many people have texted me over the past few years they heard Augment has, like, really good retrieval. They were like, how do we train good any embedding models for insert category x, y, or z, whether it's finance, or medicine or whatnot, or therapy. And it was kind of hard because I was just like, well, you need to have, like, an AI researcher on your team. And, like, there's not really any APIs for this. You can't there's nothing great out there. But now it's kind of like it's less important I do think this is kind of, like, in some sense, democratizing kind of, like, the a really good retrieval systems. Yeah. Yeah. I think Cursor is the only company I know who's really talked about fine tuning their embedding and that's mostly just for the tab model. Right? If you look at, like, the cursor agent, it's not really using embeddings that often in the agent product. But code completion, you really do want, like, very fast retrieval. Yeah. I mean, so we found fine tuning embedding models is really important. It it really does help. You know, I'll give you some examples reasons for why it helps. So we talked a little bit about, like, with code with with completions, it it's it's good, but also, like, code completions is, like, it's kind of this, like, keyword similarity. Task. But when you're, like, asking these more complicated questions, like, they're like, oh, I'm trying to, like, abstract away my LLM LLM SDKs. The embedding model needs to have, like, some understanding of, like, your particular domain. Like, it it needs to have, like, almost the reasoning capabilities of an LLM. Obviously, it's not going to because it's a much smaller model, it's, like, approximate that. And, like, most embedding models that you get off the shelf, that's not really what they're trained to do. They're trained to just, like, look for similarities between text a and text b. So it does really help. But it's just it's a little bit less of a bottleneck now that now that have this agentic retrieval. Makes sense. Makes sense. Question is pretty interesting because I think it exists in multiple scales. But what are your thoughts on memory, and how does it play with you know, other rags with coding agents? You know, I know there's compact as a way of truncation, but we'd love to hear a little bit more about that. That's a great question. I mean, obviously, memories are super important.  
Me: Right.  
Them: I guess well, how do you add memories? There's a really simple thing. I don't think this is anyone's secret sauce. You add a tool. That saves memories. Then you add a tool that reads memories. Now how does memories affect how we think about retrieval? Let's look again at this example. If we had memories here that kind of describe something like, at the end of this, you know, the model could create a memory that was something like, describing some aspects of the code base. Like, these, you know, these are the files where models are defined. And in general, like, models have these kinds of functions on them. Or, like, these are the relevant interfaces to consider. When you add this stuff into the memories, it speeds up the next time I go around the next time I try to do an agent run that kind of has a similar kind of search task as this. So yeah, so, you know, memories are personalization, but they're also in some way kind of like a, I guess, a semantic cache. That, like, speeds up future runs. Future code search or for future search runs. Yeah. This is some I I'd say kind of quick thoughts on it. Yep. I think that this is a big pretty big question, but can you also talk a little bit more about your eval harness? For this c bench work? Sure. Does success look like How do think about iterating on these things? Yeah. So it's really valuable to understand that, you know, even as you improve embedding model, it might not matter because the agent is so persistent. I feel like that's a big learning that Yeah. When once you say it. Yeah. I wasted a lot of time on this one. I mean, again, this is not to say that, like, embedding models aren't useful as tools that are very useful, but it's, like, not not as simple as, like, if you make score go up on embedding model eval, then score will go up on the end to end agent agentic retrieval eval. And so that's why I I I recommend to to start with the the end to end by eval eval first. I usually, in general, just recommend to start with five vowels. Vibe evals. I think this is something very unintuitive to people that come from, like, classical ML, who are always trying to make quantitative evals. Like, with natural language stuff, you can learn so much just from looking at a few examples. Maybe this is already kind of the consensus thing, but I've met a lot of people who still haven't really fully this. But, yeah, once you have a few examples and you get it for, how good is the system? What are the general kinds of questions or tasks that it stumbles on? Get to a point where you could kinda, like, look at a problem, and you can just tell, like, if the system's gonna get this one or not. And so, okay, once you're to that point, and it's like it's, like, it's kinda like an intuition thing. Once you're to that point, then you try to, like, poke it, and you kinda try to test at it. And you're like, which what what do I think might not work here? And you and you try to prove out, like, okay. Can I find some examples that indicate that this particular access of the system doesn't work? Let's say I in my vibe my end to end kind of IV valuing thing, I noticed, like, there are, you know, these kinds of questions, which it's both very fast on solving and questions sometimes solves them, but it's very slow. And it does not at all solve them consistently. It's kind of like a fifty fifty. Then I focus on those questions and I kind of look at them and I'm like, like, maybe it's because oh, our GREP tool like, doesn't ever use this particular kind of regex pattern. Or, like, our embedding models when we when the agent sends a query to it, like, is never asking this particular kind of question. And I think asking this particular kind of question or doing this kind of regex will really help. And then so you add that to your your tool schema. And maybe it helps. And then in terms of evals for the for those kind of, like, more pointed problems, you can you can do the Vibe Eval. Just so that embedding tool of that particular core of that particular task. And, usually, that's sufficient If you already have a quantitative eval lying around, that's that's nice. I will say once you get to a certain, like, quality bar with all these stuff of, like, different you know, following different intuitions for what's not working and what's working, You do eventually get to a point where you do need the quantitative eval, You know? So for Suitebench, that quantitative eval was it's 500 examples, which exists in this in in in Sweebench. And there's, like, a yes, no at the very end of if not the figured the problem out. Because you just look at if the test passed or not, You you do want that because as you're doing all these kind of, like, spot spot check improvements and even the vibe eval ing, like, one improvement may negate other improvements. You need to see how kind of everything that's So, eventually, you do need the quantitative eval once once you're kind of, have saturated all those those, like, early winds or all those low hanging fruit. Yeah. I wanna call out something that's really refreshing, which is when I talk to you and in the context of RAG, you're always giving very specific examples of queries that we want our system to service. I find that when we're talking to a lot of sort of really smart engineers but folks who are not as familiar with research, they sort of lead with questions like which embedding that model should I use? Should I use Hide, or should I use Agentic Rag versus your example of just, you know, what are these specific kinds of examples? What are the 10 you know, 20 examples that we can service What are the failure modes? And just becoming really familiar with the evaluation suite rather than the set of tools at our disposal. I just wanna call that out because that's not something that's, like, you know, very big in sort of a lot of the consulting work that I do. Always starts with, what tool should we use? And then it gets to we need to be able to handle contact search and scheduling much better. And we need to build specific tools to do that. Yeah. I I I'd say one of my controversial takes here is that being a researcher is actually very similar to being a product person. And okay. So what do I mean by this? Well, with both of them, you're working back from use cases. You're working backwards from examples. So most AI research these days is not solving a math question on a whiteboard. It's like data. It's like looking at, like, this particular agent run or this particular query and cross referencing it against other examples and trying to understand, like, why that query failed or not. So when you're analyzing this kind of use case level user user data or or exam you know, data of your your kind of task distribution. That's very similar to being a product person. And, like, looking at and studying how are people using my product and how can I improve it? And so I think there's actually a ton of overlap between product and and research, and engineering is, like, the skill that's kind of, like, the the odd man out. Yeah. That's that's that's pretty interesting. I'm also surprised because I think you're one of the few folks working on, like, autonomous coding that is still sort of long embedding models. What do you think is missing from these other folks? I think, for example, Klein has talked about, you know, not using embedding models in the past and going only for tool use without embeddings. Do you think this is a matter of, like, there are no good open source code embedding models out there? Is it because we're not training things correctly? Is it because you know, SONNET four is just too good at using grep that we don't need that the near term. Yeah. I mean, for for SWE bench, they're pretty small, they're pretty small repos. So GREP and FINE work great and fine. And so that's how we didn't need to use embedding models for them. Let's go again back to this guy. But, like, if you wanna get to, like, truly huge code bases, or you wanna be able to, like, search over a code base, and search over, like, low structure natural language that's, in Slack or in Notion, like, GREP and FIND are, like, less useful. And so maybe it's like you have, like, Glean as as one thing I mentioned or you for as your tool for searching over, like, Slack or whatever, or maybe you train your own embedding but it's not really just GREP and FIND. And then, you know, let's say we wanna search over third party code bases. Because these models don't they memorize a good amount of third party code bases, so they do memorize you know, the OpenAI SDK, but that, like, random SDK that you found, on GitHub that has, like, barely any stars that you're using, like, it probably hasn't memorized that. So you wanna be able to search over that. And maybe it has docs and if it has docs, like, yes. Awesome. Maybe it doesn't have docs. You actually wanna search over the code base. So the amount the corpus that you the, you know, size of the index can get really large, And in this situation, rapid find can get slow. And embedding models are really good at moving and moving quickly through these giant corpuses. So that's kind of the main thing I think about is, like, it's like how much do you wanna scale your index? And then two, like, what are the different kinds of information that you have in that index? And, like, GREP and FINE are, like, going to be less and less efficient. Oh, I'll give you another thing that I think we're going to have coding agents search over soon that GraphicFinder definitely not gonna work for. Searching over like, video recordings of user sessions. So, like, I have this maybe giant corpus of, like, you know, say I did a bunch of, like, customer studies. I I recorded them all, so I have all of these hours of video footage. And I wanna search over those when doing an agent call. Like, maybe I asked the agent, like, a product y question, or maybe I asked the agent, like, I'm thinking of moving this particular button on this web page from this location to this location. I probably need to search over all these videos. Obviously, GREP and find aren't going to be sufficient there. So it's really, like, about, like, again about your use case and asking yourself, like, okay. If I was a human working this use case and I was, a really persistent human that never got tired, would this having this other tool, like, this other search tool, would that help me? And if the answer is yes, then, like, it's probably gonna be useful for the agent. That makes sense. You feel like it's almost an artifact of SuiteBench being just, like, too much of a toy set of evals. Like, you know, I I think about, you know, my time at Facebook, where you just can't even grab the code base. We have, like, custom tools to get over our code base to understand, like, when were the like, who else called this function in the past, like, ninety days? And there's, a special UI because it's, like, some mono repo, which is know, luckily, it's a monorepo, but in other cases, that might be distributed systems and you can't grab any of that. Yeah. I I think I think that's that's spot on. SWE bench is, like, one of the most real world of the academic evals, and it's very good. But at the end of the day, it still is an academic eval. So the repos are all open source. Already biases them to be pretty small and makes them look kind of different from, like, professional repositories. And the tasks are pretty simple tasks. So there's actually so so there's Suitebench, and then there's Suitebench verified, which is what we use. So Suitebench is, like, there's many more examples. A lot of them kinda crappy, like low quality. Suitebench verified is, like, filtered down version that OpenAI released, but they just filter down to, like, 500 examples from Suitebench. So that's what what we worked with, and that's kind of the more useful one. SuiteBench verified, there there's some statistic, like, think it's 90% of the examples took less than an hour for, like, a good engineer to solve. So they're pretty simple examples. And so that simplicity also makes something like GREP and FIND a lot easier. And, again, as I mentioned, there's, like, there's still the the latency factor. Like, I don't know about you guys, but, like, I've definitely had these moments where my thumbs are twiddling for, like, five minutes because I'm waiting for an agent to run. I'm, like, that's super annoying. And, like, right now, we're, woah. These tools are so good. They're saving us so much time. Don't care about that. But, like, let's roll things forward. Like, and everyone's used to, like, gets used to the speed at which we're building with agents. Like, that's that that kind of latency is gonna get annoying. Yeah. It's like, the first time I get Wi Fi on a plane, I'm amazed. And the second time I'm I get Wi Fi on a plane, it's, like, too slow for me, and I'm already complaining. I definitely imagine that is gonna be the case. So there's about twelve minutes left. We try to end at fifty five because we have another talk in the hour. One question I'd like to ask everyone is what is the question you think people are not asking these days? And they really should. That's a really interesting topic. Seeing tokens come up. I I still you know, I don't know. I still come back to this point that I think no one very few people still under very few people understand how to build and train good retrieval systems. Like, it's like a simple thing, but it it just, like, confuses me like, why no one knows how to fine tune really good embedding models. So I'm gonna go with that one still. Wow. Is I know at least at Augment, like, we found having fine tuned embedding models was really help is really helpful net of GREP and FINE. So as you mentioned, like, other people say differently and that makes me wonder. Like, are they they know how to train embedding models really well? Yeah. Yeah. So I'm gonna go back to that that simple question. Yeah. And I and I bet a lot of it really is the fact that you know, what data do you have access to that allows you to train this embedding model? Like, the cursor team has been deploying this tab model for a year, Right? And so what they can train on is gonna be much larger volume than say, a smaller company building out, like, the cutting SDK. I can imagine codecs can probably get a really good code embedding model out pretty quickly But I don't I don't know if cursor sorry. Not cursor. Cloud Code can't because they don't really do any indexing right now. So, like, how how would you go back in time to build out that dataset? Yes. Training embedding models on user data is a very useful axis. This is correct. Yeah. I I think that's gonna be the the big factor, especially because also identifying whether or not the retrieval was good, I think it's gonna be very Come on in. Hey, Andrew. You're I'm muted. We'll cut that out. Don't worry. Yeah. I think, you know, one of the things I I just saw recently was, like, Cursor has their, like, bug bot. Right, where you make a request. It is on GitHub. There's a pull request, and maybe it's just, okay. Retrieval is good, but the pull request gets merged. But that's a ton of infrastructure to have to spin up to try to collect that data. I'm curious, like, do you sort of agree with that sentiment, or do you think it's actually something else entirely? Like, maybe it's not just we don't have the data, but, really, there's something fundamental about training these embedding models. Wait. So your question is eventually is is, like, essentially, like, does retrieval get harder for this is it retrieval hard for this new More like PR agents. Have people not trained these embedding models because they don't have the data and they don't have sort of sensors out in the world, like merge pull request to understand what is good retrieval, do you think there's something fundamental about like, the training of embedding models themselves rather than the datasets that they're they're coming from. I just think there aren't that many people that are good at this skill. Mhmm. Because it's, like, just niche enough it's, like, aren't a ton of people. And so, yeah, I think I think that's the main reason. Then I think the data is the next thing with code. There's a lot of data out there that you can wrangle in all different kinds of ways. From GitHub, and you can do all these transformations on it and stuff. Most most domains don't have that. So for code, it's mostly finding great people. Think for other domains, it's probably it's probably more the data. I'm also I wonder if just that the smartest people want to work in the LLM. And not on the event. No. That's actually an issue. That that that is, like, definitely an issue, like, more like, a macro level. Say also one interesting point that I was just thinking about when you mentioned bug bot, is, like, some other, like, code search tools that are really useful is, like, maybe I don't wanna just search over the code, but I don't wanna search over, like, a par par Git commits. I basically wanna see blame. Of, like, who edited what lie. Mhmm. Another thing is maybe I wanna do, like, hierarchical retrieval. So, like, asynchronously, not while someone's asking the agent a question, I wanna take, like, every file in the code base and, like, write, like, a one line summary of And I wanna take, like, every directory and write, like, a one line summary of that. Like, these kinds of things can have, like, a huge huge impact on, like, the performance of retrieval systems And so yeah. I don't know. I just think there's, like, so much there's so much to build here. And yeah, it's it's exciting. Yeah. I think you can also imagine giving, you know, these systems access to, like, the language servers or giving them access to the ability to sort of more naturally browse code rather than just parsing things through strings. You know? Like, I imagine, like, when I debug things, I kinda look at the stack trace. I look at the files, then I sort of traverse through functions. Maybe that traversal is one way of doing it. And then you just say, well, any sort of straight path to debugging an issue the embedding should be similar. Totally. I mean, one one thing with, like, language server protocols, which if anyone's not familiar with that is, that's essentially, like, when you're coding in your code base, see, like, little squigglies under, like, some part of your code that indicates some kind of error, like, that's this thing called the language protocol that's like, analyzing your code base at all times and, like, looking at the code and seeing if it's correct and, like, attaching, like, links between, like, a function name and, like, the function definition and so forth. Like, these are pretty slow. They sometimes don't work. Like, they don't work in broken code. They sometimes work in broken code, but, like, they don't always work. And before agents, it was a bit of a pain to, like, figure out how to, like, take LSPs with all the, like, issues. And challenges I have. Like, integrate into the code base in a way that into a retrieval system that, like, works really consistently. But with agents now, it's like, well, I'm just gonna, like, paint a very simple picture. You could have, an LSP LSP tool. And then if, like, that doesn't work, then the agent can just call the, like, code search tool or can call the bash tool. And so now we can kind of, like, more easily synthesize, like, different retrieval tools that have different pros and cons without having to, like, deal with a bunch complicated work and fine tuning re rank and stuff like that. Yeah. Think that's super relevant. I you're you're kinda like the first person outside of Oman who cursor talking about embedding models and then doing this kind of retrieval. It kinda gets me psyched to maybe take a look at this a little bit deeper myself. Yeah. For sure. Can I can I say, like, one more, like, fun Of course? Retrieval anecdote? So I'm working on, like, a a side project right now. Just like kind of like a weekend hacking project where I'm, like, building a a like, I'm trying to have, like, the world's largest index of, like, songs that you can retrieve over. So if I wanna, like, ask any int question about music ever, I have this, like, giant index of music. And I am I found, like, with ChatGPT, if I ask it a question, like, to I'm searching for a song, and I wanna find a song that's, like, like a mid nineties vibe female singer, insert other qualities. It's, like, not very good at doing this because it's like it has access to a web search tool and, like, web search is, like, not the right abstraction for doing this kind of search. So what I've done instead is I've taken every one of those songs, I've used an LLM to go do deep research on each song to come up with, like, a a dis a, like, dis like, a dossier about that song. And I also have reached on I have this description, and then now I'm doing search over that. And that works really well. I thought this was, like, a really cool example of, like, just one simple little, like, asynchronous preprocessing step. Like, took, like, a totally not working search system and turned it into something that, like, works really well. That makes a lot of sense. You're just kind creating these, like, synthetic summarization or summary embedding to use and then embedding in that. Yeah. I've I've seen that work on, like, movie scenes. I've seen that work on, like, websites, hierarchical, Yeah. I'm definitely gonna explore a little bit more about this. Yeah. And, you know, I think one thing I'll just call out is right after this talk, we have another talk coming up. From the VP of Algolia, and he's gonna basically talk about, like, query expansion, summarization, changing different embedding models, and how to learn from feedback. So I think that'll be a pretty interesting segue into some of the stuff you're talking about. Awesome. Excited. As always, we'll send an email with any of the references that you know as well as any kind of slides and GitHub repos. Before we go, Colin, is there anything else you wanted to to say? Where can we find you? Yeah. You can we'll we'll I'll have you send out my contact info, and people can message me. Perfect. Yeah. I think the feedback has that this has been super dense. People are really excited even just watch this back and get the slides. And so on behalf of everyone, thank you so much for sharing, your your expert knowledge. I feel like there's been very few people who have been really talking about kind of stuff. So this is a super for me. Alright. Awesome. Bye. 

---
Your AI Product Needs Evals
LLMs
evals
How to construct domain-specific LLM evaluation systems.
Author
Hamel Husain

Published
March 29, 2024

Table Of Contents
Motivation
Iterating Quickly == Success
Case Study: Lucy, A Real Estate AI Assistant
Problem: How To Systematically Improve The AI?
The Types Of Evaluation
Level 1: Unit Tests
Step 1: Write Scoped Tests
Step 2: Create Test Cases
Step 3: Run & Track Your Tests Regularly
Level 2: Human & Model Eval
Logging Traces
Looking At Your Traces
Automated Evaluation w/ LLMs
Level 3: A/B Testing
Evaluating RAG
Eval Systems Unlock Superpowers For Free
Fine-Tuning
Data Synthesis & Curation
Debugging
Conclusion
Motivation
I started working with language models five years ago when I led the team that created CodeSearchNet, a precursor to GitHub CoPilot. Since then, I’ve seen many successful and unsuccessful approaches to building LLM products. I’ve found that unsuccessful products almost always share a common root cause: a failure to create robust evaluation systems.

I’m currently an independent consultant who helps companies build domain-specific AI products. I hope companies can save thousands of dollars in consulting fees by reading this post carefully. As much as I love making money, I hate seeing folks make the same mistake repeatedly.

This post outlines my thoughts on building evaluation systems for LLMs-powered AI products.

Iterating Quickly == Success
Like software engineering, success with AI hinges on how fast you can iterate. You must have processes and tools for:

Evaluating quality (ex: tests).
Debugging issues (ex: logging & inspecting data).
Changing the behavior or the system (prompt eng, fine-tuning, writing code)
Many people focus exclusively on #3 above, which prevents them from improving their LLM products beyond a demo.1 Doing all three activities well creates a virtuous cycle differentiating great from mediocre AI products (see the diagram below for a visualization of this cycle).

If you streamline your evaluation process, all other activities become easy. This is very similar to how tests in software engineering pay massive dividends in the long term despite requiring up-front investment.

To ground this post in a real-world situation, I’ll walk through a case study in which we built a system for rapid improvement. I’ll primarily focus on evaluation as that is the most critical component.

Case Study: Lucy, A Real Estate AI Assistant
Rechat is a SaaS application that allows real estate professionals to perform various tasks, such as managing contracts, searching for listings, building creative assets, managing appointments, and more. The thesis of Rechat is that you can do everything in one place rather than having to context switch between many different tools.

Rechat’s AI assistant, Lucy, is a canonical AI product: a conversational interface that obviates the need to click, type, and navigate the software. During Lucy’s beginning stages, rapid progress was made with prompt engineering. However, as Lucy’s surface area expanded, the performance of the AI plateaued. Symptoms of this were:

Addressing one failure mode led to the emergence of others, resembling a game of whack-a-mole.
There was limited visibility into the AI system’s effectiveness across tasks beyond vibe checks.
Prompts expanded into long and unwieldy forms, attempting to cover numerous edge cases and examples.
Problem: How To Systematically Improve The AI?
To break through this plateau, we created a systematic approach to improving Lucy centered on evaluation. Our approach is illustrated by the diagram below.



This diagram is a best-faith effort to illustrate my mental model for improving AI systems. In reality, the process is non-linear and can take on many different forms that may or may not look like this diagram.

I discuss the various components of this system in the context of evaluation below.

The Types Of Evaluation
Rigorous and systematic evaluation is the most important part of the whole system. That is why “Eval and Curation” is highlighted in yellow at the center of the diagram. You should spend most of your time making your evaluation more robust and streamlined.

There are three levels of evaluation to consider:

Level 1: Unit Tests
Level 2: Model & Human Eval (this includes debugging)
Level 3: A/B testing
The cost of Level 3 > Level 2 > Level 1. This dictates the cadence and manner you execute them. For example, I often run Level 1 evals on every code change, Level 2 on a set cadence and Level 3 only after significant product changes. It’s also helpful to conquer a good portion of your Level 1 tests before you move into model-based tests, as they require more work and time to execute.

There isn’t a strict formula as to when to introduce each level of testing. You want to balance getting user feedback quickly, managing user perception, and the goals of your AI product. This isn’t too dissimilar from the balancing act you must do for products more generally.

Level 1: Unit Tests
Unit tests for LLMs are assertions (like you would write in pytest). Unlike typical unit tests, you want to organize these assertions for use in places beyond unit tests, such as data cleaning and automatic retries (using the assertion error to course-correct) during model inference. The important part is that these assertions should run fast and cheaply as you develop your application so that you can run them every time your code changes. If you have trouble thinking of assertions, you should critically examine your traces and failure modes. Also, do not shy away from using an LLM to help you brainstorm assertions!

Step 1: Write Scoped Tests
The most effective way to think about unit tests is to break down the scope of your LLM into features and scenarios. For example, one feature of Lucy is the ability to find real estate listings, which we can break down into scenarios like so:

Feature: Listing Finder

This feature to be tested is a function call that responds to a user request to find a real estate listing. For example, “Please find listings with more than 3 bedrooms less than $2M in San Jose, CA”

The LLM converts this into a query that gets run against the CRM. The assertion then verifies that the expected number of results is returned. In our test suite, we have three user inputs that trigger each of the scenarios below, which then execute corresponding assertions (this is an oversimplified example for illustrative purposes):

Scenario	Assertions
Only one listing matches user query	len(listing_array) == 1
Multiple listings match user query	len(listing_array) > 1
No listings match user query	len(listing_array) == 0

There are also generic tests that aren’t specific to any one feature. For example, here is the code for one such generic test that ensures the UUID is not mentioned in the output:

const noExposedUUID = message => {
  // Remove all text within double curly braces
  const sanitizedComment = message.comment.replace(/\{\{.*?\}\}/g, '')

  // Search for exposed UUIDs
  const regexp = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/ig
  const matches = Array.from(sanitizedComment.matchAll(regexp))
  expect(matches.length, 'Exposed UUIDs').to.equal(0, 'Exposed UUIDs found')
}

CRM results returned to the LLM contain fields that shouldn’t be surfaced to the user; such as the UUID associated with an entry. Our LLM prompt tells the LLM to not include UUIDs. We use a simple regex to assert that the LLM response doesn’t include UUIDs.

Rechat has hundreds of these unit tests. We continuously update them based on new failures we observe in the data as users challenge the AI or the product evolves. These unit tests are crucial to getting feedback quickly when iterating on your AI system (prompt engineering, improving RAG, etc.). Many people eventually outgrow their unit tests and move on to other levels of evaluation as their product matures, but it is essential not to skip this step!

Step 2: Create Test Cases
To test these assertions, you must generate test cases or inputs that will trigger all scenarios you wish to test. I often utilize an LLM to generate these inputs synthetically; for example, here is one such prompt Rechat uses to generate synthetic inputs for a feature that creates and retrieves contacts:

Write 50 different instructions that a real estate agent can give to his assistant to create contacts on his CRM. The contact details can include name, phone, email, partner name, birthday, tags, company, address and job.

For each of the instructions, you need to generate a second instruction which can be used to look up the created contact.

. The results should be a JSON code block with only one string as the instruction like the following:


[
  ["Create a contact for John (<EMAIL>)", 
  "What's the email address of John Smith?"]
]

Using the above prompt, we generate test cases like below:

[ 
    [
        'Create a contact for John Smith (<EMAIL>) with phone number ************ and address 123 Apple St.', 
        'What\'s the email address of John Smith?'
    ],
    [
        'Add Emily Johnson with phone ************, email <EMAIL>, and company ABC Inc.', 
        'What\'s the phone number for Emily Johnson?'
    ],
    [
        'Create a contact for Tom Williams with birthday 10/20/1985, company XYZ Ltd, and job title Manager.', 
        'What\'s Tom Williams\' job title?'
    ],
    [
        'Add a contact for Susan Brown with partner name James Brown, <NAME_EMAIL>.', 
    'What\'s the partner name of Susan Brown?'
    ],
…
]

For each of these test cases, we execute the first user input to create the contact. We then execute the second query to fetch that contact. If the CRM doesn’t return exactly 1 result then we know there was a problem either creating or fetching the contact. We can also run generic assertions like the one to verify UUIDs are not in the response. You must constantly update these tests as you observe data through human evaluation and debugging. The key is to make these as challenging as possible while representing users’ interactions with the system.

You don’t need to wait for production data to test your system. You can make educated guesses about how users will use your product and generate synthetic data. You can also let a small set of users use your product and let their usage refine your synthetic data generation strategy. One signal you are writing good tests and assertions is when the model struggles to pass them - these failure modes become problems you can solve with techniques like fine-tuning later on.

On a related note, unlike traditional unit tests, you don’t necessarily need a 100% pass rate. Your pass rate is a product decision, depending on the failures you are willing to tolerate.

Step 3: Run & Track Your Tests Regularly
There are many ways to orchestrate Level 1 tests. Rechat has been leveraging CI infrastructure (e.g., GitHub Actions, GitLab Pipelines, etc.) to execute these tests. However, the tooling for this part of the workflow is nascent and evolving rapidly.

My advice is to orchestrate tests that involve the least friction in your tech stack. In addition to tracking tests, you need to track the results of your tests over time so you can see if you are making progress. If you use CI, you should collect metrics along with versions of your tests/prompts outside your CI system for easy analysis and tracking.

I recommend starting simple and leveraging your existing analytics system to visualize your test results. For example, Rechat uses Metabase to track their LLM test results over time. Below is a screenshot of a dashboard Rechat built with Metabase:



This screenshot shows the prevalence of a particular error (shown in yellow) in Lucy before (left) vs after (right) we addressed it.

Level 2: Human & Model Eval
After you have built a solid foundation of Level 1 tests, you can move on to other forms of validation that cannot be tested by assertions alone. A prerequisite to performing human and model-based eval is to log your traces.

Logging Traces
A trace is a concept that has been around for a while in software engineering and is a log of a sequence of events such as user sessions or a request flow through a distributed system. In other words, tracing is a logical grouping of logs. In the context of LLMs, traces often refer to conversations you have with a LLM. For example, a user message, followed by an AI response, followed by another user message, would be an example of a trace.

There are a growing number of solutions for logging LLM traces.2 Rechat uses LangSmith, which logs traces and allows you to view them in a human-readable way with an interactive playground to iterate on prompts. Sometimes, logging your traces requires you to instrument your code. In this case, Rechat was using LangChain which automatically logs trace events to LangSmith for you. Here is a screenshot of what this looks like:



I like LangSmith - it doesn’t require that you use LangChain and is intuitive and easy to use. Searching, filtering, and reading traces are essential features for whatever solution you pick. I’ve found that some tools do not implement these basic functions correctly!

Looking At Your Traces
You must remove all friction from the process of looking at data. This means rendering your traces in domain-specific ways. I’ve often found that it’s better to build my own data viewing & labeling tool so I can gather all the information I need onto one screen. In Lucy’s case, we needed to look at many sources of information (trace log, the CRM, etc) to understand what the AI did. This is precisely the type of friction that needs to be eliminated. In Rechat’s case, this meant adding information like:

What tool (feature) & scenario was being evaluated.
Whether the trace resulted from a synthetic input or a real user input.
Filters to navigate between different tools and scenario combinations.
Links to the CRM and trace logging system for the current record.
I’ve built different variations of this tool for each problem I’ve worked on. Sometimes, I even need to embed another application to see what the user interaction looks like. Below is a screenshot of the tool we built to evaluate Rechat’s traces:



Another design choice specific to Lucy is that we noticed that many failures involved small mistakes in the final output of the LLM (format, content, etc). We decided to make the final output editable by a human so that we could curate & fix data for fine-tuning.

These tools can be built with lightweight front-end frameworks like Gradio, Streamlit, Panel, or Shiny in less than a day. The tool shown above was built with Shiny for Python. Furthermore, there are tools like Lilac which uses AI to search and filter data semantically, which is incredibly handy for finding a set of similar data points while debugging an issue.

I often start by labeling examples as good or bad. I’ve found that assigning scores or more granular ratings is more onerous to manage than binary ratings. There are advanced techniques you can use to make human evaluation more efficient or accurate (e.g., active learning, consensus voting, etc.), but I recommend starting with something simple. Finally, like unit tests, you should organize and analyze your human-eval results to assess if you are progressing over time.

As discussed later, these labeled examples measure the quality of your system, validate automated evaluation, and curate high-quality synthetic data for fine-tuning.

How much data should you look at?
I often get asked how much data to examine. When starting, you should examine as much data as possible. I usually read traces generated from ALL test cases and user-generated traces at a minimum. You can never stop looking at data—no free lunch exists. However, you can sample your data more over time, lessening the burden. 3

Automated Evaluation w/ LLMs
Many vendors want to sell you tools that claim to eliminate the need for a human to look at the data. Having humans periodically evaluate at least a sample of traces is a good idea. I often find that “correctness” is somewhat subjective, and you must align the model with a human.

You should track the correlation between model-based and human evaluation to decide how much you can rely on automatic evaluation. Furthermore, by collecting critiques from labelers explaining why they are making a decision, you can iterate on the evaluator model to align it with humans through prompt engineering or fine-tuning. However, I tend to favor prompt engineering for evaluator model alignment.

I love using low-tech solutions like Excel to iterate on aligning model-based eval with humans. For example, I sent my colleague Phillip the following spreadsheet every few days to grade for a different use-case involving a natural language query generator. This spreadsheet would contain the following information:

model response: this is the prediction made by the LLM.
model critique: this is a critique written by a (usually more powerful) LLM about your original LLM’s prediction.
model outcome: this is a binary label the critique model assigns to the model response as being “good” or “bad.”
Phillip then fills out his version of the same information - meaning his critique, outcome, and desired response for 25-50 examples at a time (these are the columns prefixed with “phillip_” below):



This information allowed me to iterate on the prompt of the critique model to make it sufficiently aligned with Phillip over time. This is also easy to track in a low-tech way in a spreadsheet:



This is a screenshot of a spreadsheet where we recorded our attempts to align model-based eval with a human evaluator.

General tips on model-based eval:

Use the most powerful model you can afford. It often takes advanced reasoning capabilities to critique something well. You can often get away with a slower, more powerful model for critiquing outputs relative to what you use in production.
Model-based evaluation is a meta-problem within your larger problem. You must maintain a mini-evaluation system to track its quality. I have sometimes fine-tuned a model at this stage (but I try not to).
After bringing the model-based evaluator in line with the human, you must continue doing periodic exercises to monitor the model and human agreement.
My favorite aspect about creating a good evaluator model is that its critiques can be used to curate high-quality synthetic data, which I will touch upon later.

Level 3: A/B Testing
Finally, it is always good to perform A/B tests to ensure your AI product is driving user behaviors or outcomes you desire. A/B testing for LLMs compared to other types of products isn’t too different. If you want to learn more about A/B testing, I recommend reading the Eppo blog (which was created by colleagues I used to work with who are rock stars in A/B testing).

It’s okay to put this stage off until you are sufficiently ready and convinced that your AI product is suitable for showing to real users. This level of evaluation is usually only appropriate for more mature products.

Evaluating RAG
Aside from evaluating your system as a whole, you can evaluate sub-components of your AI, like RAG. Evaluating RAG is beyond the scope of this post, but you can learn more about this subject in a post by Jason Liu.

Eval Systems Unlock Superpowers For Free
In addition to iterating fast, eval systems unlock the ability to fine-tune and debug, which can take your AI product to the next level.

Fine-Tuning
Rechat resolved many failure modes through fine-tuning that were not possible with prompt engineering alone. Fine-tuning is best for learning syntax, style, and rules, whereas techniques like RAG supply the model with context or up-to-date facts.

99% of the labor involved with fine-tuning is assembling high-quality data that covers your AI product’s surface area. However, if you have a solid evaluation system like Rechat’s, you already have a robust data generation and curation engine! I will expand more on the process of fine-tuning in a future post.4

Data Synthesis & Curation
To illustrate why data curation and synthesis come nearly for free once you have an evaluation system, consider the case where you want to create additional fine-tuning data for the listing finder mentioned earlier. First, you can use LLMs to generate synthetic data with a prompt like this:

Imagine if Zillow was able to parse natural language. Come up with 50 different ways users would be able to search listings there. Use real names for cities and neighborhoods.

You can use the following parameters:

<ommitted for confidentiality>

Output should be a JSON code block array. Example:

[
"Homes under $500k in New York"
]
This is almost identical to the exercise for producing test cases! You can then use your Level 1 & Level 2 tests to filter out undesirable data that fails assertions or that the critique model thinks are wrong. You can also use your existing human evaluation tools to look at traces to curate traces for a fine-tuning dataset.

Debugging
When you get a complaint or see an error related to your AI product, you should be able to debug this quickly. If you have a robust evaluation system, you already have:

A database of traces that you can search and filter.
A set of mechanisms (assertions, tests, etc) that can help you flag errors and bad behaviors.
Log searching & navigation tools that can help you find the root cause of the error. For example, the error could be RAG, a bug in the code, or a model performing poorly.
The ability to make changes in response to the error and quickly test its efficacy.
In short, there is an incredibly large overlap between the infrastructure needed for evaluation and that for debugging.

Conclusion
Evaluation systems create a flywheel that allows you to iterate very quickly. It’s almost always where people get stuck when building AI products. I hope this post gives you an intuition on how to go about building your evaluation systems. Some key takeaways to keep in mind:

Remove ALL friction from looking at data.
Keep it simple. Don’t buy fancy LLM tools. Use what you have first.
You are doing it wrong if you aren’t looking at lots of data.
Don’t rely on generic evaluation frameworks to measure the quality of your AI. Instead, create an evaluation system specific to your problem.
Write lots of tests and frequently update them.
LLMs can be used to unblock the creation of an eval system. Examples include using a LLM to:
Generate test cases and write assertions
Generate synthetic data
Critique and label data etc.
Re-use your eval infrastructure for debugging and fine-tuning.
I’d love to hear from you if you found this post helpful or have any questions. My <NAME_EMAIL>.

---
Frequently Asked Questions (And Answers) About AI Evals
LLMs
evals
FAQ from our course on AI Evals.
Authors
Hamel Husain

Shreya Shankar

Published
July 7, 2025

This document curates the most common questions Shreya and I received while teaching 700+ engineers & PMs AI Evals. Warning: These are sharp opinions about what works in most cases. They are not universal truths. Use your judgment.

👉 We are teaching our last and final cohort of our AI Evals course next month (we have to get back to building). Here is a 35% discount code for readers. 👈

Q: What are LLM Evals?
If you are completely new to product-specific LLM evals (not foundation model benchmarks), see these posts: part 1, part 2, part 3. Otherwise, keep reading.

Q: Is RAG dead?
Question: Should I avoid using RAG for my AI application after reading that “RAG is dead” for coding agents?

Many developers are confused about when and how to use RAG after reading articles claiming “RAG is dead.” Understanding what RAG actually means versus the narrow marketing definitions will help you make better architectural decisions for your AI applications.

The viral article claiming RAG is dead specifically argues against using naive vector database retrieval for autonomous coding agents, not RAG as a whole. This is a crucial distinction that many developers miss due to misleading marketing.

RAG simply means Retrieval-Augmented Generation - using retrieval to provide relevant context that improves your model’s output. The core principle remains essential: your LLM needs the right context to generate accurate answers. The question isn’t whether to use retrieval, but how to retrieve effectively.

For coding applications, naive vector similarity search often fails because code relationships are complex and contextual. Instead of abandoning retrieval entirely, modern coding assistants like Claude Code still uses retrieval —they just employ agentic search instead of relying solely on vector databases, similar to how human developers work.

You have multiple retrieval strategies available, ranging from simple keyword matching to embedding similarity to LLM-powered relevance filtering. The optimal approach depends on your specific use case, data characteristics, and performance requirements. Many production systems combine multiple strategies or use multi-hop retrieval guided by LLM agents.

Unfortunately, “RAG” has become a buzzword with no shared definition. Some people use it to mean any retrieval system, others restrict it to vector databases. Focus on the ultimate goal: getting your LLM the context it needs to succeed. Whether that’s through vector search, agentic exploration, or hybrid approaches is a product and engineering decision.

Rather than following categorical advice to avoid or embrace RAG, experiment with different retrieval approaches and measure what works best for your application.

Q: Can I use the same model for both the main task and evaluation?
For LLM-as-Judge selection, using the same model is usually fine because the judge is doing a different task than your main LLM pipeline. The judges we recommend building do scoped binary classification tasks. Focus on achieving high True Positive Rate (TPR) and True Negative Rate (TNR) with your judge on a held out labeled test set rather than avoiding the same model family. You can use these metrics on the test set to understand how well your judge is doing.

When selecting judge models, start with the most capable models available to establish strong alignment with human judgments. You can optimize for cost later once you’ve established reliable evaluation criteria. We do not recommend using the same model for open ended preferences or response quality (but we don’t recommend building judges this way in the first place!).

Q: How much time should I spend on model selection?
Many developers fixate on model selection as the primary way to improve their LLM applications. Start with error analysis to understand your failure modes before considering model switching. As Hamel noted in office hours, “I suggest not thinking of switching model as the main axes of how to improve your system off the bat without evidence. Does error analysis suggest that your model is the problem?”

Q: Should I build a custom annotation tool or use something off-the-shelf?
Build a custom annotation tool. This is the single most impactful investment you can make for your AI evaluation workflow. With AI-assisted development tools like Cursor or Lovable, you can build a tailored interface in hours. I often find that teams with custom annotation tools iterate ~10x faster.

Custom tools excel because:

They show all your context from multiple systems in one place
They can render your data in a product specific way (images, widgets, markdown, buttons, etc.)
They’re designed for your specific workflow (custom filters, sorting, progress bars, etc.)
Off-the-shelf tools may be justified when you need to coordinate dozens of distributed annotators with enterprise access controls. Even then, many teams find the configuration overhead and limitations aren’t worth it.

Isaac’s Anki flashcard annotation app shows the power of custom tools—handling 400+ results per query with keyboard navigation and domain-specific evaluation criteria that would be nearly impossible to configure in a generic tool.

Q: Why do you recommend binary (pass/fail) evaluations instead of 1-5 ratings (Likert scales)?
Engineers often believe that Likert scales (1-5 ratings) provide more information than binary evaluations, allowing them to track gradual improvements. However, this added complexity often creates more problems than it solves in practice.

Binary evaluations force clearer thinking and more consistent labeling. Likert scales introduce significant challenges: the difference between adjacent points (like 3 vs 4) is subjective and inconsistent across annotators, detecting statistical differences requires larger sample sizes, and annotators often default to middle values to avoid making hard decisions.

Having binary options forces people to make a decision rather than hiding uncertainty in middle values. Binary decisions are also faster to make during error analysis - you don’t waste time debating whether something is a 3 or 4.

For tracking gradual improvements, consider measuring specific sub-components with their own binary checks rather than using a scale. For example, instead of rating factual accuracy 1-5, you could track “4 out of 5 expected facts included” as separate binary checks. This preserves the ability to measure progress while maintaining clear, objective criteria.

Start with binary labels to understand what ‘bad’ looks like. Numeric labels are advanced and usually not necessary.

Q: How do I debug multi-turn conversation traces?
Start simple. Check if the whole conversation met the user’s goal with a pass/fail judgment. Look at the entire trace and focus on the first upstream failure. Read the user-visible parts first to understand if something went wrong. Only then dig into the technical details like tool calls and intermediate steps.

When you find a failure, reproduce it with the simplest possible test case. Here’s an example: suppose a shopping bot gives the wrong return policy on turn 4 of a conversation. Before diving into the full multi-turn complexity, simplify it to a single turn: “What is the return window for product X1000?” If it still fails, you’ve proven the error isn’t about conversation context - it’s likely a basic retrieval or knowledge issue you can debug more easily.

For generating test cases, you have two main approaches. First, you can simulate users with another LLM to create realistic multi-turn conversations. Second, use “N-1 testing” where you provide the first N-1 turns of a real conversation and test what happens next. The N-1 approach often works better since it uses actual conversation prefixes rather than fully synthetic interactions (but is less flexible and doesn’t test the full conversation). User simulation is getting better as models improve. Keep an eye on this space.

The key is balancing thoroughness with efficiency. Not every multi-turn failure requires multi-turn analysis.

Q: Should I build automated evaluators for every failure mode I find?
Focus automated evaluators on failures that persist after fixing your prompts. Many teams discover their LLM doesn’t meet preferences they never actually specified - like wanting short responses, specific formatting, or step-by-step reasoning. Fix these obvious gaps first before building complex evaluation infrastructure.

Consider the cost hierarchy of different evaluator types. Simple assertions and reference-based checks (comparing against known correct answers) are cheap to build and maintain. LLM-as-Judge evaluators require 100+ labeled examples, ongoing weekly maintenance, and coordination between developers, PMs, and domain experts. This cost difference should shape your evaluation strategy.

Only build expensive evaluators for problems you’ll iterate on repeatedly. Since LLM-as-Judge comes with significant overhead, save it for persistent generalization failures - not issues you can fix trivially. Start with cheap code-based checks where possible: regex patterns, structural validation, or execution tests. Reserve complex evaluation for subjective qualities that can’t be captured by simple rules.

Q: How many people should annotate my LLM outputs?
For most small to medium-sized companies, appointing a single domain expert as a “benevolent dictator” is the most effective approach. This person—whether it’s a psychologist for a mental health chatbot, a lawyer for legal document analysis, or a customer service director for support automation—becomes the definitive voice on quality standards.

A single expert eliminates annotation conflicts and prevents the paralysis that comes from “too many cooks in the kitchen”. The benevolent dictator can incorporate input and feedback from others, but they drive the process. If you feel like you need five subject matter experts to judge a single interaction, it’s a sign your product scope might be too broad.

However, larger organizations or those operating across multiple domains (like a multinational company with different cultural contexts) may need multiple annotators. When you do use multiple people, you’ll need to measure their agreement using metrics like Cohen’s Kappa, which accounts for agreement beyond chance. However, use your judgment. Even in larger companies, a single expert is often enough.

Start with a benevolent dictator whenever feasible. Only add complexity when your domain demands it.

Q: What gaps in eval tooling should I be prepared to fill myself?
Most eval tools handle the basics well: logging complete traces, tracking metrics, prompt playgrounds, and annotation queues. These are table stakes. Here are four areas where you’ll likely need to supplement existing tools.

Watch for vendors addressing these gaps—it’s a strong signal they understand practitioner needs.

1. Error Analysis and Pattern Discovery
After reviewing traces where your AI fails, can your tooling automatically cluster similar issues? For instance, if multiple traces show the assistant using casual language for luxury clients, you need something that recognizes this broader “persona-tone mismatch” pattern. We recommend building capabilities that use AI to suggest groupings, rewrite your observations into clearer failure taxonomies, help find similar cases through semantic search, etc.

2. AI-Powered Assistance Throughout the Workflow
The most effective workflows use AI to accelerate every stage of evaluation. During error analysis, you want an LLM helping categorize your open-ended observations into coherent failure modes. For example, you might annotate several traces with notes like “wrong tone for investor,” “too casual for luxury buyer,” etc. Your tooling should recognize these as the same underlying pattern and suggest a unified “persona-tone mismatch” category.

You’ll also want AI assistance in proposing fixes. After identifying 20 cases where your assistant omits pet policies from property summaries, can your workflow analyze these failures and suggest specific prompt modifications? Can it draft refinements to your SQL generation instructions when it notices patterns of missing WHERE clauses?

Additionally, good workflows help you conduct data analysis of your annotations and traces. I like using notebooks with AI in-the-loop like Julius,Hex or SolveIt. These help me discover insights like “location ambiguity errors spike 3x when users mention neighborhood names” or “tone mismatches occur 80% more often in email generation than other modalities.”

3. Custom Evaluators Over Generic Metrics
Be prepared to build most of your evaluators from scratch. Generic metrics like “hallucination score” or “helpfulness rating” rarely capture what actually matters for your application—like proposing unavailable showing times or omitting budget constraints from emails. In our experience, successful teams spend most of their effort on application-specific metrics.

4. APIs That Support Custom Annotation Apps
Custom annotation interfaces work best for most teams. This requires observability platforms with thoughtful APIs. I often have to build my own libraries and abstractions just to make bulk data export manageable. You shouldn’t have to paginate through thousands of requests or handle timeout-prone endpoints just to get your data. Look for platforms that provide true bulk export capabilities and, crucially, APIs that let you write annotations back efficiently.

Q: What is the best approach for generating synthetic data?
A common mistake is prompting an LLM to "give me test queries" without structure, resulting in generic, repetitive outputs. A structured approach using dimensions produces far better synthetic data for testing LLM applications.

Start by defining dimensions: categories that describe different aspects of user queries. Each dimension captures one type of variation in user behavior. For example:

For a recipe app, dimensions might include Dietary Restriction (vegan, gluten-free, none), Cuisine Type (Italian, Asian, comfort food), and Query Complexity (simple request, multi-step, edge case).
For a customer support bot, dimensions could be Issue Type (billing, technical, general), Customer Mood (frustrated, neutral, happy), and Prior Context (new issue, follow-up, resolved).
Choose dimensions that target likely failure modes. If you suspect your recipe app struggles with scaling ingredients for large groups or your support bot mishandles angry customers, make those dimensions. Use your application first—you need hypotheses about where failures occur. Without this, you’ll generate useless test data.

Once you have dimensions, create tuples: specific combinations selecting one value from each dimension. A tuple like (Vegan, Italian, Multi-step) represents a particular use case. Write 20 tuples manually to understand your problem space, then use an LLM to scale up.

The two-step generation process is important. First, have the LLM generate structured tuples. Then, in a separate prompt, convert each tuple to a natural language query. This separation prevents repetitive phrasing. For the vegan Italian tuple above, you might get "I need a dairy-free lasagna recipe that I can prep the day before."

Don’t generate synthetic data for problems you can fix immediately. If your prompt never mentions handling dietary restrictions, fix the prompt rather than generating hundreds of specialized queries. Save synthetic data for complex issues requiring iteration—like an LLM consistently failing at ingredient scaling math or misinterpreting ambiguous requests.

After iterating on your tuples and prompts, run these synthetic queries through your actual system to capture full traces. Sample 100 traces for error analysis. This number provides enough traces to manually review and identify failure patterns without being overwhelming. Rather than generating thousands of similar queries, ensure your 100 traces cover diverse combinations across your dimensions—this variety will reveal more failure modes than sheer volume.

Q: How do I approach evaluation when my system handles diverse user queries?
Complex applications often support vastly different query patterns—from “What’s the return policy?” to “Compare pricing trends across regions for products matching these criteria.” Each query type exercises different system capabilities, leading to confusion on how to design eval criteria.

Error Analysis is all you need. Your evaluation strategy should emerge from observed failure patterns (e.g. error analysis), not predetermined query classifications. Rather than creating a massive evaluation matrix covering every query type you can imagine, let your system’s actual behavior guide where you invest evaluation effort.

During error analysis, you’ll likely discover that certain query categories share failure patterns. For instance, all queries requiring temporal reasoning might struggle regardless of whether they’re simple lookups or complex aggregations. Similarly, queries that need to combine information from multiple sources might fail in consistent ways. These patterns discovered through error analysis should drive your evaluation priorities. It could be that query category is a fine way to group failures, but you don’t know that until you’ve analyzed your data.

To see an example of basic error analysis in action, see this video.

👉 We are teaching our last and final cohort of our AI Evals course next month (we have to get back to building). Here is a 35% discount code for readers. 👈

Q: How do I choose the right chunk size for my document processing tasks?
Unlike RAG, where chunks are optimized for retrieval, document processing assumes the model will see every chunk. The goal is to split text so the model can reason effectively without being overwhelmed. Even if a document fits within the context window, it might be better to break it up. Long inputs can degrade performance due to attention bottlenecks, especially in the middle of the context. Two task types require different strategies:

1. Fixed-Output Tasks → Large Chunks
These are tasks where the output length doesn’t grow with input: extracting a number, answering a specific question, classifying a section. For example:

“What’s the penalty clause in this contract?”
“What was the CEO’s salary in 2023?”
Use the largest chunk (with caveats) that likely contains the answer. This reduces the number of queries and avoids context fragmentation. However, avoid adding irrelevant text. Models are sensitive to distraction, especially with large inputs. The middle parts of a long input might be under-attended. Furthermore, if cost and latency are a bottleneck, you should consider preprocessing or filtering the document (via keyword search or a lightweight retriever) to isolate relevant sections before feeding a huge chunk.

2. Expansive-Output Tasks → Smaller Chunks
These include summarization, exhaustive extraction, or any task where output grows with input. For example:

“Summarize each section”
“List all customer complaints”
In these cases, smaller chunks help preserve reasoning quality and output completeness. The standard approach is to process each chunk independently, then aggregate results (e.g., map-reduce). When sizing your chunks, try to respect content boundaries like paragraphs, sections, or chapters. Chunking also helps mitigate output limits. By breaking the task into pieces, each piece’s output can stay within limits.

General Guidance
It’s important to recognize why chunk size affects results. A larger chunk means the model has to reason over more information in one go – essentially, a heavier cognitive load. LLMs have limited capacity to retain and correlate details across a long text. If too much is packed in, the model might prioritize certain parts (commonly the beginning or end) and overlook or “forget” details in the middle. This can lead to overly coarse summaries or missed facts. In contrast, a smaller chunk bounds the problem: the model can pay full attention to that section. You are trading off global context for local focus.

No rule of thumb can perfectly determine the best chunk size for your use case – you should validate with experiments. The optimal chunk size can vary by domain and model. I treat chunk size as a hyperparameter to tune.

Q: How should I approach evaluating my RAG system?
RAG systems have two distinct components that require different evaluation approaches: retrieval and generation.

The retrieval component is a search problem. Evaluate it using traditional information retrieval (IR) metrics. Common examples include Recall@k (of all relevant documents, how many did you retrieve in the top k?), Precision@k (of the k documents retrieved, how many were relevant?), or MRR (how high up was the first relevant document?). The specific metrics you choose depend on your use case. These metrics are pure search metrics that measure whether you’re finding the right documents (more on this below).

To evaluate retrieval, create a dataset of queries paired with their relevant documents. Generate this synthetically by taking documents from your corpus, extracting key facts, then generating questions those facts would answer. This reverse process gives you query-document pairs for measuring retrieval performance without manual annotation.

For the generation component—how well the LLM uses retrieved context, whether it hallucinates, whether it answers the question—use the same evaluation procedures covered throughout this course: error analysis to identify failure modes, collecting human labels, building LLM-as-judge evaluators, and validating those judges against human annotations.

Jason Liu’s “There Are Only 6 RAG Evals” provides a framework that maps well to this separation. His Tier 1 covers traditional IR metrics for retrieval. Tiers 2 and 3 evaluate relationships between Question, Context, and Answer—like whether the context is relevant (C|Q), whether the answer is faithful to context (A|C), and whether the answer addresses the question (A|Q).

In addition to Jason’s six evals, error analysis on your specific data may reveal domain-specific failure modes that warrant their own metrics. For example, a medical RAG system might consistently fail to distinguish between drug dosages for adults versus children, or a legal RAG might confuse jurisdictional boundaries. These patterns emerge only through systematic review of actual failures. Once identified, you can create targeted evaluators for these specific issues beyond the general framework.

Finally, when implementing Jason’s Tier 2 and 3 metrics, don’t just use prompts off the shelf. The standard LLM-as-judge process requires several steps: error analysis, prompt iteration, creating labeled examples, and measuring your judge’s accuracy against human labels. Once you know your judge’s True Positive and True Negative rates, you can correct its estimates to determine the actual failure rate in your system. Skip this validation and your judges may not reflect your actual quality criteria.

In summary, debug retrieval first using IR metrics, then tackle generation quality using properly validated LLM judges.

Q: What makes a good custom interface for reviewing LLM outputs?
Great interfaces make human review fast, clear, and motivating. We recommend building your own annotation tool customized to your domain. The following features are possible enhancements we’ve seen work well, but you don’t need all of them. The screenshots shown are illustrative examples to clarify concepts. In practice, I rarely implement all these features in a single app. It’s ultimately a judgment call based on your specific needs and constraints.

1. Render Traces Intelligently, Not Generically: Present the trace in a way that’s intuitive for the domain. If you’re evaluating generated emails, render them to look like emails. If the output is code, use syntax highlighting. Allow the reviewer to see the full trace (user input, tool calls, and LLM reasoning), but keep less important details in collapsed sections that can be expanded. Here is an example of a custom annotation tool for reviewing real estate assistant emails:



A custom interface for reviewing emails for a real estate assistant.
2. Show Progress and Support Keyboard Navigation: Keep reviewers in a state of flow by minimizing friction and motivating completion. Include progress indicators (e.g., “Trace 45 of 100”) to keep the review session bounded and encourage completion. Enable hotkeys for navigating between traces (e.g., N for next), applying labels, and saving notes quickly. Below is an illustration of these features:



An annotation interface with a progress bar and hotkey guide
4. Trace navigation through clustering, filtering, and search: Allow reviewers to filter traces by metadata or search by keywords. Semantic search helps find conceptually similar problems. Clustering similar traces (like grouping by user persona) lets reviewers spot recurring issues and explore hypotheses. Below is an illustration of these features:



Cluster view showing groups of emails, such as property-focused or client-focused examples. Reviewers can drill into a group to see individual traces.
5. Prioritize labeling traces you think might be problematic: Surface traces flagged by guardrails, CI failures, or automated evaluators for review. Provide buttons to take actions like adding to datasets, filing bugs, or re-running pipeline tests. Display relevant context (pipeline version, eval scores, reviewer info) directly in the interface to minimize context switching. Below is an illustration of these ideas:



A trace view that allows you to quickly see auto-evaluator verdict, add traces to dataset or open issues. Also shows metadata like pipeline version, reviewer info, and more.
General Principle: Keep it minimal
Keep your annotation interface minimal. Only incorporate these ideas if they provide a benefit that outweighs the additional complexity and maintenance overhead.

Q: How much of my development budget should I allocate to evals?
It’s important to recognize that evaluation is part of the development process rather than a distinct line item, similar to how debugging is part of software development.

You should always be doing error analysis. When you discover issues through error analysis, many will be straightforward bugs you’ll fix immediately. These fixes don’t require separate evaluation infrastructure as they’re just part of development.

The decision to build automated evaluators comes down to cost-benefit analysis. If you can catch an error with a simple assertion or regex check, the cost is minimal and probably worth it. But if you need to align an LLM-as-judge evaluator, consider whether the failure mode warrants that investment.

In the projects we’ve worked on, we’ve spent 60-80% of our development time on error analysis and evaluation. Expect most of your effort to go toward understanding failures (i.e. looking at data) rather than building automated checks.

Be wary of optimizing for high eval pass rates. If you’re passing 100% of your evals, you’re likely not challenging your system enough. A 70% pass rate might indicate a more meaningful evaluation that’s actually stress-testing your application. Focus on evals that help you catch real issues, not ones that make your metrics look good.

Q: Why is “error analysis” so important in LLM evals, and how is it performed?
Error analysis is the most important activity in evals. Error analysis helps you decide what evals to write in the first place. It allows you to identify failure modes unique to your application and data. The process involves:

Creating a Dataset: Gathering representative traces of user interactions with the LLM. If you do not have any data, you can generate synthetic data to get started.

Open Coding: Human annotator(s) (ideally a benevolent dictator) review and write open-ended notes about traces, noting any issues. This process is akin to “journaling” and is adapted from qualitative research methodologies. When beginning, it is recommended to focus on noting the first failure observed in a trace, as upstream errors can cause downstream issues, though you can also tag all independent failures if feasible. A domain expert should be performing this step.

Axial Coding: Categorize the open-ended notes into a “failure taxonomy.”. In other words, group similar failures into distinct categories. This is the most important step. At the end, count the number of failures in each category. You can use a LLM to help with this step.

Iterative Refinement: Keep iterating on more traces until you reach theoretical saturation, meaning new traces do not seem to reveal new failure modes or information to you. As a rule of thumb, you should aim to review at least 100 traces.

You should frequently revisit this process. There are advanced ways to sample data more efficiently, like clustering, sorting by user feedback, and sorting by high probability failure patterns. Over time, you’ll develop a “nose” for where to look for failures in your data.

Do not skip error analysis. It ensures that the evaluation metrics you develop are supported by real application behaviors instead of counter-productive generic metrics (which most platforms nudge you to use). For examples of how error analysis can be helpful, see this video, or this blog post.

Q: What’s the difference between guardrails & evaluators?
Guardrails are inline safety checks that sit directly in the request/response path. They validate inputs or outputs before anything reaches a user, so they typically are:

Fast and deterministic – typically a few milliseconds of latency budget.
Simple and explainable – regexes, keyword block-lists, schema or type validators, lightweight classifiers.
Targeted at clear-cut, high-impact failures – PII leaks, profanity, disallowed instructions, SQL injection, malformed JSON, invalid code syntax, etc.
If a guardrail triggers, the system can redact, refuse, or regenerate the response. Because these checks are user-visible when they fire, false positives are treated as production bugs; teams version guardrail rules, log every trigger, and monitor rates to keep them conservative.

On the other hand, evaluators typically run after a response is produced. Evaluators measure qualities that simple rules cannot, such as factual correctness, completeness, etc. Their verdicts feed dashboards, regression tests, and model-improvement loops, but they do not block the original answer.

Evaluators are usually run asynchronously or in batch to afford heavier computation such as a LLM-as-a-Judge. Inline use of an LLM-as-Judge is possible only when the latency budget and reliability targets allow it. Slow LLM judges might be feasible in a cascade that runs on the minority of borderline cases.

Apply guardrails for immediate protection against objective failures requiring intervention. Use evaluators for monitoring and improving subjective or nuanced criteria. Together, they create layered protection.

Word of caution: Do not use llm guardrails off the shelf blindly. Always look at the prompt.

Q: What’s a minimum viable evaluation setup?
Start with error analysis, not infrastructure. Spend 30 minutes manually reviewing 20-50 LLM outputs whenever you make significant changes. Use one domain expert who understands your users as your quality decision maker (a “benevolent dictator”).

If possible, use notebooks to help you review traces and analyze data. In our opinion, this is the single most effective tool for evals because you can write arbitrary code, visualize data, and iterate quickly. You can even build your own custom annotation interface right inside notebooks, as shown in this video.

Q: How do I evaluate agentic workflows?
We recommend evaluating agentic workflows in two phases:

1. End-to-end task success. Treat the agent as a black box and ask “did we meet the user’s goal?”. Define a precise success rule per task (exact answer, correct side-effect, etc.) and measure with human or aligned LLM judges. Take note of the first upstream failure when conducting error analysis.

Once error analysis reveals which workflows fail most often, move to step-level diagnostics to understand why they’re failing.

2. Step-level diagnostics. Assuming that you have sufficiently instrumented your system with details of tool calls and responses, you can score individual components such as: - Tool choice: was the selected tool appropriate? - Parameter extraction: were inputs complete and well-formed? - Error handling: did the agent recover from empty results or API failures? - Context retention: did it preserve earlier constraints? - Efficiency: how many steps, seconds, and tokens were spent? - Goal checkpoints: for long workflows verify key milestones.

Example: “Find Berkeley homes under $1M and schedule viewings” breaks into: parameters extracted correctly, relevant listings retrieved, availability checked, and calendar invites sent. Each checkpoint can pass or fail independently, making debugging tractable.

Use transition failure matrices to understand error patterns. Create a matrix where rows represent the last successful state and columns represent where the first failure occurred. This is a great way to understand where the most failures occur.



Transition failure matrix showing hotspots in text-to-SQL agent workflow
Transition matrices transform overwhelming agent complexity into actionable insights. Instead of drowning in individual trace reviews, you can immediately see that GenSQL → ExecSQL transitions cause 12 failures while DecideTool → PlanCal causes only 2. This data-driven approach guides where to invest debugging effort. Here is another example from Bryan Bischof, that is also a text-to-SQL agent:



Bischof, Bryan “Failure is A Funnel - Data Council, 2025”
In this example, Bryan shows variation in transition matrices across experiments. How you organize your transition matrix depends on the specifics of your application. For example, Bryan’s text-to-SQL agent has an inherent sequential workflow which he exploits for further analytical insight. You can watch his full talk for more details.

Creating Test Cases for Agent Failures

Creating test cases for agent failures follows the same principles as our previous FAQ on debugging multi-turn conversation traces (i.e. try to reproduce the error in the simplest way possible, only use multi-turn tests when the failure actually requires conversation context, etc.).

Q: Seriously Hamel. Stop the bullshit. What’s your favorite eval vendor?
Eval tools are in an intensely competitive space. It would be futile to compare their features. If I tried to do such an analysis, it would be invalidated in a week! Vendors I encounter the most organically in my work are: Langsmith, Arize and Braintrust.

When I help clients with vendor selection, the decision weighs heavily towards who can offer the best support, as opposed to purely features. This changes depending on size of client, use case, etc. Yes - it’s mainly the human factor that matters, and dare I say, vibes.

I have no favorite vendor. At the core, their features are very similar - and I often build custom tools on top of them to fit my needs.

My suggestion is to explore the vendors and see which one you like the most.

Q: How are evaluations used differently in CI/CD vs. monitoring production?
The most important difference between CI vs. production evaluation is the data used for testing.

Test datasets for CI are small (in many cases 100+ examples) and purpose-built. Examples cover core features, regression tests for past bugs, and known edge cases. Since CI tests are run frequently, the cost of each test has to be carefully considered (that’s why you carefully curate the dataset). Favor assertions or other deterministic checks over LLM-as-judge evaluators.

For evaluating production traffic, you can sample live traces and run evaluators against them asynchronously. Since you usually lack reference outputs on production data, you might rely more on on more expensive reference-free evaluators like LLM-as-judge. Additionally, track confidence intervals for production metrics. If the lower bound crosses your threshold, investigate further.

These two systems are complementary: when production monitoring reveals new failure patterns through error analysis and evals, add representative examples to your CI dataset. This mitigates regressions on new issues.

Q: Are similarity metrics (BERTScore, ROUGE, etc.) useful for evaluating LLM outputs?
Generic metrics like BERTScore, ROUGE, cosine similarity, etc. are not useful for evaluating LLM outputs in most AI applications. Instead, we recommend using error analysis to identify metrics specific to your application’s behavior. We recommend designing binary pass/fail evals (using LLM-as-judge) or code-based assertions.

As an example, consider a real estate CRM assistant. Suggesting showings that aren’t available (can be tested with an assertion) or confusing client personas (can be tested with a LLM-as-judge) is problematic . Generic metrics like similarity or verbosity won’t catch this. A relevant quote from the course:

“The abuse of generic metrics is endemic. Many eval vendors promote off the shelf metrics, which ensnare engineers into superfluous tasks.”

Similarity metrics aren’t always useless. They have utility in domains like search and recommendation (and therefore can be useful for optimizing and debugging retrieval for RAG). For example, cosine similarity between embeddings can measure semantic closeness in retrieval systems, and average pairwise similarity can assess output diversity (where lower similarity indicates higher diversity).

Q: Should I use “ready-to-use” evaluation metrics?
No. Generic evaluations waste time and create false confidence. (Unless you’re using them for exploration).

One instructor noted:

“All you get from using these prefab evals is you don’t know what they actually do and in the best case they waste your time and in the worst case they create an illusion of confidence that is unjustified.”1

Generic evaluation metrics are everywhere. Eval libraries contain scores like helpfulness, coherence, quality, etc. promising easy evaluation. These metrics measure abstract qualities that may not matter for your use case. Good scores on them don’t mean your system works.

Instead, conduct error analysis to understand failures. Define binary failure modes based on real problems. Create custom evaluators for those failures and validate them against human judgment. Essentially, the entire evals process.

Experienced practitioners may still use these metrics, just not how you’d expect. As Picasso said: “Learn the rules like a pro, so you can break them like an artist.” Once you understand why generic metrics fail as evaluations, you can repurpose them as exploration tools to find interesting traces (explained in the next FAQ).

Q: How can I efficiently sample production traces for review?
It can be cubersome to review traces randomly, especially when most traces don’t have an error. These sampling strategies help you find traces more likely to reveal problems:

Outlier detection: Sort by any metric (response length, latency, tool calls) and review extremes.
User feedback signals: Prioritize traces with negative feedback, support tickets, or escalations.
Metric-based sorting: Generic metrics can serve as exploration signals to find interesting traces. Review both high and low scores and treat them as exploration clues. Based on what you learn, you can build custom evaluators for the failure modes you find.
Stratified sampling: Group traces by key dimensions (user type, feature, query category) and sample from each group.
---

Creating a LLM-as-a-Judge That Drives Business Results
LLMs
evals
A step-by-step guide with my learnings from 30+ AI implementations.
Author
Hamel Husain

Published
October 29, 2024

Table Of Contents
The Problem: AI Teams Are Drowning in Data
Step 1: Find The Principal Domain Expert
Next Steps
Step 2: Create a Dataset
Why a Diverse Dataset Matters
Dimensions for Structuring Your Dataset
Examples of Features, Scenarios, and Personas
This taxonomy is not universal
Generating Data
Example LLM Prompts for Generating User Inputs
Generating Synthetic Data
Next Steps
Step 3: Direct The Domain Expert to Make Pass/Fail Judgments with Critiques
Why are simple pass/fail metrics important?
The Role of Critiques
Examples of Good Critiques
Don’t stray from binary pass/fail judgments when starting out
Make it easy for the domain expert to review data
How many examples do you need?
Step 4: Fix Errors
Step 5: Build Your LLM as A Judge, Iteratively
The Hidden Power of Critiques
Start with Expert Examples
Keep Iterating on the Prompt Until Convergence With Domain Expert
How to Optimize the LLM Judge Prompt?
The Human Side of the Process
How Often Should You Evaluate?
What if this doesn’t work?
Mistakes I’ve noticed in LLM judge prompts
Step 6: Perform Error Analysis
Classify Traces
An Interactive Walkthrough of Error Analysis
Fix Your Errors, Again
Doing this well requires data literacy
Step 7: Create More Specialized LLM Judges (if needed)
Recap of Critique Shadowing
It’s Not The Judge That Created Value, Afterall
Do You Really Need This?
FAQ
If I have a good judge LLM, isn’t that also the LLM I’d also want to use?
Do you recommend fine-tuning judges?
What’s wrong with off-the-shelf LLM judges?
How Do you evaluate the LLM judge?
What model do you use for the LLM judge?
What about guardrails?
I’m using LLM as a judge, and getting tremendous value but I didn’t follow this approach.
How do you choose between traditional ML techniques, LLM-as-a-judge and human annotations?
Can you make judges from small models?
How do you ensure consistency when updating your LLM model?
How do you phase out human in the loop to scale this?
Resources
Stay Connected
Earlier this year, I wrote Your AI product needs evals. Many of you asked, “How do I get started with LLM-as-a-judge?” This guide shares what I’ve learned after helping over 30 companies set up their evaluation systems.

The Problem: AI Teams Are Drowning in Data
Ever spend weeks building an AI system, only to realize you have no idea if it’s actually working? You’re not alone. I’ve noticed teams repeat the same mistakes when using LLMs to evaluate AI outputs:

Too Many Metrics: Creating numerous measurements that become unmanageable.
Arbitrary Scoring Systems: Using uncalibrated scales (like 1-5) across multiple dimensions, where the difference between scores is unclear and subjective. What makes something a 3 versus a 4? Nobody knows, and different evaluators often interpret these scales differently.
Ignoring Domain Experts: Not involving the people who understand the subject matter deeply.
Unvalidated Metrics: Using measurements that don’t truly reflect what matters to the users or the business.
The result? Teams end up buried under mountains of metrics or data they don’t trust and can’t use. Progress grinds to a halt. Everyone gets frustrated.

For example, it’s not uncommon for me to see dashboards that look like this:



An illustrative example of a bad eval dashboard
Tracking a bunch of scores on a 1-5 scale is often a sign of a bad eval process (I’ll discuss why later). In this post, I’ll show you how to avoid these pitfalls. The solution is to use a technique that I call “Critique Shadowing”. Here’s how to do it, step by step.

Step 1: Find The Principal Domain Expert
In most organizations there is usually one (maybe two) key individuals whose judgment is crucial for the success of your AI product. These are the people with deep domain expertise or represent your target users. Identifying and involving this Principal Domain Expert early in the process is critical.

Why is finding the right domain expert so important?

They Set the Standard: This person not only defines what is acceptable technically, but also helps you understand if you’re building something users actually want.

Capture Unspoken Expectations: By involving them, you uncover their preferences and expectations, which they might not be able to fully articulate upfront. Through the evaluation process, you help them clarify what a “passable” AI interaction looks like.

Consistency in Judgment: People in your organization may have different opinions about the AI’s performance. Focusing on the principal expert ensures that evaluations are consistent and aligned with the most critical standards.

Sense of Ownership: Involving the expert gives them a stake in the AI’s development. They feel invested because they’ve had a hand in shaping it. In the end, they are more likely to approve of the AI.

Examples of Principal Domain Experts:

A psychologist for a mental health AI assistant.
A lawyer for an AI that analyzes legal documents.
A customer service director for a support chatbot.
A lead teacher or curriculum developer for an educational AI tool.
Exceptions
In a smaller company, this might be the CEO or founder. If you are an independent developer, you should be the domain expert (but be honest with yourself about your expertise).

If you must rely on leadership, you should regularly validate their assumptions against real user feedback.

Many developers attempt to act as the domain expert themselves, or find a convenient proxy (ex: their superior). This is a recipe for disaster. People will have varying opinions about what is acceptable, and you can’t make everyone happy. What’s important is that your principal domain expert is satisfied.

Remember: This doesn’t have to take a lot of the domain expert’s time. Later in this post, I’ll discuss how you can make the process efficient. Their involvement is absolutely critical to the AI’s success.

Next Steps
Once you’ve found your expert, we need to give them the right data to review. Let’s talk about how to do that next.

Step 2: Create a Dataset
With your principal domain expert on board, the next step is to build a dataset that captures problems that your AI will encounter. It’s important that the dataset is diverse and represents the types of interactions that your AI will have in production.

Why a Diverse Dataset Matters
Comprehensive Testing: Ensures your AI is evaluated across a wide range of situations.
Realistic Interactions: Reflects actual user behavior for more relevant evaluations.
Identifies Weaknesses: Helps uncover areas where the AI may struggle or produce errors.
Dimensions for Structuring Your Dataset
You want to define dimensions that make sense for your use case. For example, here are ones that I often use for B2C applications:

Features: Specific functionalities of your AI product.
Scenarios: Situations or problems the AI may encounter and needs to handle.
Personas: Representative user profiles with distinct characteristics and needs.
Examples of Features, Scenarios, and Personas
Features
Feature	Description
Email Summarization	Condensing lengthy emails into key points.
Meeting Scheduler	Automating the scheduling of meetings across time zones.
Order Tracking	Providing shipment status and delivery updates.
Contact Search	Finding and retrieving contact information from a database.
Language Translation	Translating text between languages.
Content Recommendation	Suggesting articles or products based on user interests.
Scenarios
Scenarios are situations the AI needs to handle, (not based on the outcome of the AI’s response).

Scenario	Description
Multiple Matches Found	User’s request yields multiple results that need narrowing down. For example: User asks “Where’s my order?” but has three active orders (#123, #124, #125). AI must help identify which specific order they’re asking about.
No Matches Found	User’s request yields no results, requiring alternatives or corrections. For example: User searches for order #ABC-123 which doesn’t exist. AI should explain valid order formats and suggest checking their confirmation email.
Ambiguous Request	User input lacks necessary specificity. For example: User says “I need to change my delivery” without specifying which order or what aspect of delivery (date, address, etc.) they want to change.
Invalid Data Provided	User provides incorrect data type or format. For example: User tries to track a return using a regular order number instead of a return authorization (RMA) number.
System Errors	Technical issues prevent normal operation. For example: While looking up an order, the inventory database is temporarily unavailable. AI needs to explain the situation and provide alternatives.
Incomplete Information	User omits required details. For example: User wants to initiate a return but hasn’t provided the order number or reason. AI needs to collect this information step by step.
Unsupported Feature	User requests functionality that doesn’t exist. For example: User asks to change payment method after order has shipped. AI must explain why this isn’t possible and suggest alternatives.
Personas
Persona	Description
New User	Unfamiliar with the system; requires guidance.
Expert User	Experienced; expects efficiency and advanced features.
Non-Native Speaker	May have language barriers; uses non-standard expressions.
Busy Professional	Values quick, concise responses; often multitasking.
Technophobe	Uncomfortable with technology; needs simple instructions.
Elderly User	May not be tech-savvy; requires patience and clear guidance.
This taxonomy is not universal
This taxonomy (features, scenarios, personas) is not universal. For example, it may not make sense to even have personas if users aren’t directly engaging with your AI. The idea is you should outline dimensions that make sense for your use case and generate data that covers them. You’ll likely refine these after the first round of evaluations.

Generating Data
To build your dataset, you can:

Use Existing Data: Sample real user interactions or behaviors from your AI system.
Generate Synthetic Data: Use LLMs to create realistic user inputs covering various features, scenarios, and personas.
Often, you’ll do a combination of both to ensure comprehensive coverage. Synthetic data is not as good as real data, but it’s a good starting point. Also, we are only using LLMs to generate the user inputs, not the LLM responses or internal system behavior.

Regardless of whether you use existing data or synthetic data, you want good coverage across the dimensions you’ve defined.

Incorporating System Information

When making test data, use your APIs and databases where appropriate. This will create realistic data and trigger the right scenarios. Sometimes you’ll need to write simple programs to get this information. That’s what the “Assumptions” column is referring to in the examples below.

Example LLM Prompts for Generating User Inputs
Here are some example prompts that illustrate how to use an LLM to generate synthetic user inputs for different combinations of features, scenarios, and personas:

ID	Feature	Scenario	Persona	LLM Prompt to Generate User Input	Assumptions (not directly in the prompt)
1	Order Tracking	Invalid Data Provided	Frustrated Customer	“Generate a user input from someone who is clearly irritated and impatient, using short, terse language to demand information about their order status for order number #**********. Include hints of previous negative experiences.”	Order number #********** does not exist in the system.
2	Contact Search	Multiple Matches Found	New User	“Create a user input from someone who seems unfamiliar with the system, using hesitant language and asking for help to find contact information for a person named ‘Alex’. The user should appear unsure about what information is needed.”	Multiple contacts named ‘Alex’ exist in the system.
3	Meeting Scheduler	Ambiguous Request	Busy Professional	“Simulate a user input from someone who is clearly in a hurry, using abbreviated language and minimal details to request scheduling a meeting. The message should feel rushed and lack specific information.”	N/A
4	Content Recommendation	No Matches Found	Expert User	“Produce a user input from someone who demonstrates in-depth knowledge of their industry, using specific terminology to request articles on sustainable supply chain management. Use the information in this article involving sustainable supply chain management to formulate a plausible query: {{article}}”	No articles on ‘Emerging trends in sustainable supply chain management’ exist in the system.
Generating Synthetic Data
When generating synthetic data, you only need to create the user inputs. You then feed these inputs into your AI system to generate the AI’s responses. It’s important that you log everything so you can evaluate your AI. To recap, here’s the process:

Generate User Inputs: Use the LLM prompts to create realistic user inputs.
Feed Inputs into Your AI System: Input the user interactions into your AI as it currently exists.
Capture AI Responses: Record the AI’s responses to form complete interactions.
Organize the Interactions: Create a table to store the user inputs, AI responses, and relevant metadata.
How much data should you generate?
There is no right answer here. At a minimum, you want to generate enough data so that you have examples for each combination of dimensions (in this toy example: features, scenarios, and personas). However, you also want to keep generating more data until you feel like you have stopped seeing new failure modes. The amount of data I generate varies significantly depending on the use case.

Does synthetic data actually work?
You might be skeptical of using synthetic data. After all, it’s not real data, so how can it be a good proxy? In my experience, it works surprisingly well. Some of my favorite AI products, like Hex use synthetic data to power their evals:

“LLMs are surprisingly good at generating excellent - and diverse - examples of user prompts. This can be relevant for powering application features, and sneakily, for building Evals. If this sounds a bit like the Large Language Snake is eating its tail, I was just as surprised as you! All I can say is: it works, ship it.” Bryan Bischof, Head of AI Engineering at Hex

Next Steps
With your dataset ready, now comes the most important part: getting your principal domain expert to evaluate the interactions.

Step 3: Direct The Domain Expert to Make Pass/Fail Judgments with Critiques
The domain expert’s job is to focus on one thing: “Did the AI achieve the desired outcome?” No complex scoring scales or multiple metrics. Just a clear pass or fail decision. In addition to the pass/fail decision, the domain expert should write a critique that explains their reasoning.

Why are simple pass/fail metrics important?
Clarity and Focus: A binary decision forces everyone to consider what truly matters. It simplifies the evaluation to a single, crucial question.

Actionable Insights: Pass/fail judgments are easy to interpret and act upon. They help you quickly identify whether the AI meets the user’s needs.

Forces Articulation of Expectations: When domain experts must decide if an interaction passes or fails, they are compelled to articulate their expectations clearly. This process uncovers nuances and unspoken assumptions about how the AI should behave.

Efficient Use of Resources: Keeps the evaluation process manageable, especially when starting out. You avoid getting bogged down in detailed metrics that might not be meaningful yet.

The Role of Critiques
Alongside a binary pass/fail judgment, it’s important to write a detailed critique of the LLM-generated output. These critiques:

Captures Nuances: The critique allows you to note if something was mostly correct but had areas for improvement.

Guide Improvement: Detailed feedback provides specific insights into how the AI can be enhanced.

Balance Simplicity with Depth: While the pass/fail offers a clear verdict, the critique offers the depth needed to understand the reasoning behind the judgment.

Why Write Critiques?:

In practice, domain experts may not have fully internalized all the judgment criteria. By forcing them to make a pass/fail decision and explain their reasoning, they clarify their expectations and provide valuable guidance for refining the AI.

“But my problem is complex!” Trust me—starting simple forces you to focus on what truly matters. You can introduce more complexity later if needed.

Examples of Good Critiques
To illustrate how simple pass/fail judgments combined with detailed critiques work in practice, here’s a table showcasing examples of user interactions with an AI assistant. The table includes both passes and fails, with critiques explaining why the AI received that judgment. In cases where the AI passed despite critical issues, the critique highlights these aspects and justifies why it still passed overall. For failed interactions, the critique explains the critical elements that led to the failure.

User Interaction with AI	Judgment	Critique
User: “I need to cancel my flight booking for next week.”
AI: “Your flight booking for next week has been canceled. A confirmation email has been sent to you.”	Pass	The AI successfully executed the user’s request by canceling the flight and sending a confirmation, achieving the primary goal. However, it should have asked for confirmation before proceeding to prevent accidental cancellations. Despite this critical oversight, the AI fulfilled the user’s request, so it passes, but adding a confirmation step would improve user safety.
User: “What’s the capital of France?”
AI: “The capital of France is Paris.”	Pass	The AI correctly answered the user’s question, providing accurate information promptly. Although the response is sufficient, the AI could enhance the interaction by offering additional interesting facts or asking if the user has more questions. Despite this opportunity for added engagement, the AI met the user’s primary need, so it passes.
User: “I can’t log into my account; it says my password is incorrect.”
AI: “Please reset your password by clicking ‘Forgot Password’.”	Fail	The AI provided a generic solution without acknowledging the user’s frustration or checking for other issues like account lockout. It failed to offer empathetic support or additional assistance. Due to the lack of personalized help and poor user experience, it fails.
User: “Book a table for two at an Italian restaurant tonight at 7 PM.”
AI: “Table for two at Luigi’s Italian Restaurant booked for tonight at 7 PM. Confirmation number is 12345.”	Pass	The AI efficiently completed the booking as requested, providing all necessary details and a confirmation number. However, it could have asked if the user had seating preferences or dietary restrictions. The AI accomplished the primary task successfully, so it passes, with minor opportunities for enhanced service.
User: “I need help with my billing statement.”
AI: “Please provide your account number and full name.”	Fail	The AI requested sensitive information without verifying the user’s identity through secure means, potentially compromising security. It should guide the user through a secure authentication process first. Due to this critical oversight in user data protection, it fails.
These examples demonstrate how the AI can receive both “Pass” and “Fail” judgments. In the critiques:

For passes, we explain why the AI succeeded in meeting the user’s primary need, even if there were critical aspects that could be improved. We highlight these areas for enhancement while justifying the overall passing judgment.

For fails, we identify the critical elements that led to the failure, explaining why the AI did not meet the user’s main objective or compromised important factors like user experience or security.

Most importantly, the critique should be detailed enough so that you can use it in a few-shot prompt for a LLM judge. In other words, it should be detailed enough that a new employee could understand it. Being too terse is a common mistake.

Note that the example user interactions with the AI are simplified for brevity - but you might need to give the domain expert more context to make a judgement. More on that later.

Note
At this point, you don’t need to perform a root cause analysis into the technical reasons behind why the AI failed. Many times, it’s useful to get a sense of overall behavior before diving into the weeds.

Don’t stray from binary pass/fail judgments when starting out
A common mistake is straying from binary pass/fail judgments. Let’s revisit the dashboard from earlier:



If your evaluations consist of a bunch of metrics that LLMs score on a 1-5 scale (or any other scale), you’re doing it wrong. Let’s unpack why.

It’s not actionable: People don’t know what to do with a 3 or 4. It’s not immediately obvious how this number is better than a 2. You need to be able to say “this interaction passed because…” and “this interaction failed because…”.
More often than not, these metrics do not matter. Every time I’ve analyzed data on domain expert judgments, they tend not to correlate with these kind of metrics. By having a domain expert make a binary judgment, you can figure out what truly matters.
This is why I hate off the shelf metrics that come with many evaluation frameworks. They tend to lead people astray.

Common Objections to Pass/Fail Judgments:

“The business said that these 8 dimensions are important, so we need to evaluate all of them.”
“We need to be able to say why an interaction passed or failed.”
I can guarantee you that if someone says you need to measure 8 things on a 1-5 scale, they don’t know what they are looking for. They are just guessing. You have to let the domain expert drive and make a pass/fail judgment with critiques so you can figure out what truly matters. Stand your ground here.

Make it easy for the domain expert to review data
Finally, you need to remove all friction from reviewing data. I’ve written about this here. Sometimes, you can just use a spreadsheet. It’s a judgement call in terms of what is easiest for the domain expert. I found that I often have to provide additional context to help the domain expert understand the user interaction, such as:

Metadata about the user, such as their location, subscription tier, etc.
Additional context about the system, such as the current time, inventory levels, etc.
Resources so you can check if the AI’s response is correct (ex: ability to search a database, etc.)
All of this data needs to be presented on a single screen so the domain expert can review it without jumping through hoops. That’s why I recommend building a simple web app to review data.

How many examples do you need?
The number of examples you need depends on the complexity of the task. My heuristic is that I start with around 30 examples and keep going until I do not see any new failure modes. From there, I keep going until I’m not learning anything new.

Next, we’ll look at how to use this data to build an LLM judge.

Step 4: Fix Errors
After looking at the data, it’s likely you will find errors in your AI system. Instead of plowing ahead and building an LLM judge, you want to fix any obvious errors. Remember, the whole point of the LLM as a judge is to help you find these errors, so it’s totally fine if you find them earlier!

If you have already developed Level 1 evals as outlined in my previous post, you should not have any pervasive errors. However, these errors can sometimes slip through the cracks. If you find pervasive errors, fix them and go back to step 3. Keep iterating until you feel like you have stabilized your system.

Step 5: Build Your LLM as A Judge, Iteratively
The Hidden Power of Critiques
You cannot write a good judge prompt until you’ve seen the data. The paper from Shankar et al., “Who Validates the Validators? Aligning LLM-Assisted Evaluation of LLM Outputs with Human Preferences” summarizes this well:

to grade outputs,people need to externalize and define their evaluation criteria; however, the process of grading outputs helps them to define that very criteria. We dub this phenomenon criteria drift, and it implies thatit is impossible to completely determine evaluation criteria prior to human judging of LLM outputs.

Start with Expert Examples
Let me share a real-world example of building an LLM judge you can apply to your own use case. When I was helping Honeycomb build their Query Assistant feature, we needed a way to evaluate if the AI was generating good queries. Here’s what our LLM judge prompt looked like, including few-shot examples of critiques from our domain expert, Phillip:

You are a Honeycomb query evaluator with advanced capabilities to judge if a query is good or not.
You understand the nuances of the Honeycomb query language, including what is likely to be
most useful from an analytics perspective. 

Here is information about the Honeycomb query language:
{{query_language_info}}

Here are some guidelines for evaluating queries:
{{guidelines}}

Example evaluations:

<examples>

<example-1>
<nlq>show me traces where ip is *********</nlq>
<query>
{
  "breakdowns": ["trace.trace_id"],
  "calculations": [{"op": "COUNT"}],
  "filters": [{"column": "net.host.ip", "op": "=", "value": "*********"}]
}
</query>
<critique>
{
  "critique": "The query correctly filters for traces with an IP address of ********* 
   and counts the occurrences of those traces, grouped by trace.trace_id. The response 
   is good as it meets the requirement of showing traces from a specific IP address 
   without additional complexities.",
  "outcome": "good"
}
</critique>
</example-1>

<example-2>
<nlq>show me slowest trace</nlq>
<query>
{
  "calculations": [{"column": "duration_ms", "op": "MAX"}],
  "orders": [{"column": "duration_ms", "op": "MAX", "order": "descending"}],
  "limit": 1
}
</query>
<critique>
{
  "critique": "While the query attempts to find the slowest trace using MAX(duration_ms) 
   and ordering correctly, it fails to group by trace.trace_id. Without this grouping, 
   the query only shows the MAX(duration_ms) measurement over time, not the actual 
   slowest trace.",
  "outcome": "bad"
}
</critique>
</example-2>

<example-3>
<nlq>count window-hash where window-hash exists per hour</nlq>
<query>
{
  "breakdowns": ["window-hash"],
  "calculations": [{"op": "COUNT"}],
  "filters": [{"column": "window-hash", "op": "exists"}],
  "time_range": 3600
}
</query>
<critique>
{
  "critique": "While the query correctly counts window-hash occurrences, the time_range 
   of 3600 seconds (1 hour) is insufficient for per-hour analysis. When we say 'per hour', 
   we need a time_range of at least 36000 seconds to show meaningful hourly patterns.",
  "outcome": "bad"
}
</critique>
</example-3>

</examples>

For the following query, first write a detailed critique explaining your reasoning,
then provide a pass/fail judgment in the same format as above.

<nlq>{{user_input}}</nlq>
<query>
{{generated_query}}
</query>
<critique>
Notice how each example includes:

The natural language query (NLQ) in <nlq> tags
The generated query in <query> tags
The critique and outcome in <critique> tags
In the prompt above, the example critiques are fixed. An advanced approach is to include examples dynamically based upon the item you are judging. You can learn more in this post about Continual In-Context Learning.

Keep Iterating on the Prompt Until Convergence With Domain Expert
In this case, I used a low-tech approach to iterate on the prompt. I sent Phillip a spreadsheet with the following information:

The NLQ
The generated query
The critique
The outcome (pass or fail)
Phillip would then fill out his own version of the spreadsheet with his critiques. I used this to iteratively improve the prompt. The spreadsheet looked like this:



I also tracked agreement rates over time to ensure we were converging on a good prompt.



It took us only three iterations to achieve > 90% agreement between the LLM and Phillip. Your mileage may vary depending on the complexity of the task. For example, Swyx has conducted a similar process hundreds of times for AI News, an extremely popular news aggregator with high quality recommendations. The quality of the AI owing to this process is why this product has received critical acclaim.

How to Optimize the LLM Judge Prompt?
I usually adjust the prompts by hand. I haven’t had much luck with prompt optimizers like DSPy. However, my friend Eugene Yan has just released a promising tool named ALIGN Eval. I like it because it’s simple and effective. Also, don’t forget the approach of continual in-context learning mentioned earlier - it can be effective when implemented correctly.

In rare cases, I might fine-tune a judge, but I prefer not to. I talk about this more in the FAQ section.

The Human Side of the Process
Something unexpected happened during this process. Phillip Carter, our domain expert at Honeycomb, found that reviewing the LLM’s critiques helped him articulate his own evaluation criteria more clearly. He said,

“Seeing how the LLM breaks down its reasoning made me realize I wasn’t being consistent about how I judged certain edge cases.”

This is a pattern I’ve seen repeatedly—the process of building an LLM judge often helps standardize evaluation criteria.

Furthermore, because this process forces the domain expert to look at data carefully, I always uncover new insights about the product, AI capabilities, and user needs. The resulting benefits are often more valuable than creating a LLM judge!

How Often Should You Evaluate?
I conduct this human review at regular intervals and whenever something material changes. For example, if I update a model, I’ll run the process again. I don’t get too scientific here; instead, I rely on my best judgment. Also note that after the first two iterations, I tend to focus more on errors rather than sampling randomly. For example, if I find an error, I’ll search for more examples that I think might trigger the same error. However, I always do a bit of random sampling as well.

What if this doesn’t work?
I’ve seen this process fail when:

The AI is overscoped: Example - a chatbot in a SaaS product that promises to do anything you want.
The process is not followed correctly: Not using the principal domain expert, not writing proper critiques, etc.
The expectations of alignment are unrealistic or not feasible.
In each of these cases, I try to address the root cause instead of trying to force alignment. Sometimes, you may not be able to achieve the alignment you want and may have to lean heavier on human annotations. However, after following the process described here, you will have metrics that help you understand how much you can trust the LLM judge.

Mistakes I’ve noticed in LLM judge prompts
Most of the mistakes I’ve seen in LLM judge prompts have to do with not providing good examples:

Not providing any critiques.
Writing extremely terse critiques.
Not providing external context. Your examples should contain the same information you use to evaluate, including external information like user metadata, system information etc.
Not providing diverse examples. You need a wide variety of examples to ensure that your judge works for a wide variety of inputs.
Sometimes, you may encounter difficulties with fitting everything you need into the prompt, and may have to get creative about how you structure the examples. However, this is becoming less of an issue thanks to expanding context windows and prompt caching.

Step 6: Perform Error Analysis
After you have created a LLM as a judge, you will have a dataset of user interactions with the AI, and the LLM’s judgments. If your metrics show an acceptable agreement between the domain expert and the LLM judge, you can apply this judge against real or synthetic interactions. After this, you can you calculate error rates for different dimensions of your data. You should calculate the error on unseen data only to make sure your aren’t getting biased results.

For example, if you have segmented your data by persona, scenario, feature, etc, your data analysis may look like this

Error Rates by Key Dimensions

Feature	Scenario	Persona	Total Examples	Failure Rate
Order Tracking	Multiple Matches	New User	42	24.3%
Order Tracking	Multiple Matches	Expert User	38	18.4%
Order Tracking	No Matches	Expert User	30	23.3%
Order Tracking	No Matches	New User	20	75.0%
Contact Search	Multiple Matches	New User	35	22.9%
Contact Search	Multiple Matches	Expert User	32	19.7%
Contact Search	No Matches	New User	25	68.0%
Contact Search	No Matches	Expert User	28	21.4%
Classify Traces
Once you know where the errors are now you can perform an error analysis to get to the root cause of the errors. My favorite way is to look at examples of each type of error and classify them by hand. I recommend using a spreadsheet for this. For example, a trace for Order tracking where there are no matches for new users might look like this:

Example Trace
In this example trace, the user provides an invalid order number. The AI correctly identifies that the order number is invalid but provides an unhelpful response. If you are not familiar with logging LLM traces, refer to my previous post on evals.

Note that this trace is formatted for readability.

{
 "user_input": "Where's my order #ABC123?",
 "function_calls": [
   {
     "name": "search_order_database",
     "args": {"order_id": "ABC123"},
     "result": {
       "status": "not_found",
       "valid_patterns": ["XXX-XXX-XXX"]
     }
   },
   {
     "name": "retrieve_context",
     "result": {
       "relevant_docs": [
         "Order numbers follow format XXX-XXX-XXX",
         "New users should check confirmation email"
       ]
     }
   }
 ],
 "llm_intermediate_steps": [
   {
     "thought": "User is new and order format is invalid",
     "action": "Generate help message with format info"
   }
 ],
 "final_response": "I cannot find that order #. Please check the number and try again."
}

In this case, you might classify the error as: Missing User Education. The system retrieved new user context and format information but failed to include it in the response, which suggests we could improve our prompt. After you have classified a number of errors, you can calculate the distribution of errors by root cause. That might look like this:

Root Cause Distribution (20 Failed Interactions)

Root Cause	Count	Percentage
Missing User Education	8	40%
Authentication/Access Issues	6	30%
Poor Context Handling	4	20%
Inadequate Error Messages	2	10%
Now you know where to focus your efforts. This doesn’t have to take an extraordinary amount of time. You can get quite far in just 15 minutes. Also, you can use a LLM to help you with this classification, but that is beyond the scope of this post (you can use a LLM to help you do anything in this post, as long as you have a process to verify the results).

An Interactive Walkthrough of Error Analysis
Error analysis has been around in Machine Learning for quite some time. This video by Andrew Ng does a great job of walking through the process interactively:


Fix Your Errors, Again
Now that you have a sense of the errors, you can go back and fix them again. Go back to step 3 and iterate until you are satisfied. Note that every time you fix an error, you should try to write a test case for it. Sometimes, this can be an assertion in your test suite, but other times you may need to create a more “specialized” LLM judge for these failures. We’ll talk about this next.

Doing this well requires data literacy
Investigating your data is much harder in practice than I made it look in this post. It requires a nose for data that only comes from practice. It also helps to have some basic familiarity with statistics and data analysis tools. My favorite post on data literacy is this one by Jason Liu and Eugene Yan.

Step 7: Create More Specialized LLM Judges (if needed)
Now that you have a sense for where the problems in your AI are, you can decide where and if to invest in more targeted LLM judges. For example, if you find that the AI has trouble with citing sources correctly, you can created a targeted eval for that. You might not even need a LLM judge for some errors (and use a code-based assertion instead).

The key takeaway is don’t jump directly to using specialized LLM judges until you have gone through this critique shadowing process. This will help you rationalize where to invest your time.

Recap of Critique Shadowing
Using an LLM as a judge can streamline your AI evaluation process if approached correctly. Here’s a visual illustration of the process (there is a description of the process below the diagram as well):

Yes

No

No

Yes

Yes

No

Yes

Start

1 Find Principal Domain Expert

2 Create Dataset

3 Domain Expert Reviews Data

Found Errors?

4 Fix Errors

5 Build LLM Judge

Test Against Domain Expert

Acceptable Agreement?

Refine Prompt

6 Perform Error Analysis

Critical Issues Found?

7 Fix Issues & Create Specialized Judges

Material Changes or Periodic Review?

The Critique Shadowing process is iterative, with feedback loops. Let’s list out the steps:

Find Principal Domain Expert
Create A Dataset
Generate diverse examples covering your use cases
Include real or synthetic user interactions
Domain Expert Reviews Data
Expert makes pass/fail judgments
Expert writes detailed critiques explaining their reasoning
Fix Errors (if found)
Address any issues discovered during review
Return to expert review to verify fixes
Go back to step 3 if errors are found
Build LLM Judge
Create prompt using expert examples
Test against expert judgments
Refine prompt until agreement is satisfactory
Perform Error Analysis
Calculate error rates across different dimensions
Identify patterns and root causes
Fix errors and go back to step 3 if needed
Create specialized judges as needed
This process never truly ends. It repeats periodically or when material changes occur.

It’s Not The Judge That Created Value, Afterall
The real value of this process is looking at your data and doing careful analysis. Even though an AI judge can be a helpful tool, going through this process is what drives results. I would go as far as saying that creating a LLM judge is a nice “hack” I use to trick people into carefully looking at their data!

That’s right. The real business value comes from looking at your data. But hey, potato, potahto.

Do You Really Need This?
Phew, this seems like a lot of work! Do you really need this? Well, it depends. There are cases where you can take a shortcut through this process. For example, let’s say:

You are an independent developer who is also a domain expert.
You are working with test data that already available. (Tweets, etc.)
Looking at data is not costly (etc. you can manually look at enough data in a few hours)
In this scenario, you can jump directly to something that looks like step 3 and start looking at data right away. Also, since it’s not that costly to look at data, it’s probably fine to just do error analysis without a judge (at least initially). You can incorporate what you learn directly back into your primary model right away. This example is not exhaustive, but gives you an idea of how you can adapt this process to your needs.

However, you can never completely eliminate looking at your data! This is precisely the step that most people skip. Don’t be that person.

FAQ
I received a lot of questions about this topic. Here are answers to the most common ones:

If I have a good judge LLM, isn’t that also the LLM I’d also want to use?
Effective judges often use larger models or more compute (via longer prompts, chain-of-thought, etc.) than the systems they evaluate.

However, If the cost of the most powerful LLM is not prohibitive, and latency is not an issue, then you might want to consider where you invest your efforts differently. In this case, it might make sense to put more effort towards specialist LLM judges, code-based assertions, and A/B testing. However, you should still go through the process of looking at data and critiquing the LLM’s output before you adopt specialized judges.

Do you recommend fine-tuning judges?
I prefer not to fine-tune LLM judges. I’d rather spend the effort fine-tuning the actual LLM instead. However, fine-tuning guardrails or other specialized judges can be useful (especially if they are small classifiers).

As a related note, you can leverage a LLM judge to curate and transform data for fine-tuning your primary model. For example, you can use the judge to:

Eliminate bad examples for fine-tuning.
Generate higher quality outputs (by referencing the critique).
Simulate high quality chain-of-thought with critiques.
Using a LLM judge for enhancing fine-tuning data is even more compelling when you are trying to distill a large LLM into a smaller one. The details of fine-tuning are beyond the scope of this post. If you are interested in learning more, see these resources.

What’s wrong with off-the-shelf LLM judges?
Nothing is strictly wrong with them. It’s just that many people are led astray by them. If you are disciplined you can apply them to your data and see if they are telling you something valuable. However, I’ve found that these tend to cause more confusion than value.

How Do you evaluate the LLM judge?
You will collect metrics on the agreement between the domain expert and the LLM judge. This tells you how much you can trust the judge and in what scenarios. Your domain expert doesn’t have to inspect every single example, you just need a representative sample so you can have reliable statistics.

What model do you use for the LLM judge?
For the kind of judge articulated in this blog post, I like to use the most powerful model I can afford in my cost/latency budget. This budget might be different than my primary model, depending on the number of examples I need to score. This can vary significantly according to the use case.

What about guardrails?
Guardrails are a separate but related topic. They are a way to prevent the LLM from saying/doing something harmful or inappropriate. This blog post focuses on helping you create a judge that’s aligned with business goals, especially when starting out.

I’m using LLM as a judge, and getting tremendous value but I didn’t follow this approach.
I believe you. This blog post is not the only way to use a LLM as a judge. In fact, I’ve seen people use a LLM as a judge in all sorts of creative ways, which include ranking, classification, model selection and so-on. I’m focused on an approach that works well when you are getting started, and avoids the pitfalls of confusing metric sprawl. However, the general process of looking at the data is still central no matter what kind of judge you are building.

How do you choose between traditional ML techniques, LLM-as-a-judge and human annotations?
The answer to this (and many other questions) is: do the simplest thing that works. And simple doesn’t always mean traditional ML techniques. Depending on your situation, it might be easier to use a LLM API as a classifier than to train a model and deploy it.

Can you make judges from small models?
Yes, potentially. I’ve only used the larger models for judges. You have to base the answer to this question on the data (i.e. the agreement with the domain expert).

How do you ensure consistency when updating your LLM model?
You have to go through the process again and measure the results.

How do you phase out human in the loop to scale this?
You don’t need a domain expert to grade every single example. You just need a representative sample. I don’t think you can eliminate humans completely, because the LLM still needs to be aligned to something, and that something is usually a human. As your evaluation system gets better, it naturally reduces the amount of human effort required.

---
Frequently Asked Questions (And Answers)
About AI Evals
Hamel Husain Shreya Shankar
2025-07-01
Contents
Q: Is RAG dead? . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
Q: Can I use the same model for both the main task and evaluation? . . . . . . . . . 3
Q: How much time should I spend on model selection? . . . . . . . . . . . . . . . . . 3
Q: Should I build a custom annotation tool or use something off-the-shelf? . . . . . . 3
Q: Why do you recommend binary (pass/fail) evaluations instead of 1-5 ratings (Likert scales)? . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 4
Q: How do I debug multi-turn conversation traces? . . . . . . . . . . . . . . . . . . . 4
Q: Should I build automated evaluators for every failure mode I find? . . . . . . . . 5
Q: How many people should annotate my LLM outputs? . . . . . . . . . . . . . . . . 5
Q: What gaps in eval tooling should I be prepared to fill myself? . . . . . . . . . . . 6
Q: What is the best approach for generating synthetic data? . . . . . . . . . . . . . . 7
Q: How do I approach evaluation when my system handles diverse user queries? . . . 8
Q: How do I choose the right chunk size for my document processing tasks? . . . . . 8
Q: How should I approach evaluating my RAG system? . . . . . . . . . . . . . . . . 10
Q: What makes a good custom interface for reviewing LLM outputs? . . . . . . . . . 11
Q: How much of my development budget should I allocate to evals? . . . . . . . . . . 14
Q: Why is “error analysis” so important in LLM evals, and how is it performed? . . 15
Q: What’s the difference between guardrails & evaluators? . . . . . . . . . . . . . . . 15
Q: What’s a minimum viable evaluation setup? . . . . . . . . . . . . . . . . . . . . . 16
Q: How do I evaluate agentic workflows? . . . . . . . . . . . . . . . . . . . . . . . . . 16
Q: Seriously Hamel. Stop the bullshit. What’s your favorite eval vendor? . . . . . . 18
Q: How are evaluations used differently in CI/CD vs. monitoring production? . . . . 19
This post curates the most common questions Shreya and I have recieved from teaching 700+
engineers & PMs in AI Evals. Warning: These are sharp opinions about what works in most
cases. They are not universal truths. Use your judgment.
1
ff We are teaching our last and final cohort of our AI Evals course next month (we have to get
back to building). Here is a 35% discount code for readers of this post. ff
Q: Is RAG dead?
Question: Should I avoid using RAG for my AI application after reading that “RAG is dead”
for coding agents?
Many developers are confused about when and how to use RAG after reading
articles claiming “RAG is dead.” Understanding what RAG actually means versus
the narrow marketing definitions will help you make better architectural decisions
for your AI applications.
The viral article claiming RAG is dead specifically argues against using naive vector database
retrieval for autonomous coding agents, not RAG as a whole. This is a crucial distinction that
many developers miss due to misleading marketing.
RAG simply means Retrieval-Augmented Generation - using retrieval to provide relevant context that improves your model’s output. The core principle remains essential: your LLM needs
the right context to generate accurate answers. The question isn’t whether to use retrieval,
but how to retrieve effectively.
For coding applications, naive vector similarity search often fails because code relationships
are complex and contextual. Instead of abandoning retrieval entirely, modern coding assistants
like Claude Code still uses retrieval —they just employ agentic search instead of relying solely
on vector databases.similar to how human developers work.
You have multiple retrieval strategies available, ranging from simple keyword matching to
embedding similarity to LLM-powered relevance filtering. The optimal approach depends on
your specific use case, data characteristics, and performance requirements. Many production
systems combine multiple strategies or use multi-hop retrieval guided by LLM agents.
Unforunately, “RAG” has become a buzzword with no shared definition. Some people use it
to mean any retrieval system, others restrict it to vector databases. Focus on the fundamental
goal: getting your LLM the context it needs to succeed. Whether that’s through vector search,
agentic exploration, or hybrid approaches is a product and engineering decision that requires
understanding your users’ failure modes and usage patterns.
Rather than following categorical advice to avoid or embrace RAG, experiment with different
retrieval approaches and measure what works best for your application.
2
Q: Can I use the same model for both the main task and evaluation?
For LLM-as-Judge selection, using the same model is usually fine because the judge is doing
a different task than your main LLM pipeline. The judges we recommend building do scoped
binary classification tasks. Focus on achieving high True Positive Rate (TPR) and True
Negative Rate (TNR) with your judge on a held out labeled test set rather than avoiding the
same model family. You can use these metrics on the test set to understand how well your
judge is doing.
When selecting judge models, start with the most capable models available to establish strong
alignment with human judgments. You can optimize for cost later once you’ve established
reliable evaluation criteria. We do not recommend using the same model for open ended
preferences or response quality (but we don’t recommend building judges this way in the first
place!).
Q: How much time should I spend on model selection?
Many developers fixate on model selection as the primary way to improve their LLM applications. Start with error analysis to understand your failure modes before considering model
switching. As Hamel noted in office hours, “I suggest not thinking of switching model as the
main axes of how to improve your system off the bat without evidence. Does error analysis
suggest that your model is the problem?”
Q: Should I build a custom annotation tool or use something off-the-shelf?
Build a custom annotation tool. This is the single most impactful investment you can
make for your AI evaluation workflow. With AI-assisted development tools like Cursor or
Lovable, you can build a tailored interface in hours. I often find that teams with custom
annotation tools iterate ~10x faster.
Custom tools excel because:
• They show all your context from multiple systems in one place
• They can render your data in a product specific way (images, widgets, markdown, buttons, etc.)
• They’re designed for your specific workflow (custom filters, sorting, progress bars, etc.)
Off-the-shelf tools may be justified when you need to coordinate dozens of distributed annotators with enterprise access controls. Even then, many teams find the configuration overhead
and limitations aren’t worth it.
3
Isaac’s Anki flashcard annotation app shows the power of custom tools—handling 400+ results
per query with keyboard navigation and domain-specific evaluation criteria that would be
nearly impossible to configure in a generic tool.
Q: Why do you recommend binary (pass/fail) evaluations instead of 1-5 ratings
(Likert scales)?
Engineers often believe that Likert scales (1-5 ratings) provide more information
than binary evaluations, allowing them to track gradual improvements. However,
this added complexity often creates more problems than it solves in practice.
Binary evaluations force clearer thinking and more consistent labeling. Likert scales introduce
significant challenges: the difference between adjacent points (like 3 vs 4) is subjective and
inconsistent across annotators, detecting statistical differences requires larger sample sizes, and
annotators often default to middle values to avoid making hard decisions.
Having binary options forces people to make a decision rather than hiding uncertainty in
middle values. Binary decisions are also faster to make during error analysis - you don’t waste
time debating whether something is a 3 or 4.
For tracking gradual improvements, consider measuring specific sub-components with their
own binary checks rather than using a scale. For example, instead of rating factual accuracy
1-5, you could track “4 out of 5 expected facts included” as separate binary checks. This
preserves the ability to measure progress while maintaining clear, objective criteria.
Start with binary labels to understand what ‘bad’ looks like. Numeric labels are advanced and
usually not necessary.
Q: How do I debug multi-turn conversation traces?
Start simple. Check if the whole conversation met the user’s goal with a pass/fail judgment.
Look at the entire trace and focus on the first upstream failure. Read the user-visible parts
first to understand if something went wrong. Only then dig into the technical details like tool
calls and intermediate steps.
When you find a failure, reproduce it with the simplest possible test case. Here’s a example:
suppose a shopping bot gives the wrong return policy on turn 4 of a conversation. Before
diving into the full multi-turn complexity, simplify it to a single turn: “What is the return
window for product X1000?” If it still fails, you’ve proven the error isn’t about conversation
context - it’s likely a basic retrieval or knowledge issue you can debug more easily.
For generating test cases, you have two main approaches. First, you can simulate users with
another LLM to create realistic multi-turn conversations. Second, use “N-1 testing” where
you provide the first N-1 turns of a real conversation and test what happens next. The N-1
4
approach often works better since it uses actual conversation prefixes rather than fully synthetic
interactions (but is less flexible and doesn’t test the full conversation). User simulation is
getting better as models improve. Keep an eye on this space.
The key is balancing thoroughness with efficiency. Not every multi-turn failure requires multiturn analysis.
Q: Should I build automated evaluators for every failure mode I find?
Focus automated evaluators on failures that persist after fixing your prompts. Many teams
discover their LLM doesn’t meet preferences they never actually specified - like wanting short
responses, specific formatting, or step-by-step reasoning. Fix these obvious gaps first before
building complex evaluation infrastructure.
Consider the cost hierarchy of different evaluator types. Simple assertions and referencebased checks (comparing against known correct answers) are cheap to build and maintain.
LLM-as-Judge evaluators require 100+ labeled examples, ongoing weekly maintenance, and
coordination between developers, PMs, and domain experts. This cost difference should shape
your evaluation strategy.
Only build expensive evaluators for problems you’ll iterate on repeatedly. Since LLM-as-Judge
comes with significant overhead, save it for persistent generalization failures - not issues you
can fix trivially. Start with cheap code-based checks where possible: regex patterns, structural
validation, or execution tests. Reserve complex evaluation for subjective qualities that can’t
be captured by simple rules.
Q: How many people should annotate my LLM outputs?
For most small to medium-sized companies, appointing a single domain expert as a “benevolent
dictator” is the most effective approach. This person—whether it’s a psychologist for a mental
health chatbot, a lawyer for legal document analysis, or a customer service director for support
automation—becomes the definitive voice on quality standards.
A single expert eliminates annotation conflicts and prevents the paralysis that comes from
“too many cooks in the kitchen”. The benevolent dictator can incorporate input and feedback
from others, but they drive the process. If you feel like you need five subject matter experts
to judge a single interaction, it’s a sign your product scope might be too broad.
However, larger organizations or those operating across multiple domains (like a multinational
company with different cultural contexts) may need multiple annotators. When you do use
multiple people, you’ll need to measure their agreement using metrics like Cohen’s Kappa,
which accounts for agreement beyond chance. However, use your judgment. Even in larger
companies, a single expert is often enough.
5
Start with a benevolent dictator whenever feasible. Only add complexity when your domain
demands it.
Q: What gaps in eval tooling should I be prepared to fill myself?
Most eval tools handle the basics well: logging complete traces, tracking metrics, prompt
playgrounds, and annotation queues. These are table stakes. Here are four areas where you’ll
likely need to supplement existing tools.
Watch for vendors addressing these gaps—it’s a strong signal they understand practitioner
needs.
1. Error Analysis and Pattern Discovery
After reviewing traces where your AI fails, can your tooling automatically cluster similar issues?
For instance, if multiple traces show the assistant using casual language for luxury clients, you
need something that recognizes this broader “persona-tone mismatch” pattern. We recommend
building capabilities that use AI to suggest groupings, rewrite your observations into clearer
failure taxonomies, help find similar cases through semantic search, etc.
2. AI-Powered Assistance Throughout the Workflow
The most effective workflows use AI to accelerate every stage of evaluation. During error
analysis, you want an LLM helping categorize your open-ended observations into coherent
failure modes. For example, you might annotate several traces with notes like “wrong tone for
investor,” “too casual for luxury buyer,” etc. Your tooling should recognize these as the same
underlying pattern and suggest a unified “persona-tone mismatch” category.
You’ll also want AI assistance in proposing fixes. After identifying 20 cases where your assistant omits pet policies from property summaries, can your workflow analyze these failures
and suggest specific prompt modifications? Can it draft refinements to your SQL generation
instructions when it notices patterns of missing WHERE clauses?
Additionally, good workflows help you conduct data analysis of your annotations and traces.
I like using notebooks with AI in-the-loop like Julius,Hex or SolveIt. These help me discover
insights like “location ambiguity errors spike 3x when users mention neighborhood names” or
“tone mismatches occur 80% more often in email generation than other modalities.”
6
3. Custom Evaluators Over Generic Metrics
Be prepared to build most of your evaluators from scratch. Generic metrics like “hallucination
score” or “helpfulness rating” rarely capture what actually matters for your application—
like proposing unavailable showing times or omitting budget constraints from emails. In our
experience, successful teams spend most of their effort on application-specific metrics.
4. APIs That Support Custom Annotation Apps
Custom annotation interfaces work best for most teams. This requires observability platforms
with thoughtful APIs. I often have to build my own libraries and abstractions just to make
bulk data export manageable. You shouldn’t have to paginate through thousands of requests
or handle timeout-prone endpoints just to get your data. Look for platforms that provide true
bulk export capabilities and, crucially, APIs that let you write annotations back efficiently.
Q: What is the best approach for generating synthetic data?
A common mistake is prompting an LLM to "give me test queries" without structure,
resulting in generic, repetitive outputs. A structured approach using dimensions produces far
better synthetic data for testing LLM applications.
Start by defining dimensions: categories that describe different aspects of user queries.
Each dimension captures one type of variation in user behavior. For example:
• For a recipe app, dimensions might include Dietary Restriction (vegan, gluten-free, none),
Cuisine Type (Italian, Asian, comfort food), and Query Complexity (simple request,
multi-step, edge case).
• For a customer support bot, dimensions could be Issue Type (billing, technical, general),
Customer Mood (frustrated, neutral, happy), and Prior Context (new issue, follow-up,
resolved).
Choose dimensions that target likely failure modes. If you suspect your recipe app
struggles with scaling ingredients for large groups or your support bot mishandles angry customers, make those dimensions. Use your application first—you need hypotheses about where
failures occur. Without this, you’ll generate useless test data.
Once you have dimensions, create tuples: specific combinations selecting one value from
each dimension. A tuple like (Vegan, Italian, Multi-step) represents a particular use case.
Write 20 tuples manually to understand your problem space, then use an LLM to scale up.
The two-step generation process is important. First, have the LLM generate structured tuples.
Then, in a separate prompt, convert each tuple to a natural language query. This separation
prevents repetitive phrasing. For the vegan Italian tuple above, you might get "I need a
dairy-free lasagna recipe that I can prep the day before."
7
Don’t generate synthetic data for problems you can fix immediately. If your prompt
never mentions handling dietary restrictions, fix the prompt rather than generating hundreds
of specialized queries. Save synthetic data for complex issues requiring iteration—like an LLM
consistently failing at ingredient scaling math or misinterpreting ambiguous requests.
After iterating on your tuples and prompts, run these synthetic queries through your
actual system to capture full traces. Sample 100 traces for error analysis. This number
provides enough traces to manually review and identify failure patterns without being overwhelming. Rather than generating thousands of similar queries, ensure your 100 traces cover
diverse combinations across your dimensions—this variety will reveal more failure modes than
sheer volume.
Q: How do I approach evaluation when my system handles diverse user queries?
Complex applications often support vastly different query patterns—from “What’s
the return policy?” to “Compare pricing trends across regions for products matching these criteria.” Each query type exercises different system capabilities, leading
to confusion on how to design eval criteria.
Error Analysis is all you need. Your evaluation strategy should emerge from observed
failure patterns (e.g. error analysis), not predetermined query classifications. Rather than
creating a massive evaluation matrix covering every query type you can imagine, let your
system’s actual behavior guide where you invest evaluation effort.
During error analysis, you’ll likely discover that certain query categories share failure patterns.
For instance, all queries requiring temporal reasoning might struggle regardless of whether
they’re simple lookups or complex aggregations. Similarly, queries that need to combine
information from multiple sources might fail in consistent ways. These patterns discovered
through error analysis should drive your evaluation priorities. It could be that query category
is a fine way to group failures, but you don’t know that until you’ve analyzed your data.
To see an example of basic error analysis in action, see this video.
ff We are teaching our last and final cohort of our AI Evals course next month (we have to get
back to building). Here is a 35% discount code for readers of this post. ff
Q: How do I choose the right chunk size for my document processing tasks?
Unlike RAG, where chunks are optimized for retrieval, document processing assumes the model
will see every chunk. The goal is to split text so the model can reason effectively without being
overwhelmed. Even if a document fits within the context window, it might be better to break
it up. Long inputs can degrade performance due to attention bottlenecks, especially in the
middle of the context. Two task types require different strategies:
8
1. Fixed-Output Tasks → Large Chunks
These are tasks where the output length doesn’t grow with input: extracting a number, answering a specific question, classifying a section. For example:
• “What’s the penalty clause in this contract?”
• “What was the CEO’s salary in 2023?”
Use the largest chunk (with caveats) that likely contains the answer. This reduces the number
of queries and avoids context fragmentation. However, avoid adding irrelevant text. Models are
sensitive to distraction, especially with large inputs. The middle parts of a long input might
be under-attended. Furthermore, if cost and latency are a bottleneck, you should consider
preprocessing or filtering the document (via keyword search or a lightweight retriever) to
isolate relevant sections before feeding a huge chunk.
2. Expansive-Output Tasks → Smaller Chunks
These include summarization, exhaustive extraction, or any task where output grows with
input. For example:
• “Summarize each section”
• “List all customer complaints”
In these cases, smaller chunks help preserve reasoning quality and output completeness. The
standard approach is to process each chunk independently, then aggregate results (e.g., mapreduce). When sizing your chunks, try to respect content boundaries like paragraphs, sections,
or chapters. Chunking also helps mitigate output limits. By breaking the task into pieces,
each piece’s output can stay within limits.
General Guidance
It’s important to recognize why chunk size affects results. A larger chunk means the model
has to reason over more information in one go – essentially, a heavier cognitive load. LLMs
have limited capacity to retain and correlate details across a long text. If too much
is packed in, the model might prioritize certain parts (commonly the beginning or end) and
overlook or “forget” details in the middle. This can lead to overly coarse summaries or missed
facts. In contrast, a smaller chunk bounds the problem: the model can pay full attention to
that section. You are trading off global context for local focus.
No rule of thumb can perfectly determine the best chunk size for your use case – you should
validate with experiments. The optimal chunk size can vary by domain and model. I treat
chunk size as a hyper parameter to tune.
9
Q: How should I approach evaluating my RAG system?
RAG systems have two distinct components that require different evaluation approaches: retrieval and generation.
The retrieval component is a search problem. Evaluate it using traditional information retrieval
(IR) metrics. Common examples include Recall@k (of all relevant documents, how many
did you retrieve in the top k?), Precision@k (of the k documents retrieved, how many were
relevant?), or MRR (how high up was the first relevant document?). The specific metrics you
choose depend on your use case. These metrics are pure search metrics that measure whether
you’re finding the right documents (more on this below).
To evaluate retrieval, create a dataset of queries paired with their relevant documents. Generate this synthetically by taking documents from your corpus, extracting key facts, then
generating questions those facts would answer. This reverse process gives you query-document
pairs for measuring retrieval performance without manual annotation.
For the generation component—how well the LLM uses retrieved context, whether it hallucinates, whether it answers the question—use the same evaluation procedures covered throughout this course: error analysis to identify failure modes, collecting human labels, building
LLM-as-judge evaluators, and validating those judges against human annotations.
Jason Liu’s “There Are Only 6 RAG Evals” provides a framework that maps well to this
separation. His Tier 1 covers traditional IR metrics for retrieval. Tiers 2 and 3 evaluate
relationships between Question, Context, and Answer—like whether the context is relevant
(C|Q), whether the answer is faithful to context (A|C), and whether the answer addresses the
question (A|Q).
In addition to Jason’s six evals, error analysis on your specific data may reveal domain-specific
failure modes that warrant their own metrics. For example, a medical RAG system might
consistently fail to distinguish between drug dosages for adults versus children, or a legal
RAG might confuse jurisdictional boundaries. These patterns emerge only through systematic
review of actual failures. Once identified, you can create targeted evaluators for these specific
issues beyond the general framework.
Finally, when implementing Jason’s Tier 2 and 3 metrics, don’t just use prompts off the shelf.
The standard LLM-as-judge process requires several steps: error analysis, prompt iteration,
creating labeled examples, and measuring your judge’s accuracy against human labels. Once
you know your judge’s True Positive and True Negative rates, you can correct its estimates
to determine the actual failure rate in your system. Skip this validation and your judges may
not reflect your actual quality criteria.
In summary, debug retrieval first using IR metrics, then tackle generation quality using properly validated LLM judges.
10
Q: What makes a good custom interface for reviewing LLM outputs?
Great interfaces make human review fast, clear, and motivating. We recommend building
your own annotation tool customized to your domain. The following features are possible
enhancements we’ve seen work well, but you don’t need all of them. The screenshots shown
are illustrative examples to clarify concepts. In practice, I rarely implement all these features
in a single app. It’s ultimately a judgment call based on your specific needs and constraints.
1. Render Traces Intelligently, Not Generically: Present the trace in a way that’s
intuitive for the domain. If you’re evaluating generated emails, render them to look like
emails. If the output is code, use syntax highlighting. Allow the reviewer to see the full
trace (user input, tool calls, and LLM reasoning), but keep less important details in collapsed
sections that can be expanded. Here is an example of a custom annotation tool for reviewing
real estate assistant emails:
Figure 1: A custom interface for reviewing emails for a real estate assistant.
2. Show Progress and Support Keyboard Navigation: Keep reviewers in a state of flow
by minimizing friction and motivating completion. Include progress indicators (e.g., “Trace
45 of 100”) to keep the review session bounded and encourage completion. Enable hotkeys for
11
navigating between traces (e.g., N for next), applying labels, and saving notes quickly. Below
is an illustration of these features:
Figure 2: An annotation interface with a progress bar and hotkey guide
4. Trace navigation through clustering, filtering, and search: Allow reviewers to
filter traces by metadata or search by keywords. Semantic search helps find conceptually
similar problems. Clustering similar traces (like grouping by user persona) lets reviewers spot
recurring issues and explore hypotheses. Below is an illustration of these features:
12
Figure 3: Cluster view showing groups of emails, such as property-focused or client-focused
examples. Reviewers can drill into a group to see individual traces.
5. Prioritize labeling traces you think might be problematic: Surface traces flagged by
guardrails, CI failures, or automated evaluators for review. Provide buttons to take actions like
adding to datasets, filing bugs, or re-running pipeline tests. Display relevant context (pipeline
version, eval scores, reviewer info) directly in the interface to minimize context switching.
Below is an illustration of these ideas:
13
Figure 4: A trace view that allows you to quickly see auto-evaluator verdict, add traces to
dataset or open issues. Also shows metadata like pipeline version, reviewer info, and
more.
General Principle: Keep it minimal
Keep your annotation interface minimal. Only incorporate these ideas if they provide a benefit
that outweighs the additional complexity and maintenance overhead.
Q: How much of my development budget should I allocate to evals?
It’s important to recognize that evaluation is part of the development process rather than a
distinct line item, similar to how debugging is part of software development.
You should always be doing error analysis. When you discover issues through error analysis,
many will be straightforward bugs you’ll fix immediately. These fixes don’t require separate
evaluation infrastructure as they’re just part of development.
The decision to build automated evaluators comes down to cost-benefit analysis. If you can
catch an error with a simple assertion or regex check, the cost is minimal and probably worth
it. But if you need to align an LLM-as-judge evaluator, consider whether the failure mode
warrants that investment.
In the projects we’ve worked on, we’ve spent 60-80% of our development time on error
analysis and evaluation. Expect most of your effort to go toward understanding failures
(i.e. looking at data) rather than building automated checks.
14
Be wary of optimizing for high eval pass rates. If you’re passing 100% of your evals, you’re
likely not challenging your system enough. A 70% pass rate might indicate a more meaningful
evaluation that’s actually stress-testing your application. Focus on evals that help you catch
real issues, not ones that make your metrics look good.
Q: Why is “error analysis” so important in LLM evals, and how is it performed?
Error analysis is the most important activity in evals. Error analysis helps you decide
what evals to write in the first place. It allows you to identify failure modes unique to your
application and data. The process involves:
1. Creating a Dataset: Gathering representative traces of user interactions with the LLM.
If you do not have any data, you can generate synthetic data to get started.
2. Open Coding: Human annotator(s) (ideally a benevolent dictator) review and write
open-ended notes about traces, noting any issues. This process is akin to “journaling” and
is adapted from qualitative research methodologies. When beginning, it is recommended
to focus on noting the first failure observed in a trace, as upstream errors can cause
downstream issues, though you can also tag all independent failures if feasible. A domain
expert should be performing this step.
3. Axial Coding: Categorize the open-ended notes into a “failure taxonomy.”. In other
words, group similar failures into distinct categories. This is the most important step.
At the end, count the number of failures in each category. You can use a LLM to help
with this step.
4. Iterative Refinement: Keep iterating on more traces until you reach theoretical saturation, meaning new traces do not seem to reveal new failure modes or information to
you. As a rule of thumb, you should aim to review at least 100 traces.
You should frequently revisit this process. There are advanced ways to sample data more
efficiently, like clustering, sorting by user feedback, and sorting by high probability failure
patterns. Over time, you’ll develop a “nose” for where to look for failures in your data.
Do not skip error analysis. It ensures that the evaluation metrics you develop are supported by
real application behaviors instead of counter-productive generic metrics (which most platforms
nudge you to use). For examples of how error analysis can be helpful, see this video, or this
blog post.
Q: What’s the difference between guardrails & evaluators?
Guardrails are inline safety checks that sit directly in the request/response path. They
validate inputs or outputs before anything reaches a user, so they typically are:
15
• Fast and deterministic – typically a few milliseconds of latency budget.
• Simple and explainable – regexes, keyword block-lists, schema or type validators,
lightweight classifiers.
• Targeted at clear-cut, high-impact failures – PII leaks, profanity, disallowed instructions, SQL injection, malformed JSON, invalid code syntax, etc.
If a guardrail triggers, the system can redact, refuse, or regenerate the response. Because these
checks are user-visible when they fire, false positives are treated as production bugs; teams
version guardrail rules, log every trigger, and monitor rates to keep them conservative.
On the other hand, evaluators typically run after a response is produced. Evaluators measure
qualities that simple rules cannot, such as factual correctness, completeness, etc. Their verdicts
feed dashboards, regression tests, and model-improvement loops, but they do not block the
original answer.
Evaluators are usually run asynchronously or in batch to afford heavier computation such as
a LLM-as-a-Judge. Inline use of an LLM-as-Judge is possible only when the latency budget
and reliability targets allow it. Slow LLM judges might be feasible in a cascade that runs on
the minority of borderline cases.
Apply guardrails for immediate protection against objective failures requiring intervention.
Use evaluators for monitoring and improving subjective or nuanced criteria. Together, they
create layered protection.
Word of caution: Do not use llm guardrails off the shelf blindly. Always look at the prompt.
Q: What’s a minimum viable evaluation setup?
Start with error analysis, not infrastructure. Spend 30 minutes manually reviewing 20-50 LLM
outputs whenever you make significant changes. Use one domain expert who understands your
users as your quality decision maker (a “benevolent dictator”).
If possible, use notebooks to help you review traces and analyze data. In our opinion, this
is the single most effective tool for evals because you can write arbitrary code, visualize data,
and iterate quickly. You can even build your own custom annotation interface right inside
notebooks, as shown in this video.
Q: How do I evaluate agentic workflows?
We recommend evaluating agentic workflows in two phases:
1. End-to-end task success. Treat the agent as a black box and ask “did we meet the
user’s goal?”. Define a precise success rule per task (exact answer, correct side-effect, etc.)
16
and measure with human or aligned LLM judges. Take note of the first upstream failure when
conducting error analysis.
Once error analysis reveals which workflows fail most often, move to step-level diagnostics to
understand why they’re failing.
2. Step-level diagnostics. Assuming that you have sufficiently instrumented your system
with details of tool calls and responses, you can score individual components such as: - Tool
choice: was the selected tool appropriate? - Parameter extraction: were inputs complete and
well-formed? - Error handling: did the agent recover from empty results or API failures? -
Context retention: did it preserve earlier constraints? - Efficiency: how many steps, seconds,
and tokens were spent? - Goal checkpoints: for long workflows verify key milestones.
Example: “Find Berkeley homes under $1M and schedule viewings” breaks into: parameters
extracted correctly, relevant listings retrieved, availability checked, and calendar invites sent.
Each checkpoint can pass or fail independently, making debugging tractable.
Use transition failure matrices to understand error patterns. Create a matrix where
rows represent the last successful state and columns represent where the first failure occurred.
This is a great way to understand where the most failures occur.
Figure 5: Transition failure matrix showing hotspots in text-to-SQL agent workflow
Transition matrices transform overwhelming agent complexity into actionable insights. Instead
of drowning in individual trace reviews, you can immediately see that GenSQL → ExecSQL
transitions cause 12 failures while DecideTool → PlanCal causes only 2. This data-driven
17
approach guides where to invest debugging effort. Here is another example from Bryan Bischof,
that is also a text-to-SQL agent:
Figure 6: Bischof, Bryan “Failure is A Funnel - Data Council, 2025”
In this example, Bryan shows variation in transition matrices across experiments. How you
organize your transition matrix depends on the specifics of your application. For example,
Bryan’s text-to-SQL agent has an inherent sequential workflow which he exploits for further
analytical insight. You can watch his full talk for more details.
Creating Test Cases for Agent Failures
Creating test cases for agent failures follows the same principles as our previous FAQ on
debugging multi-turn conversation traces (i.e. try to reproduce the error in the simplest way
possible, only use multi-turn tests when the failure actually requires conversation context,
etc.).
Q: Seriously Hamel. Stop the bullshit. What’s your favorite eval vendor?
Eval tools are in an intensely competitive space. It would be futile to compare their features.
If I tried to do such an analysis, it would be invalidated in a week! Vendors I encounter the
most organically in my work are: Langsmith, Arize and Braintrust.
When I help clients with vendor selection, the decision weighs heavily towards who can offer
the best support, as opposed to purely features. This changes depending on size of client, use
case, etc. Yes - its mainly the human factor that matters, and dare I say, vibes.
I have no favorite vendor. At the core, their features are very similar - and I often build custom
tools on top of them to fit my needs.
My suggestion is to explore the vendors and see which one you like the most.
18
Q: How are evaluations used differently in CI/CD vs. monitoring production?
The most important difference between CI vs. production evaluation is the data used for
testing.
Test datasets for CI are small (in many cases 100+ examples) and purpose-built. Examples
cover core features, regression tests for past bugs, and known edge cases. Since CI tests are run
frequently, the cost of each test has to be carefully considered (that’s why you carefully curate
the dataset). Favor assertions or other deterministic checks over LLM-as-judge evaluators.
For evaluating production traffic, you can sample live traces and run evaluators against them
asynchronously. Since you usually lack reference outputs on production data, you might rely
more on on more expensive reference-free evaluators like LLM-as-judge. Additionally, track
confidence intervals for production metrics. If the lower bound crosses your threshold, investigate further.
These two systems are complementary: when production monitoring reveals new failure patterns through error analysis and evals, add representative examples to your CI dataset. This
mitigates regressions on new issues.
ff We are teaching our last and final cohort of our AI Evals course next month (we have to get
back to building). Here is a 35% discount code for readers of this post. ff
19

What is Retrieval Augmented Generation?¶
Retrieval augmented generation (RAG) is a technique that enhances the capabilities of large language models (LLMs) by integrating them with external knowledge sources. In essence, RAG combines the generative power of LLMs with the vast information stored in databases, documents, and other repositories. This approach enables LLMs to generate more accurate, relevant, and contextually grounded responses.

Why Use RAG?¶
LLMs, while impressive in their language processing abilities, often suffer from limitations such as:

Hallucinations: Generating incorrect or nonsensical information.
Lack of Factual Grounding: Providing responses not anchored in real-world knowledge.
Inability to Access Updated Information: LLMs are trained on static datasets and cannot access information updated after their training period.
RAG addresses these limitations by providing LLMs with a mechanism to access and incorporate external information. Instead of solely relying on the knowledge encoded during training, RAG allows LLMs to:

Retrieve relevant information from external sources based on a user's query.
Use the retrieved information as context for generating a response.
Key Components of a RAG System¶
A typical RAG system consists of several components working together:

Knowledge Base: This is the external source of information that the RAG system will draw upon. It can be a database, a collection of documents, a knowledge graph, or any other structured or unstructured data repository.
Retrieval Model: This component is responsible for selecting the most relevant information from the knowledge base, given a user's query. Common retrieval models include keyword-based search (like BM25), semantic search using embeddings, or a combination of both.
Language Model (LLM): The LLM is the heart of the RAG system, tasked with generating the final response. It takes the user's query and the information retrieved from the knowledge base as input.
Re-ranker (Optional): A re-ranker is often used to refine the initial retrieval results, ensuring that the most relevant information is passed to the LLM.
Query Understanding (Optional): This component goes beyond simple keyword matching. It tries to interpret the user's intent and potentially rewrites the query for more effective retrieval. For instance, it might extract dates, locations, or other entities from a query to refine the search. Learn more about query understanding in my post RAG is more than embeddings.
Benefits of Using RAG¶
The sources highlight numerous benefits of incorporating RAG into LLM-based applications:

Improved Accuracy and Relevance: By accessing and incorporating relevant external information, RAG helps LLMs generate more accurate and factual responses.
Enhanced User Experience: Users receive more informative and trustworthy answers, leading to a better overall experience.
Addressing Complex Queries: RAG allows for the handling of queries that require specific domain knowledge or access to up-to-date information.
Scalability and Flexibility: RAG systems can be easily scaled by adding more data to the knowledge base. They can also be adapted to different domains by tailoring the knowledge base and retrieval models.
Continuous Improvement: RAG systems are designed for iterative development. By monitoring user feedback, analyzing retrieval metrics (like precision and recall), and refining the system's components, you can achieve continuous improvement.
Building and Improving a RAG System¶
The sources offer a comprehensive "RAG Playbook" and guidance on systematically developing and enhancing RAG systems. You can explore this in detail in my RAG improvement guide and RAG flywheel strategy. Key insights include:

Start with Synthetic Data: Before deploying to real users, generate synthetic queries and test your retrieval model. This establishes a baseline and allows for rapid iteration. See my guide on low-hanging fruit in RAG for practical tips.
Focus on Leading Metrics: Monitor metrics like retrieval precision, recall, and the number of retrieval experiments conducted. These metrics provide early indicators of system performance. For a complete evaluation framework, see the only 6 RAG evaluations you need.
Collect Real-World Data: As you gain real user data, analyze queries and cluster them into topics and capability categories. This identifies areas for improvement. I explain this in detail in my posts on RAG decomposition and topics and capabilities.
Utilize Metadata: Extract and leverage document metadata (dates, authors, tags, etc.) for better filtering and retrieval.
Combine Search Methods: Use both full-text search (like BM25) and semantic search (embeddings) for more robust retrieval.
Implement User Feedback: Provide clear feedback mechanisms for users to rate the system's responses. Analyze this feedback to identify weaknesses.
Continuously Experiment: Test different embedding models, re-ranking methods, and query rewriting techniques to optimize your system.
Beyond Question Answering¶
While RAG has often been used for question answering, the sources predict a shift towards report generation. This evolution stems from the observation that reports provide more value than simple answers. I explore this future direction in RAG++: The future of RAG:

Decision Making: Reports help users analyze information and make better decisions.
Resource Allocation: Reports guide businesses in allocating resources more effectively.
Standardized Processes: Reports can be structured using standard operating procedures (SOPs) to ensure consistency and scalability.
The sources suggest that RAG-powered report generation will open up new possibilities, including a marketplace of report templates tailored to specific needs.

Challenges in Building RAG Systems¶
While RAG offers significant advantages, there are also challenges to consider:

Data Quality and Management: Building and maintaining a high-quality knowledge base is crucial. Issues like data inconsistency, incompleteness, and bias can negatively impact system performance.
Resource Intensity: Training and fine-tuning embedding models and LLMs can be computationally expensive and require significant resources.
Complexity: Designing effective retrieval models, query understanding components, and re-rankers can be complex, requiring expertise in natural language processing, information retrieval, and machine learning. To understand the different levels of complexity, see my guide on levels of RAG complexity.
Learn More¶
For a deeper understanding of RAG systems:

Take my comprehensive RAG course
Read the RAG course breakdown
Check out the RAG FAQ
Learn from RAG anti-patterns
Conclusion¶
RAG represents a powerful approach to combining the strengths of LLMs and external knowledge sources. By carefully designing and implementing the components of a RAG system, you can build applications that deliver more accurate, relevant, and useful information to users. The continuous improvement cycle, driven by data analysis, feedback, and experimentation, ensures that RAG systems evolve and become more sophisticated over time. As the field of RAG continues to advance, we can expect to see even more innovative applications that transform the way we interact with information.

Want to learn more?¶
I also wrote a 6 week email course on RAG, where I cover everything in my consulting work. It's free and you can:

---
Low-Hanging Fruit for RAG Search¶
RAG Course

If you're looking to deepen your understanding of RAG systems and learn how to systematically improve them, consider enrolling in the Systematically Improving RAG Applications course. This 4-week program covers everything from evaluation techniques to advanced retrieval methods, helping you build a data flywheel for continuous improvement.

RAG (Retrieval-Augmented Generation), is a powerful technique that combines information retrieval with LLMs to provide relevant and accurate responses to user queries. By searching through a large corpus of text and retrieving the most relevant chunks, RAG systems can generate answers that are grounded in factual information.

In this post, we'll explore six key areas where you can focus your efforts to improve your RAG search system. These include using synthetic data for baseline metrics, adding date filters, improving user feedback copy, tracking average cosine distance and Cohere reranking score, incorporating full-text search, and efficiently generating synthetic data for testing.

If you want to learn more about I systematically improve RAG applications check out my free 6 email improving rag crash course

Check out the free email course here

By addressing these low-hanging fruit, you can take your RAG search system to the next level, providing users with more relevant, accurate, and timely information. Let's dive in and explore each of these opportunities in more detail.

1. Synthetic Data for Baseline Metrics¶
Synthetic data can be used to establish baseline precision and recall metrics for your reverse search. The simplest kind of synthetic data is to take existing text chunks, generate synthetic questions, and verify that when we query our synthetic questions, the sourced text chunk is retrieved correctly.

Benefits:

Establishes a foundation for measuring system complexity and performance
Pinpoints areas for improvement and drives optimization efforts
Enables affordable, repeatable testing and evaluation
Provides a consistent reference point when introducing new models or features, allowing for meaningful comparisons. If the baseline remains unchanged, production data can be leveraged to enhance synthetic question generation or the system as a whole.
Costs:

This should really just be a matter of writing a simple prompt that generates questions, hopefully with a few shot examples, and iterating over existing text chunks. Once you have that, you can store pairs of query strings and chunk IDs. And a simple forloup can be used to verify that the query strings are retrieving the correct chunks.

2. Adding Date Filters¶
Incorporating date filters into your search system can significantly improve the user experience by providing more relevant and up-to-date information. A big issue that I see oftentimes is people asking questions like, what is the latest, blah, blah, blah. This fundamentally does not embed anything and you need to end up using date filters and additional prompting to extract ranges out.

Benefits:

Increased relevance and freshness of search results
Improved efficiency in narrowing down results
Enabling trend analysis and historical context
Costs

I talk about query understanding in more detail in the Instructor documentation. Adding query understanding typically adds around 500-700 milliseconds to processing time.

3. Improving Thumbs Up/Down Copy¶
Using specific copy for your thumbs up/down buttons, such as "Did we answer your question?" instead of generic phrases, offers several benefits. This is particularly relevant when we care about question answer accuracy, but want to explicitly avoid getting negative feedback for being slow or verbose or having poor formatting. You might care about different things, but it's important to be explicit. Do not use generic copy like, did you like our response?

Benefits:

Focused feedback on the relevance and quality of search results
Reduced ambiguity in user interpretation
Actionable insights for improving the search system
Costs

It might just be worth having a separate index or table that just stores question answer pairs and whether or not we're satisfied. This would be enough to drawing back onto our similarity data below and do some clustering and data analysis to figure out what the and priorities should be.

4. Tracking Average Cosine Distance and Cohere Reranking Score¶
Monitoring the average cosine distance and Cohere reranking score for each question can help identify challenging queries and prioritize improvements. Once you have a table of query and scores, you will be able to do data analysis to figure out areas where you are underperforming, at least in the relevancy.

Benefits:

Identifying strengths and weaknesses of the search system
Enabling targeted optimization for specific query types
Data-driven decision making for resource allocation and feature prioritization
Costs

Again, here we're just logging things. As long as we have a request ID, we can do something pretty simple like...


{
  "request_id": "12345",
  "query": "What is the latest news?",
  "mean_cosine_distance": 0.3,
  "mean_cohere_reranking_score": 0.4
}
5. Using Full-Text Search¶
Incorporating both full-text search and semantic search (vector search) can improve the overall performance of your search system. This one is almost obvious for anyone who's building actual search systems. Include BM25 and you will likely see better results.

Benefits:

Identifying relevant documents based on exact keyword matches
Uncovering conceptually similar documents
Improving the overall effectiveness of the search system
Cost

Here you gotta make sure your user system that uses full text search. Something like LanceDB really improves the UX.

6. Making Text Chunks Look Like Questions¶
When generating synthetic data for testing your search system, it's more efficient to make text chunks look like questions rather than the other way around. Generating Hyde introduces more latency at query time, but if you really care about results, you should be willing to incur ingestion costs to make search better at runtime. It's good for you to think that your text chunks and your queries should have similar embeddings, so it might be good to embed question-answer pairs if you know what kind of questions people are asking ahead of time.

Benefits:

Reduced latency compared to generating hypothetical document embeddings
More effective testing of the search system's performance
Avoiding the overhead of generating embeddings for every possible question variant
7. Including File and Document Metadata¶
When chunking text for your search system, it's beneficial to include file and document metadata as additional text in each chunk. This metadata can provide valuable context and improve search relevance.

Benefits:

Improved search relevance by leveraging metadata information
Ability to filter and narrow down search results based on metadata fields
Enhanced understanding of the document structure and hierarchy
Costs:

Including metadata requires modifying the text chunking process to append the relevant information to each chunk. This may involve extracting metadata from file paths, document headers, or a separate metadata database. The additional text will slightly increase the storage requirements for the chunks.

Example metadata to include:

File path
Document title
Author
Creation date
Tags or categories
By incorporating file and document metadata, you can enrich the search experience and provide users with more targeted and relevant results.

Conclusion¶
By focusing on these low-hanging fruit opportunities, you can significantly enhance the performance and usability of your RAG search system, ultimately providing a better experience for your users.

If you have any questions about these details, please leave a comment below and let's get a conversation started. My goal really is to bring the unconscious conscious and being able to answer questions will really help me clarify my own thinking.

--
FAQ on Improving RAG Applications¶
This FAQ is generated by NotebookLM and Gemini and addresses common questions from the "Systematically Improving RAG Applications" course. The course is a comprehensive, six-week program that guides you through:

RAG fundamentals
Advanced implementation strategies
Synthetic data generation techniques
Query routing optimization
Embedding fine-tuning methods
IF you want to get a preview of the maven course you can:

Sign up for the Free Email Course

Now, let's dive into some frequently asked questions from the course:

Week 1 FAQ: Building a Foundation for RAG System Improvement¶
Here are some answers to frequently asked questions about Week 1 of the course "Systematically Improving RAG Applications".

Q: What is the overall objective for Week 1?

The main goal of Week 1 is to build a solid foundation for iteratively improving Retrieval-Augmented Generation (RAG) systems. The focus is on establishing a starting point for development, even if it isn't perfect, rather than trying to achieve immediate perfection.

Q: Why is synthetic data generation so important in the beginning stages of RAG development?

Synthetic data generation is particularly crucial when starting without real user data.

It allows for the rapid evaluation of different implementations, such as BM25, embeddings, and re-rankers, enabling developers to experiment and iterate quickly.
It also provides a baseline for measuring future improvements and helps determine whether experimental results are promising enough to warrant further exploration.
Q: What are some recommended methods for generating synthetic data for RAG systems?

Leverage language models to generate potential user questions based on existing assets.
Develop hypothetical text chunks designed to answer these generated questions.
Utilize a basic retrieval method, like chunk ID retrieval, for an initial performance evaluation.
As real user data becomes available, it should be incorporated into the synthetic data generation process to enhance the realism of the generated questions.
Language models can also be used to generate weak ranking data, which can further improve the quality of the synthetic dataset.
Q: How do we set appropriate baselines for our RAG system's performance?

Use tools like LanceDB to experiment with different implementations of search and retrieval components.
Establish clear baselines to serve as a reference point for determining when experimental changes are not yielding meaningful improvements.
For queries that return no relevant results (zero-recall), carefully analyze whether it would have been possible to answer those questions at all using the available data. This helps identify limitations in the data sources themselves.
Q: What are some specific recommendations for those who already have RAG systems in production?

Allocate a portion of real user traffic for synthetic data generation, enriching the training data with actual usage patterns.
Explore the use of Large Language Models (LLMs) as re-rankers and carefully assess their impact on system performance.
Establish a regular schedule for reviewing documents and user queries that consistently result in poor recall, addressing potential issues in data quality and retrieval mechanisms.
Q: Are there any common pitfalls to avoid in the early stages of RAG system development?

Avoid oversimplifying the questions used for evaluation. Aim for a mix of difficulty levels to ensure a comprehensive assessment of the system's capabilities.
If recall metrics are consistently too high, it may indicate that the evaluation questions are not challenging enough. Incorporate more complex queries to better understand the system's limitations.
Continuously improve the synthetic dataset by incorporating real user data and refining the generation processes.
Ensure that the search implementations being tested and evaluated are the same ones that will be deployed in the production environment, avoiding discrepancies in performance assessments.
Week 2 FAQ: Segmentation and Prioritization in RAG Systems¶
Q: What are the main topics covered in Week 2 of the course?

Building upon the groundwork laid in Week 1, Week 2 shifts focus to the critical role of segmentation and prioritization in systematically improving RAG applications. The sources emphasize understanding and responding to the varied needs of your user base and identifying areas where improvements will have the greatest impact.

Q: Why is segmentation crucial for RAG systems?

Segmentation allows developers to gain actionable insights into user behavior and system performance that broad metrics might obscure. This nuanced understanding enables data-driven decisions about system improvements, feature development, and resource allocation.

Q: What are some specific examples of segmentation strategies?

The sources provide several examples of how to segment users and queries, including:

Roles in Organizations: Recognizing that different roles often have distinct information needs.
User Cohorts: Identifying patterns based on user signup time to understand the needs and expectations of new versus long-term users.
Industry or Sector: Recognizing industry-specific needs or challenges when a RAG system serves multiple industries.
Query Intent: Classifying user queries based on their underlying purpose, as illustrated by the case study in the sources.
Q: How do the concepts covered in Week 2 relate to the upcoming weeks of the course?

The principles of segmentation and prioritization will continue to be relevant throughout the course. Week 3 will introduce structured extraction and multimodality, enabling more sophisticated segmentation strategies. Weeks 4, 5, and 6 will build upon these concepts, covering modality selection, embedding and re-ranker fine-tuning, and product design considerations.

Week 3 FAQ: Structured Extraction and Specialized Search Methods¶
Q: What are the main topics covered in Week 3 of the course?

Building upon the foundations of data generation, segmentation, and prioritization covered in Weeks 1 and 2, Week 3 focuses on structured extraction and specialized search methods. This week emphasizes the ability to handle diverse data types, going deeper in improving specific segments identified in Week 2.

Q: Why is structured extraction important for RAG systems?

Many real-world applications involve data that goes beyond simple text, such as documents, images, and tables. Structured extraction allows RAG systems to process and understand these diverse data types effectively, enabling more sophisticated and accurate retrieval and generation.

Q: What are the main areas of focus for Week 3?

The sources identify three key areas for handling data in Week 3:

Document Handling: Exploring advanced techniques for extracting and structuring information from various document formats.
Image Handling: Addressing the challenges of image search and retrieval, including image captioning, object recognition, and multimodal search.
Table Handling: Developing methods for understanding and querying tabular data effectively.
Q: Can you provide specific examples of how structured extraction is applied in practice?

While the sources don't provide detailed examples, they do offer a case study for document processing:

Technical Support RAG System: A company implemented a RAG system to assist with technical support queries. They recognized that user intent was often more nuanced than their initial 5-star rating system could capture.
Refined Feedback: The team refined their feedback mechanism to include specific questions about query understanding and problem resolution, providing more actionable insights.
Structured Data Analysis: This feedback data, along with query logs, was then used to identify trends and prioritize areas for improvement. For instance, frequently misunderstood topics were flagged for the development of more comprehensive responses.
Q: What specific techniques are mentioned for improving structured extraction?

The sources mention using a "tool router" as a key technique for enhancing structured extraction. This involves:

Defining a set of specialized search tools tailored to different data types or tasks.
Developing a routing mechanism that analyzes user queries and selects the appropriate tool or tools for retrieval.
Testing and evaluating tool recall, similar to the chunk recall metrics discussed in previous weeks.
Q: What are the potential benefits of using a tool router in a RAG system?

A well-designed tool router can:

Improve the overall accuracy and relevance of search results.
Enhance user experience by providing more tailored and informative responses.
Increase system efficiency by utilizing specialized tools optimized for specific tasks.
Offer valuable insights for improving the routing logic and tool selection process.
Q: How does the concept of tool routing relate to the broader course objectives?

Tool routing aligns with the course's emphasis on building a data-driven flywheel for iterative improvement. By monitoring tool recall and analyzing user interactions, developers can continuously refine the routing logic, add new specialized tools, and optimize the RAG system's ability to handle diverse data sources effectively.

Week 4: Query Routing and Tool Recall¶
Q: What are the main topics covered in Week 4 of the course?

Week 4 of the course centers on query routing and specialized search methods. The content builds upon the insights from Week 2's segmentation analysis and the specialized search methods from Week 3, combining these elements for a more robust and efficient RAG system.

Q: What is query routing, and why is it important?

Query routing is the process of directing user queries to the most appropriate search index or tool. This is vital because different queries might require access to different data sources or specialized processing techniques. Effective query routing can:

Enhance accuracy and relevance: By selecting the best tool for each query, you can improve the quality of the retrieved information.
Improve user experience: Tailored responses based on the specific needs of each query lead to a more satisfying user experience.
Optimize efficiency: Utilizing specialized tools designed for particular tasks can improve the overall performance of your RAG system.
Q: How do you design a query router for a RAG system?

The sources provide guidance on designing a query router, emphasizing the use of language models (LMs) for this task. The recommended approach involves:

Prompt Engineering: Craft a prompt that guides the LM in selecting the right tool.

Include clear descriptions of each available search tool.

Provide illustrative examples of when to use each tool.
Instruct the LM to choose the most appropriate tool(s) based on the given query.

Execution: Once the router (the LM) has chosen the appropriate tool(s), execute the search.

Call the selected tool(s) with the necessary arguments.
Collect the retrieved results.
Combine or present the results in a user-friendly format.
Q: How does tool recall relate to overall system performance?

Tool recall is a crucial component of overall RAG system effectiveness. If the router consistently selects the wrong tools, the system will struggle to provide accurate and relevant information, regardless of the quality of individual search tools. By optimizing tool recall, you ensure that the right information is retrieved from the right source, maximizing the chances of generating a high-quality response.

Q: Are there any additional insights or best practices related to query routing in RAG systems?

Data Collection: As highlighted in Week 1, start collecting data early, both for training the router and evaluating its performance.

Iterative Refinement: The sources emphasize the importance of continuous improvement. Regularly analyze tool recall metrics and user feedback to identify areas for refinement in your query routing strategy.

Experimentation: The specific tools and techniques for query routing will vary depending on your unique application and use case. Don't hesitate to experiment with different approaches, such as rule-based routing, machine learning classifiers, or more advanced LM-based methods, to find what works best for your system.

Week 5: Fine-tuning and its Importance¶
Q: What is the core argument presented in the sources regarding fine-tuning?

The sources strongly advocate for fine-tuning as a critical step in building high-performing RAG systems. They argue that fine-tuning embedding models and re-rankers, while potentially resource-intensive, is essential for achieving significant improvements in retrieval accuracy and user satisfaction.

Q: Why is fine-tuning considered so crucial for RAG system performance?

Fine-tuning allows you to tailor pre-trained models to the specific nuances of your data and application. This customization results in more accurate embeddings, leading to better retrieval and ranking of relevant information.

Q: What are the specific benefits of fine-tuning embedding models and re-rankers?

Fine-tuning offers several advantages:

Improved Relevance: Fine-tuned models learn to capture the specific relationships and semantic similarities within your data, leading to more relevant search results.
Enhanced Accuracy: By adapting to your data distribution, fine-tuned models can significantly improve the precision of retrieval, reducing irrelevant results.
Tailored Performance: Fine-tuning allows you to optimize the models for your specific use case, whether prioritizing top-1 accuracy, handling long documents, or addressing unique domain-specific challenges.
Q: What are the key takeaways regarding fine-tuning embedding models?

Data is Paramount: The quality and quantity of your training data directly impact the effectiveness of fine-tuning. The sources stress the importance of collecting data early and thoughtfully, focusing on positive and negative examples that reflect the desired behavior of the system.
Understanding Similarity: Critically analyzing what "similarity" and "relevance" mean within your specific context is crucial. This informs the selection of appropriate fine-tuning techniques and evaluation metrics.
Consider Self-Hosting: For large datasets or high query volumes, self-hosting embedding models might offer benefits in terms of control and cost efficiency.
Q: Are there any specific challenges or considerations associated with fine-tuning?

The sources acknowledge that fine-tuning can be a demanding process. Here are some points to consider:

Resource Intensive: Fine-tuning often requires significant computational resources and technical expertise.
Data Management: Gathering, cleaning, and managing high-quality training data is crucial for successful fine-tuning.
Evaluation and Iteration: Regular evaluation and iterative refinement of the fine-tuned models are necessary to achieve optimal performance.
Q: How does Week 5's content on fine-tuning prepare you for the final week of the course?

By understanding the importance of fine-tuning and the techniques involved, you are better equipped to appreciate the overall system design considerations discussed in Week 6. A well-designed RAG system should incorporate fine-tuning as part of a holistic approach to improving retrieval accuracy, user experience, and system performance.

Week 6: Product Design and Beyond¶
Q: What is the overarching theme of Week 6, and how does it connect to the technical aspects covered in previous weeks?

Week 6 marks a shift from the predominantly technical focus of the preceding weeks to a more user-centric perspective. While the earlier weeks concentrated on optimizing the core components of a RAG system—embedding models, re-rankers, query routing, and specialized search—Week 6 emphasizes the crucial role of product design in maximizing user satisfaction and overall system effectiveness.

The sources argue that even a technically sophisticated RAG system can fall short if it lacks a well-designed user interface and a clear understanding of user needs and expectations. This week bridges the gap between technical excellence and user-centered design, emphasizing the importance of creating RAG applications that are not only powerful but also intuitive and enjoyable to use.

Q: How does Week 6's focus on product design contribute to the overall goal of the course?

By integrating product design principles into the development process, you can ensure that the technical enhancements covered in previous weeks translate into a genuinely valuable and user-friendly application. Week 6 completes the cycle, emphasizing that a successful RAG system is not solely defined by its technical capabilities but also by its ability to meet user needs and provide a positive and engaging experience.

Q: Are there any concluding thoughts or recommendations offered in the sources?

The sources stress the importance of continuous improvement and ongoing data collection. The insights gained through user feedback and system monitoring should continually inform product design decisions and drive further refinements to the RAG system. The key takeaway is that building a successful RAG application is an iterative process that requires both technical expertise and a deep understanding of user needs and behaviors.

Thanks for reading!

If you've found this information valuable and want to apply these concepts to your own projects, we offer two learning pathways to help you enhance your RAG applications:

A free email sequence delivering bite-sized RAG insights directly to your inbox
An intensive 6-week cohort-based course for in-depth, hands-on learning
Choose the option that best suits your learning style and professional goals:

Systematically Improving Your RAG¶
This article explains how to make Retrieval-Augmented Generation (RAG) systems better. It's based on a talk I had with Hamel and builds on other articles I've written about RAG. For a comprehensive understanding of RAG fundamentals, see my guide on what RAG is.

In RAG is More Than Just Embeddings, I talk about how RAG is more than just vector embeddings. This helps you understand RAG better. I also wrote How to Build a Terrible RAG System, where I show what not to do, which can help you learn good practices.

If you want to learn about how complex RAG systems can be, check out Levels of RAG Complexity. This article breaks down RAG into smaller parts, making it easier to understand. For quick tips on making your RAG system better, read Low Hanging Fruit in RAG.

I also wrote about what I think will happen with RAG in the future in Predictions for the Future of RAG. This article talks about how RAG might be used to create reports in the future.

All these articles work together to give you a full guide on how to make RAG systems better. They offer useful tips for developers and companies who want to improve their systems. For additional improvement strategies, check out my six tips for improving RAG and insights on RAG anti-patterns. If you're interested in AI engineering in general, you might enjoy my talk at the AI Engineer Summit. In this talk, I explain how tools like Pydantic can help with prompt engineering, which is useful for building RAG systems.

Through all these articles, I try to give you a complete view of RAG systems. I cover everything from basic ideas to advanced uses and future predictions. This should help you understand and do well in this fast-changing field.

By the end of this post, you'll understand my step-by-step approach to making RAG applications better for the companies I work with. We'll look at important areas like:

Making fake questions and answers to quickly test how well your system works
Using both full-text search and vector search together for the best results
Setting up the right ways to get feedback from users about what you want to study
Using grouping to find sets of questions that have problems, sorted by topics and abilities
Building specific systems to improve abilities
Constantly checking and testing as you get more real-world data
Through this step-by-step runbook, you'll gain practical knowledge on how to incrementally enhance the performance and utility of your RAG applications, unlocking their full potential to deliver exceptional user experiences and drive business value. Let's dive in and explore how to systematically improve your RAG systems together!

Start with Synthetic Data¶
I think the biggest mistake around improving the system is that most people are spending too much time on the actual synthesis without actually understanding whether or not the data is being retrieved correctly. To avoid this:

Create synthetic questions for each text chunk in your database
Use these questions to test your retrieval system
Calculate precision and recall scores to establish a baseline
Identify areas for improvement based on the baseline scores
What we should be finding with synthetic data is that synthetic data should just be around 97% recall precision. And synthetic data might just look like something very simple to begin with.

We might just say, for every text chunk, I want it to synthetically generate a set of questions that this text chunk answers. For those questions, can we retrieve those text chunks? And you might think the answer is always going to be yes. But I found in practice that when I was doing tests against essays, full text search and embeddings basically performed the same, except full text search was about 10 times faster. This approach is part of my broader RAG flywheel strategy.

Whereas when I did the same experiment on pulling issues from a repository, it was the case that full text search got around 55% recall, and then embedding search got around 65% recall. And just knowing how challenging these questions are on the baseline is super important to figure out what kind of experimentation you need to perform better. This will give you a baseline to work with and help you identify areas for improvement. For a detailed breakdown of evaluation metrics, see my guide on the only 6 RAG evaluations you need.

Utilize Metadata¶
Ensuring relevant metadata (e.g., date ranges, file names, ownership) is extracted and searchable is crucial for improving search results.

Extract relevant metadata from your documents
Include metadata in your search indexes
Use query understanding to extract metadata from user queries
Expand search queries with relevant metadata to improve results
For example, if someone asks, "What is the latest x, y, and z?" Text search will never get that answer. Semantic search will never get that answer.

You need to perform query understanding to extract date ranges. There will be some prompt engineering that needs to happen. For enterprise implementations, see my guide on RAG enterprise process. That's the metadata, and being aware that there will be questions that people aren't answering because those filters can never be caught by full text search and semantic search.

And what this looks like in practice is if you ask the question, what are recent developments in the field, the search query is now expanded out to more terms. There's a date range where the language model has reasoned about what recent looks like for the research, and it's also decided that you should only be searching specific sources. If you don't do this, then you may not get trusted sources. You may be unable to figure out what recent means.

You'll need to do some query understanding to extract date ranges and include metadata in your search.

Use Both Full-Text Search and Vector Search¶
Utilize both full-text search and vector search (embeddings) for retrieving relevant documents. Ideally, you should use a single database system to avoid synchronization issues.

Implement both full-text search and vector search
Test the performance of each method on your specific use case
Consider using a single database system to store both types of data
Evaluate the trade-offs between speed and recall for your application
In my experience, full-text search can be faster, but vector search can provide better recall.

What ended up being very complicated was if you have a single knowledge base, maybe that complexity is fine, because you have more configuration of each one.

But one of my clients who was doing construction data, they had to create separate indices per project, and now they just had this exploding array of different data sources that get in or out of sync. Like, maybe the database has an outage, and now the data is not in the database, but it's in another system. So if the embedding gets pulled up, then text is missing.

And this complex configuration becomes a huge pain. And so, for example, some tools are able to do all 3 in a single object. And so even if you had a lot of partitioned data sources, you can do full text search, embedding search, and write SQL against a single data object. And that has been really helpful, especially when you think about these examples where you want to find the latest. Now you can just do a full text search query and then order by date and have a between clause.

Test both and see what works best for your use case.

Implement Clear User Feedback Mechanisms¶
Implementing clear user feedback systems (e.g., thumbs up/down) is essential for gathering data on your system's performance and identifying areas for improvement.

Add user feedback mechanisms to your application
Make sure the copy for these mechanisms clearly describes what you're measuring
Ask specific questions like "Did we answer the question correctly?" instead of general ones like "How did we do?"
Use the feedback data to identify areas for improvement and prioritize fixes
I find that it's important to build out these feedback mechanisms as soon as possible. And making sure that the copy of these feedback mechanisms explicitly describe what you're worried about.

Sometimes, we'll get a thumbs down even if the answer is correct, but they didn't like the tone. Or the answer was correct, but the latency was too high. Or it took too many hops.

This means we couldn't actually produce an evaluation dataset just by figuring out what was a thumbs up and a thumbs down. It was a lot of confounding variables. We had to change the copy to just "Did we answer the question correctly? Yes or no." We need to recognize that improvements in tone and improvements in latency will come eventually. But we needed the user feedback to build us that evaluation dataset.

Make sure the copy for these feedback mechanisms explicitly describes what you're worried about. This will help you isolate the specific issues users are facing.

Cluster and Model Topics¶
Analyze user queries and feedback to identify topic clusters, capabilities, and areas of user dissatisfaction. This will help you prioritize improvements.

Why should we do this? Let me give you an example. I once worked with a company that provided a technical documentation search system. By clustering user queries, we identified two main issues:

Topic Clusters: A significant portion of user queries were related to a specific product feature that had recently been updated. However, our system was not retrieving the most up-to-date documentation for this feature, leading to confusion and frustration among users.

Capability Gaps: Another cluster of queries revealed that users were frequently asking for troubleshooting steps and error code explanations. While our system could retrieve relevant documentation, it struggled to provide direct, actionable answers to these types of questions.

Based on these insights, we prioritized updating the product feature documentation and implementing a feature to extract step-by-step instructions and error code explanations. These targeted improvements led to higher user satisfaction and reduced support requests.

Look for patterns like:

Topic clusters: Are users asking about specific topics more than others? This could indicate a need for more content in those areas or better retrieval of existing content. I explore this concept further in my post on RAG decomposition and topics and capabilities.

Capabilities: Are there types of questions your system categorically cannot answer? This could indicate a need for new features or capabilities, such as direct answer extraction, multi-document summarization, or domain-specific reasoning.

By continuously analyzing topic clusters and capability gaps, you can identify high-impact areas for improvement and allocate your resources more effectively. This data-driven approach to prioritization ensures that you're always working on the most critical issues affecting your users.

Once you have this in place, once you have these topics and these clusters, you can talk to domain experts for a couple of weeks to figure out what these categories are explicitly. Then, you can build out systems to tag that as data comes in.

In the same way that when you open up ChatGPT and make a conversation, it creates an automatic title in the corner. You can now do that for every question. As part of that capability, you can add the classification, such as what are the topics and what are the capabilities. Capabilities could include ownership and responsibility, fetching tables, fetching images, fetching documents only, no synthesis, compare and contrast, deadlines, and so on. For more on selecting the right tools and capabilities, see my post on trade-offs in tool selection.

You can then put this information into a tool like Amplitude or Sentry. This will give you a running stream of the types of queries people are asking, which can help you understand how to prioritize these capabilities and topics.

Continuously Monitor and Experiment¶
Continuously monitor your system's performance and run experiments to test improvements.

Set up monitoring and logging to track system performance over time
Regularly review the data to identify trends and issues
Design and run experiments to test potential improvements
Measure the impact of changes on precision, recall, and other relevant metrics
Implement changes that show significant improvements
This could include tweaking search parameters, adding metadata, or trying different embedding models. Measure the impact on precision and recall to see if the changes are worthwhile.

Once you now have these questions in place, you have your synthetic data set and a bunch of user data with ratings. This is where the real work begins when it comes to systematically improving your RAG.

The system will be running many clusters of topic modeling around the questions, modeling that against the thumbs up and thumbs down ratings to figure out what clusters are underperforming. It will then determine the count and probability of user dissatisfaction for each cluster.

The system will be doing this on a regular cadence, figuring out for what volume of questions and user satisfaction levels it should focus on improving these specific use cases.

What might happen is you onboard a new organization, and all of a sudden, those distributions shift because their use cases are different. That's when you can go in and say, "We onboarded these new clients, and they very much care about deadlines. We knew we decided not to service deadlines, but now we know this is a priority, as it went from 2% of questions asking about deadlines to 80%." You can then determine what kind of education or improvements can be done around that.

Balance Latency and Performance¶
Finally, make informed decisions about trade-offs between system latency and search performance based on your specific use case and user requirements.

Understand the latency and performance requirements for your application
Measure the impact of different configurations on latency and performance
Make trade-offs based on what's most important for your users
Consider different requirements for different use cases (e.g., medical diagnosis vs. general search)
Here, this is where having the synthetic questions that test against will effectively answer that question. Because what we'll do is we'll run the query with and without this parent document retriever, and we will have a recall with and without that feature and the latency improvement of that feature.

And so now we'll be able to say, okay. Well, recall doubles. The latency increases by 20%, then a conversation can happen. Or, is that worth the investment? But if latency goes up double and the recall goes up 1%, again, it depends on, okay.

Well, if this is a medical diagnostic, maybe I do care that the 1% is included because the stakes are so high. But if it's for a doc page, maybe the increased latency will reduce in churn.

If you can improve recall by 1%, and the results are too complex, it's not worth deploying it in the future as well.

For example, if you're building a medical diagnostic tool, a slight increase in latency might be worth it for better recall. But if you're building a general-purpose search tool, faster results might be more important.


--------
Levels of Complexity: RAG Applications¶
This guide explores different levels of complexity in Retrieval-Augmented Generation (RAG) applications. We'll cover everything from basic ideas to advanced methods, making it useful for beginners and experienced developers alike.

We'll start with the basics, like breaking text into chunks, creating embeddings, and storing data. Then, we'll move on to more complex topics such as improved search methods, creating structured responses, and making systems work better. By the end, you'll know how to build strong RAG systems that can answer tricky questions accurately.

As we explore these topics, we'll use ideas from other resources, like our articles on data flywheels and improving tool retrieval in RAG systems. These ideas will help you understand how to create systems that keep improving themselves, making your product better and keeping users more engaged.

Key topics we'll explore include:

Basic text processing and embedding techniques
Efficient data storage and retrieval methods
Advanced search and ranking algorithms
Asynchronous programming for improved performance
Observability and logging for system monitoring
Evaluation strategies using synthetic and real-world data
Query enhancement and summarization techniques
This guide aligns with the insights from our RAG flywheel article, which emphasizes the importance of continuous improvement in RAG systems through data-driven iterations and user feedback integration.

If you want to learn more about I systematically improve RAG applications check out my free 6 email improving rag crash course

Check out the free email course here

Level 1: The Basics¶
Welcome to the foundational level of RAG applications! Here, we'll start with the basics, laying the groundwork for your journey into the realm of Retrieval-Augmented Generation. This level is designed to introduce you to the core concepts and techniques essential for working with RAG models. By the end of this section, you'll have a solid understanding of how to traverse file systems for text generation, chunk and batch text for processing, and interact with embedding APIs. Let's dive in and explore the exciting capabilities of RAG applications together!

Recursively traverse the file system to generate text.
Utilize a generator for text chunking.
Employ a generator to batch requests and asynchronously send them to an embedding API.
Store data in LanceDB.
Implement a CLI for querying, embedding questions, yielding text chunks, and generating responses.
Processing Pipeline¶

from dataclasses import dataclass
from typing import Iterable, List
import asyncio

sem = asyncio.Semaphore(10)

class TextChunk(BaseModel):
    id: int
    text: str
    embedding: np.array
    filename: str
    uuid: str = Field(default_factory=uuid.uuid4)

def flatmap(f, items):
    for item in items:
        for subitem in f(item):
            yield subitem

def get_texts():
    for file in files:
        yield TextChunk(
            text=file.read(),
            embedding=None,
            filename=file.name
        )

def chunk_text(items:Iterable[TextChunk], window_size: int, overlap: int=0):
    for i in range(0, len(items), window_size-overlap):
        yield TextChunk(
            text = items[i:i+window_size],
            embedding = None,
            filename = items[i].filename
        )

def batched(iterable, n=1):
    l = len(iterable)
    for ndx in range(0, l, n):
        yield iterable[ndx:min(ndx + n, l)]

def embed_batch(chunks: List[TextChunk]) -> List[TextChunk]:
    texts = [chunk.text for chunk in chunks]
    resp = embedding_api( # this is just the openai call
        texts=texts
    )
    for chunk, embedding in zip(chunks, resp):
        chunk.embedding = embedding
        yield chunks

def save_batch(chunks: List[TextChunk]):
    for chunk in chunks:
        db.insert(chunk)

if __name__ == "__main__":
    # This is the CLI
    texts = get_texts()
    chunks = flatmap(chunk_text, texts)
    batched_chunks = batched(chunks, 10)
    for chunks in tqdm(batched_chunks):
        chunk_with_embedding = embed_batch(chunks)
        save_batch(chunk_with_embedding)
Search Pipeline¶

def search(question: str) -> List[TextChunk]:
    embeddings = embedding_api(texts=[question])
    results = db.search(question)
    return results

if __name__ == "__main__":
    question = input("Ask a question: ")
    results = search(question)
    for chunk in results:
        print(chunk.text)
Answer Pipeline¶

def answer(question: str, results: List[TextChunk]) -> str:
    return client.chat.completions.create(
        model="gpt-3.5-turbo",
        stream=False,
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt(question, results)}
        ]
    )

if __name__ == "__main__":
    question = input("Ask a question: ")
    results = search(question)
    response = answer(question, results)
    for chunk in response:
        print(chunk.text, end="")
Level 2: More Structured Processing¶
Here we delve deeper into the world of Retrieval-Augmented Generation (RAG) applications. This level is designed for those who have grasped the basics and are ready to explore more advanced techniques and optimizations. Here, we focus on enhancing the efficiency and effectiveness of our RAG applications through better asynchronous programming, improved chunking strategies, and robust retry mechanisms in processing pipelines.

In the search pipeline, we introduce sophisticated methods such as better ranking algorithms, query expansion and rewriting, and executing parallel queries to elevate the quality and relevance of search results.

Furthermore, the answering pipeline is refined to provide more structured and informative responses, including citing specific text chunks and employing a streaming response model for better interaction.

Processing¶
Better Asyncio
Better Chunking
Better Retries
Search¶
Better Ranking (Cohere)
Query Expansion / Rewriting
Parallel Queries

class SearchQuery(BaseModel):
    semantic_search: str

def extract_query(question: str) -> Iterable[SearchQuery]:
    return client.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {
                "role": "system",
                "content": "Extract a query"
            },
            {
                "role": "user",
                "content": question
            }
        ],
        response_model=Iterable[SearchQuery]
    )

def search(search: Iterable[SearchQuery]) -> List[TextChunk]:
    with LanceDB() as db:
        results = db.search(search)
        return results


def rerank(question: str, results: List[TextChunk]) -> List[TextChunk]:
    return cohere_api(
        question=question,
        texts=[chunk.text for chunk in results]
    )

if __name__ == "__main__":
    question = input("Ask a question: ")
    search_query = extract_query(question)
    results = search(search_query)
    ranked_results = rerank(question, results)
    for chunk in ranked_results:
        print(chunk.text)
Answering¶
Citating specific text chunks
Streaming Response Model for better structure.

class MultiPartResponse(BaseModel):
    response: str
    followups: List[str]
    sources: List[int]

def answer(question: str, results: List[TextChunk]) -> Iterable[MultiPartResponse]:
    return client.chat.completions.create(
        model="gpt-3.5-turbo",
        stream=True,
        response_model=instructor.Partial[MultiPartResponse]
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt(question, results)}
        ]
    )

if __name__ == "__main__":
    from rich.console import Console

    question = input("Ask a question: ")

    search_query = extract_query(question)
    results = search(search_query)
    results = rerank(question, results)
    response = answer(question, results)

    console = Console()
    for chunk in response:
        console.clear()
        console.print(chunk.dump_model_json())
Level 3: Observability¶
At Level 3, the focus shifts towards the critical practice of observability. This stage emphasizes the importance of implementing comprehensive logging mechanisms to monitor and measure the multifaceted performance of your application. Establishing robust observability allows you to swiftly pinpoint and address any bottlenecks or issues, ensuring optimal functionality. Below, we outline several key types of logs that are instrumental in achieving this goal.

Expanding on Wide Event Tracking¶
Wide event tracking

Do it wide
Log how the queries are being rewritten¶
When addressing a complaint we should quickly understand if the query was written correctly
Query	Rewritten Query	latency	...
...	...	...	...
example: once we found that for queries with "latest" the dates it was selecting was literally the current date, we were able to quickly fix the issue by including few shot examples that consider latest to be 1 week or more.

Training a model
We can also use all the positive examples to figure out how to train a model that does query expansion better.

Log the citations¶
By logging the citations, we can quickly understand if the model is citing the correct information, what text chunks are popular, review and understand if the model is citing the correct information. and also potentially build a model in the future that can understand what text chunks are more important.

Query	Rewritten Query	...	Citations
...	...	...	[1,2,4]
There's a couple ways you can do this. For example, when you cite something, you can include not only what was shown to the language model, but also what was cited. If something was shown as a language model but was not cited, we can include this as part of the dataset.

Query	Rewritten Query	...	sources	cited
...	...	...	[1,2,4]	[1,2]
Log mean cosine scores and reranker scores¶
By attaching this little metadata, we will be able to very cheaply identify queries that may be performing poorly.

Query	Rewritten Query	...	Mean Cosine Score	Reranker Score
What is the capital of France?	What is the capital of France?	...	0.9	0.8
Who modified the file last?	Who was last in the race?	...	0.2	0.1
Here you might see "oh clearly i can't answer questions about file modification" thats not even in my index.

Log user level metadata for the search¶
By including other group information information, we can quickly identify if a certain group is having a bad experience

Examples could be

Organization ID
User ID
User Role
Signup Date
Device Type
Geo Location
Language
This could help you understand a lot of different things about how your application is being used. Maybe people on a different device are asking shorter queries and they are performing poorly. Or maybe when a new organization signed up, the types of questions they were asking were being served poorly by the language model. In the future we'll talk about other metrics, but just by implementing the mean cosine score and the free ranker score, you get these things for free without any additional work.

Just by building up some simple dashboards that are grouped by these attributes and look at the average scores, you can learn a lot. My recommendation is to review these things during stand-up once a week, look at some examples, and figure out what we could do to improve our system. When we see poor scores, we can look at the query and the rewritten query and try to understand what exactly is going on.

Have Users¶
By this point you should definitely be having users. You've already set yourself for success by understanding queries, rewriting them, and monitoring how users are actually using your system. The next couple of steps will be around improving specific metrics and also different ways of doing that.

Level 4: Evaluations¶
Evaluations at this stage are crucial for understanding the performance and effectiveness of our systems. Primarily, we are dealing with two distinct systems: the search system and the question answering (QA) system. It's common to see a lot of focus on evaluating the QA system, given its direct interaction with the end-user's queries. However, it's equally important to not overlook the search system. The search system acts as the backbone, fetching relevant information upon which the QA system builds its answers. A comprehensive evaluation strategy should include both systems, assessing them individually and how well they integrate and complement each other in providing accurate, relevant answers to the user's queries.

Evaluating the Search System¶
The aim here is to enhance our focus on key metrics such as precision and recall at various levels (K). With the comprehensive logging of all citation data, we have a solid foundation to employ a language model for an in-depth evaluation of the search system's efficacy.

For instances where the dataset might be limited, turning to synthetic data is a practical approach. This method involves selecting random text chunks or documents and then prompting a language model to generate questions that these texts could answer. This process is crucial for verifying the search system's ability to accurately identify and retrieve the text chunks responsible for generating these questions.


def test():
    text_chunk = sample_text_chunk()
    questions = ask_ai(f"generate questions that could be ansered by {text_chunk.text}")
    for question in questions:
        search_results = search(question)

    return {
        "recall@5": (1 if text_chunk in search_results[:5] else 0),
        ...
    }

average_recall = sum(test() for _ in range(n)) / n
Your code shouldn't actually look like this, but this generally captures the idea that we can synthetically generate questions and use them as part of our evaluation. You can try to be creative. But ultimately it will be a function of how well you can actually write a generation prompt.

Evaluating the Answering System¶
This is a lot trickier, but often times people will use a framework like, I guess, to evaluate the questions. Here I recommend spending some more time building out a data set that actually has answers.


def test():
    text_chunk = sample_text_chunks(n=...)
    question, answer = ask_ai(f"generate questions and answers for {text_chunk.text}")

    ai_answer = rag_app(question)
    return ask_ai(f"for the question {question} is the answer {ai_answer} correct given that {answer} is the correct answer?")
Evaluating the Answering System: Feedback¶
It's also good to build in feedback mechanisms in order to get better scores. I recommend building a thumbs up, thumbs down rating system rather than a five star rating system. I won't go into details right now, but this is something I strongly recommend.

The purpose of synethic data¶
The purpose of synthetic data is to help you quickly get some metrics out. It will help you build out this evaluation pipeline in hopes that as you get more users and more real questions, you'll be able to understand where we're performing well and where we're performing poorly using the suite of tests that we have. Precision, recall, mean ranking scores, etc.

Level 5: Understanding Short comings¶
At this point you should be able to have a data set that is extremely diverse using both the synthetic data and production data. We should also have a suite of scores that we can use to evaluate the quality of our answers.

org_id	query	rewritten	answer	recall@k	precision@k	mean ranking score	reranker score	user feedback	citations	sources	...
org123	...	...	...	...	...	...	...	...	...	...	...
Now we can do a bunch of different things to understand how we're doing by doing exploratory data analysis. We can look at the mean ranking score and reranker score and see if there are any patterns. We can look at the citations and see if there are any patterns. We can look at the user feedback and see if there are any patterns. We can look at the sources and see if there are any patterns. We can look at the queries and see if there are any patterns.

Clustering Queries¶
We can use clustering to understand if there are any patterns in the queries. We can use clustering to understand if there are any patterns in the citations. We can use clustering to understand if there are any patterns in the sources. We can use clustering to understand if there are any patterns in the user feedback.

We'll go into more depth later, but the general idea is we can also introduce cluster topics. I find that there's usually two different kinds of clutches that we detect.

Topics
Capabilities
Topics are captured by the nature of the text chunks and the queries. Capabilities are captured by the nature of the sources or additional metadata that we have.

Capabilites could be more like:

Questions that ask about document metadata "who modified the file last"
Quyestions that require summarzation of a document "what are the main points of this document"
Questions that required timeline information "what happened in the last 3 months"
Questions that compare and contrast "what are the differences between these two documents"
There are all things you'll likely find as you cluster and explore the datasets.

Upcoming Topics¶
As we continue to explore the depths of RAG applications, the following areas will be addressed in subsequent levels, each designed to enhance the complexity and functionality of your RAG systems:

Level 6: Advanced Data Handling¶
Finding Segments and Routing: Techniques for identifying and directing data segments efficiently.
Processing Tables: Strategies for interpreting and manipulating tabular data.
Processing Images: Methods for incorporating image data into RAG applications.
Level 7: Query Enhancement¶
Building Up Timeline Queries: Crafting queries that span across different timeframes for dynamic data analysis.
Adding Additional Metadata: Leveraging metadata to enrich query context and improve response accuracy.
Level 8: Summarization Techniques¶
Summarization and Summary Indices: Developing concise summaries from extensive datasets to aid quick insights.
Level 9: Outcome Modeling¶
Modeling Business Outcomes: Applying RAG techniques to predict and model business outcomes, facilitating strategic decision-making.


----
Jason Liu
Stop using LGTM@Few as a metric (Better RAG)

Search
 
 blog
147
64
Home
Learn to Improve Your RAG
Consulting
Job Board
Writing
Table of contents
When to look at data?
When to stop?
Importance of Velocity
Simple Metrics for Relevancy and Ranking
Understanding @K
Thinking about the metrics @ K
Mean Average Recall (MAR) @ K
Mean Average Precision (MAP) @ K
Mean Reciprocal Rank (MRR) @ K
Normalized Discounted Cumulative Gain (NDCG) @ K
How to improve
Metrics lead to Business Outcomes
Conclusion
Additional Notes
Want to learn more?
Back to index
Jason Liu
Jason Liu
Creator
Metadata
2024/02/05
in RAG
10 min read
Stop using LGTM@Few as a metric (Better RAG)¶
I work with a few seed series a startups that are ramping out their retrieval augmented generation systems. I've noticed a lot of unclear thinking around what metrics to use and when to use them. I've seen a lot of people use "LGTM@Few" as a metric, and I think it's a terrible idea. I'm going to explain why and what you should use instead.

If you want to learn about my consulting practice check out my services page. If you're interested in working together please reach out to me via email

When giving advice to developers on improving their retrieval augmented generation, I usually say two things:

Look at the Data
Don't just look at the Data
Wise men speak in paradoxes because we are afraid of half-truths. This blog post will try to capture when to look at data and when to stop looking at data in the context of retrieval augmented generation.

I'll cover the different relevancy and ranking metrics, some stories to help you understand them, their trade-offs, and some general advice on how to think.

If you want to learn more about I systematically improve RAG applications check out my free 6 email improving rag crash course

Check out the free email course here

When to look at data?¶
Look at data when the problem is very new. Do not rely on any kinds of metrics just yet. Look at the queries people are asking. Look at the documents that people are submitting. Look at the text chunks and see whether or not a single text chunk could possibly answer a question your user might have, or if you need multiple text chunks to piece together a complete answer. Look at the results from initial prototypes to understand if the retrieval task is technically feasible.

There's a lot of intuition you can gain from just looking at the data.

When to stop?¶
At some point, you're going to actually want to build a system. You're going to want to iterate and improve on it. You will likely get nowhere if all you're doing is 'looking at things'. You will spend too much time guessing as to what will improve something rather than trying to measure and improve something.

"What gets measured gets managed."

Instead, define metrics, run tests, investigate when and where the metrics are poor, and then start looking at the data again.

Look at Data

Work on System

Define Metrics

Look at Metrics

Look at Data with Poor Metrics

Well, let's take a closer look at what kind of metrics we can use and how they might improve our system. And I'll give an intuitive understanding of why and how some of these metrics break down. But first I also want to talk about the importance of speed.

Importance of Velocity¶
How quickly you can get metrics and run tests determines the nature of how you iterate on your software. If you're looking at a metric that takes a long time to compute, you're going to be waiting a long time to iterate on your system. So do whatever it takes to make the test that you run and the metrics you build as fast as possible!

Example via RAG

Slow Metric: Collocating human preferences and consulting domain experts.
Still Slow Metric: AI-generated metrics. When using something like GPT4, things can become very slow.
Fast Metrics: Accuracy, Precision, Recall, MRR, NDCG, are computationally cheap given the labels.
The goal is to reason about the trade-offs between fast metrics and slow data. It takes a long time to get enough data so you can move fast. But if you never do that work, we're always gonna be stuck.

Simple Metrics for Relevancy and Ranking¶
In the retrieval context, there are plenty of metrics to choose from. I'm gonna go describe a couple of them. But before we do that, we need to understand what @k means.

Understanding @K¶
The simplest idea we should think about is the idea of @k. When we do RAG, we first have to retrieve a set of K documents. Then we will do some re-ranking potentially. And then select the top end results to show to a user or to a language model. Consider the following pipeline:

Fetch n documents via Keyword Search
Fetch n documents via Semantic Search
Combine them and re-rank
Select the top 25 chunks to show to LLM
Top 5 documents are shown to the user.
Query

Keyword Search

Semantic Search

Reranker

Top 25

Top 5

LLM

User

Now let's look at some interpretations of top-k results.

k	Interpretation
5	Is what we show the user relevant?
25	Is the reranker doing a good job?
50	Is the retrieval system doing well?
100	Did we have a shot at all?
I strongly recommend you not focus too hard on generation from an LLM, And to stay focused on being able to provide the right context. You will be able to get a language model to be more robust and as language models improve, they will only get more resilient to irrelevant information. However, as you build out your business, this data set that you curate on relevancy will stay with you.

Thinking about the metrics @ K¶
Now let's look at some metrics that we can use to evaluate the performance of our retrieval augmented generation system. The goal isn't to give a mathematical breakdown of these metrics, but instead give you a sense of what they mean and how they might be useful. And how I like to explain and interpret them at the limits.

Mean Average Recall (MAR) @ K¶
Focuses on the system's capability to retrieve all relevant documents within the top K results, emphasizing the breadth of relevant information captured.

Formula for Standard Recall

 
 
Intuition: Can we catch the right answer?

Imagine throwing a net and goal is to catch fish, and the only thing we care about is if we catch all the fish. If we accidentally catch a dolphin or a sea turtle, thats fine!

Consider a medical test that said every single person on the planet had cancer, I would have very high recall, because I would have found everyone, but it wouldnt be useful. This is why we often have to make trade-offs between how many things we catch and how precise we are in our predictions.

In the context of search, recall is the fraction of relevant documents retrieved. Now, this is somewhat theoretical since we typically don't know how many relevant results there are in the index. Also, it's much easier to measure if the retrieved results are relevant, which brings us to ...

Mean Average Precision (MAP) @ K¶
Assesses the accuracy of the top K retrieved documents, ensuring the relevance and precision of retrieved content.

Formula for Standard Precision

 
 
Intuition: Are we choosing too carefully?

If you want to go to the extremes of precision. We might want to consider a test that determines if someone is sick. If you want to be very precise, we should only identify those who are bleeding out of their eyeballs... but that's not very useful. There's gonna be a lot more people we miss as a result of our desire to be very precise.

Again, we see that in the case of precision and recall, we are often led to trade-offs.

Here's a quick table of how I like to interpret my precision and recall trade-offs.

Recall	Precision	Interpretation
High	Low	We have a shot if the LLM is robust to noise, might run out of context length.
Low	High	We might give an incomplete answer, did not get all the content
High	High	If we do poorly here, it's because our generation prompt is...bad.
Low	Low	We're not doing well at all, nuke the system!
Mean Reciprocal Rank (MRR) @ K¶
Highlights the importance of quickly surfacing at least one relevant document, with an emphasis on the efficiency of relevance delivery. Matters a lot when there are only a few items we can show to the user at any given time.

Formula

 
 
 
 
Intuition: How quickly can we get the right answer?

The best business example I can give of MRR is thinking about something like a "play next" button. If you're building Spotify, you probably don't really care if one of the next 50 songs might be a banger. If the songs in the queue are not good, users will likely churn. The same applies to YouTube rankings.

The importance of bringing the right answer to the top is paramount. The third document is worth ⅓ of the first document. The 10th document is worth 1/10 of the first document. You can see how it dramatically decreases as you go lower. Whereas the precision and recall at K-metrics are unaffected by order.

Normalized Discounted Cumulative Gain (NDCG) @ K¶
A nuanced measure that evaluates both the presence and graded relevance of documents, rewarding systems that present the most valuable information first.

 
 
What the fuck is even that?

Honestly, I wouldn't worry about it too much, especially in the context of retrieval or generation. If you want to learn more, check out this great resource.

The TLDR I want to give you here is that this is just a more holistic measure of how well things are being ranked. It's not as aggressive as MRR.

Aggressive?

It's my belief that MRR and how it pushes certain rankings to the top is likely responsible for various kinds of echo chambers that might result in recommendation systems. For example, if you're watching a conspiracy theory video, the next thing you'll probably watch is going to be a conspiracy theory video.

How to improve¶
Once you have a system in place and some metrics you want to improve, again, the steps are very simple.

Choose a metric that aligns with your goals. Distinguish between primary metrics (that must improve) and guardrail metrics (that must not regress).
Formulate a hypothesis and adjust the system.
Evaluate the impact on your chosen metric.
Look at poorly performing examples, and iterate.
Go back to step 2.
Beware of Simpson's Paradox

A paradox in which a trend that appears in different groups of data disappears when these groups are combined, and the reverse trend appears for the aggregate data.

It's very likely that you might improve the system for one type of query and make it worse for another. To avoid doing this on some level, we can do the following:

Cluster the data (e.g., by query type, data source, etc.).
Determine if the metric is consistent across different clusters.
If it is, consider building a router to conditionally select one implementation over another.
Metrics lead to Business Outcomes¶
All of these metrics must ultimately be in service of something else. By improving things like precision, recall, and relevancy, what we're really hoping to do is generate better results for the business. The question then you have to ask yourself is, "What does that actually improve?". Here are a couple of things that you might want to consider.

User Satisfaction: Are users happy with the answers they're getting? Could be defined by Thumb Up/Down or NPS.
Engagement: Are users coming back to the platform? Are they spending more time on the platform?
Conversion: Are users buying more things? Are they clicking on more ads?
Retention: Are users staying on the platform longer? Are they coming back more often? Do we want to improve time spent?
Revenue: Are we making more money?
Cost: Are we spending less money on infrastructure?
Efficiency: Are we able to answer more questions with the same amount of resources?
The sooner you can relate some of these short-term fast metrics with larger slow metrics, The more you can make sure that you're going down the right path. Rather than trying to optimize something that has no impact down the road.

Conclusion¶
I hope this post has provided you with better intuition on how to think about relevancy of your text chunk.

Analyze data manually when facing a new problem, without relying on metrics initially.
Distinguish between primary metrics (that must improve) and guardrail metrics (that must not regress).
Velocity, clock speed of your iteration, is paramount. Make sure you can measure and iterate quickly.
Define metrics, conduct tests, investigate areas of poor performance, and then reevaluate the system.
Explore simple metrics for relevance and ranking, such as MAR, MAP, MRR, and NDCG.
Remember that these metrics should ultimately align with desired business outcomes.
Additional Notes¶
Notice that for MAR and MAP, They do not depend on the rank only the presence of the relevant document in the top K. This is why they are often used in the context of retrieval.
Notice that for MRR and NDCG, they depend on the rank of the relevant document. This is why they are often used in the context of ranking. If you end up building a sophisticated RAG application, you'll find that a lot of the time, many of the queries are just asking for documents which great opportunity to consider a ranking metric above just a regular context retrieval mechanism. If each document is 20 pages, you'll likely really care about which document shows up first.
Showing your work is super important for products that need to gain the user's trust. Again, ranking becomes really relevant even though language models themselves might not care.
--------
How to build a terrible RAG system¶
If you've followed my work on RAG systems, you'll know I emphasize treating them as recommendation systems at their core. In this post, we'll explore the concept of inverted thinking to tackle the challenge of building an exceptional RAG system.

What is inverted thinking?

Inverted thinking is a problem-solving approach that flips the perspective. Instead of asking, "How can I build a great RAG system?", we ask, "How could I create the worst possible RAG system?" By identifying potential pitfalls, we can more effectively avoid them and build towards excellence.

This approach aligns with our broader discussion on RAG systems, which you can explore further in our RAG flywheel article and our comprehensive guide on Levels of Complexity in RAG Applications.

If you want to learn more about I systematically improve RAG applications check out my free 6 email improving rag crash course

Check out the free email course here

Inventory

You'll often see me use the term inventory. I use it to refer to the set of documents that we're searching over. It's a term that I picked up from the e-commerce world. It's a great term because it's a lot more general than the term corpus. It's also a lot more specific than the term collection. It's a term that can be used to refer to the set of documents that we're searching over, the set of products that we're selling, or the set of items that we're recommending.

Don't worry about latency¶
There must be a reason that chat GPT tries to stream text out. Instead, we should only show the results once the entire response is completed. Many e-commerce websites have found that 100 ms improvement in latency can increase revenue by 1%. Check out How One Second Could Cost Amazon $1.6 Billion In Sales.

Don't show intermediate results¶
Users love love staring at a blank screen. It's a great way to build anticipation. If we communicated intermittent steps like the ones listed below, we'd just be giving away the secret sauce and users prefer to be left in the dark about what's going on.

Understanding your question
Searching with "..."
Finding the answer
Generating response
Don't Show Them the Source Document¶
Never show the source documents, and never highlight the origin of the text used to generate the response. Users should never have to fact-check our sources or verify the accuracy of the response. We should assume that they trust us and that there is no risk of false statements.

We Should Not Worry About Churn¶
We are not building a platform; we are just developing a machine learning system to gather metrics. Instead of focusing on churn, we should concentrate on the local metrics of our machine learning system like AUC and focus on benchmarks on HuggingFace.

We Should Use a Generic Search Index¶
Rather than asking users or trying to understand the types of queries they make, we should stick with a generic search and not allow users to generate more specific queries. There is no reason for Amazon to enable filtering by stars, price, or brand. It would be a waste of time! Google should not separate queries into web, images, maps, shopping, news, videos, books, and flights. There should be a single search bar, and we should assume that users will find what they're looking for.

We Should Not Develop Custom UI¶
It doesn't make sense to build a specific weather widget when the user asks for weather information. Instead, we should display the most relevant information. Semantic search is flawless and can effectively handle location or time-based queries. It can also re-rank the results to ensure relevance.

We Should Not Fine-Tune Our Embeddings¶
A company like Netflix should have a generic movie embedding that can be used to recommend movies to people. There's no need to rely on individual preferences (likes or dislikes) to improve the user or movie embeddings. Generic embeddings that perform well on benchmarks are sufficient for building a product.

We Should Train an LLM¶
Running inference on a large language model locally, which scales well, is cost-effective and efficient. There's no reason to depend on OpenAI for this task. Instead, we should consider hiring someone and paying them $250k a year to figure out scaling and running inference on a large language model. OpenAI does not offer any additional convenience or ease of use. By doing this, we can save money on labor costs.

We Should Not Manually Curate Our Inventory¶
There's no need for manual curation of our inventory. Instead, we can use a generic search index and assume that the documents we have are relevant to the user's query. Netflix should not have to manually curate the movies they offer or add additional metadata like actors and actresses to determine which thumbnails to show for improving click rates. The content ingested on day one is sufficient to create a great recommendation system.

We Should Not Analyze Inbound Queries¶
Analyzing the best and worst performing queries over time or understanding how different user cohorts ask questions will not provide any valuable insights. Looking at the data itself will not help us generate new ideas to improve specific segments of our recommendation system. Instead, we should focus on improving the recommendation system as a whole and avoid specialization.

Imagine if Netflix observed that people were searching for "movies with Will Smith" and decided to add a feature that allows users to search for movies with Will Smith. That would be a waste of time. There's no need to analyze the data and make system improvements based on such observations.

Machine Learning Engineers Should Not Be Involved in Ingestion¶
Machine Learning Engineers (MLEs) do not gain valuable insights by examining the data source or consulting domain experts. Their role should be limited to working with the given features. Theres no way that MLEs who love music would do a better job at Spotify, or a MLE who loves movies would do a better job at Netflix. Their only job is to take in data and make predictions.

We Should Use a Knowledge Graph¶
Our problem is so unique that it cannot be handled by a search index and a relational database. It is unnecessary to perform 1-2 left joins to answer a single question. Instead, considering the trending popularity of knowledge graphs on Twitter, it might be worth exploring the use of a knowledge graph for our specific case.

We should treat all inbound inventory the same¶
There's no need to understand the different types of documents that we're ingesting. How different could marketing content, construction documents, and energy bills be? Just because some have images, some have tables, and some have text doesn't mean we should treat them differently. It's all text, and so an LLM should just be able to handle it.

We should not have to build special ingestion pipelines¶
GPT-4 has solve all of data processing so if i handle a photo album, a pdf, and a word doc, it should be able to handle any type of document. There's no need to build special injestion pipelines for different types of documents. We should just assume that the LLM will be able to handle it. I shouldn't dont even have to think about what kinds of questions I need to answer. I should just be able to ask it anything and it should be able to answer it.

We should never have to ask the data provider for clean data¶
If Universal studios gave Netflix a bunch of MOV files with no metadata, Netflix should not have to ask Universal studios to provide additional movie metadata. Universal might not know the runtime, or the cast list and its netflix's job to figure that out. Universal should not have to provide any additional information about the movies they're providing.

We should never have to cluster our inventory¶
Theres only one kind of inventory and one kind of question. We should just assume that the LLM will be able to handle it. I shouldn't dont even have to think about what kinds of questions I need to answer. Topic clustering would only show us how uniform our inventory is and how little variation there is in the types of questions that users ask.

We should focus on local evals and not A/B tests¶
Once we run our GPT-4 self critique evaluations we'll know how well our system is doing and it'll make us more money, We should spend most of our time writing evaluation prompts and measuring precision / recall and just launching the best one. A/B tests are a waste of time and we should just assume that the best performing prompt will be the best performing business outcome.


--------

RAG is more than just embedding search¶
With the advent of large language models (LLM), retrieval augmented generation (RAG) has become a hot topic. However throught the past year of helping startups integrate LLMs into their stack I've noticed that the pattern of taking user queries, embedding them, and directly searching a vector store is effectively demoware.

What is RAG?

Retrieval augmented generation (RAG) is a technique that uses an LLM to generate responses, but uses a search backend to augment the generation. In the past year using text embeddings with a vector databases has been the most popular approach I've seen being socialized.

RAG

Simple RAG that embedded the user query and makes a search.
So let's kick things off by examining what I like to call the 'Dumb' RAG Model—a basic setup that's more common than you'd think.

If you want to learn more about I systematically improve RAG applications check out my free 6 email improving rag crash course

Check out the free email course here

The 'Dumb' RAG Model¶
When you ask a question like, "what is the capital of France?" The RAG 'dumb' model embeds the query and searches in some unopinonated search endpoint. Limited to a single method API like search(query: str) -> List[str]. This is fine for simple queries, since you'd expect words like 'paris is the capital of france' to be in the top results of say, your wikipedia embeddings.

Why is this a problem?¶
Query-Document Mismatch: This model assumes that query embedding and the content embedding are similar in the embedding space, which is not always true based on the text you're trying to search over. Only using queries that are semantically similar to the content is a huge limitation!

Monolithic Search Backend: Assumes a single search backend, which is not always the case. You may have multiple search backends, each with their own API, and you want to route the query to vector stores, search clients, sql databases, and more.

Limitation of text search: Restricts complex queries to a single string ({query: str}), sacrificing expressiveness, in using keywords, filters, and other advanced features. For example, asking what problems did we fix last week cannot be answered by a simple text search since documents that contain problem, last week won't be present every week or may reference the wrong period of time entirely.

Limited ability to plan: Assumes that the query is the only input to the search backend, but you may want to use other information to improve the search, like the user's location, or the time of day using the context to rewrite the query. For example, if you present the language model of more context its able to plan a suite of queries to execute to return the best results.

Now let's dive into how we can make it smarter with query understanding. This is where things get interesting.

Improving the RAG Model with Query Understanding¶
Shoutouts

Much of this work has been inspired by / done in collab with a few of my clients at new.computer, Metaphor Systems, and Naro, go check them out!

Ultimately what you want to deploy is a system that understands how to take the query and rewrite it to improve precision and recall.

RAG

Query Understanding system routes to multiple search backends.
Not convinced? Let's move from theory to practice with a real-world example. First up, Metaphor Systems.

Whats instructor?¶
Instructor uses Pydantic to simplify the interaction between the programmer and language models via the function calling API.

Widespread Adoption: Pydantic is a popular tool among Python developers.
Simplicity: Pydantic allows model definition in Python.
Framework Compatibility: Many Python frameworks already use Pydantic.
Case Study 1: Metaphor Systems¶
Take Metaphor Systems, which turns natural language queries into their custom search-optimized query. If you take a look web UI you'll notice that they have an auto-prompt option, which uses function calls to furthur optimize your query using a language model, and turns it into a fully specified metaphor systems query.

Metaphor Systems

Metaphor Systems UI
If we peek under the hood, we can see that the query is actually a complex object, with a date range, and a list of domains to search in. It's actually more complex than this but this is a good start. We can model this structured output in Pydantic using the instructor library


class DateRange(BaseModel):
    start: datetime.date
    end: datetime.date

class MetaphorQuery(BaseModel):
    rewritten_query: str
    published_daterange: DateRange
    domains_allow_list: List[str]

    async def execute():
        return await metaphor.search(...)
Note how we model a rewritten query, range of published dates, and a list of domains to search in. This powerful pattern allows the user query to be restructured for better performance without the user having to know the details of how the search backend works.


import instructor
from openai import OpenAI

# Enables response_model in the openai client
client = instructor.patch(OpenAI())

query = client.chat.completions.create(
    model="gpt-4",
    response_model=MetaphorQuery,
    messages=[
        {
            "role": "system",
            "content": "You're a query understanding system for the Metafor Systems search engine. Here are some tips: ..."
        },
        {
            "role": "user",
            "content": "What are some recent developments in AI?"
        }
    ],
)
Example Output


{
  "rewritten_query": "novel developments advancements ai artificial intelligence machine learning",
  "published_daterange": {
    "start": "2021-06-17",
    "end": "2023-09-17"
  },
  "domains_allow_list": ["arxiv.org"]
}
This isn't just about adding some date ranges. It's about nuanced, tailored searches, that are deeply integrated with the backend. Metaphor Systems has a whole suite of other filters and options that you can use to build a powerful search query. They can even use some chain of thought prompting to improve how they use some of these advanced features.


class DateRange(BaseModel):
    start: datetime.date
    end: datetime.date
    chain_of_thought: str = Field(
        None,
        description="Think step by step to plan what is the best time range to search in"
    )
Now, let's see how this approach can help model an agent like personal assistant.

Case Study 2: Personal Assistant¶
Another great example of this multiple dispatch pattern is a personal assistant. You might ask, "What do I have today?", from a vague query you might want events, emails, reminders etc. That data will likely exist in multiple backends, but what you want is one unified summary of results. Here you can't assume that text of those documents are all embedded in a search backend. There might be a calendar client, email client, across personal and profession accounts.


class ClientSource(enum.Enum):
    GMAIL = "gmail"
    CALENDAR = "calendar"

class SearchClient(BaseModel):
    query: str
    keywords: List[str]
    email: str
    source: ClientSource
    start_date: datetime.date
    end_date: datetime.date

    async def execute(self) -> str:
        if self.source == ClientSource.GMAIL:
            ...
        elif self.source == ClientSource.CALENDAR:
            ...

class Retrieval(BaseModel):
    queries: List[SearchClient]

    async def execute(self) -> str:
        return await asyncio.gather(*[query.execute() for query in self.queries])
Now we can call this with a simple query like "What do I have today?" and it will try to async dispatch to the correct backend. It's still important to prompt the language model well, but we'll leave that for another day.


import instructor
from openai import OpenAI

# Enables response_model in the openai client
client = instructor.patch(OpenAI())

retrieval = client.chat.completions.create(
    model="gpt-4",
    response_model=Retrieval,
    messages=[
        {"role": "system", "content": "You are Jason's personal assistant."},
        {"role": "user", "content": "What do I have today?"}
    ],
)
Example Output


{
    "queries": [
        {
            "query": None,
            "keywords": None,
            "email": "<EMAIL>",
            "source": "gmail",
            "start_date": "2023-09-17",
            "end_date": None
        },
        {
            "query": None,
            "keywords": ["meeting", "call", "zoom"]]],
            "email": "<EMAIL>",
            "source": "calendar",
            "start_date": "2023-09-17",
            "end_date": None

        }
    ]
}
Notice that we have a list of queries that route to different search backends (email and calendar). We can even dispatch them async to be as performant as possible. Not only do we dispatch to different backends (that we have no control over), but you are likely going to render them to the user differently as well. Perhaps you want to summarize the emails in text, but you want to render the calendar events as a list that they can scroll across on a mobile app.

Both of these examples showcase how both search providers and consumers can use instructor to model their systems. This is a powerful pattern that allows you to build a system that can be used by anyone, and can be used to build an LLM layer, from scratch, in front of any arbitrary backend.


----
Back to index
Metadata
2025/05/19
8 min read
There Are Only 6 RAG Evals¶
The world of RAG evaluation feels needlessly complex. Everyone's building frameworks, creating metrics, and generating dashboards that make you feel like you need a PhD just to know if your system is working.

But what if I told you there are only 6 fundamental ways to evaluate a RAG system?

Just math and symmetry.

The Insight¶
RAG systems have three core components:

A question (Q)
Retrieved context (C)
An answer (A)
That's it. Three variables.

Exhaustive by Design

The power of focusing on Question (Q), Context (C), and Answer (A) is that these three components, and their conditional relationships, cover every possible aspect of RAG evaluation. There are no hidden variables.

If we look at this through the lens of conditional relationships — the quality of one component given another — we get exactly six possible relationships. No more, no less.

Think about it: when one thing breaks in your RAG system, it's always one of these relationships failing. This relates directly to what we've learned about the systematic approach to RAG improvement - identifying specific failure points rather than making vague statements about "making the AI better."

The 6 Core Evaluation Metrics¶
Let's break down each relationship (I'll use the notation X|Y to mean "quality of X given Y"). Rather than treating all metrics equally, I'll organize them into three practical tiers based on implementation complexity and business impact - similar to how we think about the improvement flywheel for RAG products:

Tier 1: Foundation Metrics (Before RAG Evaluation)¶
Before we even get to our six core metrics, we need to acknowledge the foundation of any retrieval system:

Retrieval Precision & Recall: These traditional information retrieval metrics measure how well your retriever finds relevant documents from the corpus. They're fast to compute, don't require LLMs, and provide quick feedback for retriever tuning.

This aligns perfectly with our approach to starting the flywheel with synthetic data - you need to establish baseline retrieval performance with clear, measurable metrics before moving to more complex evaluation techniques. These metrics serve as the leading indicators that predict future success, rather than lagging indicators that just tell you about past performance.

Tier 2: Primary RAG Relationships¶
1. Context Relevance (C|Q)

Definition: How well do the retrieved chunks address the question's information needs? This measures whether your retriever component is doing its job—finding passages that contain information relevant to answering the user's question.

Example (Good):

Question: "What are the health benefits of meditation?"

Context: "Regular meditation has been shown to reduce stress hormones like cortisol. A 2018 study in the Journal of Cognitive Enhancement found meditation improves attention and working memory."
Reasoning: Strong relevance. The context directly addresses multiple health benefits with specific details.

Example (Bad):

Question: "What are the health benefits of meditation?"
Context: "Meditation practices vary widely across different traditions. Mindfulness meditation, which originated in Buddhist practices, focuses on present-moment awareness, while transcendental meditation uses mantras to achieve deeper states of consciousness."
Reasoning: Low relevance. Despite being factually correct about meditation, this context discusses types and origins rather than any health benefits. The retriever has found topically related content but missed the specific information need.
Irrelevant Context Dooms Generation

If your retriever pulls irrelevant context, your generator is doomed from the start. This is a common pitfall, reflecting "absence blindness" where teams obsess over generation quality while neglecting to ensure retrieval (C|Q) is even working correctly.

2. Faithfulness/Groundedness (A|C)

Definition: To what extent does the answer restrict itself only to claims that can be verified from the retrieved context? This evaluates the generator's ability to avoid hallucinations.

Example (Good):

Context: "The Great Barrier Reef is the world's largest coral reef system."

Answer: "The Great Barrier Reef is the largest coral reef system in the world."
Reasoning: Perfect faithfulness. The answer only states what's in the context.

Example (Bad):

Context: "The Great Barrier Reef is the world's largest coral reef system. It stretches for over 2,300 kilometers along the coast of Queensland, Australia."

Answer: "The Great Barrier Reef, the world's largest coral reef system, stretches for over 2,300 kilometers along Australia's eastern coast and is home to about 10% of the world's fish species."
Reasoning: Mixed faithfulness. The first part is supported, but the claim about "10% of the world's fish species" isn't in the provided context. This subtle hallucination appears plausible and might be factually correct, but it's not grounded in the retrieved context.

Why It Matters: Hallucination undermines trust. This is why we implement validation patterns, interactive citations, and chain-of-thought reasoning in our RAG applications - to catch errors before they reach users and build trust through transparency.

3. Answer Relevance (A|Q)

Definition: How directly does the answer address the specific information need expressed in the question? This evaluates the end-to-end system performance.

Example (Good):

Question: "How does compound interest work in investing?"

Answer: "Compound interest works by adding the interest earned back to your principal investment, so that future interest is calculated on the new, larger amount."
Reasoning: High relevance. The answer directly explains the concept asked about.

Example (Bad):

Question: "How does compound interest work in investing?"

Answer: "Interest in investing can be simple or compound. Compound interest is more powerful than simple interest and is an important concept in finance. It's the reason why starting to invest early is so beneficial for long-term wealth building."
Reasoning: Low relevance. Despite being about compound interest, the answer doesn't actually explain the mechanism of how it works. It tells you it's important but fails to address the specific how question.

Why It Matters: This is the ultimate user experience metric. It's also why we focus on building feedback mechanisms that specifically ask "Did we answer your question?" rather than vague "How did we do?" feedback prompts. Specific feedback aligned with this metric increases response rates dramatically.

Tier 3: Advanced RAG Relationships¶
4. Context Support Coverage (C|A)

Definition: Does the retrieved context contain all the information needed to fully support every claim in the answer? This measures whether the context is both sufficient and focused.
This metric connects directly to what we've learned about specialized retrievers and the query routing architecture. Different content types may require different retrieval approaches to ensure complete coverage. For instance, when answering questions about blueprints in construction projects, you might need both image retrieval and document retrieval working together.

5. Question Answerability (Q|C)

Definition: Given the context provided, is it actually possible to formulate a satisfactory answer to the question? This evaluates whether the question is reasonable given the available information.
This relates to the strategic rejection pattern we've discussed. When a query can't be answered with the available context, the most honest response is to acknowledge this limitation. This builds trust through transparency rather than generating a hallucinated answer.

6. Self-Containment (Q|A)

Definition: Can the original question be inferred from the answer alone? This measures whether the answer provides enough context to stand on its own.
This connects to our discussion of monologues and chain-of-thought approaches that make thinking visible. Answers that restate and address the core question directly create better user experiences, especially in asynchronous communication contexts.

Implementing Tiered Evaluation in Practice¶
Based on recent academic research and practical experience, here's how to approach RAG evaluation with these metrics:

Start with Tier 1: Implement fast retrieval metrics for daily development

Use precision, recall, MAP@K, and MRR@K to tune your retriever
These don't require LLM evaluation and provide quick feedback cycles
This directly mirrors our approach of starting the improvement flywheel with synthetic data and focused evaluation metrics before moving to more complex approaches.

Focus on Tier 2: Implement the three primary RAG relationships

These core metrics (C|Q, A|C, A|Q) directly assess how well your RAG system functions
Most benchmarks prioritize these three metrics
Use LLM-based evaluation for more nuanced assessment of these relationships
This aligns with our focus on building feedback mechanisms and quality-of-life improvements that enhance trust and transparency.

Extend to Tier 3: Add advanced metrics when you need deeper insights

These metrics (C|A, Q|C, Q|A) connect technical performance to business outcomes
Use them for monthly evaluations, major releases, and strategic decisions
Different domains may require emphasis on different Tier 3 metrics (e.g., medical RAG needs stronger C|A)
This connects to our discussion of topic modeling and capability identification, recognizing that different query types may require different evaluation emphasis.

LLM-as-Judge¶
Most modern RAG evaluations rely on LLMs as judges. This approach, while resource-intensive, provides the most nuanced assessment of our six core relationships.

The Nuance of LLM Judges

While resource-intensive, using LLMs as judges is currently the most effective method for capturing the subtle nuances in the six core RAG relationships. Traditional metrics often fall short in this complex assessment.

Several benchmarks, including RAGAs, ARES, and TruEra RAG Triad, now use LLM evaluation by default. While traditional metrics like BLEU, ROUGE, and BERTScore still have a place, only LLM-based evaluation can effectively capture the nuanced relationships in our framework.

This parallels our discussion about using LLMs to analyze feedback and identify patterns in user queries - leveraging AI to understand AI.

Domain-Specific Evaluation¶
An interesting insight from the DomainRAG benchmark is that different domains may require different emphasis within our framework:

Medical RAG systems need higher faithfulness scores (A|C)
Customer service RAG demands better answer relevance (A|Q)
Technical documentation RAG requires stronger question answerability (Q|C)
This reinforces what we've learned about topic modeling and segmentation - different query types need different capabilities, and our evaluation should reflect those priorities. It's why we segment questions not just by topic but by the capability required to answer them.

Why This Framework Matters¶
When your RAG system fails, it fails along one of these dimensions. Every time.

Answer seems wrong? Check faithfulness (A|C).
Answer seems irrelevant? Check answer relevance (A|Q).
Answer missing key info? Check context relevance (C|Q) or context support (C|A).
The beauty of this framework is that it's complete. There are no other relationships between Q, C, and A. We've covered every possible evaluation angle.

This systematic approach to diagnosing problems aligns perfectly with our product mindset for RAG - identifying specific failure points rather than making vague statements about "making the AI better."

So What?¶
Next time you're debugging a RAG system, don't waste time on complexity theater. Focus on these six relationships organized in practical tiers. Fix the ones that are broken. Ignore the rest.

This framework complements our improvement flywheel perfectly - start with the basics, collect feedback on specific aspects, analyze that feedback to identify patterns, and make targeted improvements based on what you learn.

And if someone tries to sell you a RAG evaluation framework with 20 different metrics? Smile and ask which of the 6 core relationships they're actually measuring.

Because in RAG evaluation, as in RAG implementation, the systematic approach wins every time

---

## 🎯 **Aven AI Customer Agent - 6-Step RAG Pipeline Implementation**

### **Current Production Pipeline (2025)**

Our implementation uses a sophisticated 6-step RAG pipeline with comprehensive LangFuse tracing:

#### **Step 1: Query Analysis → Understanding request**
- **Purpose**: Understanding request and determining optimal approach
- **Implementation**: Intent classification, query parsing, request analysis
- **Tracing**: Logs query type, complexity level, identified intent
- **Performance**: ~800ms average duration

#### **Step 2: Memory Search → Previous conversation context**
- **Purpose**: Previous conversation context retrieval
- **Implementation**: Semantic search over conversation history using user ID
- **Tracing**: Logs memory results count, relevance scores
- **Performance**: Variable based on memory size

#### **Step 3: Knowledge Base Search → Agentic retrieval**
- **Purpose**: Agentic retrieval from knowledge base
- **Implementation**: Pinecone vector database with agentic retrieval system and multiple search iterations
- **Tracing**: Logs retrieval results, confidence scores, search path, Pinecone index queries
- **Performance**: Optimized with semantic caching and vector similarity search

#### **Step 4: Document Reranking → Quality scoring**
- **Purpose**: Quality scoring and document ranking
- **Implementation**: Relevance scoring algorithm with confidence thresholds
- **Tracing**: Logs rerank scores, document selection criteria
- **Performance**: ~600ms for score computation

#### **Step 5: Context Assembly → Prompt construction**
- **Purpose**: Prompt construction from assembled context
- **Implementation**: Combines memory + knowledge with token budget management
- **Tracing**: Logs context length, source distribution, assembly quality
- **Performance**: ~400ms for context preparation

#### **Step 6: Response Generation → Final answer with citations**
- **Purpose**: Final answer generation with citations
- **Implementation**: Advanced prompting with assembled context and source citations
- **Tracing**: Logs LLM calls, token usage, generation metrics, costs
- **Performance**: Variable based on response complexity

### **Advanced Features**

#### **Dual-Mode Operation**
- **Fast Mode**: Instant responses + optimized pipeline (1-2 seconds)
- **Full Pipeline Mode**: Complete 6-step process with detailed tracing (4-6 seconds)
- **UI Toggle**: Users can switch between modes based on needs

#### **Comprehensive Observability**
- **LangFuse Integration**: Full step-by-step tracing with metadata
- **Performance Metrics**: Latency, cost tracking, quality scores
- **Error Handling**: Graceful degradation with fallback mechanisms
- **Learning Loop**: Continuous improvement from interaction data

#### **Parallel Optimizations**
- **Concurrent Operations**: Memory + meeting check + cache operations run in parallel
- **Async Background Tasks**: Memory storage, learning, tracing run non-blocking
- **Smart Caching**: Semantic cache with embedding similarity thresholds
- **Instant Response Layer**: Pre-computed answers for common questions

### **Evaluation & Quality Assurance**

#### **Multi-Level Testing**
- **Unit Tests**: Individual component validation
- **Integration Tests**: End-to-end pipeline verification  
- **Human Evaluation**: Quality assessment on sample interactions
- **A/B Testing**: Performance comparison between pipeline modes

#### **Quality Metrics (Based on Framework Above)**
- **Context Relevance (C|Q)**: How well retrieved context matches the query
- **Faithfulness (A|C)**: Answer accuracy based on retrieved context
- **Answer Relevance (A|Q)**: How directly the answer addresses the question
- **Latency**: Response time across different query types
- **Cost**: Token usage and external service costs
- **User Satisfaction**: Feedback-based quality indicators

### **Production Deployment**
- **Scalability**: Handles concurrent users with resource management
- **Reliability**: Error recovery and fallback mechanisms
- **Monitoring**: Real-time performance and quality tracking
- **Maintenance**: Automated testing and deployment pipelines

### **Implementation Files**
- **Pipeline Core**: `src/lib/rag-pipeline.ts` - Main 6-step implementation
- **API Integration**: `src/app/api/chat/route.ts` - Chat API with pipeline toggle
- **Tracing**: `src/lib/langfuse.ts` - Comprehensive observability
- **UI Controls**: `src/components/SimpleChatInterface.tsx` - Pipeline mode toggle

This implementation combines the best practices from the evaluation frameworks above with practical production requirements for the Aven HELOC Credit Card customer support system.