# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=aven-support-index

# VAPI Configuration (for voice interface)
NEXT_PUBLIC_VAPI_PUBLIC_KEY=your_vapi_public_key_here
VAPI_PRIVATE_KEY=your_vapi_private_key_here
NEXT_PUBLIC_VAPI_ASSISTANT_ID=your_assistant_id_here

# MEM0 Configuration
MEM0_API_KEY=your_mem0_api_key_here

# Exa AI Configuration (for web search)
EXA_API_KEY=your_exa_api_key_here

# LangFuse Configuration (for observability)
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key_here
LANGFUSE_SECRET_KEY=your_langfuse_secret_key_here
LANGFUSE_HOST=https://us.cloud.langfuse.com

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here