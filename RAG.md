Directory structure:
└── backend/
    ├── alembic.ini
    ├── Dockerfile
    ├── Dockerfile.model_server
    ├── hello-vmlinux.bin
    ├── pyproject.toml
    ├── pytest.ini
    ├── supervisord.conf
    ├── .dockerignore
    ├── .trivyignore
    ├── alembic/
    │   ├── README.md
    │   ├── env.py
    │   ├── script.py.mako
    │   └── versions/
    │       ├── 027381bce97c_add_shortcut_option_for_users.py
    │       ├── 03bf8be6b53a_rework_kg_config.py
    │       ├── 0568ccf46a6b_add_thread_specific_model_selection.py
    │       ├── 05c07bf07c00_add_search_doc_relevance_details.py
    │       ├── 0816326d83aa_add_federated_connector_tables.py
    │       ├── 08a1eda20fe1_add_earliest_indexing_to_connector.py
    │       ├── 0a2b51deb0b8_add_starter_prompts.py
    │       ├── 0a98909f2757_enable_encrypted_fields.py
    │       ├── 0ebb1d516877_add_ccpair_deletion_failure_message.py
    │       ├── 0f7ff6d75b57_add_index_to_index_attempt_time_created.py
    │       ├── 12635f6655b7_drive_canonical_ids.py
    │       ├── 15326fcec57e_introduce_onyx_apis.py
    │       ├── 173cae5bba26_port_config_store.py
    │       ├── 177de57c21c9_display_custom_llm_models.py
    │       ├── 1a03d2c2856b_add_indexes_to_document__tag.py
    │       ├── 1b10e1fda030_add_additional_data_to_notifications.py
    │       ├── 1b8206b29c5d_add_user_delete_cascades.py
    │       ├── 1f60f60c3401_embedding_model_search_settings.py
    │       ├── 213fd978c6d8_notifications.py
    │       ├── 238b84885828_add_foreign_key_to_user__external_user_.py
    │       ├── 23957775e5f5_remove_feedback_foreignkey_constraint.py
    │       ├── 2666d766cb9b_google_oauth2.py
    │       ├── 26b931506ecb_default_chosen_assistants_to_none.py
    │       ├── 27c6ecc08586_permission_framework.py
    │       ├── 2955778aa44c_add_chunk_count_to_document.py
    │       ├── 2cdeff6d8c93_set_built_in_to_default.py
    │       ├── 2d2304e27d8c_add_above_below_to_persona.py
    │       ├── 2daa494a0851_add_group_sync_time.py
    │       ├── 2f80c6a2550f_add_chat_session_specific_temperature_.py
    │       ├── 30c1d5744104_persona_datetime_aware.py
    │       ├── 325975216eb3_add_icon_color_and_icon_shape_to_persona.py
    │       ├── 33cb72ea4d80_single_tool_call_per_message.py
    │       ├── 33ea50e88f24_foreign_key_input_prompts.py
    │       ├── 351faebd379d_add_curator_fields.py
    │       ├── 35e518e0ddf4_properly_cascade.py
    │       ├── 35e6853a51d5_server_default_chosen_assistants.py
    │       ├── 369644546676_add_composite_index_for_index_attempt_.py
    │       ├── 36e9220ab794_update_kg_trigger_functions.py
    │       ├── 3781a5eb12cb_add_chunk_stats_table.py
    │       ├── 3879338f8ba1_add_tool_table.py
    │       ├── 38eda64af7fe_add_chat_session_sharing.py
    │       ├── 3934b1bc7b62_update_github_connector_repo_name_to_.py
    │       ├── 3a7802814195_add_alternate_assistant_to_chat_message.py
    │       ├── 3b25685ff73c_move_is_public_to_cc_pair.py
    │       ├── 3bd4c84fe72f_improved_index.py
    │       ├── 3c5e35aa9af0_polling_document_count.py
    │       ├── 3c6531f32351_add_back_input_prompts.py
    │       ├── 401c1ac29467_add_tables_for_ui_based_llm_.py
    │       ├── 43cbbb3f5e6a_rename_index_origin_to_index_recursively.py
    │       ├── 44f856ae2a4a_add_cloud_embedding_model.py
    │       ├── 4505fd7302e1_added_is_internet_to_dbdoc.py
    │       ├── 465f78d9b7f9_larger_access_tokens_for_oauth.py
    │       ├── 46625e4745d4_remove_native_enum.py
    │       ├── 46b7a812670f_fix_user__external_user_group_id_fk.py
    │       ├── 4738e4b3bae1_pg_file_store.py
    │       ├── 473a1a7ca408_add_display_model_names_to_llm_provider.py
    │       ├── 47433d30de82_create_indexattempt_table.py
    │       ├── 475fcefe8826_add_name_to_api_key.py
    │       ├── 4794bc13e484_update_prompt_length.py
    │       ├── 47a07e1a38f1_fix_invalid_model_configurations_state.py
    │       ├── 47e5bef3a1d7_add_persona_categories.py
    │       ├── 48d14957fe80_add_support_for_custom_tools.py
    │       ├── 495cb26ce93e_create_knowlege_graph_tables.py
    │       ├── 4a951134c801_moved_status_to_connector_credential_.py
    │       ├── 4b08d97e175a_change_default_prune_freq.py
    │       ├── 4d58345da04a_lowercase_user_emails.py
    │       ├── 4ea2c93919c1_add_type_to_credentials.py
    │       ├── 4ee1287bd26a_add_multiple_slack_bot_support.py
    │       ├── 50b683a8295c_add_additional_retrieval_controls_to_.py
    │       ├── 52a219fb5233_add_last_synced_and_last_modified_to_document_table.py
    │       ├── 54a74a0417fc_danswerbot_onyxbot.py
    │       ├── 55546a7967ee_assistant_rework.py
    │       ├── 570282d33c49_track_onyxbot_explicitly.py
    │       ├── 57b53544726e_add_document_set_tables.py
    │       ├── 5809c0787398_add_chat_sessions.py
    │       ├── 58c50ef19f08_add_stale_column_to_user__external_user_.py
    │       ├── 5b29123cd710_nullable_search_settings_for_historic_.py
    │       ├── 5c448911b12f_add_content_type_to_userfile.py
    │       ├── 5c7fdadae813_match_any_keywords_flag_for_standard_.py
    │       ├── 5d12a446f5c0_add_api_version_and_deployment_name_to_.py
    │       ├── 5e84129c8be3_add_docs_indexed_column_to_index_.py
    │       ├── 5f4b8568a221_add_removed_documents_to_index_attempt.py
    │       ├── 5fc1f54cc252_hybrid_enum.py
    │       ├── 61ff3651add4_add_permission_syncing.py
    │       ├── 643a84a42a33_add_user_configured_names_to_llmprovider.py
    │       ├── 65bc6e0f8500_remove_kg_subtype_from_db.py
    │       ├── 6756efa39ada_id_uuid_for_chat_session.py
    │       ├── 6a804aeb4830_duplicated_no_harm_user_file_migration.py
    │       ├── 6d387b3196c2_basic_auth.py
    │       ├── 6d562f86c78b_remove_default_bot.py
    │       ├── 6fc7886d665d_make_categories_labels_and_many_to_many.py
    │       ├── 703313b75876_add_tokenratelimit_tables.py
    │       ├── 70f00c45c0f2_more_descriptive_filestore.py
    │       ├── 72bdc9929a46_permission_auto_sync_framework.py
    │       ├── 7477a5f5d728_added_model_defaults_for_users.py
    │       ├── 7547d982db8f_chat_folders.py
    │       ├── 767f1c2a00eb_count_chat_tokens.py
    │       ├── 76b60d407dfb_cc_pair_name_not_unique.py
    │       ├── 776b3bbe9092_remove_remaining_enums.py
    │       ├── 77d07dffae64_forcibly_remove_more_enum_types_from_.py
    │       ├── 78dbe7e38469_task_tracking.py
    │       ├── 795b20b85b4b_add_llm_group_permissions_control.py
    │       ├── 797089dfb4d2_persona_start_date.py
    │       ├── 79acd316403a_add_api_key_table.py
    │       ├── 7a70b7664e37_add_model_configuration_table.py
    │       ├── 7aea705850d5_added_slack_auto_filter.py
    │       ├── 7b9b952abdf6_update_entities.py
    │       ├── 7ccea01261f6_store_chat_retrieval_docs.py
    │       ├── 7da0ae5ad583_add_description_to_persona.py
    │       ├── 7da543f5672f_add_slackbotconfig_table.py
    │       ├── 7f726bad5367_slack_followup.py
    │       ├── 7f99be1cb9f5_add_index_for_getting_documents_just_by_.py
    │       ├── 800f48024ae9_add_id_to_connectorcredentialpair.py
    │       ├── 80696cf850ae_add_chat_session_to_query_event.py
    │       ├── 891cd83c87a8_add_is_visible_to_persona.py
    │       ├── 8987770549c0_add_full_exception_stack_trace.py
    │       ├── 8a87bd6ec550_associate_index_attempts_with_ccpair.py
    │       ├── 8aabb57f3b49_restructure_document_indices.py
    │       ├── 8e1ac4f39a9f_enable_contextual_retrieval.py
    │       ├── 8e26726b7683_chat_context_addition.py
    │       ├── 8f43500ee275_add_index.py
    │       ├── 904451035c9b_store_tool_details.py
    │       ├── 904e5138fffb_tags.py
    │       ├── 91a0a4d62b14_milestone.py
    │       ├── 91fd3b470d1a_remove_documentsource_from_tag.py
    │       ├── 91ffac7e65b3_add_expiry_time.py
    │       ├── 93560ba1b118_add_web_ui_option_to_slack_config.py
    │       ├── 949b4a92a401_remove_rt.py
    │       ├── 94dc3d0236f8_make_document_set_description_optional.py
    │       ├── 97dbb53fa8c8_add_syncrecord.py
    │       ├── 98a5008d8711_agent_tracking.py
    │       ├── 9aadf32dfeb4_add_user_files.py
    │       ├── 9c00a2bccb83_chat_message_agentic.py
    │       ├── 9cf5c00f72fe_add_creator_to_cc_pair.py
    │       ├── 9d97fecfab7f_added_retrieved_docs_to_query_event.py
    │       ├── 9f696734098f_combine_search_and_chat.py
    │       ├── a3795dce87be_migration_confluence_to_be_explicit.py
    │       ├── a3bfd0d64902_add_chosen_assistants_to_user_table.py
    │       ├── a570b80a5f20_usergroup_tables.py
    │       ├── a6df6b88ef81_remove_recent_assistants.py
    │       ├── a7688ab35c45_add_public_external_user_group_table.py
    │       ├── a8c2065484e6_add_auto_scroll_to_user_model.py
    │       ├── abe7378b8217_add_indexing_trigger_to_cc_pair.py
    │       ├── ac5eaac849f9_add_last_pruned_to_connector_table.py
    │       ├── acaab4ef4507_remove_inactive_ccpair_status_on_.py
    │       ├── ae62505e3acc_add_saml_accounts.py
    │       ├── aeda5f2df4f6_add_pinned_assistants.py
    │       ├── b082fec533f0_make_last_attempt_status_nullable.py
    │       ├── b156fa702355_chat_reworked.py
    │       ├── b388730a2899_nullable_preferences.py
    │       ├── b72ed7a5db0e_remove_description_from_starter_messages.py
    │       ├── b7a7eee5aa15_add_checkpointing_failure_handling.py
    │       ├── b7c2b63c4a03_add_background_reindex_enabled_field.py
    │       ├── b85f02ec1308_fix_file_type_migration.py
    │       ├── b896bbd0d5a7_backfill_is_internet_data_to_false.py
    │       ├── ba98eba0f66a_add_support_for_litellm_proxy_in_.py
    │       ├── baf71f781b9e_add_llm_model_version_override_to_.py
    │       ├── bc9771dccadf_create_usage_reports_table.py
    │       ├── bceb1e139447_add_base_url_to_cloudembeddingprovider.py
    │       ├── bd2921608c3a_non_nullable_default_persona.py
    │       ├── be2ab2aa50ee_fix_capitalization.py
    │       ├── bf7a81109301_delete_input_prompts.py
    │       ├── c0aab6edb6dd_delete_workspace.py
    │       ├── c0fd6e4da83a_add_recent_assistants.py
    │       ├── c18cdf4b497e_add_standard_answer_tables.py
    │       ├── c5b692fa265c_add_index_attempt_errors_table.py
    │       ├── c5eae4a75a1b_add_chat_message__standard_answer_table.py
    │       ├── c7bf5721733e_add_has_been_indexed_to_.py
    │       ├── c99d76fcd298_add_nullable_to_persona_id_in_chat_.py
    │       ├── c9e2cd766c29_add_s3_file_store_table.py
    │       ├── ca04500b9ee8_add_cascade_deletes_to_agent_tables.py
    │       ├── cec7ec36c505_kgentity_parent.py
    │       ├── cf90764725d8_larger_refresh_tokens.py
    │       ├── d5645c915d0e_remove_deletion_attempt_table.py
    │       ├── d61e513bef0a_add_total_docs_for_index_attempt.py
    │       ├── d7111c1238cd_remove_document_ids.py
    │       ├── d716b0791ddd_combined_slack_id_fields.py
    │       ├── d929f0c1c6af_feedback_feature.py
    │       ├── d961aca62eb3_update_status_length.py
    │       ├── d9ec13955951_remove__dim_suffix_from_model_name.py
    │       ├── da42808081e3_migrate_jira_connectors_to_new_format.py
    │       ├── da4c21c69164_chosen_assistants_changed_to_jsonb.py
    │       ├── dab04867cd88_add_composite_index_to_document_by_.py
    │       ├── dba7f71618f5_onyx_custom_tool_flow.py
    │       ├── dbaa756c2ccf_embedding_models.py
    │       ├── df0c7ad8a076_added_deletion_attempt_table.py
    │       ├── df46c75b714e_add_default_vision_provider_to_llm_.py
    │       ├── dfbe9e93d3c7_extended_role_for_non_web.py
    │       ├── e0a68a81d434_add_chat_feedback.py
    │       ├── e1392f05e840_added_input_prompts.py
    │       ├── e209dc5a8156_added_prune_frequency.py
    │       ├── e4334d5b33ba_add_deployment_name_to_llmprovider.py
    │       ├── e50154680a5c_no_source_enum.py
    │       ├── e6a4bbc13fe4_add_index_for_retrieving_latest_index_.py
    │       ├── e86866a9c78a_add_persona_to_chat_session.py
    │       ├── e91df4e935ef_private_personas_documentsets.py
    │       ├── eaa3b5593925_add_default_slack_channel_config.py
    │       ├── ec3ec2eabf7b_index_from_beginning.py
    │       ├── ec85f2b3c544_remove_last_attempt_status_from_cc_pair.py
    │       ├── ecab2b3f1a3b_add_overrides_to_the_chat_session.py
    │       ├── ee3f4b47fad5_added_alternate_model_to_chat_message.py
    │       ├── ef7da92f7213_add_files_to_chatmessage.py
    │       ├── efb35676026c_standard_answer_match_regex_flag.py
    │       ├── f11b408e39d3_force_lowercase_all_users.py
    │       ├── f13db29f3101_add_composite_index_for_last_modified_.py
    │       ├── f17bf3b0d9f1_embedding_provider_by_provider_type.py
    │       ├── f1c6478c3fd8_add_pre_defined_feedback.py
    │       ├── f1ca58b2f2ec_add_passthrough_auth_to_tool.py
    │       ├── f32615f71aeb_add_custom_headers_to_tools.py
    │       ├── f39c5794c10a_add_background_errors_table.py
    │       ├── f5437cc136c5_delete_non_search_assistants.py
    │       ├── f71470ba9274_add_prompt_length_limit.py
    │       ├── f7505c5b0284_updated_constraints_for_ccpairs.py
    │       ├── f7a894b06d02_non_nullbale_slack_bot_id_in_channel_.py
    │       ├── f7e58d357687_add_has_web_column_to_user.py
    │       ├── fad14119fb92_delete_tags_with_wrong_enum.py
    │       ├── fcd135795f21_add_slack_bot_display_type.py
    │       ├── febe9eaa0644_add_document_set_persona_relationship_.py
    │       ├── fec3db967bf7_add_time_updated_to_usergroup_and_.py
    │       └── ffc707a226b4_basic_document_metadata.py
    ├── alembic_tenants/
    │   ├── README.md
    │   ├── __init__.py
    │   ├── env.py
    │   ├── script.py.mako
    │   └── versions/
    │       ├── 14a83a331951_create_usertenantmapping_table.py
    │       ├── 34e3630c7f32_lowercase_multi_tenant_user_auth.py
    │       ├── 3b45e0018bf1_add_new_available_tenant_table.py
    │       ├── 3b9f09038764_add_read_only_kg_user.py
    │       ├── a4f6ee863c47_mapping_for_anonymous_user_path.py
    │       └── ac842f85f932_new_column_user_tenant_mapping.py
    ├── ee/
    │   ├── __init__.py
    │   ├── LICENSE
    │   └── onyx/
    │       ├── __init__.py
    │       ├── main.py
    │       ├── access/
    │       │   └── access.py
    │       ├── auth/
    │       │   ├── __init__.py
    │       │   └── users.py
    │       ├── background/
    │       │   ├── celery_utils.py
    │       │   ├── task_name_builders.py
    │       │   └── celery/
    │       │       ├── apps/
    │       │       │   ├── heavy.py
    │       │       │   ├── light.py
    │       │       │   ├── monitoring.py
    │       │       │   └── primary.py
    │       │       └── tasks/
    │       │           ├── beat_schedule.py
    │       │           ├── cleanup/
    │       │           │   └── tasks.py
    │       │           ├── cloud/
    │       │           │   └── tasks.py
    │       │           ├── doc_permission_syncing/
    │       │           │   └── tasks.py
    │       │           ├── external_group_syncing/
    │       │           │   ├── group_sync_utils.py
    │       │           │   └── tasks.py
    │       │           ├── tenant_provisioning/
    │       │           │   └── tasks.py
    │       │           └── vespa/
    │       │               └── tasks.py
    │       ├── chat/
    │       │   └── process_message.py
    │       ├── configs/
    │       │   ├── __init__.py
    │       │   ├── app_configs.py
    │       │   └── saml_config/
    │       │       └── template.settings.json
    │       ├── connectors/
    │       │   └── perm_sync_valid.py
    │       ├── db/
    │       │   ├── __init__.py
    │       │   ├── analytics.py
    │       │   ├── connector.py
    │       │   ├── connector_credential_pair.py
    │       │   ├── document.py
    │       │   ├── document_set.py
    │       │   ├── external_perm.py
    │       │   ├── persona.py
    │       │   ├── query_history.py
    │       │   ├── saml.py
    │       │   ├── standard_answer.py
    │       │   ├── token_limit.py
    │       │   ├── usage_export.py
    │       │   └── user_group.py
    │       ├── document_index/
    │       │   └── vespa/
    │       │       └── app_config/
    │       │           └── cloud-services.xml.jinja
    │       ├── external_permissions/
    │       │   ├── __init__.py
    │       │   ├── perm_sync_types.py
    │       │   ├── post_query_censoring.py
    │       │   ├── sync_params.py
    │       │   ├── utils.py
    │       │   ├── confluence/
    │       │   │   ├── __init__.py
    │       │   │   ├── constants.py
    │       │   │   ├── doc_sync.py
    │       │   │   ├── group_sync.py
    │       │   │   ├── page_access.py
    │       │   │   └── space_access.py
    │       │   ├── gmail/
    │       │   │   └── doc_sync.py
    │       │   ├── google_drive/
    │       │   │   ├── __init__.py
    │       │   │   ├── doc_sync.py
    │       │   │   ├── folder_retrieval.py
    │       │   │   ├── group_sync.py
    │       │   │   ├── models.py
    │       │   │   └── permission_retrieval.py
    │       │   ├── jira/
    │       │   │   ├── __init__.py
    │       │   │   ├── doc_sync.py
    │       │   │   ├── models.py
    │       │   │   └── page_access.py
    │       │   ├── salesforce/
    │       │   │   ├── postprocessing.py
    │       │   │   └── utils.py
    │       │   ├── slack/
    │       │   │   ├── channel_access.py
    │       │   │   ├── doc_sync.py
    │       │   │   ├── group_sync.py
    │       │   │   └── utils.py
    │       │   └── teams/
    │       │       └── doc_sync.py
    │       ├── onyxbot/
    │       │   └── slack/
    │       │       └── handlers/
    │       │           ├── __init__.py
    │       │           └── handle_standard_answers.py
    │       ├── seeding/
    │       │   └── load_docs.py
    │       ├── server/
    │       │   ├── __init__.py
    │       │   ├── auth_check.py
    │       │   ├── saml.py
    │       │   ├── seeding.py
    │       │   ├── analytics/
    │       │   │   └── api.py
    │       │   ├── documents/
    │       │   │   └── cc_pair.py
    │       │   ├── enterprise_settings/
    │       │   │   ├── api.py
    │       │   │   ├── models.py
    │       │   │   └── store.py
    │       │   ├── manage/
    │       │   │   └── standard_answer.py
    │       │   ├── middleware/
    │       │   │   └── tenant_tracking.py
    │       │   ├── oauth/
    │       │   │   ├── api.py
    │       │   │   ├── api_router.py
    │       │   │   ├── confluence_cloud.py
    │       │   │   ├── google_drive.py
    │       │   │   └── slack.py
    │       │   ├── query_and_chat/
    │       │   │   ├── __init__.py
    │       │   │   ├── chat_backend.py
    │       │   │   ├── models.py
    │       │   │   ├── query_backend.py
    │       │   │   └── token_limit.py
    │       │   ├── query_history/
    │       │   │   ├── api.py
    │       │   │   └── models.py
    │       │   ├── reporting/
    │       │   │   ├── usage_export_api.py
    │       │   │   ├── usage_export_generation.py
    │       │   │   └── usage_export_models.py
    │       │   ├── tenants/
    │       │   │   ├── __init__.py
    │       │   │   ├── access.py
    │       │   │   ├── admin_api.py
    │       │   │   ├── anonymous_user_path.py
    │       │   │   ├── anonymous_users_api.py
    │       │   │   ├── api.py
    │       │   │   ├── billing.py
    │       │   │   ├── billing_api.py
    │       │   │   ├── models.py
    │       │   │   ├── product_gating.py
    │       │   │   ├── provisioning.py
    │       │   │   ├── schema_management.py
    │       │   │   ├── team_membership_api.py
    │       │   │   ├── tenant_management_api.py
    │       │   │   ├── user_invitations_api.py
    │       │   │   └── user_mapping.py
    │       │   ├── token_rate_limits/
    │       │   │   └── api.py
    │       │   └── user_group/
    │       │       ├── api.py
    │       │       └── models.py
    │       └── utils/
    │           ├── __init__.py
    │           ├── encryption.py
    │           ├── secrets.py
    │           └── telemetry.py
    ├── generated/
    │   └── README.md
    ├── model_server/
    │   ├── __init__.py
    │   ├── constants.py
    │   ├── custom_models.py
    │   ├── encoders.py
    │   ├── main.py
    │   ├── management_endpoints.py
    │   ├── onyx_torch_model.py
    │   └── utils.py
    ├── onyx/
    │   ├── __init__.py
    │   ├── main.py
    │   ├── setup.py
    │   ├── access/
    │   │   ├── __init__.py
    │   │   ├── access.py
    │   │   ├── models.py
    │   │   └── utils.py
    │   ├── agents/
    │   │   └── agent_search/
    │   │       ├── core_state.py
    │   │       ├── models.py
    │   │       ├── run_graph.py
    │   │       ├── basic/
    │   │       │   ├── graph_builder.py
    │   │       │   ├── states.py
    │   │       │   └── utils.py
    │   │       ├── dc_search_analysis/
    │   │       │   ├── edges.py
    │   │       │   ├── graph_builder.py
    │   │       │   ├── ops.py
    │   │       │   ├── states.py
    │   │       │   └── nodes/
    │   │       │       ├── a1_search_objects.py
    │   │       │       ├── a2_research_object_source.py
    │   │       │       ├── a3_structure_research_by_object.py
    │   │       │       ├── a4_consolidate_object_research.py
    │   │       │       └── a5_consolidate_research.py
    │   │       ├── deep_search/
    │   │       │   ├── initial/
    │   │       │   │   ├── generate_individual_sub_answer/
    │   │       │   │   │   ├── edges.py
    │   │       │   │   │   ├── graph_builder.py
    │   │       │   │   │   ├── states.py
    │   │       │   │   │   └── nodes/
    │   │       │   │   │       ├── check_sub_answer.py
    │   │       │   │   │       ├── format_sub_answer.py
    │   │       │   │   │       ├── generate_sub_answer.py
    │   │       │   │   │       └── ingest_retrieved_documents.py
    │   │       │   │   ├── generate_initial_answer/
    │   │       │   │   │   ├── edges.py
    │   │       │   │   │   ├── graph_builder.py
    │   │       │   │   │   ├── states.py
    │   │       │   │   │   └── nodes/
    │   │       │   │   │       ├── generate_initial_answer.py
    │   │       │   │   │       └── validate_initial_answer.py
    │   │       │   │   ├── generate_sub_answers/
    │   │       │   │   │   ├── edges.py
    │   │       │   │   │   ├── graph_builder.py
    │   │       │   │   │   ├── states.py
    │   │       │   │   │   └── nodes/
    │   │       │   │   │       ├── decompose_orig_question.py
    │   │       │   │   │       └── format_initial_sub_answers.py
    │   │       │   │   └── retrieve_orig_question_docs/
    │   │       │   │       ├── graph_builder.py
    │   │       │   │       ├── states.py
    │   │       │   │       └── nodes/
    │   │       │   │           ├── format_orig_question_search_input.py
    │   │       │   │           └── format_orig_question_search_output.py
    │   │       │   ├── main/
    │   │       │   │   ├── edges.py
    │   │       │   │   ├── graph_builder.py
    │   │       │   │   ├── models.py
    │   │       │   │   ├── operations.py
    │   │       │   │   ├── states.py
    │   │       │   │   └── nodes/
    │   │       │   │       ├── compare_answers.py
    │   │       │   │       ├── create_refined_sub_questions.py
    │   │       │   │       ├── decide_refinement_need.py
    │   │       │   │       ├── extract_entities_terms.py
    │   │       │   │       ├── generate_validate_refined_answer.py
    │   │       │   │       ├── ingest_refined_sub_answers.py
    │   │       │   │       ├── persist_agent_results.py
    │   │       │   │       └── start_agent_search.py
    │   │       │   ├── refinement/
    │   │       │   │   └── consolidate_sub_answers/
    │   │       │   │       ├── edges.py
    │   │       │   │       └── graph_builder.py
    │   │       │   └── shared/
    │   │       │       └── expanded_retrieval/
    │   │       │           ├── edges.py
    │   │       │           ├── graph_builder.py
    │   │       │           ├── models.py
    │   │       │           ├── operations.py
    │   │       │           ├── states.py
    │   │       │           └── nodes/
    │   │       │               ├── expand_queries.py
    │   │       │               ├── format_queries.py
    │   │       │               ├── format_results.py
    │   │       │               ├── kickoff_verification.py
    │   │       │               ├── rerank_documents.py
    │   │       │               ├── retrieve_documents.py
    │   │       │               └── verify_documents.py
    │   │       ├── kb_search/
    │   │       │   ├── conditional_edges.py
    │   │       │   ├── graph_builder.py
    │   │       │   ├── graph_utils.py
    │   │       │   ├── models.py
    │   │       │   ├── ops.py
    │   │       │   ├── states.py
    │   │       │   ├── step_definitions.py
    │   │       │   └── nodes/
    │   │       │       ├── a1_extract_ert.py
    │   │       │       ├── a2_analyze.py
    │   │       │       ├── a3_generate_simple_sql.py
    │   │       │       ├── b1_construct_deep_search_filters.py
    │   │       │       ├── b2p_process_individual_deep_search.py
    │   │       │       ├── b2s_filtered_search.py
    │   │       │       ├── b3_consolidate_individual_deep_search.py
    │   │       │       ├── c1_process_kg_only_answers.py
    │   │       │       ├── d1_generate_answer.py
    │   │       │       └── d2_logging_node.py
    │   │       ├── orchestration/
    │   │       │   ├── states.py
    │   │       │   └── nodes/
    │   │       │       ├── call_tool.py
    │   │       │       ├── choose_tool.py
    │   │       │       ├── prepare_tool_input.py
    │   │       │       └── use_tool_response.py
    │   │       └── shared_graph_utils/
    │   │           ├── agent_prompt_ops.py
    │   │           ├── calculations.py
    │   │           ├── constants.py
    │   │           ├── llm.py
    │   │           ├── models.py
    │   │           ├── operators.py
    │   │           └── utils.py
    │   ├── auth/
    │   │   ├── __init__.py
    │   │   ├── api_key.py
    │   │   ├── email_utils.py
    │   │   ├── invited_users.py
    │   │   ├── noauth_user.py
    │   │   ├── oauth_refresher.py
    │   │   ├── schemas.py
    │   │   └── users.py
    │   ├── background/
    │   │   ├── error_logging.py
    │   │   ├── task_utils.py
    │   │   ├── celery/
    │   │   │   ├── celery_k8s_probe.py
    │   │   │   ├── celery_redis.py
    │   │   │   ├── celery_utils.py
    │   │   │   ├── memory_monitoring.py
    │   │   │   ├── apps/
    │   │   │   │   ├── app_base.py
    │   │   │   │   ├── beat.py
    │   │   │   │   ├── client.py
    │   │   │   │   ├── heavy.py
    │   │   │   │   ├── indexing.py
    │   │   │   │   ├── kg_processing.py
    │   │   │   │   ├── light.py
    │   │   │   │   ├── monitoring.py
    │   │   │   │   ├── primary.py
    │   │   │   │   └── task_formatters.py
    │   │   │   ├── configs/
    │   │   │   │   ├── base.py
    │   │   │   │   ├── beat.py
    │   │   │   │   ├── client.py
    │   │   │   │   ├── heavy.py
    │   │   │   │   ├── indexing.py
    │   │   │   │   ├── kg_processing.py
    │   │   │   │   ├── light.py
    │   │   │   │   ├── monitoring.py
    │   │   │   │   └── primary.py
    │   │   │   ├── tasks/
    │   │   │   │   ├── beat_schedule.py
    │   │   │   │   ├── connector_deletion/
    │   │   │   │   │   └── tasks.py
    │   │   │   │   ├── indexing/
    │   │   │   │   │   ├── tasks.py
    │   │   │   │   │   └── utils.py
    │   │   │   │   ├── kg_processing/
    │   │   │   │   │   ├── kg_indexing.py
    │   │   │   │   │   ├── tasks.py
    │   │   │   │   │   └── utils.py
    │   │   │   │   ├── llm_model_update/
    │   │   │   │   │   └── tasks.py
    │   │   │   │   ├── monitoring/
    │   │   │   │   │   └── tasks.py
    │   │   │   │   ├── periodic/
    │   │   │   │   │   ├── __init__.py
    │   │   │   │   │   └── tasks.py
    │   │   │   │   ├── pruning/
    │   │   │   │   │   └── tasks.py
    │   │   │   │   ├── shared/
    │   │   │   │   │   ├── RetryDocumentIndex.py
    │   │   │   │   │   └── tasks.py
    │   │   │   │   ├── user_file_folder_sync/
    │   │   │   │   │   └── tasks.py
    │   │   │   │   └── vespa/
    │   │   │   │       └── tasks.py
    │   │   │   └── versioned_apps/
    │   │   │       ├── beat.py
    │   │   │       ├── client.py
    │   │   │       ├── heavy.py
    │   │   │       ├── indexing.py
    │   │   │       ├── kg_processing.py
    │   │   │       ├── light.py
    │   │   │       ├── monitoring.py
    │   │   │       └── primary.py
    │   │   └── indexing/
    │   │       ├── checkpointing_utils.py
    │   │       ├── dask_utils.py
    │   │       ├── job_client.py
    │   │       ├── memory_tracer.py
    │   │       ├── models.py
    │   │       └── run_indexing.py
    │   ├── chat/
    │   │   ├── __init__.py
    │   │   ├── answer.py
    │   │   ├── chat_utils.py
    │   │   ├── llm_response_handler.py
    │   │   ├── models.py
    │   │   ├── process_message.py
    │   │   ├── prune_and_merge.py
    │   │   ├── prompt_builder/
    │   │   │   ├── answer_prompt_builder.py
    │   │   │   ├── citations_prompt.py
    │   │   │   ├── quotes_prompt.py
    │   │   │   └── utils.py
    │   │   ├── stream_processing/
    │   │   │   ├── answer_response_handler.py
    │   │   │   ├── citation_processing.py
    │   │   │   ├── quotes_processing.py
    │   │   │   └── utils.py
    │   │   ├── tool_handling/
    │   │   │   └── tool_response_handler.py
    │   │   └── user_files/
    │   │       └── parse_user_files.py
    │   ├── configs/
    │   │   ├── __init__.py
    │   │   ├── agent_configs.py
    │   │   ├── app_configs.py
    │   │   ├── chat_configs.py
    │   │   ├── constants.py
    │   │   ├── embedding_configs.py
    │   │   ├── kg_configs.py
    │   │   ├── llm_configs.py
    │   │   ├── model_configs.py
    │   │   ├── onyxbot_configs.py
    │   │   └── tool_configs.py
    │   ├── connectors/
    │   │   ├── README.md
    │   │   ├── __init__.py
    │   │   ├── connector_runner.py
    │   │   ├── credentials_provider.py
    │   │   ├── exceptions.py
    │   │   ├── factory.py
    │   │   ├── interfaces.py
    │   │   ├── models.py
    │   │   ├── airtable/
    │   │   │   └── airtable_connector.py
    │   │   ├── asana/
    │   │   │   ├── __init__.py
    │   │   │   ├── asana_api.py
    │   │   │   └── connector.py
    │   │   ├── axero/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── blob/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── bookstack/
    │   │   │   ├── __init__.py
    │   │   │   ├── client.py
    │   │   │   └── connector.py
    │   │   ├── clickup/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── confluence/
    │   │   │   ├── __init__.py
    │   │   │   ├── access.py
    │   │   │   ├── connector.py
    │   │   │   ├── models.py
    │   │   │   ├── onyx_confluence.py
    │   │   │   ├── user_profile_override.py
    │   │   │   └── utils.py
    │   │   ├── cross_connector_utils/
    │   │   │   ├── __init__.py
    │   │   │   ├── miscellaneous_utils.py
    │   │   │   └── rate_limit_wrapper.py
    │   │   ├── discord/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── discourse/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── document360/
    │   │   │   ├── __init__.py
    │   │   │   ├── connector.py
    │   │   │   └── utils.py
    │   │   ├── dropbox/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── egnyte/
    │   │   │   └── connector.py
    │   │   ├── file/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── fireflies/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── freshdesk/
    │   │   │   ├── __init__,py
    │   │   │   └── connector.py
    │   │   ├── gitbook/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── github/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── gitlab/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── gmail/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── gong/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── google_drive/
    │   │   │   ├── __init__.py
    │   │   │   ├── connector.py
    │   │   │   ├── constants.py
    │   │   │   ├── doc_conversion.py
    │   │   │   ├── file_retrieval.py
    │   │   │   ├── models.py
    │   │   │   └── section_extraction.py
    │   │   ├── google_site/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── google_utils/
    │   │   │   ├── __init__.py
    │   │   │   ├── google_auth.py
    │   │   │   ├── google_kv.py
    │   │   │   ├── google_utils.py
    │   │   │   ├── resources.py
    │   │   │   └── shared_constants.py
    │   │   ├── guru/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── highspot/
    │   │   │   ├── __init__.py
    │   │   │   ├── client.py
    │   │   │   ├── connector.py
    │   │   │   └── utils.py
    │   │   ├── hubspot/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── imap/
    │   │   │   ├── __init__.py
    │   │   │   ├── connector.py
    │   │   │   └── models.py
    │   │   ├── jira/
    │   │   │   ├── __init__.py
    │   │   │   ├── access.py
    │   │   │   ├── connector.py
    │   │   │   └── utils.py
    │   │   ├── linear/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── loopio/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── mediawiki/
    │   │   │   ├── __init__.py
    │   │   │   ├── family.py
    │   │   │   └── wiki.py
    │   │   ├── mock_connector/
    │   │   │   └── connector.py
    │   │   ├── notion/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── productboard/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── requesttracker/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── salesforce/
    │   │   │   ├── __init__.py
    │   │   │   ├── blacklist.py
    │   │   │   ├── connector.py
    │   │   │   ├── doc_conversion.py
    │   │   │   ├── onyx_salesforce.py
    │   │   │   ├── salesforce_calls.py
    │   │   │   ├── sqlite_functions.py
    │   │   │   ├── utils.py
    │   │   │   └── shelve_stuff/
    │   │   │       ├── old_test_salesforce_shelves.py
    │   │   │       ├── shelve_functions.py
    │   │   │       ├── shelve_utils.py
    │   │   │       └── test_salesforce_shelves.py
    │   │   ├── sharepoint/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── slab/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── slack/
    │   │   │   ├── __init__.py
    │   │   │   ├── access.py
    │   │   │   ├── connector.py
    │   │   │   ├── models.py
    │   │   │   ├── onyx_retry_handler.py
    │   │   │   ├── onyx_slack_web_client.py
    │   │   │   └── utils.py
    │   │   ├── teams/
    │   │   │   ├── __init__.py
    │   │   │   ├── connector.py
    │   │   │   ├── models.py
    │   │   │   └── utils.py
    │   │   ├── web/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── wikipedia/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── xenforo/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   ├── zendesk/
    │   │   │   ├── __init__.py
    │   │   │   └── connector.py
    │   │   └── zulip/
    │   │       ├── __init__.py
    │   │       ├── connector.py
    │   │       ├── schemas.py
    │   │       └── utils.py
    │   ├── context/
    │   │   └── search/
    │   │       ├── __init__.py
    │   │       ├── enums.py
    │   │       ├── models.py
    │   │       ├── pipeline.py
    │   │       ├── search_settings.py
    │   │       ├── utils.py
    │   │       ├── federated/
    │   │       │   ├── models.py
    │   │       │   └── slack_search.py
    │   │       ├── postprocessing/
    │   │       │   └── postprocessing.py
    │   │       ├── preprocessing/
    │   │       │   ├── access_filters.py
    │   │       │   └── preprocessing.py
    │   │       └── retrieval/
    │   │           └── search_runner.py
    │   ├── db/
    │   │   ├── __init__.py
    │   │   ├── api_key.py
    │   │   ├── auth.py
    │   │   ├── background_error.py
    │   │   ├── chat.py
    │   │   ├── chat_search.py
    │   │   ├── chunk.py
    │   │   ├── connector.py
    │   │   ├── connector_credential_pair.py
    │   │   ├── constants.py
    │   │   ├── credentials.py
    │   │   ├── deletion_attempt.py
    │   │   ├── document.py
    │   │   ├── document_set.py
    │   │   ├── entities.py
    │   │   ├── entity_type.py
    │   │   ├── enums.py
    │   │   ├── federated.py
    │   │   ├── feedback.py
    │   │   ├── file_record.py
    │   │   ├── folder.py
    │   │   ├── index_attempt.py
    │   │   ├── input_prompt.py
    │   │   ├── kg_config.py
    │   │   ├── kg_temp_view.py
    │   │   ├── llm.py
    │   │   ├── milestone.py
    │   │   ├── models.py
    │   │   ├── notification.py
    │   │   ├── persona.py
    │   │   ├── prompts.py
    │   │   ├── pydantic_type.py
    │   │   ├── relationships.py
    │   │   ├── search_settings.py
    │   │   ├── slack_bot.py
    │   │   ├── slack_channel_config.py
    │   │   ├── swap_index.py
    │   │   ├── sync_record.py
    │   │   ├── tag.py
    │   │   ├── tasks.py
    │   │   ├── token_limit.py
    │   │   ├── tools.py
    │   │   ├── user_documents.py
    │   │   ├── users.py
    │   │   ├── utils.py
    │   │   ├── _deprecated/
    │   │   │   └── pg_file_store.py
    │   │   ├── engine/
    │   │   │   ├── __init__.py
    │   │   │   ├── async_sql_engine.py
    │   │   │   ├── connection_warmup.py
    │   │   │   ├── iam_auth.py
    │   │   │   ├── sql_engine.py
    │   │   │   ├── tenant_utils.py
    │   │   │   └── time_utils.py
    │   │   └── seeding/
    │   │       └── chat_history_seeding.py
    │   ├── document_index/
    │   │   ├── __init__.py
    │   │   ├── document_index_utils.py
    │   │   ├── factory.py
    │   │   ├── interfaces.py
    │   │   ├── vespa_constants.py
    │   │   └── vespa/
    │   │       ├── __init__.py
    │   │       ├── chunk_retrieval.py
    │   │       ├── deletion.py
    │   │       ├── index.py
    │   │       ├── indexing_utils.py
    │   │       ├── kg_interactions.py
    │   │       ├── app_config/
    │   │       │   ├── services.xml.jinja
    │   │       │   ├── validation-overrides.xml.jinja
    │   │       │   └── schemas/
    │   │       │       └── danswer_chunk.sd.jinja
    │   │       └── shared_utils/
    │   │           ├── utils.py
    │   │           └── vespa_request_builders.py
    │   ├── federated_connectors/
    │   │   ├── __init__.py
    │   │   ├── factory.py
    │   │   ├── federated_retrieval.py
    │   │   ├── interfaces.py
    │   │   ├── models.py
    │   │   ├── oauth_utils.py
    │   │   └── slack/
    │   │       ├── __init__.py
    │   │       ├── federated_connector.py
    │   │       └── models.py
    │   ├── file_processing/
    │   │   ├── __init__.py
    │   │   ├── enums.py
    │   │   ├── extract_file_text.py
    │   │   ├── file_validation.py
    │   │   ├── html_utils.py
    │   │   ├── image_summarization.py
    │   │   ├── image_utils.py
    │   │   └── unstructured.py
    │   ├── file_store/
    │   │   ├── README.md
    │   │   ├── constants.py
    │   │   ├── file_store.py
    │   │   ├── models.py
    │   │   ├── s3_key_utils.py
    │   │   └── utils.py
    │   ├── httpx/
    │   │   └── httpx_pool.py
    │   ├── indexing/
    │   │   ├── __init__.py
    │   │   ├── chunker.py
    │   │   ├── content_classification.py
    │   │   ├── embedder.py
    │   │   ├── indexing_heartbeat.py
    │   │   ├── indexing_pipeline.py
    │   │   ├── models.py
    │   │   └── vector_db_insertion.py
    │   ├── key_value_store/
    │   │   ├── __init__.py
    │   │   ├── factory.py
    │   │   ├── interface.py
    │   │   └── store.py
    │   ├── kg/
    │   │   ├── models.py
    │   │   ├── clustering/
    │   │   │   ├── clustering.py
    │   │   │   └── normalizations.py
    │   │   ├── extractions/
    │   │   │   └── extraction_processing.py
    │   │   ├── resets/
    │   │   │   ├── reset_index.py
    │   │   │   ├── reset_source.py
    │   │   │   └── reset_vespa.py
    │   │   ├── setup/
    │   │   │   └── kg_default_entity_definitions.py
    │   │   ├── utils/
    │   │   │   ├── embeddings.py
    │   │   │   ├── extraction_utils.py
    │   │   │   └── formatting_utils.py
    │   │   └── vespa/
    │   │       └── vespa_interactions.py
    │   ├── llm/
    │   │   ├── __init__.py
    │   │   ├── chat_llm.py
    │   │   ├── custom_llm.py
    │   │   ├── exceptions.py
    │   │   ├── factory.py
    │   │   ├── interfaces.py
    │   │   ├── llm_provider_options.py
    │   │   ├── models.py
    │   │   ├── override_models.py
    │   │   └── utils.py
    │   ├── natural_language_processing/
    │   │   ├── __init__.py
    │   │   ├── exceptions.py
    │   │   ├── search_nlp_models.py
    │   │   └── utils.py
    │   ├── onyxbot/
    │   │   └── slack/
    │   │       ├── blocks.py
    │   │       ├── config.py
    │   │       ├── constants.py
    │   │       ├── formatting.py
    │   │       ├── icons.py
    │   │       ├── listener.py
    │   │       ├── models.py
    │   │       ├── utils.py
    │   │       └── handlers/
    │   │           ├── __init__.py
    │   │           ├── handle_buttons.py
    │   │           ├── handle_message.py
    │   │           ├── handle_regular_answer.py
    │   │           ├── handle_standard_answers.py
    │   │           └── utils.py
    │   ├── prompts/
    │   │   ├── __init__.py
    │   │   ├── agent_search.py
    │   │   ├── agentic_evaluation.py
    │   │   ├── answer_validation.py
    │   │   ├── chat_prompts.py
    │   │   ├── chat_tools.py
    │   │   ├── constants.py
    │   │   ├── direct_qa_prompts.py
    │   │   ├── federated_search.py
    │   │   ├── filter_extration.py
    │   │   ├── image_analysis.py
    │   │   ├── kg_prompts.py
    │   │   ├── llm_chunk_filter.py
    │   │   ├── miscellaneous_prompts.py
    │   │   ├── prompt_utils.py
    │   │   ├── query_validation.py
    │   │   ├── starter_messages.py
    │   │   ├── token_counts.py
    │   │   └── agents/
    │   │       └── dc_prompts.py
    │   ├── redis/
    │   │   ├── redis_connector.py
    │   │   ├── redis_connector_credential_pair.py
    │   │   ├── redis_connector_delete.py
    │   │   ├── redis_connector_doc_perm_sync.py
    │   │   ├── redis_connector_ext_group_sync.py
    │   │   ├── redis_connector_index.py
    │   │   ├── redis_connector_prune.py
    │   │   ├── redis_connector_stop.py
    │   │   ├── redis_connector_utils.py
    │   │   ├── redis_document_set.py
    │   │   ├── redis_object_helper.py
    │   │   ├── redis_pool.py
    │   │   ├── redis_usergroup.py
    │   │   └── redis_utils.py
    │   ├── secondary_llm_flows/
    │   │   ├── __init__.py
    │   │   ├── agentic_evaluation.py
    │   │   ├── answer_validation.py
    │   │   ├── chat_session_naming.py
    │   │   ├── choose_search.py
    │   │   ├── chunk_usefulness.py
    │   │   ├── query_expansion.py
    │   │   ├── query_validation.py
    │   │   ├── source_filter.py
    │   │   ├── starter_message_creation.py
    │   │   └── time_filter.py
    │   ├── seeding/
    │   │   ├── __init__.py
    │   │   ├── initial_docs.json
    │   │   ├── initial_docs_cohere.json
    │   │   ├── input_prompts.yaml
    │   │   ├── load_docs.py
    │   │   ├── load_yamls.py
    │   │   ├── personas.yaml
    │   │   ├── prompts.yaml
    │   │   └── user_folders.yaml
    │   ├── server/
    │   │   ├── __init__.py
    │   │   ├── auth_check.py
    │   │   ├── models.py
    │   │   ├── utils.py
    │   │   ├── api_key/
    │   │   │   ├── api.py
    │   │   │   └── models.py
    │   │   ├── documents/
    │   │   │   ├── __init__.py
    │   │   │   ├── cc_pair.py
    │   │   │   ├── connector.py
    │   │   │   ├── credential.py
    │   │   │   ├── document.py
    │   │   │   ├── models.py
    │   │   │   └── standard_oauth.py
    │   │   ├── features/
    │   │   │   ├── __init__.py
    │   │   │   ├── document_set/
    │   │   │   │   ├── __init__.py
    │   │   │   │   ├── api.py
    │   │   │   │   └── models.py
    │   │   │   ├── folder/
    │   │   │   │   ├── __init__.py
    │   │   │   │   ├── api.py
    │   │   │   │   └── models.py
    │   │   │   ├── input_prompt/
    │   │   │   │   ├── api.py
    │   │   │   │   └── models.py
    │   │   │   ├── notifications/
    │   │   │   │   └── api.py
    │   │   │   ├── password/
    │   │   │   │   ├── api.py
    │   │   │   │   └── models.py
    │   │   │   ├── persona/
    │   │   │   │   ├── __init__.py
    │   │   │   │   ├── api.py
    │   │   │   │   └── models.py
    │   │   │   └── tool/
    │   │   │       ├── api.py
    │   │   │       └── models.py
    │   │   ├── federated/
    │   │   │   ├── api.py
    │   │   │   └── models.py
    │   │   ├── gpts/
    │   │   │   └── api.py
    │   │   ├── kg/
    │   │   │   ├── api.py
    │   │   │   └── models.py
    │   │   ├── long_term_logs/
    │   │   │   └── long_term_logs_api.py
    │   │   ├── manage/
    │   │   │   ├── __init__.py
    │   │   │   ├── administrative.py
    │   │   │   ├── get_state.py
    │   │   │   ├── models.py
    │   │   │   ├── search_settings.py
    │   │   │   ├── slack_bot.py
    │   │   │   ├── users.py
    │   │   │   ├── validate_tokens.py
    │   │   │   ├── embedding/
    │   │   │   │   ├── api.py
    │   │   │   │   └── models.py
    │   │   │   └── llm/
    │   │   │       ├── api.py
    │   │   │       └── models.py
    │   │   ├── middleware/
    │   │   │   ├── latency_logging.py
    │   │   │   └── rate_limiting.py
    │   │   ├── onyx_api/
    │   │   │   ├── __init__.py
    │   │   │   ├── ingestion.py
    │   │   │   └── models.py
    │   │   ├── openai_assistants_api/
    │   │   │   ├── asssistants_api.py
    │   │   │   ├── full_openai_assistants_api.py
    │   │   │   ├── messages_api.py
    │   │   │   ├── runs_api.py
    │   │   │   └── threads_api.py
    │   │   ├── query_and_chat/
    │   │   │   ├── __init__.py
    │   │   │   ├── chat_backend.py
    │   │   │   ├── chat_utils.py
    │   │   │   ├── models.py
    │   │   │   ├── query_backend.py
    │   │   │   └── token_limit.py
    │   │   ├── runtime/
    │   │   │   └── onyx_runtime.py
    │   │   ├── settings/
    │   │   │   ├── api.py
    │   │   │   ├── models.py
    │   │   │   └── store.py
    │   │   ├── token_rate_limits/
    │   │   │   ├── api.py
    │   │   │   └── models.py
    │   │   └── user_documents/
    │   │       ├── api.py
    │   │       └── models.py
    │   ├── tools/
    │   │   ├── base_tool.py
    │   │   ├── built_in_tools.py
    │   │   ├── force.py
    │   │   ├── message.py
    │   │   ├── models.py
    │   │   ├── tool.py
    │   │   ├── tool_constructor.py
    │   │   ├── tool_runner.py
    │   │   ├── tool_selection.py
    │   │   ├── utils.py
    │   │   └── tool_implementations/
    │   │       ├── search_like_tool_utils.py
    │   │       ├── custom/
    │   │       │   ├── base_tool_types.py
    │   │       │   ├── custom_tool.py
    │   │       │   ├── custom_tool_prompts.py
    │   │       │   ├── openapi_parsing.py
    │   │       │   └── prompt.py
    │   │       ├── images/
    │   │       │   ├── image_generation_tool.py
    │   │       │   └── prompt.py
    │   │       ├── internet_search/
    │   │       │   ├── internet_search_tool.py
    │   │       │   └── models.py
    │   │       └── search/
    │   │           ├── search_tool.py
    │   │           └── search_utils.py
    │   └── utils/
    │       ├── __init__.py
    │       ├── b64.py
    │       ├── batching.py
    │       ├── callbacks.py
    │       ├── encryption.py
    │       ├── error_handling.py
    │       ├── errors.py
    │       ├── file.py
    │       ├── file_types.py
    │       ├── gpu_utils.py
    │       ├── headers.py
    │       ├── logger.py
    │       ├── long_term_log.py
    │       ├── middleware.py
    │       ├── object_size_check.py
    │       ├── retry_wrapper.py
    │       ├── sitemap.py
    │       ├── special_types.py
    │       ├── subclasses.py
    │       ├── supervisord_watchdog.py
    │       ├── telemetry.py
    │       ├── text_processing.py
    │       ├── threadpool_concurrency.py
    │       ├── timing.py
    │       ├── url.py
    │       └── variable_functionality.py
    ├── requirements/
    │   ├── combined.txt
    │   ├── default.txt
    │   ├── dev.txt
    │   ├── ee.txt
    │   └── model_server.txt
    ├── scripts/
    │   ├── add_connector_creation_script.py
    │   ├── api_inference_sample.py
    │   ├── celery_purge_queue.py
    │   ├── chat_feedback_dump.py
    │   ├── chat_history_seeding.py
    │   ├── chat_loadtest.py
    │   ├── decrypt.py
    │   ├── dev_run_background_jobs.py
    │   ├── docker_memory_tracking.sh
    │   ├── document_seeding_prep.py
    │   ├── force_delete_connector_by_id.py
    │   ├── hard_delete_chats.py
    │   ├── onyx_openapi_schema.py
    │   ├── orphan_doc_cleanup_script.py
    │   ├── reset_indexes.py
    │   ├── reset_postgres.py
    │   ├── restart_containers.sh
    │   ├── resume_paused_connectors.py
    │   ├── save_load_state.py
    │   ├── sources_selection_analysis.py
    │   ├── test-openapi-key.py
    │   ├── debugging/
    │   │   ├── onyx_db.py
    │   │   ├── onyx_list_tenants.py
    │   │   ├── onyx_redis.py
    │   │   ├── onyx_vespa.py
    │   │   └── onyx_vespa_schemas.py
    │   └── query_time_check/
    │       ├── seed_dummy_docs.py
    │       └── test_query_times.py
    ├── shared_configs/
    │   ├── __init__.py
    │   ├── configs.py
    │   ├── contextvars.py
    │   ├── enums.py
    │   ├── model_server_models.py
    │   └── utils.py
    ├── slackbot_images/
    │   └── README.md
    └── tests/
        ├── __init__.py
        ├── load_env_vars.py
        ├── api/
        │   └── test_api.py
        ├── daily/
        │   ├── conftest.py
        │   ├── connectors/
        │   │   ├── conftest.py
        │   │   ├── utils.py
        │   │   ├── airtable/
        │   │   │   └── test_airtable_basic.py
        │   │   ├── blob/
        │   │   │   └── test_blob_connector.py
        │   │   ├── confluence/
        │   │   │   ├── test_confluence_basic.py
        │   │   │   ├── test_confluence_permissions_basic.py
        │   │   │   └── test_confluence_user_email_overrides.py
        │   │   ├── discord/
        │   │   │   ├── test_discord_connector.py
        │   │   │   └── test_discord_data.json
        │   │   ├── file/
        │   │   │   └── test_file_connector.py
        │   │   ├── fireflies/
        │   │   │   ├── test_fireflies_connector.py
        │   │   │   └── test_fireflies_data.json
        │   │   ├── gitbook/
        │   │   │   └── test_gitbook_connector.py
        │   │   ├── github/
        │   │   │   └── test_github_basic.py
        │   │   ├── gitlab/
        │   │   │   └── test_gitlab_basic.py
        │   │   ├── gmail/
        │   │   │   ├── conftest.py
        │   │   │   └── test_gmail_connector.py
        │   │   ├── gong/
        │   │   │   └── test_gong.py
        │   │   ├── google_drive/
        │   │   │   ├── conftest.py
        │   │   │   ├── consts_and_utils.py
        │   │   │   ├── drive_id_mapping.json
        │   │   │   ├── test_admin_oauth.py
        │   │   │   ├── test_drive_perm_sync.py
        │   │   │   ├── test_map_test_ids.py
        │   │   │   ├── test_sections.py
        │   │   │   ├── test_service_acct.py
        │   │   │   └── test_user_1_oauth.py
        │   │   ├── highspot/
        │   │   │   ├── test_highspot_connector.py
        │   │   │   └── test_highspot_data.json
        │   │   ├── hubspot/
        │   │   │   └── test_hubspot_connector.py
        │   │   ├── imap/
        │   │   │   ├── models.py
        │   │   │   └── test_imap_connector.py
        │   │   ├── jira/
        │   │   │   └── test_jira_basic.py
        │   │   ├── notion/
        │   │   │   └── test_notion_connector.py
        │   │   ├── salesforce/
        │   │   │   ├── test_salesforce_connector.py
        │   │   │   └── test_salesforce_data.json
        │   │   ├── sharepoint/
        │   │   │   └── test_sharepoint_connector.py
        │   │   ├── slab/
        │   │   │   ├── test_slab_connector.py
        │   │   │   └── test_slab_data.json
        │   │   ├── slack/
        │   │   │   ├── conftest.py
        │   │   │   ├── test_slack_connector.py
        │   │   │   └── test_slack_perm_sync.py
        │   │   ├── teams/
        │   │   │   ├── models.py
        │   │   │   └── test_teams_connector.py
        │   │   ├── web/
        │   │   │   └── test_web_connector.py
        │   │   └── zendesk/
        │   │       ├── test_zendesk_connector.py
        │   │       └── test_zendesk_data.json
        │   ├── embedding/
        │   │   └── test_embeddings.py
        │   └── llm/
        │       └── test_bedrock.py
        ├── external_dependency_unit/
        │   ├── conftest.py
        │   ├── constants.py
        │   ├── external_group_sync/
        │   │   └── test_external_group_sync.py
        │   └── file_store/
        │       └── test_file_store_non_mocked.py
        ├── integration/
        │   ├── README.md
        │   ├── __init__.py
        │   ├── conftest.py
        │   ├── Dockerfile
        │   ├── common_utils/
        │   │   ├── chat.py
        │   │   ├── config.py
        │   │   ├── constants.py
        │   │   ├── reset.py
        │   │   ├── test_document_utils.py
        │   │   ├── test_models.py
        │   │   ├── timeout.py
        │   │   ├── vespa.py
        │   │   └── managers/
        │   │       ├── api_key.py
        │   │       ├── cc_pair.py
        │   │       ├── chat.py
        │   │       ├── connector.py
        │   │       ├── credential.py
        │   │       ├── document.py
        │   │       ├── document_search.py
        │   │       ├── document_set.py
        │   │       ├── file.py
        │   │       ├── index_attempt.py
        │   │       ├── llm_provider.py
        │   │       ├── persona.py
        │   │       ├── query_history.py
        │   │       ├── settings.py
        │   │       ├── tenant.py
        │   │       ├── user.py
        │   │       └── user_group.py
        │   ├── connector_job_tests/
        │   │   ├── google/
        │   │   │   ├── google_drive_api_utils.py
        │   │   │   └── test_google_drive_permission_sync.py
        │   │   ├── jira/
        │   │   │   ├── conftest.py
        │   │   │   └── test_jira_permission_sync.py
        │   │   └── slack/
        │   │       ├── conftest.py
        │   │       ├── slack_api_utils.py
        │   │       ├── test_permission_sync.py
        │   │       └── test_prune.py
        │   ├── mock_services/
        │   │   ├── docker-compose.mock-it-services.yml
        │   │   └── mock_connector_server/
        │   │       ├── Dockerfile
        │   │       └── main.py
        │   ├── multitenant_tests/
        │   │   ├── invitation/
        │   │   │   └── test_user_invitation.py
        │   │   ├── syncing/
        │   │   │   └── test_search_permissions.py
        │   │   └── tenants/
        │   │       └── test_tenant_creation.py
        │   ├── openai_assistants_api/
        │   │   ├── conftest.py
        │   │   ├── test_assistants.py
        │   │   ├── test_messages.py
        │   │   ├── test_runs.py
        │   │   └── test_threads.py
        │   └── tests/
        │       ├── anonymous_user/
        │       │   └── test_anonymous_user.py
        │       ├── api_key/
        │       │   └── test_api_key.py
        │       ├── auth/
        │       │   └── test_saml_user_conversion.py
        │       ├── chat/
        │       │   └── test_chat_deletion.py
        │       ├── chat_retention/
        │       │   └── test_chat_retention.py
        │       ├── connector/
        │       │   ├── test_connector_creation.py
        │       │   └── test_connector_deletion.py
        │       ├── dev_apis/
        │       │   ├── test_knowledge_chat.py
        │       │   └── test_simple_chat_api.py
        │       ├── document_set/
        │       │   └── test_syncing.py
        │       ├── image_indexing/
        │       │   └── test_indexing_images.py
        │       ├── index_attempt/
        │       │   └── test_index_attempt_pagination.py
        │       ├── indexing/
        │       │   ├── conftest.py
        │       │   ├── test_checkpointing.py
        │       │   ├── test_initial_permission_sync.py
        │       │   ├── test_polling.py
        │       │   ├── test_repeated_error_state.py
        │       │   └── file_connector/
        │       │       ├── test_file_connector_zip_metadata.py
        │       │       └── test_files/
        │       │           ├── sample1.txt
        │       │           ├── sample2.txt
        │       │           ├── with_meta.zip
        │       │           ├── without_meta.zip
        │       │           └── .onyx_metadata.json
        │       ├── kg/
        │       │   ├── test_kg_api.py
        │       │   └── test_kg_processing.py
        │       ├── llm_provider/
        │       │   └── test_llm_provider.py
        │       ├── migrations/
        │       │   └── test_migrations.py
        │       ├── permissions/
        │       │   ├── test_cc_pair_permissions.py
        │       │   ├── test_connector_permissions.py
        │       │   ├── test_credential_permissions.py
        │       │   ├── test_doc_set_permissions.py
        │       │   ├── test_persona_permissions.py
        │       │   ├── test_user_role_permissions.py
        │       │   └── test_whole_curator_flow.py
        │       ├── personas/
        │       │   └── test_persona_categories.py
        │       ├── playwright/
        │       │   └── test_playwright.py
        │       ├── pruning/
        │       │   ├── test_pruning.py
        │       │   └── website/
        │       │       ├── readme.txt
        │       │       ├── about.html
        │       │       ├── contact.html
        │       │       ├── courses.html
        │       │       ├── index.html
        │       │       ├── portfolio.html
        │       │       ├── pricing.html
        │       │       ├── css/
        │       │       │   ├── animate.css
        │       │       │   ├── custom-fonts.css
        │       │       │   ├── font-awesome.css
        │       │       │   ├── style.css
        │       │       │   └── fancybox/
        │       │       │       └── jquery.fancybox.css
        │       │       ├── fonts/
        │       │       │   ├── fontawesome-webfont.eot
        │       │       │   ├── fontawesome-webfont.ttf
        │       │       │   ├── fontawesome-webfont.woff
        │       │       │   ├── fontawesome.otf
        │       │       │   └── customicon/
        │       │       │       ├── icons.eot
        │       │       │       ├── icons.ttf
        │       │       │       └── icons.woff
        │       │       └── js/
        │       │           ├── animate.js
        │       │           ├── custom.js
        │       │           ├── jquery.easing.1.3.js
        │       │           ├── jquery.fancybox-media.js
        │       │           ├── jquery.fancybox.pack.js
        │       │           ├── jquery.flexslider.js
        │       │           ├── jquery.js
        │       │           ├── validate.js
        │       │           ├── flexslider/
        │       │           │   ├── jquery.flexslider.js
        │       │           │   └── setting.js
        │       │           ├── google-code-prettify/
        │       │           │   ├── prettify.css
        │       │           │   └── prettify.js
        │       │           ├── portfolio/
        │       │           │   ├── jquery.quicksand.js
        │       │           │   └── setting.js
        │       │           └── quicksand/
        │       │               ├── jquery.quicksand.js
        │       │               └── setting.js
        │       ├── query_history/
        │       │   ├── test_query_history.py
        │       │   ├── test_query_history_pagination.py
        │       │   ├── test_usage_reports.py
        │       │   └── utils.py
        │       ├── streaming_endpoints/
        │       │   ├── conftest.py
        │       │   └── test_chat_stream.py
        │       ├── tools/
        │       │   └── test_image_generation_tool.py
        │       ├── usergroup/
        │       │   ├── test_user_group_deletion.py
        │       │   └── test_usergroup_syncing.py
        │       └── users/
        │           └── test_user_pagination.py
        ├── regression/
        │   ├── answer_quality/
        │   │   ├── README.md
        │   │   ├── __init__.py
        │   │   ├── agent_test_script.py
        │   │   ├── api_utils.py
        │   │   ├── cli_utils.py
        │   │   ├── file_uploader.py
        │   │   ├── launch_eval_env.py
        │   │   ├── run_qa.py
        │   │   └── search_test_config.yaml.template
        │   └── search_quality/
        │       ├── README.md
        │       ├── run_search_eval.py
        │       ├── search_eval_config.yaml.template
        │       ├── test_queries.json.template
        │       ├── util_config.py
        │       ├── util_data.py
        │       ├── util_eval.py
        │       └── util_retrieve.py
        └── unit/
            ├── ee/
            │   └── onyx/
            │       └── external_permissions/
            │           └── salesforce/
            │               └── test_postprocessing.py
            ├── federated_connector/
            │   └── slack/
            │       └── test_slack_federated_connnector.py
            ├── file_store/
            │   └── test_file_store.py
            ├── model_server/
            │   ├── test_custom_models.py
            │   └── test_embedding.py
            └── onyx/
                ├── redis_ca.pem
                ├── test_redis.py
                ├── agent_search/
                │   └── test_use_tool_response.py
                ├── auth/
                │   ├── conftest.py
                │   ├── test_email.py
                │   └── test_oauth_refresher.py
                ├── celery/
                │   └── llm_model_update/
                │       └── test_llm_model_update.py
                ├── chat/
                │   ├── conftest.py
                │   ├── test_answer.py
                │   ├── test_prune_and_merge.py
                │   ├── test_skip_gen_ai.py
                │   └── stream_processing/
                │       ├── test_citation_processing.py
                │       ├── test_citation_substitution.py
                │       └── test_quotes_processing.py
                ├── connectors/
                │   ├── utils.py
                │   ├── confluence/
                │   │   ├── test_confluence_checkpointing.py
                │   │   ├── test_onyx_confluence.py
                │   │   └── test_rate_limit_handler.py
                │   ├── cross_connector_utils/
                │   │   ├── test_html_utils.py
                │   │   ├── test_rate_limit.py
                │   │   └── test_table.html
                │   ├── github/
                │   │   └── test_github_checkpointing.py
                │   ├── gmail/
                │   │   ├── test_connector.py
                │   │   └── thread.json
                │   ├── jira/
                │   │   ├── conftest.py
                │   │   ├── test_jira_checkpointing.py
                │   │   ├── test_jira_large_ticket_handling.py
                │   │   └── test_jira_permission_sync.py
                │   ├── mediawiki/
                │   │   ├── __init__.py
                │   │   ├── test_mediawiki_family.py
                │   │   └── test_wiki.py
                │   ├── salesforce/
                │   │   ├── test_account.csv
                │   │   └── test_salesforce_sqlite.py
                │   └── zendesk/
                │       └── test_zendesk_checkpointing.py
                ├── document_index/
                │   └── vespa/
                │       └── shared_utils/
                │           └── test_utils.py
                ├── indexing/
                │   ├── conftest.py
                │   ├── test_censoring.py
                │   ├── test_chunker.py
                │   ├── test_embedder.py
                │   ├── test_indexing_pipeline.py
                │   └── test_vespa.py
                ├── llm/
                │   ├── test_chat_llm.py
                │   └── test_model_is_reasoning.py
                ├── tools/
                │   ├── test_tool_utils.py
                │   └── custom/
                │       └── test_custom_tools.py
                └── utils/
                    ├── test_threadpool_concurrency.py
                    ├── test_threadpool_contextvars.py
                    └── test_vespa_query.py


Files Content:

(Files content cropped to 300k characters, download full ingest to see more)
================================================
FILE: backend/alembic.ini
================================================
# A generic, single database configuration.

[DEFAULT]
# path to migration scripts
script_location = alembic

# template used to generate migration file names; The default value is %%(rev)s_%%(slug)s
# Uncomment the line below if you want the files to be prepended with date and time
# file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s

# sys.path path, will be prepended to sys.path if present.
# defaults to the current working directory.
prepend_sys_path = .

# timezone to use when rendering the date within the migration file
# as well as the filename.
# If specified, requires the python-dateutil library that can be
# installed by adding `alembic[tz]` to the pip requirements
# string value is passed to dateutil.tz.gettz()
# leave blank for localtime
# timezone =

# max length of characters to apply to the
# "slug" field
# truncate_slug_length = 40

# set to 'true' to run the environment during
# the 'revision' command, regardless of autogenerate
# revision_environment = false

# set to 'true' to allow .pyc and .pyo files without
# a source .py file to be detected as revisions in the
# versions/ directory
# sourceless = false

# version location specification; This defaults
# to alembic/versions.  When using multiple version
# directories, initial revisions must be specified with --version-path.
# The path separator used here should be the separator specified by "version_path_separator" below.
# version_locations = %(here)s/bar:%(here)s/bat:alembic/versions

# version path separator; As mentioned above, this is the character used to split
# version_locations. The default within new alembic.ini files is "os", which uses os.pathsep.
# If this key is omitted entirely, it falls back to the legacy behavior of splitting on spaces and/or commas.
# Valid values for version_path_separator are:
#
# version_path_separator = :
# version_path_separator = ;
# version_path_separator = space
version_path_separator = os  
# Use os.pathsep. Default configuration used for new projects.

# set to 'true' to search source files recursively
# in each "version_locations" directory
# new in Alembic version 1.10
# recursive_version_locations = false

# the output encoding used when revision files
# are written from script.py.mako
# output_encoding = utf-8

# sqlalchemy.url = driver://user:pass@localhost/dbname


[post_write_hooks]
# post_write_hooks defines scripts or Python functions that are run
# on newly generated revision scripts.  See the documentation for further
# detail and examples

# format using "black" - use the console_scripts runner, against the "black" entrypoint
hooks = black
black.type = console_scripts
black.entrypoint = black
black.options = -l 79 REVISION_SCRIPT_FILENAME

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = INFO
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S


[alembic]
script_location = alembic
version_locations = %(script_location)s/versions

[schema_private]
script_location = alembic_tenants
version_locations = %(script_location)s/versions



================================================
FILE: backend/Dockerfile
================================================
FROM python:3.11.7-slim-bookworm

LABEL com.danswer.maintainer="<EMAIL>"
LABEL com.danswer.description="This image is the web/frontend container of Onyx which \
contains code for both the Community and Enterprise editions of Onyx. If you do not \
have a contract or agreement with DanswerAI, you are not permitted to use the Enterprise \
Edition features outside of personal development or testing purposes. Please reach out to \
<EMAIL> for more information. Please visit https://github.com/onyx-dot-app/onyx"

# Default ONYX_VERSION, typically overriden during builds by GitHub Actions.
ARG ONYX_VERSION=0.0.0-dev
# DO_NOT_TRACK is used to disable telemetry for Unstructured
ENV ONYX_VERSION=${ONYX_VERSION} \
    DANSWER_RUNNING_IN_DOCKER="true" \
    DO_NOT_TRACK="true"


RUN echo "ONYX_VERSION: ${ONYX_VERSION}"
# Install system dependencies
# cmake needed for psycopg (postgres)
# libpq-dev needed for psycopg (postgres)
# curl included just for users' convenience
# zip for Vespa step futher down
# ca-certificates for HTTPS
RUN apt-get update && \
    apt-get install -y \
        cmake \
        curl \
        zip \
        ca-certificates \
        libgnutls30 \
        libblkid1 \
        libmount1 \
        libsmartcols1 \
        libuuid1 \
        libxmlsec1-dev \
        pkg-config \
        gcc \
        nano \
        vim && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean



# Install Python dependencies
# Remove py which is pulled in by retry, py is not needed and is a CVE
COPY ./requirements/default.txt /tmp/requirements.txt
COPY ./requirements/ee.txt /tmp/ee-requirements.txt
RUN pip install --no-cache-dir --upgrade \
        --retries 5 \
        --timeout 30 \
        -r /tmp/requirements.txt \
        -r /tmp/ee-requirements.txt && \
    pip uninstall -y py && \
    playwright install chromium && \
    playwright install-deps chromium && \
    ln -s /usr/local/bin/supervisord /usr/bin/supervisord

# Cleanup for CVEs and size reduction
# https://github.com/tornadoweb/tornado/issues/3107
# xserver-common and xvfb included by playwright installation but not needed after
# perl-base is part of the base Python Debian image but not needed for Onyx functionality
# perl-base could only be removed with --allow-remove-essential
RUN apt-get update && \
    apt-get remove -y --allow-remove-essential \
        perl-base \
        xserver-common \
        xvfb \
        cmake \
        libldap-2.5-0 \
        libxmlsec1-dev \
        pkg-config \
        gcc && \
    apt-get install -y libxmlsec1-openssl && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/* && \
    rm -f /usr/local/lib/python3.11/site-packages/tornado/test/test.key

# Install postgresql-client for easy manual tests
# Install it here to avoid it being cleaned up above
RUN apt-get update && apt-get install -y postgresql-client

# Pre-downloading models for setups with limited egress
RUN python -c "from tokenizers import Tokenizer; \
Tokenizer.from_pretrained('nomic-ai/nomic-embed-text-v1')"

# Pre-downloading NLTK for setups with limited egress
RUN python -c "import nltk; \
nltk.download('stopwords', quiet=True); \
nltk.download('punkt_tab', quiet=True);"
# nltk.download('wordnet', quiet=True); introduce this back if lemmatization is needed

# Set up application files
WORKDIR /app

# Enterprise Version Files
COPY ./ee /app/ee
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set up application files
COPY ./onyx /app/onyx
COPY ./shared_configs /app/shared_configs
COPY ./alembic /app/alembic
COPY ./alembic_tenants /app/alembic_tenants
COPY ./alembic.ini /app/alembic.ini
COPY supervisord.conf /usr/etc/supervisord.conf
COPY ./static /app/static

# Escape hatch scripts
COPY ./scripts/debugging /app/scripts/debugging
COPY ./scripts/force_delete_connector_by_id.py /app/scripts/force_delete_connector_by_id.py

# Put logo in assets
COPY ./assets /app/assets

ENV PYTHONPATH=/app

# Default command which does nothing
# This container is used by api server and background which specify their own CMD
CMD ["tail", "-f", "/dev/null"]



================================================
FILE: backend/Dockerfile.model_server
================================================
FROM python:3.11.7-slim-bookworm

LABEL com.danswer.maintainer="<EMAIL>"
LABEL com.danswer.description="This image is for the Onyx model server which runs all of the \
AI models for Onyx. This container and all the code is MIT Licensed and free for all to use. \
You can find it at https://hub.docker.com/r/onyx/onyx-model-server. For more details, \
visit https://github.com/onyx-dot-app/onyx."

# Default ONYX_VERSION, typically overriden during builds by GitHub Actions.
ARG ONYX_VERSION=0.0.0-dev
ENV ONYX_VERSION=${ONYX_VERSION} \
    DANSWER_RUNNING_IN_DOCKER="true"


RUN echo "ONYX_VERSION: ${ONYX_VERSION}"

COPY ./requirements/model_server.txt /tmp/requirements.txt
RUN pip install --no-cache-dir --upgrade \
        --retries 5 \
        --timeout 30 \
        -r /tmp/requirements.txt

RUN apt-get remove -y --allow-remove-essential perl-base && \ 
    apt-get autoremove -y

# Pre-downloading models for setups with limited egress
# Download tokenizers, distilbert for the Onyx model
# Download model weights
# Run Nomic to pull in the custom architecture and have it cached locally
RUN python -c "from transformers import AutoTokenizer; \
AutoTokenizer.from_pretrained('distilbert-base-uncased'); \
AutoTokenizer.from_pretrained('mixedbread-ai/mxbai-rerank-xsmall-v1'); \
from huggingface_hub import snapshot_download; \
snapshot_download(repo_id='onyx-dot-app/hybrid-intent-token-classifier'); \
snapshot_download(repo_id='onyx-dot-app/information-content-model'); \
snapshot_download('nomic-ai/nomic-embed-text-v1'); \
snapshot_download('mixedbread-ai/mxbai-rerank-xsmall-v1'); \
from sentence_transformers import SentenceTransformer; \
SentenceTransformer(model_name_or_path='nomic-ai/nomic-embed-text-v1', trust_remote_code=True);"

# In case the user has volumes mounted to /root/.cache/huggingface that they've downloaded while
# running Onyx, don't overwrite it with the built in cache folder
RUN mv /root/.cache/huggingface /root/.cache/temp_huggingface

WORKDIR /app

# Utils used by model server
COPY ./onyx/utils/logger.py /app/onyx/utils/logger.py
COPY ./onyx/utils/middleware.py /app/onyx/utils/middleware.py

# Place to fetch version information
COPY ./onyx/__init__.py /app/onyx/__init__.py

# Shared between Onyx Backend and Model Server
COPY ./shared_configs /app/shared_configs

# Model Server main code
COPY ./model_server /app/model_server

ENV PYTHONPATH=/app

CMD ["uvicorn", "model_server.main:app", "--host", "0.0.0.0", "--port", "9000"]



================================================
FILE: backend/hello-vmlinux.bin
================================================
[Binary file]


================================================
FILE: backend/pyproject.toml
================================================
[tool.mypy]
plugins = "sqlalchemy.ext.mypy.plugin"
mypy_path = "$MYPY_CONFIG_FILE_DIR"
explicit_package_bases = true
disallow_untyped_defs = true
enable_error_code = ["possibly-undefined"]
strict_equality = true
exclude = [
  "^generated/.*",
]

[[tool.mypy.overrides]]
module = "alembic.versions.*"
disable_error_code = ["var-annotated"]

[[tool.mypy.overrides]]
module = "alembic_tenants.versions.*"
disable_error_code = ["var-annotated"]

[[tool.mypy.overrides]]
module = "generated.*"
follow_imports = "silent"
ignore_errors = true

[tool.ruff]
line-length = 130

[tool.ruff.lint]
ignore = []
select = [
  "E",
  "F",
  "W",
]



================================================
FILE: backend/pytest.ini
================================================
[pytest]
pythonpath = 
    .
    generated/onyx_openapi_client
markers =
    slow: marks tests as slow
filterwarnings =
    ignore::DeprecationWarning
    ignore::cryptography.utils.CryptographyDeprecationWarning
    ignore::PendingDeprecationWarning:ddtrace.internal.module
# .test.env is gitignored.
# After installing pytest-dotenv,
# you can use it to test credentials locally.
env_files =
    .test.env



================================================
FILE: backend/supervisord.conf
================================================
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisord.log

# region enable supervisorctl usage
[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
# endregion enable supervisorctl usage

# Background jobs that must be run async due to long time to completion
# NOTE: due to an issue with Celery + SQLAlchemy
# (https://github.com/celery/celery/issues/7007#issuecomment-1740139367)
# we must use the threads pool instead of the default prefork pool for now
# in order to avoid intermittent errors like:
# `billiard.exceptions.WorkerLostError: Worker exited prematurely: signal 11 (SIGSEGV)`.
#
# This means workers will not be able take advantage of multiple CPU cores
# on a system, but this should be okay for now since all our celery tasks are
# relatively compute-light (e.g. they tend to just make a bunch of requests to
# Vespa / Postgres)
[program:celery_worker_primary]
command=celery -A onyx.background.celery.versioned_apps.primary worker
    --loglevel=INFO
    --hostname=primary@%%n
    -Q celery
stdout_logfile=/var/log/celery_worker_primary.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true

# NOTE: only allowing configuration here and not in the other celery workers,
# since this is often the bottleneck for "sync" jobs (e.g. document set syncing,
# user group syncing, deletion, etc.)
[program:celery_worker_light]
command=celery -A onyx.background.celery.versioned_apps.light worker
    --loglevel=INFO
    --hostname=light@%%n
    -Q vespa_metadata_sync,connector_deletion,doc_permissions_upsert,checkpoint_cleanup
stdout_logfile=/var/log/celery_worker_light.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true

[program:celery_worker_heavy]
command=celery -A onyx.background.celery.versioned_apps.heavy worker
    --loglevel=INFO
    --hostname=heavy@%%n
    -Q connector_pruning,connector_doc_permissions_sync,connector_external_group_sync,csv_generation
stdout_logfile=/var/log/celery_worker_heavy.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true

[program:celery_worker_indexing]
command=celery -A onyx.background.celery.versioned_apps.indexing worker
    --loglevel=INFO
    --hostname=indexing@%%n
    -Q connector_indexing
stdout_logfile=/var/log/celery_worker_indexing.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true

[program:celery_worker_user_files_indexing]
command=celery -A onyx.background.celery.versioned_apps.indexing worker
    --loglevel=INFO
    --hostname=user_files_indexing@%%n
    -Q user_files_indexing
stdout_logfile=/var/log/celery_worker_user_files_indexing.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true

[program:celery_worker_monitoring]
command=celery -A onyx.background.celery.versioned_apps.monitoring worker
    --loglevel=INFO
    --hostname=monitoring@%%n
    -Q monitoring
stdout_logfile=/var/log/celery_worker_monitoring.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true

[program:celery_worker_kg_processing]
command=celery -A onyx.background.celery.versioned_apps.kg_processing worker
    --loglevel=INFO
    --hostname=kg_processing@%%n
    -Q kg_processing
stdout_logfile=/var/log/celery_worker_kg_processing.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startsecs=10
stopasgroup=true



# Job scheduler for periodic tasks
[program:celery_beat]
command=celery -A onyx.background.celery.versioned_apps.beat beat
stdout_logfile=/var/log/celery_beat.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
startsecs=10
stopasgroup=true

# watchdog to detect and restart the beat in case of inactivity
# supervisord only restarts the process if it's dead
# make sure this key matches ONYX_CELERY_BEAT_HEARTBEAT_KEY
[program:supervisord_watchdog_celery_beat]
command=python onyx/utils/supervisord_watchdog.py
    --conf /etc/supervisor/conf.d/supervisord.conf
    --key "onyx:celery:beat:heartbeat"
    --program celery_beat
stdout_logfile=/var/log/supervisord_watchdog_celery_beat.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
startsecs=10
stopasgroup=true

# Listens for Slack messages and responds with answers
# for all channels that the OnyxBot has been added to.
# If not setup, this will just fail 5 times and then stop.
# More details on setup here: https://docs.onyx.app/slack_bot_setup
[program:slack_bot]
command=python onyx/onyxbot/slack/listener.py
stdout_logfile=/var/log/slack_bot.log
stdout_logfile_maxbytes=16MB
redirect_stderr=true
autorestart=true
startretries=5
startsecs=60

# Pushes all logs from the above programs to stdout
# No log rotation here, since it's stdout it's handled by the Docker container logging
[program:log-redirect-handler]
command=tail -qF
    /var/log/celery_beat.log
    /var/log/celery_worker_primary.log
    /var/log/celery_worker_light.log
    /var/log/celery_worker_heavy.log
    /var/log/celery_worker_indexing.log
    /var/log/celery_worker_user_files_indexing.log
    /var/log/celery_worker_monitoring.log
    /var/log/slack_bot.log
    /var/log/supervisord_watchdog_celery_beat.log
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes = 0  # must be set to 0 when stdout_logfile=/dev/stdout
autorestart=true



================================================
FILE: backend/.dockerignore
================================================
**/__pycache__
venv/
env/
*.egg-info
.cache
.git/
.svn/
.vscode/
.idea/
*.log
log/
.env
secrets.yaml
build/
dist/
.coverage
htmlcov/



================================================
FILE: backend/.trivyignore
================================================
# https://github.com/madler/zlib/issues/868
# Pulled in with base Debian image, it's part of the contrib folder but unused
# zlib1g is fine
# Will be gone with Debian image upgrade
# No impact in our settings
CVE-2023-45853

# krb5 related, worst case is denial of service by resource exhaustion
# Accept the risk
CVE-2024-26458
CVE-2024-26461
CVE-2024-26462
CVE-2024-26458
CVE-2024-26461
CVE-2024-26462
CVE-2024-26458
CVE-2024-26461
CVE-2024-26462
CVE-2024-26458
CVE-2024-26461
CVE-2024-26462

# Specific to Firefox which we do not use
# No impact in our settings
CVE-2024-0743

# bind9 related, worst case is denial of service by CPU resource exhaustion
# Accept the risk
CVE-2023-50387
CVE-2023-50868
CVE-2023-50387
CVE-2023-50868

# libexpat1, XML parsing resource exhaustion
# We don't parse any user provided XMLs
# No impact in our settings
CVE-2023-52425
CVE-2024-28757

# sqlite, only used by NLTK library to grab word lemmatizer and stopwords
# No impact in our settings
CVE-2023-7104

# libharfbuzz0b, O(n^2) growth, worst case is denial of service
# Accept the risk
CVE-2023-25193



================================================
FILE: backend/alembic/README.md
================================================
<!-- ONYX_METADATA={"link": "https://github.com/onyx-dot-app/onyx/blob/main/backend/alembic/README.md"} -->

# Alembic DB Migrations

These files are for creating/updating the tables in the Relational DB (Postgres).
Onyx migrations use a generic single-database configuration with an async dbapi.

## To generate new migrations:

run from onyx/backend:
`alembic revision --autogenerate -m <DESCRIPTION_OF_MIGRATION>`

More info can be found here: https://alembic.sqlalchemy.org/en/latest/autogenerate.html

## Running migrations

To run all un-applied migrations:
`alembic upgrade head`

To undo migrations:
`alembic downgrade -X`
where X is the number of migrations you want to undo from the current state

### Multi-tenant migrations

For multi-tenant deployments, you can use additional options:

**Upgrade all tenants:**
```bash
alembic -x upgrade_all_tenants=true upgrade head
```

**Upgrade specific schemas:**
```bash
# Single schema
alembic -x schemas=tenant_12345678-1234-1234-1234-123456789012 upgrade head

# Multiple schemas (comma-separated)
alembic -x schemas=tenant_12345678-1234-1234-1234-123456789012,public,another_tenant upgrade head
```

**Upgrade tenants within an alphabetical range:**
```bash
# Upgrade tenants 100-200 when sorted alphabetically (positions 100 to 200)
alembic -x upgrade_all_tenants=true -x tenant_range_start=100 -x tenant_range_end=200 upgrade head

# Upgrade tenants starting from position 1000 alphabetically
alembic -x upgrade_all_tenants=true -x tenant_range_start=1000 upgrade head

# Upgrade first 500 tenants alphabetically
alembic -x upgrade_all_tenants=true -x tenant_range_end=500 upgrade head
```

**Continue on error (for batch operations):**
```bash
alembic -x upgrade_all_tenants=true -x continue=true upgrade head
```

The tenant range filtering works by:
1. Sorting tenant IDs alphabetically
2. Using 1-based position numbers (1st, 2nd, 3rd tenant, etc.)
3. Filtering to the specified range of positions
4. Non-tenant schemas (like 'public') are always included



================================================
FILE: backend/alembic/env.py
================================================
from typing import Any, Literal
from onyx.db.engine.iam_auth import get_iam_auth_token
from onyx.configs.app_configs import USE_IAM_AUTH
from onyx.configs.app_configs import POSTGRES_HOST
from onyx.configs.app_configs import POSTGRES_PORT
from onyx.configs.app_configs import POSTGRES_USER
from onyx.configs.app_configs import AWS_REGION_NAME
from onyx.db.engine.sql_engine import build_connection_string
from onyx.db.engine.tenant_utils import get_all_tenant_ids
from sqlalchemy import event
from sqlalchemy import pool
from sqlalchemy import text
from sqlalchemy.engine.base import Connection
import os
import ssl
import asyncio
import logging
from logging.config import fileConfig

from alembic import context
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.sql.schema import SchemaItem
from onyx.configs.constants import SSL_CERT_FILE
from shared_configs.configs import (
    MULTI_TENANT,
    POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE,
    TENANT_ID_PREFIX,
)
from onyx.db.models import Base
from celery.backends.database.session import ResultModelBase  # type: ignore
from onyx.db.engine.sql_engine import SqlEngine

# Make sure in alembic.ini [logger_root] level=INFO is set or most logging will be
# hidden! (defaults to level=WARN)

# Alembic Config object
config = context.config

if config.config_file_name is not None and config.attributes.get(
    "configure_logger", True
):
    fileConfig(config.config_file_name)

target_metadata = [Base.metadata, ResultModelBase.metadata]

EXCLUDE_TABLES = {"kombu_queue", "kombu_message"}

logger = logging.getLogger(__name__)

ssl_context: ssl.SSLContext | None = None
if USE_IAM_AUTH:
    if not os.path.exists(SSL_CERT_FILE):
        raise FileNotFoundError(f"Expected {SSL_CERT_FILE} when USE_IAM_AUTH is true.")
    ssl_context = ssl.create_default_context(cafile=SSL_CERT_FILE)


def include_object(
    object: SchemaItem,
    name: str | None,
    type_: Literal[
        "schema",
        "table",
        "column",
        "index",
        "unique_constraint",
        "foreign_key_constraint",
    ],
    reflected: bool,
    compare_to: SchemaItem | None,
) -> bool:
    if type_ == "table" and name in EXCLUDE_TABLES:
        return False
    return True


def filter_tenants_by_range(
    tenant_ids: list[str], start_range: int | None = None, end_range: int | None = None
) -> list[str]:
    """
    Filter tenant IDs by alphabetical position range.

    Args:
        tenant_ids: List of tenant IDs to filter
        start_range: Starting position in alphabetically sorted list (1-based, inclusive)
        end_range: Ending position in alphabetically sorted list (1-based, inclusive)

    Returns:
        Filtered list of tenant IDs in their original order
    """
    if start_range is None and end_range is None:
        return tenant_ids

    # Separate tenant IDs from non-tenant schemas
    tenant_schemas = [tid for tid in tenant_ids if tid.startswith(TENANT_ID_PREFIX)]
    non_tenant_schemas = [
        tid for tid in tenant_ids if not tid.startswith(TENANT_ID_PREFIX)
    ]

    # Sort tenant schemas alphabetically.
    # NOTE: can cause missed schemas if a schema is created in between workers
    # fetching of all tenant IDs. We accept this risk for now. Just re-running
    # the migration will fix the issue.
    sorted_tenant_schemas = sorted(tenant_schemas)

    # Apply range filtering (0-based indexing)
    start_idx = start_range if start_range is not None else 0
    end_idx = end_range if end_range is not None else len(sorted_tenant_schemas)

    # Ensure indices are within bounds
    start_idx = max(0, start_idx)
    end_idx = min(len(sorted_tenant_schemas), end_idx)

    # Get the filtered tenant schemas
    filtered_tenant_schemas = sorted_tenant_schemas[start_idx:end_idx]

    # Combine with non-tenant schemas and preserve original order
    filtered_tenants = []
    for tenant_id in tenant_ids:
        if tenant_id in filtered_tenant_schemas or tenant_id in non_tenant_schemas:
            filtered_tenants.append(tenant_id)

    return filtered_tenants


def get_schema_options() -> (
    tuple[bool, bool, bool, int | None, int | None, list[str] | None]
):
    x_args_raw = context.get_x_argument()
    x_args = {}
    for arg in x_args_raw:
        if "=" in arg:
            key, value = arg.split("=", 1)
            x_args[key.strip()] = value.strip()
        else:
            raise ValueError(f"Invalid argument: {arg}")

    create_schema = x_args.get("create_schema", "true").lower() == "true"
    upgrade_all_tenants = x_args.get("upgrade_all_tenants", "false").lower() == "true"

    # continue on error with individual tenant
    # only applies to online migrations
    continue_on_error = x_args.get("continue", "false").lower() == "true"

    # Tenant range filtering
    tenant_range_start = None
    tenant_range_end = None

    if "tenant_range_start" in x_args:
        try:
            tenant_range_start = int(x_args["tenant_range_start"])
        except ValueError:
            raise ValueError(
                f"Invalid tenant_range_start value: {x_args['tenant_range_start']}. Must be an integer."
            )

    if "tenant_range_end" in x_args:
        try:
            tenant_range_end = int(x_args["tenant_range_end"])
        except ValueError:
            raise ValueError(
                f"Invalid tenant_range_end value: {x_args['tenant_range_end']}. Must be an integer."
            )

    # Validate range
    if tenant_range_start is not None and tenant_range_end is not None:
        if tenant_range_start > tenant_range_end:
            raise ValueError(
                f"tenant_range_start ({tenant_range_start}) cannot be greater than tenant_range_end ({tenant_range_end})"
            )

    # Specific schema names filtering (replaces both schema_name and the old tenant_ids approach)
    schemas = None
    if "schemas" in x_args:
        schema_names_str = x_args["schemas"].strip()
        if schema_names_str:
            # Split by comma and strip whitespace
            schemas = [
                name.strip() for name in schema_names_str.split(",") if name.strip()
            ]
            if schemas:
                logger.info(f"Specific schema names specified: {schemas}")

    # Validate that only one method is used at a time
    range_filtering = tenant_range_start is not None or tenant_range_end is not None
    specific_filtering = schemas is not None and len(schemas) > 0

    if range_filtering and specific_filtering:
        raise ValueError(
            "Cannot use both tenant range filtering (tenant_range_start/tenant_range_end) "
            "and specific schema filtering (schemas) at the same time. "
            "Please use only one filtering method."
        )

    if upgrade_all_tenants and specific_filtering:
        raise ValueError(
            "Cannot use both upgrade_all_tenants=true and schemas at the same time. "
            "Use either upgrade_all_tenants=true for all tenants, or schemas for specific schemas."
        )

    # If any filtering parameters are specified, we're not doing the default single schema migration
    if range_filtering:
        upgrade_all_tenants = True

    # Validate multi-tenant requirements
    if MULTI_TENANT and not upgrade_all_tenants and not specific_filtering:
        raise ValueError(
            "In multi-tenant mode, you must specify either upgrade_all_tenants=true "
            "or provide schemas. Cannot run default migration."
        )

    return (
        create_schema,
        upgrade_all_tenants,
        continue_on_error,
        tenant_range_start,
        tenant_range_end,
        schemas,
    )


def do_run_migrations(
    connection: Connection, schema_name: str, create_schema: bool
) -> None:
    if create_schema:
        connection.execute(text(f'CREATE SCHEMA IF NOT EXISTS "{schema_name}"'))
        connection.execute(text("COMMIT"))

    connection.execute(text(f'SET search_path TO "{schema_name}"'))

    context.configure(
        connection=connection,
        target_metadata=target_metadata,  # type: ignore
        include_object=include_object,
        version_table_schema=schema_name,
        include_schemas=True,
        compare_type=True,
        compare_server_default=True,
        script_location=config.get_main_option("script_location"),
    )

    with context.begin_transaction():
        context.run_migrations()


def provide_iam_token_for_alembic(
    dialect: Any, conn_rec: Any, cargs: Any, cparams: Any
) -> None:
    if USE_IAM_AUTH:
        # Database connection settings
        region = AWS_REGION_NAME
        host = POSTGRES_HOST
        port = POSTGRES_PORT
        user = POSTGRES_USER

        # Get IAM authentication token
        token = get_iam_auth_token(host, port, user, region)

        # For Alembic / SQLAlchemy in this context, set SSL and password
        cparams["password"] = token
        cparams["ssl"] = ssl_context


async def run_async_migrations() -> None:
    (
        create_schema,
        upgrade_all_tenants,
        continue_on_error,
        tenant_range_start,
        tenant_range_end,
        schemas,
    ) = get_schema_options()

    if not schemas and not MULTI_TENANT:
        schemas = [POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE]

    # without init_engine, subsequent engine calls fail hard intentionally
    SqlEngine.init_engine(pool_size=20, max_overflow=5)

    engine = create_async_engine(
        build_connection_string(),
        poolclass=pool.NullPool,
    )

    if USE_IAM_AUTH:

        @event.listens_for(engine.sync_engine, "do_connect")
        def event_provide_iam_token_for_alembic(
            dialect: Any, conn_rec: Any, cargs: Any, cparams: Any
        ) -> None:
            provide_iam_token_for_alembic(dialect, conn_rec, cargs, cparams)

    if schemas:
        # Use specific schema names directly without fetching all tenants
        logger.info(f"Migrating specific schema names: {schemas}")

        i_schema = 0
        num_schemas = len(schemas)
        for schema in schemas:
            i_schema += 1
            logger.info(
                f"Migrating schema: index={i_schema} num_schemas={num_schemas} schema={schema}"
            )
            try:
                async with engine.connect() as connection:
                    await connection.run_sync(
                        do_run_migrations,
                        schema_name=schema,
                        create_schema=create_schema,
                    )
            except Exception as e:
                logger.error(f"Error migrating schema {schema}: {e}")
                if not continue_on_error:
                    logger.error("--continue=true is not set, raising exception!")
                    raise

                logger.warning("--continue=true is set, continuing to next schema.")

    elif upgrade_all_tenants:
        tenant_schemas = get_all_tenant_ids()

        filtered_tenant_schemas = filter_tenants_by_range(
            tenant_schemas, tenant_range_start, tenant_range_end
        )

        if tenant_range_start is not None or tenant_range_end is not None:
            logger.info(
                f"Filtering tenants by range: start={tenant_range_start}, end={tenant_range_end}"
            )
            logger.info(
                f"Total tenants: {len(tenant_schemas)}, Filtered tenants: {len(filtered_tenant_schemas)}"
            )

        i_tenant = 0
        num_tenants = len(filtered_tenant_schemas)
        for schema in filtered_tenant_schemas:
            i_tenant += 1
            logger.info(
                f"Migrating schema: index={i_tenant} num_tenants={num_tenants} schema={schema}"
            )
            try:
                async with engine.connect() as connection:
                    await connection.run_sync(
                        do_run_migrations,
                        schema_name=schema,
                        create_schema=create_schema,
                    )
            except Exception as e:
                logger.error(f"Error migrating schema {schema}: {e}")
                if not continue_on_error:
                    logger.error("--continue=true is not set, raising exception!")
                    raise

                logger.warning("--continue=true is set, continuing to next schema.")

    else:
        # This should not happen in the new design since we require either
        # upgrade_all_tenants=true or schemas in multi-tenant mode
        # and for non-multi-tenant mode, we should use schemas with the default schema
        raise ValueError(
            "No migration target specified. Use either upgrade_all_tenants=true for all tenants "
            "or schemas for specific schemas."
        )

    await engine.dispose()


def run_migrations_offline() -> None:
    """
    NOTE(rkuo): This generates a sql script that can be used to migrate the database ...
    instead of migrating the db live via an open connection

    Not clear on when this would be used by us or if it even works.

    If it is offline, then why are there calls to the db engine?

    This doesn't really get used when we migrate in the cloud."""

    logger.info("run_migrations_offline starting.")

    # without init_engine, subsequent engine calls fail hard intentionally
    SqlEngine.init_engine(pool_size=20, max_overflow=5)

    (
        create_schema,
        upgrade_all_tenants,
        continue_on_error,
        tenant_range_start,
        tenant_range_end,
        schemas,
    ) = get_schema_options()
    url = build_connection_string()

    if schemas:
        # Use specific schema names directly without fetching all tenants
        logger.info(f"Migrating specific schema names: {schemas}")

        for schema in schemas:
            logger.info(f"Migrating schema: {schema}")
            context.configure(
                url=url,
                target_metadata=target_metadata,  # type: ignore
                literal_binds=True,
                include_object=include_object,
                version_table_schema=schema,
                include_schemas=True,
                script_location=config.get_main_option("script_location"),
                dialect_opts={"paramstyle": "named"},
            )

            with context.begin_transaction():
                context.run_migrations()

    elif upgrade_all_tenants:
        engine = create_async_engine(url)

        if USE_IAM_AUTH:

            @event.listens_for(engine.sync_engine, "do_connect")
            def event_provide_iam_token_for_alembic_offline(
                dialect: Any, conn_rec: Any, cargs: Any, cparams: Any
            ) -> None:
                provide_iam_token_for_alembic(dialect, conn_rec, cargs, cparams)

        tenant_schemas = get_all_tenant_ids()
        engine.sync_engine.dispose()

        filtered_tenant_schemas = filter_tenants_by_range(
            tenant_schemas, tenant_range_start, tenant_range_end
        )

        if tenant_range_start is not None or tenant_range_end is not None:
            logger.info(
                f"Filtering tenants by range: start={tenant_range_start}, end={tenant_range_end}"
            )
            logger.info(
                f"Total tenants: {len(tenant_schemas)}, Filtered tenants: {len(filtered_tenant_schemas)}"
            )

        for schema in filtered_tenant_schemas:
            logger.info(f"Migrating schema: {schema}")
            context.configure(
                url=url,
                target_metadata=target_metadata,  # type: ignore
                literal_binds=True,
                include_object=include_object,
                version_table_schema=schema,
                include_schemas=True,
                script_location=config.get_main_option("script_location"),
                dialect_opts={"paramstyle": "named"},
            )

            with context.begin_transaction():
                context.run_migrations()
    else:
        # This should not happen in the new design
        raise ValueError(
            "No migration target specified. Use either upgrade_all_tenants=true for all tenants "
            "or schemas for specific schemas."
        )


def run_migrations_online() -> None:
    logger.info("run_migrations_online starting.")
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()



================================================
FILE: backend/alembic/script.py.mako
================================================
"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    ${downgrades if downgrades else "pass"}



================================================
FILE: backend/alembic/versions/027381bce97c_add_shortcut_option_for_users.py
================================================
"""add shortcut option for users

Revision ID: 027381bce97c
Revises: 6fc7886d665d
Create Date: 2025-01-14 12:14:00.814390

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "027381bce97c"
down_revision = "6fc7886d665d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "user",
        sa.Column(
            "shortcut_enabled", sa.Boolean(), nullable=False, server_default="false"
        ),
    )


def downgrade() -> None:
    op.drop_column("user", "shortcut_enabled")



================================================
FILE: backend/alembic/versions/03bf8be6b53a_rework_kg_config.py
================================================
"""rework-kg-config

Revision ID: 03bf8be6b53a
Revises: 65bc6e0f8500
Create Date: 2025-06-16 10:52:34.815335

"""

import json


from datetime import datetime
from datetime import timedelta
from sqlalchemy.dialects import postgresql
from sqlalchemy import text
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "03bf8be6b53a"
down_revision = "65bc6e0f8500"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # get current config
    current_configs = (
        op.get_bind()
        .execute(text("SELECT kg_variable_name, kg_variable_values FROM kg_config"))
        .all()
    )
    current_config_dict = {
        config.kg_variable_name: (
            config.kg_variable_values[0]
            if config.kg_variable_name
            not in ("KG_VENDOR_DOMAINS", "KG_IGNORE_EMAIL_DOMAINS")
            else config.kg_variable_values
        )
        for config in current_configs
        if config.kg_variable_values
    }

    # not using the KGConfigSettings model here in case it changes in the future
    kg_config_settings = json.dumps(
        {
            "KG_EXPOSED": current_config_dict.get("KG_EXPOSED", False),
            "KG_ENABLED": current_config_dict.get("KG_ENABLED", False),
            "KG_VENDOR": current_config_dict.get("KG_VENDOR", None),
            "KG_VENDOR_DOMAINS": current_config_dict.get("KG_VENDOR_DOMAINS", []),
            "KG_IGNORE_EMAIL_DOMAINS": current_config_dict.get(
                "KG_IGNORE_EMAIL_DOMAINS", []
            ),
            "KG_COVERAGE_START": current_config_dict.get(
                "KG_COVERAGE_START",
                (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d"),
            ),
            "KG_MAX_COVERAGE_DAYS": current_config_dict.get("KG_MAX_COVERAGE_DAYS", 90),
            "KG_MAX_PARENT_RECURSION_DEPTH": current_config_dict.get(
                "KG_MAX_PARENT_RECURSION_DEPTH", 2
            ),
            "KG_BETA_PERSONA_ID": current_config_dict.get("KG_BETA_PERSONA_ID", None),
        }
    )
    op.execute(
        f"INSERT INTO key_value_store (key, value) VALUES ('kg_config', '{kg_config_settings}')"
    )

    # drop kg config table
    op.drop_table("kg_config")


def downgrade() -> None:
    # get current config
    current_config_dict = {
        "KG_EXPOSED": False,
        "KG_ENABLED": False,
        "KG_VENDOR": [],
        "KG_VENDOR_DOMAINS": [],
        "KG_IGNORE_EMAIL_DOMAINS": [],
        "KG_COVERAGE_START": (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d"),
        "KG_MAX_COVERAGE_DAYS": 90,
        "KG_MAX_PARENT_RECURSION_DEPTH": 2,
    }
    current_configs = (
        op.get_bind()
        .execute(text("SELECT value FROM key_value_store WHERE key = 'kg_config'"))
        .one_or_none()
    )
    if current_configs is not None:
        current_config_dict.update(current_configs[0])
    insert_values = [
        {
            "kg_variable_name": name,
            "kg_variable_values": (
                [str(val).lower() if isinstance(val, bool) else str(val)]
                if not isinstance(val, list)
                else val
            ),
        }
        for name, val in current_config_dict.items()
    ]

    op.create_table(
        "kg_config",
        sa.Column("id", sa.Integer(), primary_key=True, nullable=False, index=True),
        sa.Column("kg_variable_name", sa.String(), nullable=False, index=True),
        sa.Column("kg_variable_values", postgresql.ARRAY(sa.String()), nullable=False),
        sa.UniqueConstraint("kg_variable_name", name="uq_kg_config_variable_name"),
    )
    op.bulk_insert(
        sa.table(
            "kg_config",
            sa.column("kg_variable_name", sa.String),
            sa.column("kg_variable_values", postgresql.ARRAY(sa.String)),
        ),
        insert_values,
    )

    op.execute("DELETE FROM key_value_store WHERE key = 'kg_config'")



================================================
FILE: backend/alembic/versions/0568ccf46a6b_add_thread_specific_model_selection.py
================================================
"""Add thread specific model selection

Revision ID: 0568ccf46a6b
Revises: e209dc5a8156
Create Date: 2024-06-19 14:25:36.376046

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0568ccf46a6b"
down_revision = "e209dc5a8156"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_session",
        sa.Column("current_alternate_model", sa.String(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("chat_session", "current_alternate_model")



================================================
FILE: backend/alembic/versions/05c07bf07c00_add_search_doc_relevance_details.py
================================================
"""add search doc relevance details

Revision ID: 05c07bf07c00
Revises: b896bbd0d5a7
Create Date: 2024-07-10 17:48:15.886653

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "05c07bf07c00"
down_revision = "b896bbd0d5a7"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "search_doc",
        sa.Column("is_relevant", sa.Boolean(), nullable=True),
    )
    op.add_column(
        "search_doc",
        sa.Column("relevance_explanation", sa.String(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("search_doc", "relevance_explanation")
    op.drop_column("search_doc", "is_relevant")



================================================
FILE: backend/alembic/versions/0816326d83aa_add_federated_connector_tables.py
================================================
"""add federated connector tables

Revision ID: 0816326d83aa
Revises: 12635f6655b7
Create Date: 2025-06-29 14:09:45.109518

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = "0816326d83aa"
down_revision = "12635f6655b7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create federated_connector table
    op.create_table(
        "federated_connector",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("source", sa.String(), nullable=False),
        sa.Column("credentials", sa.LargeBinary(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create federated_connector_oauth_token table
    op.create_table(
        "federated_connector_oauth_token",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("federated_connector_id", sa.Integer(), nullable=False),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("token", sa.LargeBinary(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["federated_connector_id"], ["federated_connector.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create federated_connector__document_set table
    op.create_table(
        "federated_connector__document_set",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("federated_connector_id", sa.Integer(), nullable=False),
        sa.Column("document_set_id", sa.Integer(), nullable=False),
        sa.Column("entities", postgresql.JSONB(), nullable=False),
        sa.ForeignKeyConstraint(
            ["federated_connector_id"], ["federated_connector.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["document_set_id"], ["document_set.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "federated_connector_id",
            "document_set_id",
            name="uq_federated_connector_document_set",
        ),
    )


def downgrade() -> None:
    # Drop tables in reverse order due to foreign key dependencies
    op.drop_table("federated_connector__document_set")
    op.drop_table("federated_connector_oauth_token")
    op.drop_table("federated_connector")



================================================
FILE: backend/alembic/versions/08a1eda20fe1_add_earliest_indexing_to_connector.py
================================================
"""add_indexing_start_to_connector

Revision ID: 08a1eda20fe1
Revises: 8a87bd6ec550
Create Date: 2024-07-23 11:12:39.462397

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "08a1eda20fe1"
down_revision = "8a87bd6ec550"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "connector", sa.Column("indexing_start", sa.DateTime(), nullable=True)
    )


def downgrade() -> None:
    op.drop_column("connector", "indexing_start")



================================================
FILE: backend/alembic/versions/0a2b51deb0b8_add_starter_prompts.py
================================================
"""Add starter prompts

Revision ID: 0a2b51deb0b8
Revises: 5f4b8568a221
Create Date: 2024-03-02 23:23:49.960309

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0a2b51deb0b8"
down_revision = "5f4b8568a221"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "persona",
        sa.Column(
            "starter_messages",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )


def downgrade() -> None:
    op.drop_column("persona", "starter_messages")



================================================
FILE: backend/alembic/versions/0a98909f2757_enable_encrypted_fields.py
================================================
"""Enable Encrypted Fields

Revision ID: 0a98909f2757
Revises: 570282d33c49
Create Date: 2024-05-05 19:30:34.317972

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table
from sqlalchemy.dialects import postgresql
import json

from onyx.utils.encryption import encrypt_string_to_bytes

# revision identifiers, used by Alembic.
revision = "0a98909f2757"
down_revision = "570282d33c49"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    connection = op.get_bind()

    op.alter_column("key_value_store", "value", nullable=True)
    op.add_column(
        "key_value_store",
        sa.Column(
            "encrypted_value",
            sa.LargeBinary,
            nullable=True,
        ),
    )

    # Need a temporary column to translate the JSONB to binary
    op.add_column("credential", sa.Column("temp_column", sa.LargeBinary()))

    creds_table = table(
        "credential",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "credential_json",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
        ),
        sa.Column(
            "temp_column",
            sa.LargeBinary(),
            nullable=False,
        ),
    )

    results = connection.execute(sa.select(creds_table))

    # This uses the MIT encrypt which does not actually encrypt the credentials
    # In other words, this upgrade does not apply the encryption. Porting existing sensitive data
    # and key rotation currently is not supported and will come out in the future
    for row_id, creds, _ in results:
        creds_binary = encrypt_string_to_bytes(json.dumps(creds))
        connection.execute(
            creds_table.update()
            .where(creds_table.c.id == row_id)
            .values(temp_column=creds_binary)
        )

    op.drop_column("credential", "credential_json")
    op.alter_column("credential", "temp_column", new_column_name="credential_json")

    op.add_column("llm_provider", sa.Column("temp_column", sa.LargeBinary()))

    llm_table = table(
        "llm_provider",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "api_key",
            sa.String(),
            nullable=False,
        ),
        sa.Column(
            "temp_column",
            sa.LargeBinary(),
            nullable=False,
        ),
    )
    results = connection.execute(sa.select(llm_table))

    for row_id, api_key, _ in results:
        llm_key = encrypt_string_to_bytes(api_key)
        connection.execute(
            llm_table.update()
            .where(llm_table.c.id == row_id)
            .values(temp_column=llm_key)
        )

    op.drop_column("llm_provider", "api_key")
    op.alter_column("llm_provider", "temp_column", new_column_name="api_key")


def downgrade() -> None:
    # Some information loss but this is ok. Should not allow decryption via downgrade.
    op.drop_column("credential", "credential_json")
    op.drop_column("llm_provider", "api_key")

    op.add_column("llm_provider", sa.Column("api_key", sa.String()))
    op.add_column(
        "credential",
        sa.Column("credential_json", postgresql.JSONB(astext_type=sa.Text())),
    )

    op.execute("DELETE FROM key_value_store WHERE value IS NULL")
    op.alter_column("key_value_store", "value", nullable=False)
    op.drop_column("key_value_store", "encrypted_value")



================================================
FILE: backend/alembic/versions/0ebb1d516877_add_ccpair_deletion_failure_message.py
================================================
"""add ccpair deletion failure message

Revision ID: 0ebb1d516877
Revises: 52a219fb5233
Create Date: 2024-09-10 15:03:48.233926

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "0ebb1d516877"
down_revision = "52a219fb5233"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "connector_credential_pair",
        sa.Column("deletion_failure_message", sa.String(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("connector_credential_pair", "deletion_failure_message")



================================================
FILE: backend/alembic/versions/0f7ff6d75b57_add_index_to_index_attempt_time_created.py
================================================
"""add index to index_attempt.time_created

Revision ID: 0f7ff6d75b57
Revises: 369644546676
Create Date: 2025-01-10 14:01:14.067144

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0f7ff6d75b57"
down_revision = "fec3db967bf7"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_index(
        op.f("ix_index_attempt_status"),
        "index_attempt",
        ["status"],
        unique=False,
    )

    op.create_index(
        op.f("ix_index_attempt_time_created"),
        "index_attempt",
        ["time_created"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(op.f("ix_index_attempt_time_created"), table_name="index_attempt")

    op.drop_index(op.f("ix_index_attempt_status"), table_name="index_attempt")



================================================
FILE: backend/alembic/versions/12635f6655b7_drive_canonical_ids.py
================================================
"""drive-canonical-ids

Revision ID: 12635f6655b7
Revises: 58c50ef19f08
Create Date: 2025-06-20 14:44:54.241159

"""

from alembic import op
import sqlalchemy as sa
from urllib.parse import urlparse, urlunparse
from httpx import HTTPStatusError
import httpx
from onyx.document_index.factory import get_default_document_index
from onyx.db.search_settings import SearchSettings
from onyx.document_index.vespa.shared_utils.utils import get_vespa_http_client
from onyx.document_index.vespa.shared_utils.utils import (
    replace_invalid_doc_id_characters,
)
from onyx.document_index.vespa_constants import DOCUMENT_ID_ENDPOINT
from onyx.utils.logger import setup_logger
import os

logger = setup_logger()

# revision identifiers, used by Alembic.
revision = "12635f6655b7"
down_revision = "58c50ef19f08"
branch_labels = None
depends_on = None

SKIP_CANON_DRIVE_IDS = os.environ.get("SKIP_CANON_DRIVE_IDS", "true").lower() == "true"


def active_search_settings() -> tuple[SearchSettings, SearchSettings | None]:
    result = op.get_bind().execute(
        sa.text(
            """
        SELECT * FROM search_settings WHERE status = 'PRESENT' ORDER BY id DESC LIMIT 1
        """
        )
    )
    search_settings_fetch = result.fetchall()
    search_settings = (
        SearchSettings(**search_settings_fetch[0]._asdict())
        if search_settings_fetch
        else None
    )

    result2 = op.get_bind().execute(
        sa.text(
            """
        SELECT * FROM search_settings WHERE status = 'FUTURE' ORDER BY id DESC LIMIT 1
        """
        )
    )
    search_settings_future_fetch = result2.fetchall()
    search_settings_future = (
        SearchSettings(**search_settings_future_fetch[0]._asdict())
        if search_settings_future_fetch
        else None
    )

    if not isinstance(search_settings, SearchSettings):
        raise RuntimeError(
            "current search settings is of type " + str(type(search_settings))
        )
    if (
        not isinstance(search_settings_future, SearchSettings)
        and search_settings_future is not None
    ):
        raise RuntimeError(
            "future search settings is of type " + str(type(search_settings_future))
        )

    return search_settings, search_settings_future


def normalize_google_drive_url(url: str) -> str:
    """Remove query parameters from Google Drive URLs to create canonical document IDs.
    NOTE: copied from drive doc_conversion.py
    """
    parsed_url = urlparse(url)
    parsed_url = parsed_url._replace(query="")
    spl_path = parsed_url.path.split("/")
    if spl_path and (spl_path[-1] in ["edit", "view", "preview"]):
        spl_path.pop()
        parsed_url = parsed_url._replace(path="/".join(spl_path))
    # Remove query parameters and reconstruct URL
    return urlunparse(parsed_url)


def get_google_drive_documents_from_database() -> list[dict]:
    """Get all Google Drive documents from the database."""
    bind = op.get_bind()
    result = bind.execute(
        sa.text(
            """
            SELECT d.id, cc.id as cc_pair_id
            FROM document d
            JOIN document_by_connector_credential_pair dcc ON d.id = dcc.id
            JOIN connector_credential_pair cc ON dcc.connector_id = cc.connector_id
                AND dcc.credential_id = cc.credential_id
            JOIN connector c ON cc.connector_id = c.id
            WHERE c.source = 'GOOGLE_DRIVE'
        """
        )
    )

    documents = []
    for row in result:
        documents.append({"document_id": row.id, "cc_pair_id": row.cc_pair_id})

    return documents


def update_document_id_in_database(
    old_doc_id: str, new_doc_id: str, index_name: str
) -> None:
    """Update document IDs in all relevant database tables using copy-and-swap approach."""
    bind = op.get_bind()

    # print(f"Updating database tables for document {old_doc_id} -> {new_doc_id}")

    # Check if new document ID already exists
    result = bind.execute(
        sa.text("SELECT COUNT(*) FROM document WHERE id = :new_id"),
        {"new_id": new_doc_id},
    )
    row = result.fetchone()
    if row and row[0] > 0:
        # print(f"Document with ID {new_doc_id} already exists, deleting old one")
        delete_document_from_db(old_doc_id, index_name)
        return

    # Step 1: Create a new document row with the new ID (copy all fields from old row)
    # Use a conservative approach to handle columns that might not exist in all installations
    try:
        bind.execute(
            sa.text(
                """
                INSERT INTO document (id, from_ingestion_api, boost, hidden, semantic_id,
                                    link, doc_updated_at, primary_owners, secondary_owners,
                                    external_user_emails, external_user_group_ids, is_public,
                                    chunk_count, last_modified, last_synced, kg_stage, kg_processing_time)
                SELECT :new_id, from_ingestion_api, boost, hidden, semantic_id,
                       link, doc_updated_at, primary_owners, secondary_owners,
                       external_user_emails, external_user_group_ids, is_public,
                       chunk_count, last_modified, last_synced, kg_stage, kg_processing_time
                FROM document
                WHERE id = :old_id
            """
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )
        # print(f"Successfully updated database tables for document {old_doc_id} -> {new_doc_id}")
    except Exception as e:
        # If the full INSERT fails, try a more basic version with only core columns
        logger.warning(f"Full INSERT failed, trying basic version: {e}")
        bind.execute(
            sa.text(
                """
                INSERT INTO document (id, from_ingestion_api, boost, hidden, semantic_id,
                                    link, doc_updated_at, primary_owners, secondary_owners)
                SELECT :new_id, from_ingestion_api, boost, hidden, semantic_id,
                       link, doc_updated_at, primary_owners, secondary_owners
                FROM document
                WHERE id = :old_id
            """
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )

    # Step 2: Update all foreign key references to point to the new ID

    # Update document_by_connector_credential_pair table
    bind.execute(
        sa.text(
            "UPDATE document_by_connector_credential_pair SET id = :new_id WHERE id = :old_id"
        ),
        {"new_id": new_doc_id, "old_id": old_doc_id},
    )
    # print(f"Successfully updated document_by_connector_credential_pair table for document {old_doc_id} -> {new_doc_id}")

    # Update search_doc table (stores search results for chat replay)
    # This is critical for agent functionality
    bind.execute(
        sa.text(
            "UPDATE search_doc SET document_id = :new_id WHERE document_id = :old_id"
        ),
        {"new_id": new_doc_id, "old_id": old_doc_id},
    )
    # print(f"Successfully updated search_doc table for document {old_doc_id} -> {new_doc_id}")
    # Update document_retrieval_feedback table (user feedback on documents)
    bind.execute(
        sa.text(
            "UPDATE document_retrieval_feedback SET document_id = :new_id WHERE document_id = :old_id"
        ),
        {"new_id": new_doc_id, "old_id": old_doc_id},
    )
    # print(f"Successfully updated document_retrieval_feedback table for document {old_doc_id} -> {new_doc_id}")
    # Update document__tag table (document-tag relationships)
    bind.execute(
        sa.text(
            "UPDATE document__tag SET document_id = :new_id WHERE document_id = :old_id"
        ),
        {"new_id": new_doc_id, "old_id": old_doc_id},
    )
    # print(f"Successfully updated document__tag table for document {old_doc_id} -> {new_doc_id}")
    # Update user_file table (user uploaded files linked to documents)
    bind.execute(
        sa.text(
            "UPDATE user_file SET document_id = :new_id WHERE document_id = :old_id"
        ),
        {"new_id": new_doc_id, "old_id": old_doc_id},
    )
    # print(f"Successfully updated user_file table for document {old_doc_id} -> {new_doc_id}")
    # Update KG and chunk_stats tables (these may not exist in all installations)
    try:
        # Update kg_entity table
        bind.execute(
            sa.text(
                "UPDATE kg_entity SET document_id = :new_id WHERE document_id = :old_id"
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )
        # print(f"Successfully updated kg_entity table for document {old_doc_id} -> {new_doc_id}")
        # Update kg_entity_extraction_staging table
        bind.execute(
            sa.text(
                "UPDATE kg_entity_extraction_staging SET document_id = :new_id WHERE document_id = :old_id"
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )
        # print(f"Successfully updated kg_entity_extraction_staging table for document {old_doc_id} -> {new_doc_id}")
        # Update kg_relationship table
        bind.execute(
            sa.text(
                "UPDATE kg_relationship SET source_document = :new_id WHERE source_document = :old_id"
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )
        # print(f"Successfully updated kg_relationship table for document {old_doc_id} -> {new_doc_id}")
        # Update kg_relationship_extraction_staging table
        bind.execute(
            sa.text(
                "UPDATE kg_relationship_extraction_staging SET source_document = :new_id WHERE source_document = :old_id"
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )
        # print(f"Successfully updated kg_relationship_extraction_staging table for document {old_doc_id} -> {new_doc_id}")
        # Update chunk_stats table
        bind.execute(
            sa.text(
                "UPDATE chunk_stats SET document_id = :new_id WHERE document_id = :old_id"
            ),
            {"new_id": new_doc_id, "old_id": old_doc_id},
        )
        # print(f"Successfully updated chunk_stats table for document {old_doc_id} -> {new_doc_id}")
        # Update chunk_stats ID field which includes document_id
        bind.execute(
            sa.text(
                """
                UPDATE chunk_stats
                SET id = REPLACE(id, :old_id, :new_id)
                WHERE id LIKE :old_id_pattern
            """
            ),
            {
                "new_id": new_doc_id,
                "old_id": old_doc_id,
                "old_id_pattern": f"{old_doc_id}__%",
            },
        )
        # print(f"Successfully updated chunk_stats ID field for document {old_doc_id} -> {new_doc_id}")
    except Exception as e:
        logger.warning(f"Some KG/chunk tables may not exist or failed to update: {e}")

    # Step 3: Delete the old document row (this should now be safe since all FKs point to new row)
    bind.execute(
        sa.text("DELETE FROM document WHERE id = :old_id"), {"old_id": old_doc_id}
    )
    # print(f"Successfully deleted document {old_doc_id} from database")


def _visit_chunks(
    *,
    http_client: httpx.Client,
    index_name: str,
    selection: str,
    continuation: str | None = None,
) -> tuple[list[dict], str | None]:
    """Helper that calls the /document/v1 visit API once and returns (docs, next_token)."""

    # Use the same URL as the document API, but with visit-specific params
    base_url = DOCUMENT_ID_ENDPOINT.format(index_name=index_name)

    params: dict[str, str] = {
        "selection": selection,
        "wantedDocumentCount": "1000",
    }
    if continuation:
        params["continuation"] = continuation

    # print(f"Visiting chunks for selection '{selection}' with params {params}")
    resp = http_client.get(base_url, params=params, timeout=None)
    # print(f"Visited chunks for document {selection}")
    resp.raise_for_status()

    payload = resp.json()
    return payload.get("documents", []), payload.get("continuation")


def delete_document_chunks_from_vespa(index_name: str, doc_id: str) -> None:
    """Delete all chunks for *doc_id* from Vespa using continuation-token paging (no offset)."""

    total_deleted = 0
    # Use exact match instead of contains - Document Selector Language doesn't support contains
    selection = f'{index_name}.document_id=="{doc_id}"'

    with get_vespa_http_client() as http_client:
        continuation: str | None = None
        while True:
            docs, continuation = _visit_chunks(
                http_client=http_client,
                index_name=index_name,
                selection=selection,
                continuation=continuation,
            )

            if not docs:
                break

            for doc in docs:
                vespa_full_id = doc.get("id")
                if not vespa_full_id:
                    continue

                vespa_doc_uuid = vespa_full_id.split("::")[-1]
                delete_url = f"{DOCUMENT_ID_ENDPOINT.format(index_name=index_name)}/{vespa_doc_uuid}"

                try:
                    resp = http_client.delete(delete_url)
                    resp.raise_for_status()
                    total_deleted += 1
                except Exception as e:
                    print(f"Failed to delete chunk {vespa_doc_uuid}: {e}")

            if not continuation:
                break


def update_document_id_in_vespa(
    index_name: str, old_doc_id: str, new_doc_id: str
) -> None:
    """Update all chunks' document_id field from *old_doc_id* to *new_doc_id* using continuation paging."""

    clean_new_doc_id = replace_invalid_doc_id_characters(new_doc_id)

    # Use exact match instead of contains - Document Selector Language doesn't support contains
    selection = f'{index_name}.document_id=="{old_doc_id}"'

    with get_vespa_http_client() as http_client:
        continuation: str | None = None
        while True:
            # print(f"Visiting chunks for document {old_doc_id} -> {new_doc_id}")
            docs, continuation = _visit_chunks(
                http_client=http_client,
                index_name=index_name,
                selection=selection,
                continuation=continuation,
            )

            if not docs:
                break

            for doc in docs:
                vespa_full_id = doc.get("id")
                if not vespa_full_id:
                    continue

                vespa_doc_uuid = vespa_full_id.split("::")[-1]
                vespa_url = f"{DOCUMENT_ID_ENDPOINT.format(index_name=index_name)}/{vespa_doc_uuid}"

                update_request = {
                    "fields": {"document_id": {"assign": clean_new_doc_id}}
                }

                try:
                    resp = http_client.put(vespa_url, json=update_request)
                    resp.raise_for_status()
                except Exception as e:
                    print(f"Failed to update chunk {vespa_doc_uuid}: {e}")
                    raise

            if not continuation:
                break


def delete_document_from_db(current_doc_id: str, index_name: str) -> None:
    # Delete all foreign key references first, then delete the document
    try:
        bind = op.get_bind()

        # Delete from agent-related tables first (order matters due to foreign keys)
        # Delete from agent__sub_query__search_doc first since it references search_doc
        bind.execute(
            sa.text(
                """
                DELETE FROM agent__sub_query__search_doc
                WHERE search_doc_id IN (
                    SELECT id FROM search_doc WHERE document_id = :doc_id
                )
                """
            ),
            {"doc_id": current_doc_id},
        )

        # Delete from chat_message__search_doc
        bind.execute(
            sa.text(
                """
                DELETE FROM chat_message__search_doc
                WHERE search_doc_id IN (
                    SELECT id FROM search_doc WHERE document_id = :doc_id
                )
                """
            ),
            {"doc_id": current_doc_id},
        )

        # Now we can safely delete from search_doc
        bind.execute(
            sa.text("DELETE FROM search_doc WHERE document_id = :doc_id"),
            {"doc_id": current_doc_id},
        )

        # Delete from document_by_connector_credential_pair
        bind.execute(
            sa.text(
                "DELETE FROM document_by_connector_credential_pair WHERE id = :doc_id"
            ),
            {"doc_id": current_doc_id},
        )

        # Delete from other tables that reference this document
        bind.execute(
            sa.text(
                "DELETE FROM document_retrieval_feedback WHERE document_id = :doc_id"
            ),
            {"doc_id": current_doc_id},
        )

        bind.execute(
            sa.text("DELETE FROM document__tag WHERE document_id = :doc_id"),
            {"doc_id": current_doc_id},
        )

        bind.execute(
            sa.text("DELETE FROM user_file WHERE document_id = :doc_id"),
            {"doc_id": current_doc_id},
        )

        # Delete from KG tables if they exist
        try:
            bind.execute(
                sa.text("DELETE FROM kg_entity WHERE document_id = :doc_id"),
                {"doc_id": current_doc_id},
            )

            bind.execute(
                sa.text(
                    "DELETE FROM kg_entity_extraction_staging WHERE document_id = :doc_id"
                ),
                {"doc_id": current_doc_id},
            )

            bind.execute(
                sa.text("DELETE FROM kg_relationship WHERE source_document = :doc_id"),
                {"doc_id": current_doc_id},
            )

            bind.execute(
                sa.text(
                    "DELETE FROM kg_relationship_extraction_staging WHERE source_document = :doc_id"
                ),
                {"doc_id": current_doc_id},
            )

            bind.execute(
                sa.text("DELETE FROM chunk_stats WHERE document_id = :doc_id"),
                {"doc_id": current_doc_id},
            )

            bind.execute(
                sa.text("DELETE FROM chunk_stats WHERE id LIKE :doc_id_pattern"),
                {"doc_id_pattern": f"{current_doc_id}__%"},
            )

        except Exception as e:
            logger.warning(
                f"Some KG/chunk tables may not exist or failed to delete from: {e}"
            )

        # Finally delete the document itself
        bind.execute(
            sa.text("DELETE FROM document WHERE id = :doc_id"),
            {"doc_id": current_doc_id},
        )

        # Delete chunks from vespa
        delete_document_chunks_from_vespa(index_name, current_doc_id)

    except Exception as e:
        print(f"Failed to delete duplicate document {current_doc_id}: {e}")
        # Continue with other documents instead of failing the entire migration


def upgrade() -> None:
    if SKIP_CANON_DRIVE_IDS:
        return
    current_search_settings, future_search_settings = active_search_settings()
    document_index = get_default_document_index(
        current_search_settings,
        future_search_settings,
    )

    # Get the index name
    if hasattr(document_index, "index_name"):
        index_name = document_index.index_name
    else:
        # Default index name if we can't get it from the document_index
        index_name = "danswer_index"

    # Get all Google Drive documents from the database (this is faster and more reliable)
    gdrive_documents = get_google_drive_documents_from_database()

    if not gdrive_documents:
        return

    # Track normalized document IDs to detect duplicates
    all_normalized_doc_ids = set()
    updated_count = 0

    for doc_info in gdrive_documents:
        current_doc_id = doc_info["document_id"]
        normalized_doc_id = normalize_google_drive_url(current_doc_id)

        print(f"Processing document {current_doc_id} -> {normalized_doc_id}")
        # Check for duplicates
        if normalized_doc_id in all_normalized_doc_ids:
            # print(f"Deleting duplicate document {current_doc_id}")
            delete_document_from_db(current_doc_id, index_name)
            continue

        all_normalized_doc_ids.add(normalized_doc_id)

        # If the document ID already doesn't have query parameters, skip it
        if current_doc_id == normalized_doc_id:
            # print(f"Skipping document {current_doc_id} -> {normalized_doc_id} because it already has no query parameters")
            continue

        try:
            # Update both database and Vespa in order
            # Database first to ensure consistency
            update_document_id_in_database(
                current_doc_id, normalized_doc_id, index_name
            )

            # For Vespa, we can now use the original document IDs since we're using contains matching
            update_document_id_in_vespa(index_name, current_doc_id, normalized_doc_id)
            updated_count += 1
            # print(f"Finished updating document {current_doc_id} -> {normalized_doc_id}")
        except Exception as e:
            print(f"Failed to update document {current_doc_id}: {e}")

            if isinstance(e, HTTPStatusError):
                print(f"HTTPStatusError: {e}")
                print(f"Response: {e.response.text}")
                print(f"Status: {e.response.status_code}")
                print(f"Headers: {e.response.headers}")
                print(f"Request: {e.request.url}")
                print(f"Request headers: {e.request.headers}")
            # Note: Rollback is complex with copy-and-swap approach since the old document is already deleted
            # In case of failure, manual intervention may be required
            # Continue with other documents instead of failing the entire migration
            continue

    logger.info(f"Migration complete. Updated {updated_count} Google Drive documents")


def downgrade() -> None:
    # this is a one way migration, so no downgrade.
    # It wouldn't make sense to store the extra query parameters
    # and duplicate documents to allow a reversal.
    pass



================================================
FILE: backend/alembic/versions/15326fcec57e_introduce_onyx_apis.py
================================================
"""Introduce Onyx APIs

Revision ID: 15326fcec57e
Revises: 77d07dffae64
Create Date: 2023-11-11 20:51:24.228999

"""

from alembic import op
import sqlalchemy as sa

from onyx.configs.constants import DocumentSource

# revision identifiers, used by Alembic.
revision = "15326fcec57e"
down_revision = "77d07dffae64"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.alter_column("credential", "is_admin", new_column_name="admin_public")
    op.add_column(
        "document",
        sa.Column("from_ingestion_api", sa.Boolean(), nullable=True),
    )
    op.alter_column(
        "connector",
        "source",
        type_=sa.String(length=50),
        existing_type=sa.Enum(DocumentSource, native_enum=False),
        existing_nullable=False,
    )


def downgrade() -> None:
    op.drop_column("document", "from_ingestion_api")
    op.alter_column("credential", "admin_public", new_column_name="is_admin")



================================================
FILE: backend/alembic/versions/173cae5bba26_port_config_store.py
================================================
"""Port Config Store

Revision ID: 173cae5bba26
Revises: e50154680a5c
Create Date: 2024-03-19 15:30:44.425436

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "173cae5bba26"
down_revision = "e50154680a5c"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "key_value_store",
        sa.Column("key", sa.String(), nullable=False),
        sa.Column("value", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.PrimaryKeyConstraint("key"),
    )


def downgrade() -> None:
    op.drop_table("key_value_store")



================================================
FILE: backend/alembic/versions/177de57c21c9_display_custom_llm_models.py
================================================
"""display custom llm models

Revision ID: 177de57c21c9
Revises: 4ee1287bd26a
Create Date: 2024-11-21 11:49:04.488677

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import and_

revision = "177de57c21c9"
down_revision = "4ee1287bd26a"
branch_labels = None
depends_on = None
depends_on = None


def upgrade() -> None:
    conn = op.get_bind()
    llm_provider = sa.table(
        "llm_provider",
        sa.column("id", sa.Integer),
        sa.column("provider", sa.String),
        sa.column("model_names", postgresql.ARRAY(sa.String)),
        sa.column("display_model_names", postgresql.ARRAY(sa.String)),
    )

    excluded_providers = ["openai", "bedrock", "anthropic", "azure"]

    providers_to_update = sa.select(
        llm_provider.c.id,
        llm_provider.c.model_names,
        llm_provider.c.display_model_names,
    ).where(
        and_(
            ~llm_provider.c.provider.in_(excluded_providers),
            llm_provider.c.model_names.isnot(None),
        )
    )

    results = conn.execute(providers_to_update).fetchall()

    for provider_id, model_names, display_model_names in results:
        if display_model_names is None:
            display_model_names = []

        combined_model_names = list(set(display_model_names + model_names))
        update_stmt = (
            llm_provider.update()
            .where(llm_provider.c.id == provider_id)
            .values(display_model_names=combined_model_names)
        )
        conn.execute(update_stmt)


def downgrade() -> None:
    pass



================================================
FILE: backend/alembic/versions/1a03d2c2856b_add_indexes_to_document__tag.py
================================================
"""Add indexes to document__tag

Revision ID: 1a03d2c2856b
Revises: 9c00a2bccb83
Create Date: 2025-02-18 10:45:13.957807

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "1a03d2c2856b"
down_revision = "9c00a2bccb83"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_index(
        op.f("ix_document__tag_tag_id"),
        "document__tag",
        ["tag_id"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(op.f("ix_document__tag_tag_id"), table_name="document__tag")



================================================
FILE: backend/alembic/versions/1b10e1fda030_add_additional_data_to_notifications.py
================================================
"""add additional data to notifications

Revision ID: 1b10e1fda030
Revises: 6756efa39ada
Create Date: 2024-10-15 19:26:44.071259

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "1b10e1fda030"
down_revision = "6756efa39ada"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "notification", sa.Column("additional_data", postgresql.JSONB(), nullable=True)
    )


def downgrade() -> None:
    op.drop_column("notification", "additional_data")



================================================
FILE: backend/alembic/versions/1b8206b29c5d_add_user_delete_cascades.py
================================================
"""add_user_delete_cascades

Revision ID: 1b8206b29c5d
Revises: 35e6853a51d5
Create Date: 2024-09-18 11:48:59.418726

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "1b8206b29c5d"
down_revision = "35e6853a51d5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_constraint("credential_user_id_fkey", "credential", type_="foreignkey")
    op.create_foreign_key(
        "credential_user_id_fkey",
        "credential",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    op.drop_constraint("chat_session_user_id_fkey", "chat_session", type_="foreignkey")
    op.create_foreign_key(
        "chat_session_user_id_fkey",
        "chat_session",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    op.drop_constraint("chat_folder_user_id_fkey", "chat_folder", type_="foreignkey")
    op.create_foreign_key(
        "chat_folder_user_id_fkey",
        "chat_folder",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    op.drop_constraint("prompt_user_id_fkey", "prompt", type_="foreignkey")
    op.create_foreign_key(
        "prompt_user_id_fkey", "prompt", "user", ["user_id"], ["id"], ondelete="CASCADE"
    )

    op.drop_constraint("notification_user_id_fkey", "notification", type_="foreignkey")
    op.create_foreign_key(
        "notification_user_id_fkey",
        "notification",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    op.drop_constraint("inputprompt_user_id_fkey", "inputprompt", type_="foreignkey")
    op.create_foreign_key(
        "inputprompt_user_id_fkey",
        "inputprompt",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    op.drop_constraint("credential_user_id_fkey", "credential", type_="foreignkey")
    op.create_foreign_key(
        "credential_user_id_fkey", "credential", "user", ["user_id"], ["id"]
    )

    op.drop_constraint("chat_session_user_id_fkey", "chat_session", type_="foreignkey")
    op.create_foreign_key(
        "chat_session_user_id_fkey", "chat_session", "user", ["user_id"], ["id"]
    )

    op.drop_constraint("chat_folder_user_id_fkey", "chat_folder", type_="foreignkey")
    op.create_foreign_key(
        "chat_folder_user_id_fkey", "chat_folder", "user", ["user_id"], ["id"]
    )

    op.drop_constraint("prompt_user_id_fkey", "prompt", type_="foreignkey")
    op.create_foreign_key("prompt_user_id_fkey", "prompt", "user", ["user_id"], ["id"])

    op.drop_constraint("notification_user_id_fkey", "notification", type_="foreignkey")
    op.create_foreign_key(
        "notification_user_id_fkey", "notification", "user", ["user_id"], ["id"]
    )

    op.drop_constraint("inputprompt_user_id_fkey", "inputprompt", type_="foreignkey")
    op.create_foreign_key(
        "inputprompt_user_id_fkey", "inputprompt", "user", ["user_id"], ["id"]
    )



================================================
FILE: backend/alembic/versions/1f60f60c3401_embedding_model_search_settings.py
================================================
"""embedding model -> search settings

Revision ID: 1f60f60c3401
Revises: f17bf3b0d9f1
Create Date: 2024-08-25 12:39:51.731632

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from onyx.configs.chat_configs import NUM_POSTPROCESSED_RESULTS

# revision identifiers, used by Alembic.
revision = "1f60f60c3401"
down_revision = "f17bf3b0d9f1"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.drop_constraint(
        "index_attempt__embedding_model_fk", "index_attempt", type_="foreignkey"
    )
    # Rename the table
    op.rename_table("embedding_model", "search_settings")

    # Add new columns
    op.add_column(
        "search_settings",
        sa.Column(
            "multipass_indexing", sa.Boolean(), nullable=False, server_default="false"
        ),
    )
    op.add_column(
        "search_settings",
        sa.Column(
            "multilingual_expansion",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
    )
    op.add_column(
        "search_settings",
        sa.Column(
            "disable_rerank_for_streaming",
            sa.Boolean(),
            nullable=False,
            server_default="false",
        ),
    )
    op.add_column(
        "search_settings", sa.Column("rerank_model_name", sa.String(), nullable=True)
    )
    op.add_column(
        "search_settings", sa.Column("rerank_provider_type", sa.String(), nullable=True)
    )
    op.add_column(
        "search_settings", sa.Column("rerank_api_key", sa.String(), nullable=True)
    )
    op.add_column(
        "search_settings",
        sa.Column(
            "num_rerank",
            sa.Integer(),
            nullable=False,
            server_default=str(NUM_POSTPROCESSED_RESULTS),
        ),
    )

    # Add the new column as nullable initially
    op.add_column(
        "index_attempt", sa.Column("search_settings_id", sa.Integer(), nullable=True)
    )

    # Populate the new column with data from the existing embedding_model_id
    op.execute("UPDATE index_attempt SET search_settings_id = embedding_model_id")

    # Create the foreign key constraint
    op.create_foreign_key(
        "fk_index_attempt_search_settings",
        "index_attempt",
        "search_settings",
        ["search_settings_id"],
        ["id"],
    )

    # Make the new column non-nullable
    op.alter_column("index_attempt", "search_settings_id", nullable=False)

    # Drop the old embedding_model_id column
    op.drop_column("index_attempt", "embedding_model_id")


def downgrade() -> None:
    # Add back the embedding_model_id column
    op.add_column(
        "index_attempt", sa.Column("embedding_model_id", sa.Integer(), nullable=True)
    )

    # Populate the old column with data from search_settings_id
    op.execute("UPDATE index_attempt SET embedding_model_id = search_settings_id")

    # Make the old column non-nullable
    op.alter_column("index_attempt", "embedding_model_id", nullable=False)

    # Drop the foreign key constraint
    op.drop_constraint(
        "fk_index_attempt_search_settings", "index_attempt", type_="foreignkey"
    )

    # Drop the new search_settings_id column
    op.drop_column("index_attempt", "search_settings_id")

    # Rename the table back
    op.rename_table("search_settings", "embedding_model")

    # Remove added columns
    op.drop_column("embedding_model", "num_rerank")
    op.drop_column("embedding_model", "rerank_api_key")
    op.drop_column("embedding_model", "rerank_provider_type")
    op.drop_column("embedding_model", "rerank_model_name")
    op.drop_column("embedding_model", "disable_rerank_for_streaming")
    op.drop_column("embedding_model", "multilingual_expansion")
    op.drop_column("embedding_model", "multipass_indexing")

    op.create_foreign_key(
        "index_attempt__embedding_model_fk",
        "index_attempt",
        "embedding_model",
        ["embedding_model_id"],
        ["id"],
    )



================================================
FILE: backend/alembic/versions/213fd978c6d8_notifications.py
================================================
"""notifications

Revision ID: 213fd978c6d8
Revises: 5fc1f54cc252
Create Date: 2024-08-10 11:13:36.070790

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "213fd978c6d8"
down_revision = "5fc1f54cc252"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "notification",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "notif_type",
            sa.String(),
            nullable=False,
        ),
        sa.Column(
            "user_id",
            sa.UUID(),
            nullable=True,
        ),
        sa.Column("dismissed", sa.Boolean(), nullable=False),
        sa.Column("last_shown", sa.DateTime(timezone=True), nullable=False),
        sa.Column("first_shown", sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("notification")



================================================
FILE: backend/alembic/versions/238b84885828_add_foreign_key_to_user__external_user_.py
================================================
"""Add foreign key to user__external_user_group_id

Revision ID: 238b84885828
Revises: a7688ab35c45
Create Date: 2025-05-19 17:15:33.424584

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "238b84885828"
down_revision = "a7688ab35c45"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # First, clean up any entries that don't have a valid cc_pair_id
    op.execute(
        """
        DELETE FROM user__external_user_group_id
        WHERE cc_pair_id NOT IN (SELECT id FROM connector_credential_pair)
        """
    )

    # Add foreign key constraint with cascade delete
    op.create_foreign_key(
        "fk_user__external_user_group_id_cc_pair_id",
        "user__external_user_group_id",
        "connector_credential_pair",
        ["cc_pair_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    # Drop the foreign key constraint
    op.drop_constraint(
        "fk_user__external_user_group_id_cc_pair_id",
        "user__external_user_group_id",
        type_="foreignkey",
    )



================================================
FILE: backend/alembic/versions/23957775e5f5_remove_feedback_foreignkey_constraint.py
================================================
"""remove-feedback-foreignkey-constraint

Revision ID: 23957775e5f5
Revises: bc9771dccadf
Create Date: 2024-06-27 16:04:51.480437

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "23957775e5f5"
down_revision = "bc9771dccadf"
branch_labels = None  # type: ignore
depends_on = None  # type: ignore


def upgrade() -> None:
    op.drop_constraint(
        "chat_feedback__chat_message_fk", "chat_feedback", type_="foreignkey"
    )
    op.create_foreign_key(
        "chat_feedback__chat_message_fk",
        "chat_feedback",
        "chat_message",
        ["chat_message_id"],
        ["id"],
        ondelete="SET NULL",
    )
    op.alter_column(
        "chat_feedback", "chat_message_id", existing_type=sa.Integer(), nullable=True
    )
    op.drop_constraint(
        "document_retrieval_feedback__chat_message_fk",
        "document_retrieval_feedback",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "document_retrieval_feedback__chat_message_fk",
        "document_retrieval_feedback",
        "chat_message",
        ["chat_message_id"],
        ["id"],
        ondelete="SET NULL",
    )
    op.alter_column(
        "document_retrieval_feedback",
        "chat_message_id",
        existing_type=sa.Integer(),
        nullable=True,
    )


def downgrade() -> None:
    op.alter_column(
        "chat_feedback", "chat_message_id", existing_type=sa.Integer(), nullable=False
    )
    op.drop_constraint(
        "chat_feedback__chat_message_fk", "chat_feedback", type_="foreignkey"
    )
    op.create_foreign_key(
        "chat_feedback__chat_message_fk",
        "chat_feedback",
        "chat_message",
        ["chat_message_id"],
        ["id"],
    )

    op.alter_column(
        "document_retrieval_feedback",
        "chat_message_id",
        existing_type=sa.Integer(),
        nullable=False,
    )
    op.drop_constraint(
        "document_retrieval_feedback__chat_message_fk",
        "document_retrieval_feedback",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "document_retrieval_feedback__chat_message_fk",
        "document_retrieval_feedback",
        "chat_message",
        ["chat_message_id"],
        ["id"],
    )



================================================
FILE: backend/alembic/versions/2666d766cb9b_google_oauth2.py
================================================
"""Google OAuth2

Revision ID: 2666d766cb9b
Revises: 6d387b3196c2
Create Date: 2023-05-05 15:49:35.716016

"""

import fastapi_users_db_sqlalchemy
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = "2666d766cb9b"
down_revision = "6d387b3196c2"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "oauth_account",
        sa.Column("id", fastapi_users_db_sqlalchemy.generics.GUID(), nullable=False),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=False,
        ),
        sa.Column("oauth_name", sa.String(length=100), nullable=False),
        sa.Column("access_token", sa.String(length=1024), nullable=False),
        sa.Column("expires_at", sa.Integer(), nullable=True),
        sa.Column("refresh_token", sa.String(length=1024), nullable=True),
        sa.Column("account_id", sa.String(length=320), nullable=False),
        sa.Column("account_email", sa.String(length=320), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="cascade"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_oauth_account_account_id"),
        "oauth_account",
        ["account_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_oauth_account_oauth_name"),
        "oauth_account",
        ["oauth_name"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(op.f("ix_oauth_account_oauth_name"), table_name="oauth_account")
    op.drop_index(op.f("ix_oauth_account_account_id"), table_name="oauth_account")
    op.drop_table("oauth_account")



================================================
FILE: backend/alembic/versions/26b931506ecb_default_chosen_assistants_to_none.py
================================================
"""default chosen assistants to none

Revision ID: 26b931506ecb
Revises: 2daa494a0851
Create Date: 2024-11-12 13:23:29.858995

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "26b931506ecb"
down_revision = "2daa494a0851"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "user", sa.Column("chosen_assistants_new", postgresql.JSONB(), nullable=True)
    )

    op.execute(
        """
    UPDATE "user"
    SET chosen_assistants_new =
        CASE
            WHEN chosen_assistants = '[-2, -1, 0]' THEN NULL
            ELSE chosen_assistants
        END
    """
    )

    op.drop_column("user", "chosen_assistants")

    op.alter_column(
        "user", "chosen_assistants_new", new_column_name="chosen_assistants"
    )


def downgrade() -> None:
    op.add_column(
        "user",
        sa.Column(
            "chosen_assistants_old",
            postgresql.JSONB(),
            nullable=False,
            server_default="[-2, -1, 0]",
        ),
    )

    op.execute(
        """
    UPDATE "user"
    SET chosen_assistants_old =
        CASE
            WHEN chosen_assistants IS NULL THEN '[-2, -1, 0]'::jsonb
            ELSE chosen_assistants
        END
    """
    )

    op.drop_column("user", "chosen_assistants")

    op.alter_column(
        "user", "chosen_assistants_old", new_column_name="chosen_assistants"
    )



================================================
FILE: backend/alembic/versions/27c6ecc08586_permission_framework.py
================================================
"""Permission Framework

Revision ID: 27c6ecc08586
Revises: 2666d766cb9b
Create Date: 2023-05-24 18:45:17.244495

"""

import fastapi_users_db_sqlalchemy
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "27c6ecc08586"
down_revision = "2666d766cb9b"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.execute("TRUNCATE TABLE index_attempt")
    op.create_table(
        "connector",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column(
            "source",
            sa.Enum(
                "SLACK",
                "WEB",
                "GOOGLE_DRIVE",
                "GITHUB",
                "CONFLUENCE",
                name="documentsource",
                native_enum=False,
            ),
            nullable=False,
        ),
        sa.Column(
            "input_type",
            sa.Enum(
                "LOAD_STATE",
                "POLL",
                "EVENT",
                name="inputtype",
                native_enum=False,
            ),
            nullable=True,
        ),
        sa.Column(
            "connector_specific_config",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
        ),
        sa.Column("refresh_freq", sa.Integer(), nullable=True),
        sa.Column(
            "time_created",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("disabled", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "credential",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "credential_json",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
        ),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
        sa.Column("public_doc", sa.Boolean(), nullable=False),
        sa.Column(
            "time_created",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "connector_credential_pair",
        sa.Column("connector_id", sa.Integer(), nullable=False),
        sa.Column("credential_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["connector_id"],
            ["connector.id"],
        ),
        sa.ForeignKeyConstraint(
            ["credential_id"],
            ["credential.id"],
        ),
        sa.PrimaryKeyConstraint("connector_id", "credential_id"),
    )
    op.add_column(
        "index_attempt",
        sa.Column("connector_id", sa.Integer(), nullable=True),
    )
    op.add_column(
        "index_attempt",
        sa.Column("credential_id", sa.Integer(), nullable=True),
    )
    op.create_foreign_key(
        "fk_index_attempt_credential_id",
        "index_attempt",
        "credential",
        ["credential_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_index_attempt_connector_id",
        "index_attempt",
        "connector",
        ["connector_id"],
        ["id"],
    )
    op.drop_column("index_attempt", "connector_specific_config")
    op.drop_column("index_attempt", "source")
    op.drop_column("index_attempt", "input_type")


def downgrade() -> None:
    op.execute("TRUNCATE TABLE index_attempt")
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    existing_columns = {col["name"] for col in inspector.get_columns("index_attempt")}

    if "input_type" not in existing_columns:
        op.add_column(
            "index_attempt",
            sa.Column("input_type", sa.VARCHAR(), autoincrement=False, nullable=False),
        )

    if "source" not in existing_columns:
        op.add_column(
            "index_attempt",
            sa.Column("source", sa.VARCHAR(), autoincrement=False, nullable=False),
        )

    if "connector_specific_config" not in existing_columns:
        op.add_column(
            "index_attempt",
            sa.Column(
                "connector_specific_config",
                postgresql.JSONB(astext_type=sa.Text()),
                autoincrement=False,
                nullable=False,
            ),
        )

    # Check if the constraint exists before dropping
    constraints = inspector.get_foreign_keys("index_attempt")

    if any(
        constraint["name"] == "fk_index_attempt_credential_id"
        for constraint in constraints
    ):
        op.drop_constraint(
            "fk_index_attempt_credential_id", "index_attempt", type_="foreignkey"
        )

    if any(
        constraint["name"] == "fk_index_attempt_connector_id"
        for constraint in constraints
    ):
        op.drop_constraint(
            "fk_index_attempt_connector_id", "index_attempt", type_="foreignkey"
        )

    if "credential_id" in existing_columns:
        op.drop_column("index_attempt", "credential_id")

    if "connector_id" in existing_columns:
        op.drop_column("index_attempt", "connector_id")

    op.execute("DROP TABLE IF EXISTS connector_credential_pair CASCADE")
    op.execute("DROP TABLE IF EXISTS credential CASCADE")
    op.execute("DROP TABLE IF EXISTS connector CASCADE")



================================================
FILE: backend/alembic/versions/2955778aa44c_add_chunk_count_to_document.py
================================================
"""add chunk count to document

Revision ID: 2955778aa44c
Revises: c0aab6edb6dd
Create Date: 2025-01-04 11:39:43.268612

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "2955778aa44c"
down_revision = "c0aab6edb6dd"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column("document", sa.Column("chunk_count", sa.Integer(), nullable=True))


def downgrade() -> None:
    op.drop_column("document", "chunk_count")



================================================
FILE: backend/alembic/versions/2cdeff6d8c93_set_built_in_to_default.py
================================================
"""set built in to default

Revision ID: 2cdeff6d8c93
Revises: f5437cc136c5
Create Date: 2025-02-11 14:57:51.308775

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "2cdeff6d8c93"
down_revision = "f5437cc136c5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Prior to this migration / point in the codebase history,
    # built in personas were implicitly treated as default personas (with no option to change this)
    # This migration makes that explicit
    op.execute(
        """
        UPDATE persona
        SET is_default_persona = TRUE
        WHERE builtin_persona = TRUE
    """
    )


def downgrade() -> None:
    pass



================================================
FILE: backend/alembic/versions/2d2304e27d8c_add_above_below_to_persona.py
================================================
"""Add Above Below to Persona

Revision ID: 2d2304e27d8c
Revises: 4b08d97e175a
Create Date: 2024-08-21 19:15:15.762948

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2d2304e27d8c"
down_revision = "4b08d97e175a"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("persona", sa.Column("chunks_above", sa.Integer(), nullable=True))
    op.add_column("persona", sa.Column("chunks_below", sa.Integer(), nullable=True))

    op.execute(
        "UPDATE persona SET chunks_above = 1, chunks_below = 1 WHERE chunks_above IS NULL AND chunks_below IS NULL"
    )

    op.alter_column("persona", "chunks_above", nullable=False)
    op.alter_column("persona", "chunks_below", nullable=False)


def downgrade() -> None:
    op.drop_column("persona", "chunks_below")
    op.drop_column("persona", "chunks_above")



================================================
FILE: backend/alembic/versions/2daa494a0851_add_group_sync_time.py
================================================
"""add-group-sync-time

Revision ID: 2daa494a0851
Revises: c0fd6e4da83a
Create Date: 2024-11-11 10:57:22.991157

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2daa494a0851"
down_revision = "c0fd6e4da83a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "last_time_external_group_sync",
            sa.DateTime(timezone=True),
            nullable=True,
        ),
    )


def downgrade() -> None:
    op.drop_column("connector_credential_pair", "last_time_external_group_sync")



================================================
FILE: backend/alembic/versions/2f80c6a2550f_add_chat_session_specific_temperature_.py
================================================
"""add chat session specific temperature override

Revision ID: 2f80c6a2550f
Revises: 33ea50e88f24
Create Date: 2025-01-31 10:30:27.289646

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "2f80c6a2550f"
down_revision = "33ea50e88f24"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "chat_session", sa.Column("temperature_override", sa.Float(), nullable=True)
    )
    op.add_column(
        "user",
        sa.Column(
            "temperature_override_enabled",
            sa.Boolean(),
            nullable=False,
            server_default=sa.false(),
        ),
    )


def downgrade() -> None:
    op.drop_column("chat_session", "temperature_override")
    op.drop_column("user", "temperature_override_enabled")



================================================
FILE: backend/alembic/versions/30c1d5744104_persona_datetime_aware.py
================================================
"""Persona Datetime Aware

Revision ID: 30c1d5744104
Revises: 7f99be1cb9f5
Create Date: 2023-10-16 23:21:01.283424

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "30c1d5744104"
down_revision = "7f99be1cb9f5"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("persona", sa.Column("datetime_aware", sa.Boolean(), nullable=True))
    op.execute("UPDATE persona SET datetime_aware = TRUE")
    op.alter_column("persona", "datetime_aware", nullable=False)
    op.create_index(
        "_default_persona_name_idx",
        "persona",
        ["name"],
        unique=True,
        postgresql_where=sa.text("default_persona = true"),
    )


def downgrade() -> None:
    op.drop_index(
        "_default_persona_name_idx",
        table_name="persona",
        postgresql_where=sa.text("default_persona = true"),
    )
    op.drop_column("persona", "datetime_aware")



================================================
FILE: backend/alembic/versions/325975216eb3_add_icon_color_and_icon_shape_to_persona.py
================================================
"""Add icon_color and icon_shape to Persona

Revision ID: 325975216eb3
Revises: 91ffac7e65b3
Create Date: 2024-07-24 21:29:31.784562

"""

import random
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column, select

# revision identifiers, used by Alembic.
revision = "325975216eb3"
down_revision = "91ffac7e65b3"
branch_labels: None = None
depends_on: None = None


colorOptions = [
    "#FF6FBF",
    "#6FB1FF",
    "#B76FFF",
    "#FFB56F",
    "#6FFF8D",
    "#FF6F6F",
    "#6FFFFF",
]


# Function to generate a random shape ensuring at least 3 of the middle 4 squares are filled
def generate_random_shape() -> int:
    center_squares = [12, 10, 6, 14, 13, 11, 7, 15]
    center_fill = random.choice(center_squares)
    remaining_squares = [i for i in range(16) if not (center_fill & (1 << i))]
    random.shuffle(remaining_squares)
    for i in range(10 - bin(center_fill).count("1")):
        center_fill |= 1 << remaining_squares[i]
    return center_fill


def upgrade() -> None:
    op.add_column("persona", sa.Column("icon_color", sa.String(), nullable=True))
    op.add_column("persona", sa.Column("icon_shape", sa.Integer(), nullable=True))
    op.add_column("persona", sa.Column("uploaded_image_id", sa.String(), nullable=True))

    persona = table(
        "persona",
        column("id", sa.Integer),
        column("icon_color", sa.String),
        column("icon_shape", sa.Integer),
    )

    conn = op.get_bind()
    personas = conn.execute(select(persona.c.id))

    for persona_id in personas:
        random_color = random.choice(colorOptions)
        random_shape = generate_random_shape()
        conn.execute(
            persona.update()
            .where(persona.c.id == persona_id[0])
            .values(icon_color=random_color, icon_shape=random_shape)
        )


def downgrade() -> None:
    op.drop_column("persona", "icon_shape")
    op.drop_column("persona", "uploaded_image_id")
    op.drop_column("persona", "icon_color")



================================================
FILE: backend/alembic/versions/33cb72ea4d80_single_tool_call_per_message.py
================================================
"""single tool call per message

Revision ID: 33cb72ea4d80
Revises: 5b29123cd710
Create Date: 2024-11-01 12:51:01.535003

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "33cb72ea4d80"
down_revision = "5b29123cd710"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Step 1: Delete extraneous ToolCall entries
    # Keep only the ToolCall with the smallest 'id' for each 'message_id'
    op.execute(
        sa.text(
            """
            DELETE FROM tool_call
            WHERE id NOT IN (
                SELECT MIN(id)
                FROM tool_call
                WHERE message_id IS NOT NULL
                GROUP BY message_id
            );
        """
        )
    )

    # Step 2: Add a unique constraint on message_id
    op.create_unique_constraint(
        constraint_name="uq_tool_call_message_id",
        table_name="tool_call",
        columns=["message_id"],
    )


def downgrade() -> None:
    # Step 1: Drop the unique constraint on message_id
    op.drop_constraint(
        constraint_name="uq_tool_call_message_id",
        table_name="tool_call",
        type_="unique",
    )



================================================
FILE: backend/alembic/versions/33ea50e88f24_foreign_key_input_prompts.py
================================================
"""foreign key input prompts

Revision ID: 33ea50e88f24
Revises: a6df6b88ef81
Create Date: 2025-01-29 10:54:22.141765

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "33ea50e88f24"
down_revision = "a6df6b88ef81"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Safely drop constraints if exists
    op.execute(
        """
        ALTER TABLE inputprompt__user
        DROP CONSTRAINT IF EXISTS inputprompt__user_input_prompt_id_fkey
        """
    )
    op.execute(
        """
        ALTER TABLE inputprompt__user
        DROP CONSTRAINT IF EXISTS inputprompt__user_user_id_fkey
        """
    )

    # Recreate with ON DELETE CASCADE
    op.create_foreign_key(
        "inputprompt__user_input_prompt_id_fkey",
        "inputprompt__user",
        "inputprompt",
        ["input_prompt_id"],
        ["id"],
        ondelete="CASCADE",
    )

    op.create_foreign_key(
        "inputprompt__user_user_id_fkey",
        "inputprompt__user",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    # Drop the new FKs with ondelete
    op.drop_constraint(
        "inputprompt__user_input_prompt_id_fkey",
        "inputprompt__user",
        type_="foreignkey",
    )
    op.drop_constraint(
        "inputprompt__user_user_id_fkey",
        "inputprompt__user",
        type_="foreignkey",
    )

    # Recreate them without cascading
    op.create_foreign_key(
        "inputprompt__user_input_prompt_id_fkey",
        "inputprompt__user",
        "inputprompt",
        ["input_prompt_id"],
        ["id"],
    )
    op.create_foreign_key(
        "inputprompt__user_user_id_fkey",
        "inputprompt__user",
        "user",
        ["user_id"],
        ["id"],
    )



================================================
FILE: backend/alembic/versions/351faebd379d_add_curator_fields.py
================================================
"""Add curator fields

Revision ID: 351faebd379d
Revises: ee3f4b47fad5
Create Date: 2024-08-15 22:37:08.397052

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "351faebd379d"
down_revision = "ee3f4b47fad5"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # Add is_curator column to User__UserGroup table
    op.add_column(
        "user__user_group",
        sa.Column("is_curator", sa.Boolean(), nullable=False, server_default="false"),
    )

    # Use batch mode to modify the enum type
    with op.batch_alter_table("user", schema=None) as batch_op:
        batch_op.alter_column(  # type: ignore[attr-defined]
            "role",
            type_=sa.Enum(
                "BASIC",
                "ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                name="userrole",
                native_enum=False,
            ),
            existing_type=sa.Enum("BASIC", "ADMIN", name="userrole", native_enum=False),
            existing_nullable=False,
        )
    # Create the association table
    op.create_table(
        "credential__user_group",
        sa.Column("credential_id", sa.Integer(), nullable=False),
        sa.Column("user_group_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["credential_id"],
            ["credential.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_group_id"],
            ["user_group.id"],
        ),
        sa.PrimaryKeyConstraint("credential_id", "user_group_id"),
    )
    op.add_column(
        "credential",
        sa.Column(
            "curator_public", sa.Boolean(), nullable=False, server_default="false"
        ),
    )


def downgrade() -> None:
    # Update existing records to ensure they fit within the BASIC/ADMIN roles
    op.execute(
        "UPDATE \"user\" SET role = 'ADMIN' WHERE role IN ('CURATOR', 'GLOBAL_CURATOR')"
    )

    # Remove is_curator column from User__UserGroup table
    op.drop_column("user__user_group", "is_curator")

    with op.batch_alter_table("user", schema=None) as batch_op:
        batch_op.alter_column(  # type: ignore[attr-defined]
            "role",
            type_=sa.Enum(
                "BASIC", "ADMIN", name="userrole", native_enum=False, length=20
            ),
            existing_type=sa.Enum(
                "BASIC",
                "ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                name="userrole",
                native_enum=False,
            ),
            existing_nullable=False,
        )
    # Drop the association table
    op.drop_table("credential__user_group")
    op.drop_column("credential", "curator_public")



================================================
FILE: backend/alembic/versions/35e518e0ddf4_properly_cascade.py
================================================
"""properly_cascade

Revision ID: 35e518e0ddf4
Revises: 91a0a4d62b14
Create Date: 2024-09-20 21:24:04.891018

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "35e518e0ddf4"
down_revision = "91a0a4d62b14"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Update chat_message foreign key constraint
    op.drop_constraint(
        "chat_message_chat_session_id_fkey", "chat_message", type_="foreignkey"
    )
    op.create_foreign_key(
        "chat_message_chat_session_id_fkey",
        "chat_message",
        "chat_session",
        ["chat_session_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # Update chat_message__search_doc foreign key constraints
    op.drop_constraint(
        "chat_message__search_doc_chat_message_id_fkey",
        "chat_message__search_doc",
        type_="foreignkey",
    )
    op.drop_constraint(
        "chat_message__search_doc_search_doc_id_fkey",
        "chat_message__search_doc",
        type_="foreignkey",
    )

    op.create_foreign_key(
        "chat_message__search_doc_chat_message_id_fkey",
        "chat_message__search_doc",
        "chat_message",
        ["chat_message_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "chat_message__search_doc_search_doc_id_fkey",
        "chat_message__search_doc",
        "search_doc",
        ["search_doc_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # Add CASCADE delete for tool_call foreign key
    op.drop_constraint("tool_call_message_id_fkey", "tool_call", type_="foreignkey")
    op.create_foreign_key(
        "tool_call_message_id_fkey",
        "tool_call",
        "chat_message",
        ["message_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    # Revert chat_message foreign key constraint
    op.drop_constraint(
        "chat_message_chat_session_id_fkey", "chat_message", type_="foreignkey"
    )
    op.create_foreign_key(
        "chat_message_chat_session_id_fkey",
        "chat_message",
        "chat_session",
        ["chat_session_id"],
        ["id"],
    )

    # Revert chat_message__search_doc foreign key constraints
    op.drop_constraint(
        "chat_message__search_doc_chat_message_id_fkey",
        "chat_message__search_doc",
        type_="foreignkey",
    )
    op.drop_constraint(
        "chat_message__search_doc_search_doc_id_fkey",
        "chat_message__search_doc",
        type_="foreignkey",
    )

    op.create_foreign_key(
        "chat_message__search_doc_chat_message_id_fkey",
        "chat_message__search_doc",
        "chat_message",
        ["chat_message_id"],
        ["id"],
    )
    op.create_foreign_key(
        "chat_message__search_doc_search_doc_id_fkey",
        "chat_message__search_doc",
        "search_doc",
        ["search_doc_id"],
        ["id"],
    )

    # Revert tool_call foreign key constraint
    op.drop_constraint("tool_call_message_id_fkey", "tool_call", type_="foreignkey")
    op.create_foreign_key(
        "tool_call_message_id_fkey",
        "tool_call",
        "chat_message",
        ["message_id"],
        ["id"],
    )



================================================
FILE: backend/alembic/versions/35e6853a51d5_server_default_chosen_assistants.py
================================================
"""server default chosen assistants

Revision ID: 35e6853a51d5
Revises: c99d76fcd298
Create Date: 2024-09-13 13:20:32.885317

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "35e6853a51d5"
down_revision = "c99d76fcd298"
branch_labels = None
depends_on = None

DEFAULT_ASSISTANTS = [-2, -1, 0]


def upgrade() -> None:
    # Step 1: Update any NULL values to the default value
    # This upgrades existing users without ordered assistant
    # to have default assistants set to visible assistants which are
    # accessible by them.
    op.execute(
        """
        UPDATE "user" u
        SET chosen_assistants = (
            SELECT jsonb_agg(
                p.id ORDER BY
                    COALESCE(p.display_priority, 2147483647) ASC,
                    p.id ASC
            )
            FROM persona p
            LEFT JOIN persona__user pu ON p.id = pu.persona_id AND pu.user_id = u.id
            WHERE p.is_visible = true
            AND (p.is_public = true OR pu.user_id IS NOT NULL)
        )
        WHERE chosen_assistants IS NULL
        OR chosen_assistants = 'null'
        OR jsonb_typeof(chosen_assistants) = 'null'
        OR (jsonb_typeof(chosen_assistants) = 'string' AND chosen_assistants = '"null"')
    """
    )

    # Step 2: Alter the column to make it non-nullable
    op.alter_column(
        "user",
        "chosen_assistants",
        type_=postgresql.JSONB(astext_type=sa.Text()),
        nullable=False,
        server_default=sa.text(f"'{DEFAULT_ASSISTANTS}'::jsonb"),
    )


def downgrade() -> None:
    op.alter_column(
        "user",
        "chosen_assistants",
        type_=postgresql.JSONB(astext_type=sa.Text()),
        nullable=True,
        server_default=None,
    )



================================================
FILE: backend/alembic/versions/369644546676_add_composite_index_for_index_attempt_.py
================================================
"""add composite index for index attempt time updated

Revision ID: 369644546676
Revises: 2955778aa44c
Create Date: 2025-01-08 15:38:17.224380

"""

from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = "369644546676"
down_revision = "2955778aa44c"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_index(
        "ix_index_attempt_ccpair_search_settings_time_updated",
        "index_attempt",
        [
            "connector_credential_pair_id",
            "search_settings_id",
            text("time_updated DESC"),
        ],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(
        "ix_index_attempt_ccpair_search_settings_time_updated",
        table_name="index_attempt",
    )



================================================
FILE: backend/alembic/versions/36e9220ab794_update_kg_trigger_functions.py
================================================
"""update_kg_trigger_functions

Revision ID: 36e9220ab794
Revises: c9e2cd766c29
Create Date: 2025-06-22 17:33:25.833733

"""

from alembic import op
from sqlalchemy.orm import Session
from sqlalchemy import text
from shared_configs.configs import POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE

# revision identifiers, used by Alembic.
revision = "36e9220ab794"
down_revision = "c9e2cd766c29"
branch_labels = None
depends_on = None


def _get_tenant_contextvar(session: Session) -> str:
    """Get the current schema for the migration"""
    current_tenant = session.execute(text("SELECT current_schema()")).scalar()
    if isinstance(current_tenant, str):
        return current_tenant
    else:
        raise ValueError("Current tenant is not a string")


def upgrade() -> None:

    bind = op.get_bind()
    session = Session(bind=bind)

    # Create kg_entity trigger to update kg_entity.name and its trigrams
    tenant_id = _get_tenant_contextvar(session)
    alphanum_pattern = r"[^a-z0-9]+"
    truncate_length = 1000
    function = "update_kg_entity_name"
    op.execute(
        text(
            f"""
            CREATE OR REPLACE FUNCTION "{tenant_id}".{function}()
            RETURNS TRIGGER AS $$
            DECLARE
                name text;
                cleaned_name text;
            BEGIN
                -- Set name to semantic_id if document_id is not NULL
                IF NEW.document_id IS NOT NULL THEN
                    SELECT lower(semantic_id) INTO name
                    FROM "{tenant_id}".document
                    WHERE id = NEW.document_id;
                ELSE
                    name = lower(NEW.name);
                END IF;

                -- Clean name and truncate if too long
                cleaned_name = regexp_replace(
                    name,
                    '{alphanum_pattern}', '', 'g'
                );
                IF length(cleaned_name) > {truncate_length} THEN
                    cleaned_name = left(cleaned_name, {truncate_length});
                END IF;

                -- Set name and name trigrams
                NEW.name = name;
                NEW.name_trigrams = {POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE}.show_trgm(cleaned_name);
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
        )
    )
    trigger = f"{function}_trigger"
    op.execute(f'DROP TRIGGER IF EXISTS {trigger} ON "{tenant_id}".kg_entity')
    op.execute(
        f"""
        CREATE TRIGGER {trigger}
            BEFORE INSERT OR UPDATE OF name
            ON "{tenant_id}".kg_entity
            FOR EACH ROW
            EXECUTE FUNCTION "{tenant_id}".{function}();
        """
    )

    # Create kg_entity trigger to update kg_entity.name and its trigrams
    function = "update_kg_entity_name_from_doc"
    op.execute(
        text(
            f"""
            CREATE OR REPLACE FUNCTION "{tenant_id}".{function}()
            RETURNS TRIGGER AS $$
            DECLARE
                doc_name text;
                cleaned_name text;
            BEGIN
                doc_name = lower(NEW.semantic_id);

                -- Clean name and truncate if too long
                cleaned_name = regexp_replace(
                    doc_name,
                    '{alphanum_pattern}', '', 'g'
                );
                IF length(cleaned_name) > {truncate_length} THEN
                    cleaned_name = left(cleaned_name, {truncate_length});
                END IF;

                -- Set name and name trigrams for all entities referencing this document
                UPDATE "{tenant_id}".kg_entity
                SET
                    name = doc_name,
                    name_trigrams = {POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE}.show_trgm(cleaned_name)
                WHERE document_id = NEW.id;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
        )
    )
    trigger = f"{function}_trigger"
    op.execute(f'DROP TRIGGER IF EXISTS {trigger} ON "{tenant_id}".document')
    op.execute(
        f"""
        CREATE TRIGGER {trigger}
            AFTER UPDATE OF semantic_id
            ON "{tenant_id}".document
            FOR EACH ROW
            EXECUTE FUNCTION "{tenant_id}".{function}();
        """
    )


def downgrade() -> None:
    pass



================================================
FILE: backend/alembic/versions/3781a5eb12cb_add_chunk_stats_table.py
================================================
"""add chunk stats table

Revision ID: 3781a5eb12cb
Revises: df46c75b714e
Create Date: 2025-03-10 10:02:30.586666

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3781a5eb12cb"
down_revision = "df46c75b714e"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "chunk_stats",
        sa.Column("id", sa.String(), primary_key=True, index=True),
        sa.Column(
            "document_id",
            sa.String(),
            sa.ForeignKey("document.id"),
            nullable=False,
            index=True,
        ),
        sa.Column("chunk_in_doc_id", sa.Integer(), nullable=False),
        sa.Column("information_content_boost", sa.Float(), nullable=True),
        sa.Column(
            "last_modified",
            sa.DateTime(timezone=True),
            nullable=False,
            index=True,
            server_default=sa.func.now(),
        ),
        sa.Column("last_synced", sa.DateTime(timezone=True), nullable=True, index=True),
        sa.UniqueConstraint(
            "document_id", "chunk_in_doc_id", name="uq_chunk_stats_doc_chunk"
        ),
    )

    op.create_index(
        "ix_chunk_sync_status", "chunk_stats", ["last_modified", "last_synced"]
    )


def downgrade() -> None:
    op.drop_index("ix_chunk_sync_status", table_name="chunk_stats")
    op.drop_table("chunk_stats")



================================================
FILE: backend/alembic/versions/3879338f8ba1_add_tool_table.py
================================================
"""Add tool table

Revision ID: 3879338f8ba1
Revises: f1c6478c3fd8
Create Date: 2024-05-11 16:11:23.718084

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3879338f8ba1"
down_revision = "f1c6478c3fd8"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "tool",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("in_code_tool_id", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "persona__tool",
        sa.Column("persona_id", sa.Integer(), nullable=False),
        sa.Column("tool_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["persona_id"],
            ["persona.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tool_id"],
            ["tool.id"],
        ),
        sa.PrimaryKeyConstraint("persona_id", "tool_id"),
    )


def downgrade() -> None:
    op.drop_table("persona__tool")
    op.drop_table("tool")



================================================
FILE: backend/alembic/versions/38eda64af7fe_add_chat_session_sharing.py
================================================
"""Add chat session sharing

Revision ID: 38eda64af7fe
Revises: 776b3bbe9092
Create Date: 2024-03-27 19:41:29.073594

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "38eda64af7fe"
down_revision = "776b3bbe9092"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_session",
        sa.Column(
            "shared_status",
            sa.Enum(
                "PUBLIC",
                "PRIVATE",
                name="chatsessionsharedstatus",
                native_enum=False,
            ),
            nullable=True,
        ),
    )
    op.execute("UPDATE chat_session SET shared_status='PRIVATE'")
    op.alter_column(
        "chat_session",
        "shared_status",
        nullable=False,
    )


def downgrade() -> None:
    op.drop_column("chat_session", "shared_status")



================================================
FILE: backend/alembic/versions/3934b1bc7b62_update_github_connector_repo_name_to_.py
================================================
"""Update GitHub connector repo_name to repositories

Revision ID: 3934b1bc7b62
Revises: b7c2b63c4a03
Create Date: 2025-03-05 10:50:30.516962

"""

from alembic import op
import sqlalchemy as sa
import json
import logging

# revision identifiers, used by Alembic.
revision = "3934b1bc7b62"
down_revision = "b7c2b63c4a03"
branch_labels = None
depends_on = None

logger = logging.getLogger("alembic.runtime.migration")


def upgrade() -> None:
    # Get all GitHub connectors
    conn = op.get_bind()

    # First get all GitHub connectors
    github_connectors = conn.execute(
        sa.text(
            """
            SELECT id, connector_specific_config
            FROM connector
            WHERE source = 'GITHUB'
            """
        )
    ).fetchall()

    # Update each connector's config
    updated_count = 0
    for connector_id, config in github_connectors:
        try:
            if not config:
                logger.warning(f"Connector {connector_id} has no config, skipping")
                continue

            # Parse the config if it's a string
            if isinstance(config, str):
                config = json.loads(config)

            if "repo_name" not in config:
                continue

            # Create new config with repositories instead of repo_name
            new_config = dict(config)
            repo_name_value = new_config.pop("repo_name")
            new_config["repositories"] = repo_name_value

            # Update the connector with the new config
            conn.execute(
                sa.text(
                    """
                    UPDATE connector
                    SET connector_specific_config = :new_config
                    WHERE id = :connector_id
                    """
                ),
                {"connector_id": connector_id, "new_config": json.dumps(new_config)},
            )
            updated_count += 1
        except Exception as e:
            logger.error(f"Error updating connector {connector_id}: {str(e)}")


def downgrade() -> None:
    # Get all GitHub connectors
    conn = op.get_bind()

    logger.debug(
        "Starting rollback of GitHub connectors from repositories to repo_name"
    )

    github_connectors = conn.execute(
        sa.text(
            """
            SELECT id, connector_specific_config
            FROM connector
            WHERE source = 'GITHUB'
            """
        )
    ).fetchall()

    logger.debug(f"Found {len(github_connectors)} GitHub connectors to rollback")

    # Revert each GitHub connector to use repo_name instead of repositories
    reverted_count = 0
    for connector_id, config in github_connectors:
        try:
            if not config:
                continue

            # Parse the config if it's a string
            if isinstance(config, str):
                config = json.loads(config)

            if "repositories" not in config:
                continue

            # Create new config with repo_name instead of repositories
            new_config = dict(config)
            repositories_value = new_config.pop("repositories")
            new_config["repo_name"] = repositories_value

            # Update the connector with the new config
            conn.execute(
                sa.text(
                    """
                    UPDATE connector
                    SET connector_specific_config = :new_config
                    WHERE id = :connector_id
                    """
                ),
                {"new_config": json.dumps(new_config), "connector_id": connector_id},
            )
            reverted_count += 1
        except Exception as e:
            logger.error(f"Error reverting connector {connector_id}: {str(e)}")



================================================
FILE: backend/alembic/versions/3a7802814195_add_alternate_assistant_to_chat_message.py
================================================
"""add alternate assistant to chat message

Revision ID: 3a7802814195
Revises: 23957775e5f5
Create Date: 2024-06-05 11:18:49.966333

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "3a7802814195"
down_revision = "23957775e5f5"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_message", sa.Column("alternate_assistant_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        "fk_chat_message_persona",
        "chat_message",
        "persona",
        ["alternate_assistant_id"],
        ["id"],
    )


def downgrade() -> None:
    op.drop_constraint("fk_chat_message_persona", "chat_message", type_="foreignkey")
    op.drop_column("chat_message", "alternate_assistant_id")



================================================
FILE: backend/alembic/versions/3b25685ff73c_move_is_public_to_cc_pair.py
================================================
"""Move is_public to cc_pair

Revision ID: 3b25685ff73c
Revises: e0a68a81d434
Create Date: 2023-10-05 18:47:09.582849

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3b25685ff73c"
down_revision = "e0a68a81d434"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "connector_credential_pair",
        sa.Column("is_public", sa.Boolean(), nullable=True),
    )
    # fill in is_public for existing rows
    op.execute(
        "UPDATE connector_credential_pair SET is_public = true WHERE is_public IS NULL"
    )
    op.alter_column("connector_credential_pair", "is_public", nullable=False)

    op.add_column(
        "credential",
        sa.Column("is_admin", sa.Boolean(), nullable=True),
    )
    op.execute("UPDATE credential SET is_admin = true WHERE is_admin IS NULL")
    op.alter_column("credential", "is_admin", nullable=False)

    op.drop_column("credential", "public_doc")


def downgrade() -> None:
    op.add_column(
        "credential",
        sa.Column("public_doc", sa.Boolean(), nullable=True),
    )
    # setting public_doc to false for all existing rows to be safe
    # NOTE: this is likely not the correct state of the world but it's the best we can do
    op.execute("UPDATE credential SET public_doc = false WHERE public_doc IS NULL")
    op.alter_column("credential", "public_doc", nullable=False)
    op.drop_column("connector_credential_pair", "is_public")
    op.drop_column("credential", "is_admin")



================================================
FILE: backend/alembic/versions/3bd4c84fe72f_improved_index.py
================================================
"""improved index

Revision ID: 3bd4c84fe72f
Revises: 8f43500ee275
Create Date: 2025-02-26 13:07:56.217791

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "3bd4c84fe72f"
down_revision = "8f43500ee275"
branch_labels = None
depends_on = None


# NOTE:
# This migration addresses issues with the previous migration (8f43500ee275) which caused
# an outage by creating an index without using CONCURRENTLY. This migration:
#
# 1. Creates more efficient full-text search capabilities using tsvector columns and GIN indexes
# 2. Adds indexes to both chat_message and chat_session tables for comprehensive search
# 3. Note: CONCURRENTLY was removed due to operational issues


def upgrade() -> None:
    # First, drop any existing indexes to avoid conflicts
    op.execute("DROP INDEX IF EXISTS idx_chat_message_tsv;")
    op.execute("DROP INDEX IF EXISTS idx_chat_session_desc_tsv;")
    op.execute("DROP INDEX IF EXISTS idx_chat_message_message_lower;")

    # Drop existing columns if they exist
    op.execute("ALTER TABLE chat_message DROP COLUMN IF EXISTS message_tsv;")
    op.execute("ALTER TABLE chat_session DROP COLUMN IF EXISTS description_tsv;")

    # Create a GIN index for full-text search on chat_message.message
    op.execute(
        """
        ALTER TABLE chat_message
        ADD COLUMN message_tsv tsvector
        GENERATED ALWAYS AS (to_tsvector('english', message)) STORED;
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_chat_message_tsv
        ON chat_message
        USING GIN (message_tsv)
        """
    )

    # Also add a stored tsvector column for chat_session.description
    op.execute(
        """
        ALTER TABLE chat_session
        ADD COLUMN description_tsv tsvector
        GENERATED ALWAYS AS (to_tsvector('english', coalesce(description, ''))) STORED;
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_chat_session_desc_tsv
        ON chat_session
        USING GIN (description_tsv)
        """
    )


def downgrade() -> None:
    # Drop the indexes first
    op.execute("DROP INDEX IF EXISTS idx_chat_message_tsv;")
    op.execute("DROP INDEX IF EXISTS idx_chat_session_desc_tsv;")

    # Then drop the columns
    op.execute("ALTER TABLE chat_message DROP COLUMN IF EXISTS message_tsv;")
    op.execute("ALTER TABLE chat_session DROP COLUMN IF EXISTS description_tsv;")

    op.execute("DROP INDEX IF EXISTS idx_chat_message_message_lower;")



================================================
FILE: backend/alembic/versions/3c5e35aa9af0_polling_document_count.py
================================================
"""Polling Document Count

Revision ID: 3c5e35aa9af0
Revises: 27c6ecc08586
Create Date: 2023-06-14 23:45:51.760440

"""

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = "3c5e35aa9af0"
down_revision = "27c6ecc08586"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "last_successful_index_time",
            sa.DateTime(timezone=True),
            nullable=True,
        ),
    )
    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "last_attempt_status",
            sa.Enum(
                "NOT_STARTED",
                "IN_PROGRESS",
                "SUCCESS",
                "FAILED",
                name="indexingstatus",
                native_enum=False,
            ),
            nullable=False,
        ),
    )
    op.add_column(
        "connector_credential_pair",
        sa.Column("total_docs_indexed", sa.Integer(), nullable=False),
    )


def downgrade() -> None:
    op.drop_column("connector_credential_pair", "total_docs_indexed")
    op.drop_column("connector_credential_pair", "last_attempt_status")
    op.drop_column("connector_credential_pair", "last_successful_index_time")



================================================
FILE: backend/alembic/versions/3c6531f32351_add_back_input_prompts.py
================================================
"""add back input prompts

Revision ID: 3c6531f32351
Revises: aeda5f2df4f6
Create Date: 2025-01-13 12:49:51.705235

"""

from alembic import op
import sqlalchemy as sa
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = "3c6531f32351"
down_revision = "aeda5f2df4f6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "inputprompt",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("prompt", sa.String(), nullable=False),
        sa.Column("content", sa.String(), nullable=False),
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.Column("is_public", sa.Boolean(), nullable=False),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "inputprompt__user",
        sa.Column("input_prompt_id", sa.Integer(), nullable=False),
        sa.Column(
            "user_id", fastapi_users_db_sqlalchemy.generics.GUID(), nullable=False
        ),
        sa.Column("disabled", sa.Boolean(), nullable=False, default=False),
        sa.ForeignKeyConstraint(
            ["input_prompt_id"],
            ["inputprompt.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("input_prompt_id", "user_id"),
    )


def downgrade() -> None:
    op.drop_table("inputprompt__user")
    op.drop_table("inputprompt")



================================================
FILE: backend/alembic/versions/401c1ac29467_add_tables_for_ui_based_llm_.py
================================================
"""Add tables for UI-based LLM configuration

Revision ID: 401c1ac29467
Revises: 703313b75876
Create Date: 2024-04-13 18:07:29.153817

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "401c1ac29467"
down_revision = "703313b75876"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "llm_provider",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("api_key", sa.String(), nullable=True),
        sa.Column("api_base", sa.String(), nullable=True),
        sa.Column("api_version", sa.String(), nullable=True),
        sa.Column(
            "custom_config",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
        sa.Column("default_model_name", sa.String(), nullable=False),
        sa.Column("fast_default_model_name", sa.String(), nullable=True),
        sa.Column("is_default_provider", sa.Boolean(), unique=True, nullable=True),
        sa.Column("model_names", postgresql.ARRAY(sa.String()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )

    op.add_column(
        "persona",
        sa.Column("llm_model_provider_override", sa.String(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("persona", "llm_model_provider_override")

    op.drop_table("llm_provider")



================================================
FILE: backend/alembic/versions/43cbbb3f5e6a_rename_index_origin_to_index_recursively.py
================================================
"""Rename index_origin to index_recursively

Revision ID: 1d6ad76d1f37
Revises: e1392f05e840
Create Date: 2024-08-01 12:38:54.466081

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "1d6ad76d1f37"
down_revision = "e1392f05e840"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE connector
        SET connector_specific_config = jsonb_set(
            connector_specific_config,
            '{index_recursively}',
            'true'::jsonb
        ) - 'index_origin'
        WHERE connector_specific_config ? 'index_origin'
    """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE connector
        SET connector_specific_config = jsonb_set(
            connector_specific_config,
            '{index_origin}',
            connector_specific_config->'index_recursively'
        ) - 'index_recursively'
        WHERE connector_specific_config ? 'index_recursively'
    """
    )



================================================
FILE: backend/alembic/versions/44f856ae2a4a_add_cloud_embedding_model.py
================================================
"""add cloud embedding model and update embedding_model

Revision ID: 44f856ae2a4a
Revises: d716b0791ddd
Create Date: 2024-06-28 20:01:05.927647

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "44f856ae2a4a"
down_revision = "d716b0791ddd"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # Create embedding_provider table
    op.create_table(
        "embedding_provider",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("api_key", sa.LargeBinary(), nullable=True),
        sa.Column("default_model_id", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )

    # Add cloud_provider_id to embedding_model table
    op.add_column(
        "embedding_model", sa.Column("cloud_provider_id", sa.Integer(), nullable=True)
    )

    # Add foreign key constraints
    op.create_foreign_key(
        "fk_embedding_model_cloud_provider",
        "embedding_model",
        "embedding_provider",
        ["cloud_provider_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_embedding_provider_default_model",
        "embedding_provider",
        "embedding_model",
        ["default_model_id"],
        ["id"],
    )


def downgrade() -> None:
    # Remove foreign key constraints
    op.drop_constraint(
        "fk_embedding_model_cloud_provider", "embedding_model", type_="foreignkey"
    )
    op.drop_constraint(
        "fk_embedding_provider_default_model", "embedding_provider", type_="foreignkey"
    )

    # Remove cloud_provider_id column
    op.drop_column("embedding_model", "cloud_provider_id")

    # Drop embedding_provider table
    op.drop_table("embedding_provider")



================================================
FILE: backend/alembic/versions/4505fd7302e1_added_is_internet_to_dbdoc.py
================================================
"""added is_internet to DBDoc

Revision ID: 4505fd7302e1
Revises: c18cdf4b497e
Create Date: 2024-06-18 20:46:09.095034

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4505fd7302e1"
down_revision = "c18cdf4b497e"


def upgrade() -> None:
    op.add_column("search_doc", sa.Column("is_internet", sa.Boolean(), nullable=True))
    op.add_column("tool", sa.Column("display_name", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("tool", "display_name")
    op.drop_column("search_doc", "is_internet")



================================================
FILE: backend/alembic/versions/465f78d9b7f9_larger_access_tokens_for_oauth.py
================================================
"""Larger Access Tokens for OAUTH

Revision ID: 465f78d9b7f9
Revises: 3c5e35aa9af0
Create Date: 2023-07-18 17:33:40.365034

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "465f78d9b7f9"
down_revision = "3c5e35aa9af0"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.alter_column("oauth_account", "access_token", type_=sa.Text())


def downgrade() -> None:
    op.alter_column("oauth_account", "access_token", type_=sa.String(length=1024))



================================================
FILE: backend/alembic/versions/46625e4745d4_remove_native_enum.py
================================================
"""Remove Native Enum

Revision ID: 46625e4745d4
Revises: 9d97fecfab7f
Create Date: 2023-10-27 11:38:33.803145

"""

from alembic import op
from sqlalchemy import String

# revision identifiers, used by Alembic.
revision = "46625e4745d4"
down_revision = "9d97fecfab7f"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # At this point, we directly changed some previous migrations,
    # https://github.com/onyx-dot-app/onyx/pull/637
    # Due to using Postgres native Enums, it caused some complications for first time users.
    # To remove those complications, all Enums are only handled application side moving forward.
    # This migration exists to ensure that existing users don't run into upgrade issues.
    op.alter_column("index_attempt", "status", type_=String)
    op.alter_column("connector_credential_pair", "last_attempt_status", type_=String)
    op.execute("DROP TYPE IF EXISTS indexingstatus")


def downgrade() -> None:
    # We don't want Native Enums, do nothing
    pass



================================================
FILE: backend/alembic/versions/46b7a812670f_fix_user__external_user_group_id_fk.py
================================================
"""fix_user__external_user_group_id_fk

Revision ID: 46b7a812670f
Revises: f32615f71aeb
Create Date: 2024-09-23 12:58:03.894038

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "46b7a812670f"
down_revision = "f32615f71aeb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop the existing primary key
    op.drop_constraint(
        "user__external_user_group_id_pkey",
        "user__external_user_group_id",
        type_="primary",
    )

    # Add the new composite primary key
    op.create_primary_key(
        "user__external_user_group_id_pkey",
        "user__external_user_group_id",
        ["user_id", "external_user_group_id", "cc_pair_id"],
    )


def downgrade() -> None:
    # Drop the composite primary key
    op.drop_constraint(
        "user__external_user_group_id_pkey",
        "user__external_user_group_id",
        type_="primary",
    )
    # Delete all entries from the table
    op.execute("DELETE FROM user__external_user_group_id")

    # Recreate the original primary key on user_id
    op.create_primary_key(
        "user__external_user_group_id_pkey", "user__external_user_group_id", ["user_id"]
    )



================================================
FILE: backend/alembic/versions/4738e4b3bae1_pg_file_store.py
================================================
"""PG File Store

Revision ID: 4738e4b3bae1
Revises: e91df4e935ef
Create Date: 2024-03-20 18:53:32.461518

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4738e4b3bae1"
down_revision = "e91df4e935ef"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "file_store",
        sa.Column("file_name", sa.String(), nullable=False),
        sa.Column("lobj_oid", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("file_name"),
    )


def downgrade() -> None:
    op.drop_table("file_store")



================================================
FILE: backend/alembic/versions/473a1a7ca408_add_display_model_names_to_llm_provider.py
================================================
"""Add display_model_names to llm_provider

Revision ID: 473a1a7ca408
Revises: 325975216eb3
Create Date: 2024-07-25 14:31:02.002917

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "473a1a7ca408"
down_revision = "325975216eb3"
branch_labels: None = None
depends_on: None = None

default_models_by_provider = {
    "openai": ["gpt-4", "gpt-4o", "gpt-4o-mini"],
    "bedrock": [
        "meta.llama3-1-70b-instruct-v1:0",
        "meta.llama3-1-8b-instruct-v1:0",
        "anthropic.claude-3-opus-20240229-v1:0",
        "mistral.mistral-large-2402-v1:0",
        "anthropic.claude-3-5-sonnet-20240620-v1:0",
    ],
    "anthropic": ["claude-3-opus-20240229", "claude-3-5-sonnet-20240620"],
}


def upgrade() -> None:
    op.add_column(
        "llm_provider",
        sa.Column("display_model_names", postgresql.ARRAY(sa.String()), nullable=True),
    )

    connection = op.get_bind()
    for provider, models in default_models_by_provider.items():
        connection.execute(
            sa.text(
                "UPDATE llm_provider SET display_model_names = :models WHERE provider = :provider"
            ),
            {"models": models, "provider": provider},
        )


def downgrade() -> None:
    op.drop_column("llm_provider", "display_model_names")



================================================
FILE: backend/alembic/versions/47433d30de82_create_indexattempt_table.py
================================================
"""Create IndexAttempt table

Revision ID: 47433d30de82
Revises:
Create Date: 2023-05-04 00:55:32.971991

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "47433d30de82"
down_revision: None = None
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "index_attempt",
        sa.Column("id", sa.Integer(), nullable=False),
        # String type since python enum will change often
        sa.Column(
            "source",
            sa.String(),
            nullable=False,
        ),
        # String type to easily accomodate new ways of pulling
        # in documents
        sa.Column(
            "input_type",
            sa.String(),
            nullable=False,
        ),
        sa.Column(
            "connector_specific_config",
            postgresql.JSONB(),
            nullable=False,
        ),
        sa.Column(
            "time_created",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            server_onupdate=sa.text("now()"),  # type: ignore
            nullable=True,
        ),
        sa.Column(
            "status",
            sa.Enum(
                "NOT_STARTED",
                "IN_PROGRESS",
                "SUCCESS",
                "FAILED",
                name="indexingstatus",
                native_enum=False,
            ),
            nullable=False,
        ),
        sa.Column("document_ids", postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column("error_msg", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("index_attempt")



================================================
FILE: backend/alembic/versions/475fcefe8826_add_name_to_api_key.py
================================================
"""Add name to api_key

Revision ID: 475fcefe8826
Revises: ecab2b3f1a3b
Create Date: 2024-04-11 11:05:18.414438

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "475fcefe8826"
down_revision = "ecab2b3f1a3b"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("api_key", sa.Column("name", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("api_key", "name")



================================================
FILE: backend/alembic/versions/4794bc13e484_update_prompt_length.py
================================================
"""update prompt length

Revision ID: 4794bc13e484
Revises: f7505c5b0284
Create Date: 2025-04-02 11:26:36.180328

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "4794bc13e484"
down_revision = "f7505c5b0284"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.alter_column(
        "prompt",
        "system_prompt",
        existing_type=sa.TEXT(),
        type_=sa.String(length=5000000),
        existing_nullable=False,
    )
    op.alter_column(
        "prompt",
        "task_prompt",
        existing_type=sa.TEXT(),
        type_=sa.String(length=5000000),
        existing_nullable=False,
    )


def downgrade() -> None:
    op.alter_column(
        "prompt",
        "system_prompt",
        existing_type=sa.String(length=5000000),
        type_=sa.TEXT(),
        existing_nullable=False,
    )
    op.alter_column(
        "prompt",
        "task_prompt",
        existing_type=sa.String(length=5000000),
        type_=sa.TEXT(),
        existing_nullable=False,
    )



================================================
FILE: backend/alembic/versions/47a07e1a38f1_fix_invalid_model_configurations_state.py
================================================
"""Fix invalid model-configurations state

Revision ID: 47a07e1a38f1
Revises: 7a70b7664e37
Create Date: 2025-04-23 15:39:43.159504

"""

from alembic import op
from pydantic import BaseModel, ConfigDict
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from onyx.llm.llm_provider_options import (
    fetch_model_names_for_provider_as_set,
    fetch_visible_model_names_for_provider_as_set,
)


# revision identifiers, used by Alembic.
revision = "47a07e1a38f1"
down_revision = "7a70b7664e37"
branch_labels = None
depends_on = None


class _SimpleModelConfiguration(BaseModel):
    # Configure model to read from attributes
    model_config = ConfigDict(from_attributes=True)

    id: int
    llm_provider_id: int
    name: str
    is_visible: bool
    max_input_tokens: int | None


def upgrade() -> None:
    llm_provider_table = sa.sql.table(
        "llm_provider",
        sa.column("id", sa.Integer),
        sa.column("provider", sa.String),
        sa.column("model_names", postgresql.ARRAY(sa.String)),
        sa.column("display_model_names", postgresql.ARRAY(sa.String)),
        sa.column("default_model_name", sa.String),
        sa.column("fast_default_model_name", sa.String),
    )
    model_configuration_table = sa.sql.table(
        "model_configuration",
        sa.column("id", sa.Integer),
        sa.column("llm_provider_id", sa.Integer),
        sa.column("name", sa.String),
        sa.column("is_visible", sa.Boolean),
        sa.column("max_input_tokens", sa.Integer),
    )

    connection = op.get_bind()

    llm_providers = connection.execute(
        sa.select(
            llm_provider_table.c.id,
            llm_provider_table.c.provider,
        )
    ).fetchall()

    for llm_provider in llm_providers:
        llm_provider_id, provider_name = llm_provider

        default_models = fetch_model_names_for_provider_as_set(provider_name)
        display_models = fetch_visible_model_names_for_provider_as_set(
            provider_name=provider_name
        )

        # if `fetch_model_names_for_provider_as_set` returns `None`, then
        # that means that `provider_name` is not a well-known llm provider.
        if not default_models:
            continue

        if not display_models:
            raise RuntimeError(
                "If `default_models` is non-None, `display_models` must be non-None too."
            )

        model_configurations = [
            _SimpleModelConfiguration.model_validate(model_configuration)
            for model_configuration in connection.execute(
                sa.select(
                    model_configuration_table.c.id,
                    model_configuration_table.c.llm_provider_id,
                    model_configuration_table.c.name,
                    model_configuration_table.c.is_visible,
                    model_configuration_table.c.max_input_tokens,
                ).where(model_configuration_table.c.llm_provider_id == llm_provider_id)
            ).fetchall()
        ]

        if model_configurations:
            at_least_one_is_visible = any(
                [
                    model_configuration.is_visible
                    for model_configuration in model_configurations
                ]
            )

            # If there is at least one model which is public, this is a valid state.
            # Therefore, don't touch it and move on to the next one.
            if at_least_one_is_visible:
                continue

            existing_visible_model_names: set[str] = set(
                [
                    model_configuration.name
                    for model_configuration in model_configurations
                    if model_configuration.is_visible
                ]
            )

            difference = display_models.difference(existing_visible_model_names)

            for model_name in difference:
                if not model_name:
                    continue

                insert_statement = postgresql.insert(model_configuration_table).values(
                    llm_provider_id=llm_provider_id,
                    name=model_name,
                    is_visible=True,
                    max_input_tokens=None,
                )

                connection.execute(
                    insert_statement.on_conflict_do_update(
                        index_elements=["llm_provider_id", "name"],
                        set_={"is_visible": insert_statement.excluded.is_visible},
                    )
                )
        else:
            for model_name in default_models:
                connection.execute(
                    model_configuration_table.insert().values(
                        llm_provider_id=llm_provider_id,
                        name=model_name,
                        is_visible=model_name in display_models,
                        max_input_tokens=None,
                    )
                )


def downgrade() -> None:
    pass



================================================
FILE: backend/alembic/versions/47e5bef3a1d7_add_persona_categories.py
================================================
"""add persona categories

Revision ID: 47e5bef3a1d7
Revises: dfbe9e93d3c7
Create Date: 2024-11-05 18:55:02.221064

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "47e5bef3a1d7"
down_revision = "dfbe9e93d3c7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create the persona_category table
    op.create_table(
        "persona_category",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )

    # Add category_id to persona table
    op.add_column("persona", sa.Column("category_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "fk_persona_category",
        "persona",
        "persona_category",
        ["category_id"],
        ["id"],
        ondelete="SET NULL",
    )


def downgrade() -> None:
    op.drop_constraint("persona_category_id_fkey", "persona", type_="foreignkey")
    op.drop_column("persona", "category_id")
    op.drop_table("persona_category")



================================================
FILE: backend/alembic/versions/48d14957fe80_add_support_for_custom_tools.py
================================================
"""Add support for custom tools

Revision ID: 48d14957fe80
Revises: b85f02ec1308
Create Date: 2024-06-09 14:58:19.946509

"""

from alembic import op
import fastapi_users_db_sqlalchemy
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "48d14957fe80"
down_revision = "b85f02ec1308"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "tool",
        sa.Column(
            "openapi_schema",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )
    op.add_column(
        "tool",
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
    )
    op.create_foreign_key("tool_user_fk", "tool", "user", ["user_id"], ["id"])

    op.create_table(
        "tool_call",
        sa.Column("id", sa.Integer(), primary_key=True),
        sa.Column("tool_id", sa.Integer(), nullable=False),
        sa.Column("tool_name", sa.String(), nullable=False),
        sa.Column(
            "tool_arguments", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column(
            "tool_result", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.Column(
            "message_id", sa.Integer(), sa.ForeignKey("chat_message.id"), nullable=False
        ),
    )


def downgrade() -> None:
    op.drop_table("tool_call")

    op.drop_constraint("tool_user_fk", "tool", type_="foreignkey")
    op.drop_column("tool", "user_id")
    op.drop_column("tool", "openapi_schema")



================================================
FILE: backend/alembic/versions/495cb26ce93e_create_knowlege_graph_tables.py
================================================
"""create knowledge graph tables

Revision ID: 495cb26ce93e
Revises: ca04500b9ee8
Create Date: 2025-03-19 08:51:14.341989

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import text
from datetime import datetime, timedelta

from onyx.configs.app_configs import DB_READONLY_USER
from onyx.configs.app_configs import DB_READONLY_PASSWORD
from shared_configs.configs import MULTI_TENANT
from shared_configs.configs import POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE


# revision identifiers, used by Alembic.
revision = "495cb26ce93e"
down_revision = "ca04500b9ee8"
branch_labels = None
depends_on = None


def upgrade() -> None:

    # Create a new permission-less user to be later used for knowledge graph queries.
    # The user will later get temporary read privileges for a specific view that will be
    # ad hoc generated specific to a knowledge graph query.
    #
    # Note: in order for the migration to run, the DB_READONLY_USER and DB_READONLY_PASSWORD
    # environment variables MUST be set. Otherwise, an exception will be raised.

    if not MULTI_TENANT:

        # Enable pg_trgm extension if not already enabled
        op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")

        # Create read-only db user here only in single tenant mode. For multi-tenant mode,
        # the user is created in the alembic_tenants migration.
        if not (DB_READONLY_USER and DB_READONLY_PASSWORD):
            raise Exception("DB_READONLY_USER or DB_READONLY_PASSWORD is not set")

        op.execute(
            text(
                f"""
                DO $$
                BEGIN
                    -- Check if the read-only user already exists
                    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{DB_READONLY_USER}') THEN
                        -- Create the read-only user with the specified password
                        EXECUTE format('CREATE USER %I WITH PASSWORD %L', '{DB_READONLY_USER}', '{DB_READONLY_PASSWORD}');
                        -- First revoke all privileges to ensure a clean slate
                        EXECUTE format('REVOKE ALL ON DATABASE %I FROM %I', current_database(), '{DB_READONLY_USER}');
                        -- Grant only the CONNECT privilege to allow the user to connect to the database
                        -- but not perform any operations without additional specific grants
                        EXECUTE format('GRANT CONNECT ON DATABASE %I TO %I', current_database(), '{DB_READONLY_USER}');
                    END IF;
                END
                $$;
                """
            )
        )

    # Grant usage on current schema to readonly user
    op.execute(
        text(
            f"""
            DO $$
            BEGIN
                IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{DB_READONLY_USER}') THEN
                    EXECUTE format('GRANT USAGE ON SCHEMA %I TO %I', current_schema(), '{DB_READONLY_USER}');
                END IF;
            END
            $$;
            """
        )
    )

    op.execute("DROP TABLE IF EXISTS kg_config CASCADE")
    op.create_table(
        "kg_config",
        sa.Column("id", sa.Integer(), primary_key=True, nullable=False, index=True),
        sa.Column("kg_variable_name", sa.String(), nullable=False, index=True),
        sa.Column("kg_variable_values", postgresql.ARRAY(sa.String()), nullable=False),
        sa.UniqueConstraint("kg_variable_name", name="uq_kg_config_variable_name"),
    )

    # Insert initial data into kg_config table
    op.bulk_insert(
        sa.table(
            "kg_config",
            sa.column("kg_variable_name", sa.String),
            sa.column("kg_variable_values", postgresql.ARRAY(sa.String)),
        ),
        [
            {"kg_variable_name": "KG_EXPOSED", "kg_variable_values": ["false"]},
            {"kg_variable_name": "KG_ENABLED", "kg_variable_values": ["false"]},
            {"kg_variable_name": "KG_VENDOR", "kg_variable_values": []},
            {"kg_variable_name": "KG_VENDOR_DOMAINS", "kg_variable_values": []},
            {"kg_variable_name": "KG_IGNORE_EMAIL_DOMAINS", "kg_variable_values": []},
            {
                "kg_variable_name": "KG_EXTRACTION_IN_PROGRESS",
                "kg_variable_values": ["false"],
            },
            {
                "kg_variable_name": "KG_CLUSTERING_IN_PROGRESS",
                "kg_variable_values": ["false"],
            },
            {
                "kg_variable_name": "KG_COVERAGE_START",
                "kg_variable_values": [
                    (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")
                ],
            },
            {"kg_variable_name": "KG_MAX_COVERAGE_DAYS", "kg_variable_values": ["90"]},
            {
                "kg_variable_name": "KG_MAX_PARENT_RECURSION_DEPTH",
                "kg_variable_values": ["2"],
            },
        ],
    )

    op.execute("DROP TABLE IF EXISTS kg_entity_type CASCADE")
    op.create_table(
        "kg_entity_type",
        sa.Column("id_name", sa.String(), primary_key=True, nullable=False, index=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("grounding", sa.String(), nullable=False),
        sa.Column(
            "attributes",
            postgresql.JSONB,
            nullable=False,
            server_default="{}",
        ),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column("active", sa.Boolean(), nullable=False, default=False),
        sa.Column("deep_extraction", sa.Boolean(), nullable=False, default=False),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            onupdate=sa.text("now()"),
        ),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.Column("grounded_source_name", sa.String(), nullable=True),
        sa.Column("entity_values", postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column(
            "clustering",
            postgresql.JSONB,
            nullable=False,
            server_default="{}",
        ),
    )

    op.execute("DROP TABLE IF EXISTS kg_relationship_type CASCADE")
    # Create KGRelationshipType table
    op.create_table(
        "kg_relationship_type",
        sa.Column("id_name", sa.String(), primary_key=True, nullable=False, index=True),
        sa.Column("name", sa.String(), nullable=False, index=True),
        sa.Column(
            "source_entity_type_id_name", sa.String(), nullable=False, index=True
        ),
        sa.Column(
            "target_entity_type_id_name", sa.String(), nullable=False, index=True
        ),
        sa.Column("definition", sa.Boolean(), nullable=False, default=False),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column("type", sa.String(), nullable=False, index=True),
        sa.Column("active", sa.Boolean(), nullable=False, default=True),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            onupdate=sa.text("now()"),
        ),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.Column(
            "clustering",
            postgresql.JSONB,
            nullable=False,
            server_default="{}",
        ),
        sa.ForeignKeyConstraint(
            ["source_entity_type_id_name"], ["kg_entity_type.id_name"]
        ),
        sa.ForeignKeyConstraint(
            ["target_entity_type_id_name"], ["kg_entity_type.id_name"]
        ),
    )

    op.execute("DROP TABLE IF EXISTS kg_relationship_type_extraction_staging CASCADE")
    # Create KGRelationshipTypeExtractionStaging table
    op.create_table(
        "kg_relationship_type_extraction_staging",
        sa.Column("id_name", sa.String(), primary_key=True, nullable=False, index=True),
        sa.Column("name", sa.String(), nullable=False, index=True),
        sa.Column(
            "source_entity_type_id_name", sa.String(), nullable=False, index=True
        ),
        sa.Column(
            "target_entity_type_id_name", sa.String(), nullable=False, index=True
        ),
        sa.Column("definition", sa.Boolean(), nullable=False, default=False),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column("type", sa.String(), nullable=False, index=True),
        sa.Column("active", sa.Boolean(), nullable=False, default=True),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.Column(
            "clustering",
            postgresql.JSONB,
            nullable=False,
            server_default="{}",
        ),
        sa.Column("transferred", sa.Boolean(), nullable=False, server_default="false"),
        sa.ForeignKeyConstraint(
            ["source_entity_type_id_name"], ["kg_entity_type.id_name"]
        ),
        sa.ForeignKeyConstraint(
            ["target_entity_type_id_name"], ["kg_entity_type.id_name"]
        ),
    )

    op.execute("DROP TABLE IF EXISTS kg_entity CASCADE")

    # Create KGEntity table
    op.create_table(
        "kg_entity",
        sa.Column("id_name", sa.String(), primary_key=True, nullable=False, index=True),
        sa.Column("name", sa.String(), nullable=False, index=True),
        sa.Column("entity_class", sa.String(), nullable=True, index=True),
        sa.Column("entity_subtype", sa.String(), nullable=True, index=True),
        sa.Column("entity_key", sa.String(), nullable=True, index=True),
        sa.Column("name_trigrams", postgresql.ARRAY(sa.String(3)), nullable=True),
        sa.Column("document_id", sa.String(), nullable=True, index=True),
        sa.Column(
            "alternative_names",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
        sa.Column("entity_type_id_name", sa.String(), nullable=False, index=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column(
            "keywords",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column(
            "acl", postgresql.ARRAY(sa.String()), nullable=False, server_default="{}"
        ),
        sa.Column("boosts", postgresql.JSONB, nullable=False, server_default="{}"),
        sa.Column("attributes", postgresql.JSONB, nullable=False, server_default="{}"),
        sa.Column("event_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            onupdate=sa.text("now()"),
        ),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.ForeignKeyConstraint(["entity_type_id_name"], ["kg_entity_type.id_name"]),
        sa.ForeignKeyConstraint(["document_id"], ["document.id"]),
        sa.UniqueConstraint(
            "name",
            "entity_type_id_name",
            "document_id",
            name="uq_kg_entity_name_type_doc",
        ),
    )
    op.create_index("ix_entity_type_acl", "kg_entity", ["entity_type_id_name", "acl"])
    op.create_index(
        "ix_entity_name_search", "kg_entity", ["name", "entity_type_id_name"]
    )

    op.execute("DROP TABLE IF EXISTS kg_entity_extraction_staging CASCADE")
    # Create KGEntityExtractionStaging table
    op.create_table(
        "kg_entity_extraction_staging",
        sa.Column("id_name", sa.String(), primary_key=True, nullable=False, index=True),
        sa.Column("name", sa.String(), nullable=False, index=True),
        sa.Column("document_id", sa.String(), nullable=True, index=True),
        sa.Column(
            "alternative_names",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
        sa.Column("entity_type_id_name", sa.String(), nullable=False, index=True),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column(
            "keywords",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column(
            "acl", postgresql.ARRAY(sa.String()), nullable=False, server_default="{}"
        ),
        sa.Column("boosts", postgresql.JSONB, nullable=False, server_default="{}"),
        sa.Column("attributes", postgresql.JSONB, nullable=False, server_default="{}"),
        sa.Column("transferred_id_name", sa.String(), nullable=True, default=None),
        sa.Column("entity_class", sa.String(), nullable=True, index=True),
        sa.Column("entity_key", sa.String(), nullable=True, index=True),
        sa.Column("entity_subtype", sa.String(), nullable=True, index=True),
        sa.Column("parent_key", sa.String(), nullable=True, index=True),
        sa.Column("event_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.ForeignKeyConstraint(["entity_type_id_name"], ["kg_entity_type.id_name"]),
        sa.ForeignKeyConstraint(["document_id"], ["document.id"]),
    )
    op.create_index(
        "ix_entity_extraction_staging_acl",
        "kg_entity_extraction_staging",
        ["entity_type_id_name", "acl"],
    )
    op.create_index(
        "ix_entity_extraction_staging_name_search",
        "kg_entity_extraction_staging",
        ["name", "entity_type_id_name"],
    )

    op.execute("DROP TABLE IF EXISTS kg_relationship CASCADE")
    # Create KGRelationship table
    op.create_table(
        "kg_relationship",
        sa.Column("id_name", sa.String(), nullable=False, index=True),
        sa.Column("source_node", sa.String(), nullable=False, index=True),
        sa.Column("target_node", sa.String(), nullable=False, index=True),
        sa.Column("source_node_type", sa.String(), nullable=False, index=True),
        sa.Column("target_node_type", sa.String(), nullable=False, index=True),
        sa.Column("source_document", sa.String(), nullable=True, index=True),
        sa.Column("type", sa.String(), nullable=False, index=True),
        sa.Column("relationship_type_id_name", sa.String(), nullable=False, index=True),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            onupdate=sa.text("now()"),
        ),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.ForeignKeyConstraint(["source_node"], ["kg_entity.id_name"]),
        sa.ForeignKeyConstraint(["target_node"], ["kg_entity.id_name"]),
        sa.ForeignKeyConstraint(["source_node_type"], ["kg_entity_type.id_name"]),
        sa.ForeignKeyConstraint(["target_node_type"], ["kg_entity_type.id_name"]),
        sa.ForeignKeyConstraint(["source_document"], ["document.id"]),
        sa.ForeignKeyConstraint(
            ["relationship_type_id_name"], ["kg_relationship_type.id_name"]
        ),
        sa.UniqueConstraint(
            "source_node",
            "target_node",
            "type",
            name="uq_kg_relationship_source_target_type",
        ),
        sa.PrimaryKeyConstraint("id_name", "source_document"),
    )
    op.create_index(
        "ix_kg_relationship_nodes", "kg_relationship", ["source_node", "target_node"]
    )

    op.execute("DROP TABLE IF EXISTS kg_relationship_extraction_staging CASCADE")
    # Create KGRelationshipExtractionStaging table
    op.create_table(
        "kg_relationship_extraction_staging",
        sa.Column("id_name", sa.String(), nullable=False, index=True),
        sa.Column("source_node", sa.String(), nullable=False, index=True),
        sa.Column("target_node", sa.String(), nullable=False, index=True),
        sa.Column("source_node_type", sa.String(), nullable=False, index=True),
        sa.Column("target_node_type", sa.String(), nullable=False, index=True),
        sa.Column("source_document", sa.String(), nullable=True, index=True),
        sa.Column("type", sa.String(), nullable=False, index=True),
        sa.Column("relationship_type_id_name", sa.String(), nullable=False, index=True),
        sa.Column("occurrences", sa.Integer(), server_default="1", nullable=False),
        sa.Column("transferred", sa.Boolean(), nullable=False, server_default="false"),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
        sa.ForeignKeyConstraint(
            ["source_node"], ["kg_entity_extraction_staging.id_name"]
        ),
        sa.ForeignKeyConstraint(
            ["target_node"], ["kg_entity_extraction_staging.id_name"]
        ),
        sa.ForeignKeyConstraint(["source_node_type"], ["kg_entity_type.id_name"]),
        sa.ForeignKeyConstraint(["target_node_type"], ["kg_entity_type.id_name"]),
        sa.ForeignKeyConstraint(["source_document"], ["document.id"]),
        sa.ForeignKeyConstraint(
            ["relationship_type_id_name"],
            ["kg_relationship_type_extraction_staging.id_name"],
        ),
        sa.UniqueConstraint(
            "source_node",
            "target_node",
            "type",
            name="uq_kg_relationship_extraction_staging_source_target_type",
        ),
        sa.PrimaryKeyConstraint("id_name", "source_document"),
    )
    op.create_index(
        "ix_kg_relationship_extraction_staging_nodes",
        "kg_relationship_extraction_staging",
        ["source_node", "target_node"],
    )

    op.execute("DROP TABLE IF EXISTS kg_term CASCADE")
    # Create KGTerm table
    op.create_table(
        "kg_term",
        sa.Column("id_term", sa.String(), primary_key=True, nullable=False, index=True),
        sa.Column(
            "entity_types",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            onupdate=sa.text("now()"),
        ),
        sa.Column(
            "time_created", sa.DateTime(timezone=True), server_default=sa.text("now()")
        ),
    )
    op.create_index("ix_search_term_entities", "kg_term", ["entity_types"])
    op.create_index("ix_search_term_term", "kg_term", ["id_term"])

    op.add_column(
        "document",
        sa.Column("kg_stage", sa.String(), nullable=True, index=True),
    )
    op.add_column(
        "document",
        sa.Column("kg_processing_time", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "connector",
        sa.Column(
            "kg_processing_enabled",
            sa.Boolean(),
            nullable=True,
            server_default="false",
        ),
    )

    op.add_column(
        "connector",
        sa.Column(
            "kg_coverage_days",
            sa.Integer(),
            nullable=True,
            server_default=None,
        ),
    )

    # Create GIN index for clustering and normalization
    op.execute(
        "CREATE INDEX IF NOT EXISTS idx_kg_entity_clustering_trigrams "
        f"ON kg_entity USING GIN (name {POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE}.gin_trgm_ops)"
    )
    op.execute(
        "CREATE INDEX IF NOT EXISTS idx_kg_entity_normalization_trigrams "
        "ON kg_entity USING GIN (name_trigrams)"
    )

    # Create kg_entity trigger to update kg_entity.name and its trigrams
    alphanum_pattern = r"[^a-z0-9]+"
    truncate_length = 1000
    function = "update_kg_entity_name"
    op.execute(
        text(
            f"""
            CREATE OR REPLACE FUNCTION {function}()
            RETURNS TRIGGER AS $$
            DECLARE
                name text;
                cleaned_name text;
            BEGIN
                -- Set name to semantic_id if document_id is not NULL
                IF NEW.document_id IS NOT NULL THEN
                    SELECT lower(semantic_id) INTO name
                    FROM document
                    WHERE id = NEW.document_id;
                ELSE
                    name = lower(NEW.name);
                END IF;

                -- Clean name and truncate if too long
                cleaned_name = regexp_replace(
                    name,
                    '{alphanum_pattern}', '', 'g'
                );
                IF length(cleaned_name) > {truncate_length} THEN
                    cleaned_name = left(cleaned_name, {truncate_length});
                END IF;

                -- Set name and name trigrams
                NEW.name = name;
                NEW.name_trigrams = {POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE}.show_trgm(cleaned_name);
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
        )
    )
    trigger = f"{function}_trigger"
    op.execute(f"DROP TRIGGER IF EXISTS {trigger} ON kg_entity")
    op.execute(
        f"""
        CREATE TRIGGER {trigger}
            BEFORE INSERT OR UPDATE OF name
            ON kg_entity
            FOR EACH ROW
            EXECUTE FUNCTION {function}();
        """
    )

    # Create kg_entity trigger to update kg_entity.name and its trigrams
    function = "update_kg_entity_name_from_doc"
    op.execute(
        text(
            f"""
            CREATE OR REPLACE FUNCTION {function}()
            RETURNS TRIGGER AS $$
            DECLARE
                doc_name text;
                cleaned_name text;
            BEGIN
                doc_name = lower(NEW.semantic_id);

                -- Clean name and truncate if too long
                cleaned_name = regexp_replace(
                    doc_name,
                    '{alphanum_pattern}', '', 'g'
                );
                IF length(cleaned_name) > {truncate_length} THEN
                    cleaned_name = left(cleaned_name, {truncate_length});
                END IF;

                -- Set name and name trigrams for all entities referencing this document
                UPDATE kg_entity
                SET
                    name = doc_name,
                    name_trigrams = {POSTGRES_DEFAULT_SCHEMA_STANDARD_VALUE}.show_trgm(cleaned_name)
                WHERE document_id = NEW.id;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """
        )
    )
    trigger = f"{function}_trigger"
    op.execute(f"DROP TRIGGER IF EXISTS {trigger} ON document")
    op.execute(
        f"""
        CREATE TRIGGER {trigger}
            AFTER UPDATE OF semantic_id
            ON document
            FOR EACH ROW
            EXECUTE FUNCTION {function}();
        """
    )


def downgrade() -> None:

    #  Drop all views that start with 'kg_'
    op.execute(
        """
                DO $$
                DECLARE
                    view_name text;
                BEGIN
                    FOR view_name IN
                        SELECT c.relname
                        FROM pg_catalog.pg_class c
                        JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
                        WHERE c.relkind = 'v'
                        AND n.nspname = current_schema()
                        AND c.relname LIKE 'kg_relationships_with_access%'
                    LOOP
                        EXECUTE 'DROP VIEW IF EXISTS ' || quote_ident(view_name);
                    END LOOP;
                END $$;
            """
    )

    op.execute(
        """
                DO $$
                DECLARE
                    view_name text;
                BEGIN
                    FOR view_name IN
                        SELECT c.relname
                        FROM pg_catalog.pg_class c
                        JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
                        WHERE c.relkind = 'v'
                        AND n.nspname = current_schema()
                        AND c.relname LIKE 'allowed_docs%'
                    LOOP
                        EXECUTE 'DROP VIEW IF EXISTS ' || quote_ident(view_name);
                    END LOOP;
                END $$;
            """
    )

    for table, function in (
        ("kg_entity", "update_kg_entity_name"),
        ("document", "update_kg_entity_name_from_doc"),
    ):
        op.execute(f"DROP TRIGGER IF EXISTS {function}_trigger ON {table}")
        op.execute(f"DROP FUNCTION IF EXISTS {function}()")

    # Drop index
    op.execute("DROP INDEX IF EXISTS idx_kg_entity_clustering_trigrams")
    op.execute("DROP INDEX IF EXISTS idx_kg_entity_normalization_trigrams")

    # Drop tables in reverse order of creation to handle dependencies
    op.drop_table("kg_term")
    op.drop_table("kg_relationship")
    op.drop_table("kg_entity")
    op.drop_table("kg_relationship_type")
    op.drop_table("kg_relationship_extraction_staging")
    op.drop_table("kg_relationship_type_extraction_staging")
    op.drop_table("kg_entity_extraction_staging")
    op.drop_table("kg_entity_type")
    op.drop_column("connector", "kg_processing_enabled")
    op.drop_column("connector", "kg_coverage_days")
    op.drop_column("document", "kg_stage")
    op.drop_column("document", "kg_processing_time")
    op.drop_table("kg_config")

    # Revoke usage on current schema for the readonly user
    op.execute(
        text(
            f"""
            DO $$
            BEGIN
                IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{DB_READONLY_USER}') THEN
                    EXECUTE format('REVOKE ALL ON SCHEMA %I FROM %I', current_schema(), '{DB_READONLY_USER}');
                END IF;
            END
            $$;
            """
        )
    )

    if not MULTI_TENANT:
        # Drop read-only db user here only in single tenant mode. For multi-tenant mode,
        # the user is dropped in the alembic_tenants migration.

        op.execute(
            text(
                f"""
            DO $$
            BEGIN
                IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{DB_READONLY_USER}') THEN
                    -- First revoke all privileges from the database
                    EXECUTE format('REVOKE ALL ON DATABASE %I FROM %I', current_database(), '{DB_READONLY_USER}');
                    -- Then drop the user
                    EXECUTE format('DROP USER %I', '{DB_READONLY_USER}');
                END IF;
            END
            $$;
        """
            )
        )
        op.execute(text("DROP EXTENSION IF EXISTS pg_trgm"))



================================================
FILE: backend/alembic/versions/4a951134c801_moved_status_to_connector_credential_.py
================================================
"""Moved status to connector credential pair

Revision ID: 4a951134c801
Revises: 7477a5f5d728
Create Date: 2024-08-10 19:20:34.527559

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4a951134c801"
down_revision = "7477a5f5d728"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "status",
            sa.Enum(
                "ACTIVE",
                "PAUSED",
                "DELETING",
                name="connectorcredentialpairstatus",
                native_enum=False,
            ),
            nullable=True,
        ),
    )

    # Update status of connector_credential_pair based on connector's disabled status
    op.execute(
        """
        UPDATE connector_credential_pair
        SET status = CASE
            WHEN (
                SELECT disabled
                FROM connector
                WHERE connector.id = connector_credential_pair.connector_id
            ) = FALSE THEN 'ACTIVE'
            ELSE 'PAUSED'
        END
        """
    )

    # Make the status column not nullable after setting values
    op.alter_column("connector_credential_pair", "status", nullable=False)

    op.drop_column("connector", "disabled")


def downgrade() -> None:
    op.add_column(
        "connector",
        sa.Column("disabled", sa.BOOLEAN(), autoincrement=False, nullable=True),
    )

    # Update disabled status of connector based on connector_credential_pair's status
    op.execute(
        """
        UPDATE connector
        SET disabled = CASE
            WHEN EXISTS (
                SELECT 1
                FROM connector_credential_pair
                WHERE connector_credential_pair.connector_id = connector.id
                AND connector_credential_pair.status = 'ACTIVE'
            ) THEN FALSE
            ELSE TRUE
        END
        """
    )

    # Make the disabled column not nullable after setting values
    op.alter_column("connector", "disabled", nullable=False)

    op.drop_column("connector_credential_pair", "status")



================================================
FILE: backend/alembic/versions/4b08d97e175a_change_default_prune_freq.py
================================================
"""change default prune_freq

Revision ID: 4b08d97e175a
Revises: d9ec13955951
Create Date: 2024-08-20 15:28:52.993827

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "4b08d97e175a"
down_revision = "d9ec13955951"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE connector
        SET prune_freq = 2592000
        WHERE prune_freq = 86400
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE connector
        SET prune_freq = 86400
        WHERE prune_freq = 2592000
        """
    )



================================================
FILE: backend/alembic/versions/4d58345da04a_lowercase_user_emails.py
================================================
"""lowercase_user_emails

Revision ID: 4d58345da04a
Revises: f1ca58b2f2ec
Create Date: 2025-01-29 07:48:46.784041

"""

import logging
from typing import cast
from alembic import op
from sqlalchemy.exc import IntegrityError
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision = "4d58345da04a"
down_revision = "f1ca58b2f2ec"
branch_labels = None
depends_on = None

logger = logging.getLogger("alembic.runtime.migration")


def upgrade() -> None:
    """Conflicts on lowercasing will result in the uppercased email getting a
    unique integer suffix when converted to lowercase."""

    connection = op.get_bind()

    # Fetch all user emails that are not already lowercase
    user_emails = connection.execute(
        text('SELECT id, email FROM "user" WHERE email != LOWER(email)')
    ).fetchall()

    for user_id, email in user_emails:
        email = cast(str, email)
        username, domain = email.rsplit("@", 1)
        new_email = f"{username.lower()}@{domain.lower()}"
        attempt = 1

        while True:
            try:
                # Try updating the email
                connection.execute(
                    text('UPDATE "user" SET email = :new_email WHERE id = :user_id'),
                    {"new_email": new_email, "user_id": user_id},
                )
                break  # Success, exit loop
            except IntegrityError:
                next_email = f"{username.lower()}_{attempt}@{domain.lower()}"
                # Email conflict occurred, append `_1`, `_2`, etc., to the username
                logger.warning(
                    f"Conflict while lowercasing email: "
                    f"old_email={email} "
                    f"conflicting_email={new_email} "
                    f"next_email={next_email}"
                )
                new_email = next_email
                attempt += 1


def downgrade() -> None:
    # Cannot restore original case of emails
    pass



================================================
FILE: backend/alembic/versions/4ea2c93919c1_add_type_to_credentials.py
================================================
"""Add type to credentials

Revision ID: 4ea2c93919c1
Revises: 473a1a7ca408
Create Date: 2024-07-18 13:07:13.655895

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4ea2c93919c1"
down_revision = "473a1a7ca408"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # Add the new 'source' column to the 'credential' table
    op.add_column(
        "credential",
        sa.Column(
            "source",
            sa.String(length=100),  # Use String instead of Enum
            nullable=True,  # Initially allow NULL values
        ),
    )
    op.add_column(
        "credential",
        sa.Column(
            "name",
            sa.String(),
            nullable=True,
        ),
    )

    # Create a temporary table that maps each credential to a single connector source.
    # This is needed because a credential can be associated with multiple connectors,
    # but we want to assign a single source to each credential.
    # We use DISTINCT ON to ensure we only get one row per credential_id.
    op.execute(
        """
    CREATE TEMPORARY TABLE temp_connector_credential AS
    SELECT DISTINCT ON (cc.credential_id)
        cc.credential_id,
        c.source AS connector_source
    FROM connector_credential_pair cc
    JOIN connector c ON cc.connector_id = c.id
    """
    )

    # Update the 'source' column in the 'credential' table
    op.execute(
        """
    UPDATE credential cred
    SET source = COALESCE(
        (SELECT connector_source
         FROM temp_connector_credential temp
         WHERE cred.id = temp.credential_id),
        'NOT_APPLICABLE'
    )
    """
    )
    # If no exception was raised, alter the column
    op.alter_column("credential", "source", nullable=True)  # TODO modify
    # # ### end Alembic commands ###


def downgrade() -> None:
    op.drop_column("credential", "source")
    op.drop_column("credential", "name")



================================================
FILE: backend/alembic/versions/4ee1287bd26a_add_multiple_slack_bot_support.py
================================================
"""add_multiple_slack_bot_support

Revision ID: 4ee1287bd26a
Revises: 47e5bef3a1d7
Create Date: 2024-11-06 13:15:53.302644

"""

from typing import cast
from alembic import op
import sqlalchemy as sa
from sqlalchemy.orm import Session
from onyx.key_value_store.factory import get_kv_store
from onyx.db.models import SlackBot
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "4ee1287bd26a"
down_revision = "47e5bef3a1d7"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # Create new slack_bot table
    op.create_table(
        "slack_bot",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("enabled", sa.Boolean(), nullable=False, server_default="true"),
        sa.Column("bot_token", sa.LargeBinary(), nullable=False),
        sa.Column("app_token", sa.LargeBinary(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("bot_token"),
        sa.UniqueConstraint("app_token"),
    )

    # # Create new slack_channel_config table
    op.create_table(
        "slack_channel_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("slack_bot_id", sa.Integer(), nullable=True),
        sa.Column("persona_id", sa.Integer(), nullable=True),
        sa.Column("channel_config", postgresql.JSONB(), nullable=False),
        sa.Column("response_type", sa.String(), nullable=False),
        sa.Column(
            "enable_auto_filters", sa.Boolean(), nullable=False, server_default="false"
        ),
        sa.ForeignKeyConstraint(
            ["slack_bot_id"],
            ["slack_bot.id"],
        ),
        sa.ForeignKeyConstraint(
            ["persona_id"],
            ["persona.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Handle existing Slack bot tokens first
    bot_token = None
    app_token = None
    first_row_id = None

    try:
        tokens = cast(dict, get_kv_store().load("slack_bot_tokens_config_key"))
    except Exception:
        tokens = {}

    bot_token = tokens.get("bot_token")
    app_token = tokens.get("app_token")

    if bot_token and app_token:
        session = Session(bind=op.get_bind())
        new_slack_bot = SlackBot(
            name="Slack Bot (Migrated)",
            enabled=True,
            bot_token=bot_token,
            app_token=app_token,
        )
        session.add(new_slack_bot)
        session.commit()
        first_row_id = new_slack_bot.id

    # Create a default bot if none exists
    # This is in case there are no slack tokens but there are channels configured
    op.execute(
        sa.text(
            """
            INSERT INTO slack_bot (name, enabled, bot_token, app_token)
            SELECT 'Default Bot', true, '', ''
            WHERE NOT EXISTS (SELECT 1 FROM slack_bot)
            RETURNING id;
            """
        )
    )

    # Get the bot ID to use (either from existing migration or newly created)
    bot_id_query = sa.text(
        """
        SELECT COALESCE(
            :first_row_id,
            (SELECT id FROM slack_bot ORDER BY id ASC LIMIT 1)
        ) as bot_id;
        """
    )
    result = op.get_bind().execute(bot_id_query, {"first_row_id": first_row_id})
    bot_id = result.scalar()

    # CTE (Common Table Expression) that transforms the old slack_bot_config table data
    # This splits up the channel_names into their own rows
    channel_names_cte = """
        WITH channel_names AS (
            SELECT
                sbc.id as config_id,
                sbc.persona_id,
                sbc.response_type,
                sbc.enable_auto_filters,
                jsonb_array_elements_text(sbc.channel_config->'channel_names') as channel_name,
                sbc.channel_config->>'respond_tag_only' as respond_tag_only,
                sbc.channel_config->>'respond_to_bots' as respond_to_bots,
                sbc.channel_config->'respond_member_group_list' as respond_member_group_list,
                sbc.channel_config->'answer_filters' as answer_filters,
                sbc.channel_config->'follow_up_tags' as follow_up_tags
            FROM slack_bot_config sbc
        )
    """

    # Insert the channel names into the new slack_channel_config table
    insert_statement = """
        INSERT INTO slack_channel_config (
            slack_bot_id,
            persona_id,
            channel_config,
            response_type,
            enable_auto_filters
        )
        SELECT
            :bot_id,
            channel_name.persona_id,
            jsonb_build_object(
                'channel_name', channel_name.channel_name,
                'respond_tag_only',
                COALESCE((channel_name.respond_tag_only)::boolean, false),
                'respond_to_bots',
                COALESCE((channel_name.respond_to_bots)::boolean, false),
                'respond_member_group_list',
                COALESCE(channel_name.respond_member_group_list, '[]'::jsonb),
                'answer_filters',
                COALESCE(channel_name.answer_filters, '[]'::jsonb),
                'follow_up_tags',
                COALESCE(channel_name.follow_up_tags, '[]'::jsonb)
            ),
            channel_name.response_type,
            channel_name.enable_auto_filters
        FROM channel_names channel_name;
    """

    op.execute(sa.text(channel_names_cte + insert_statement).bindparams(bot_id=bot_id))

    # Clean up old tokens if they existed
    try:
        if bot_token and app_token:
            get_kv_store().delete("slack_bot_tokens_config_key")
    except Exception:
        pass
    # Rename the table
    op.rename_table(
        "slack_bot_config__standard_answer_category",
        "slack_channel_config__standard_answer_category",
    )

    # Rename the column
    op.alter_column(
        "slack_channel_config__standard_answer_category",
        "slack_bot_config_id",
        new_column_name="slack_channel_config_id",
    )

    # Drop the table with CASCADE to handle dependent objects
    op.execute("DROP TABLE slack_bot_config CASCADE")


def downgrade() -> None:
    # Recreate the old slack_bot_config table
    op.create_table(
        "slack_bot_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("persona_id", sa.Integer(), nullable=True),
        sa.Column("channel_config", postgresql.JSONB(), nullable=False),
        sa.Column("response_type", sa.String(), nullable=False),
        sa.Column("enable_auto_filters", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["persona_id"],
            ["persona.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Migrate data back to the old format
    # Group by persona_id to combine channel names back into arrays
    op.execute(
        sa.text(
            """
            INSERT INTO slack_bot_config (
                persona_id,
                channel_config,
                response_type,
                enable_auto_filters
            )
            SELECT DISTINCT ON (persona_id)
                persona_id,
                jsonb_build_object(
                    'channel_names', (
                        SELECT jsonb_agg(c.channel_config->>'channel_name')
                        FROM slack_channel_config c
                        WHERE c.persona_id = scc.persona_id
                    ),
                    'respond_tag_only', (channel_config->>'respond_tag_only')::boolean,
                    'respond_to_bots', (channel_config->>'respond_to_bots')::boolean,
                    'respond_member_group_list', channel_config->'respond_member_group_list',
                    'answer_filters', channel_config->'answer_filters',
                    'follow_up_tags', channel_config->'follow_up_tags'
                ),
                response_type,
                enable_auto_filters
            FROM slack_channel_config scc
            WHERE persona_id IS NOT NULL;
            """
        )
    )

    # Rename the table back
    op.rename_table(
        "slack_channel_config__standard_answer_category",
        "slack_bot_config__standard_answer_category",
    )

    # Rename the column back
    op.alter_column(
        "slack_bot_config__standard_answer_category",
        "slack_channel_config_id",
        new_column_name="slack_bot_config_id",
    )

    # Try to save the first bot's tokens back to KV store
    try:
        first_bot = (
            op.get_bind()
            .execute(
                sa.text(
                    "SELECT bot_token, app_token FROM slack_bot ORDER BY id LIMIT 1"
                )
            )
            .first()
        )
        if first_bot and first_bot.bot_token and first_bot.app_token:
            tokens = {
                "bot_token": first_bot.bot_token,
                "app_token": first_bot.app_token,
            }
            get_kv_store().store("slack_bot_tokens_config_key", tokens)
    except Exception:
        pass

    # Drop the new tables in reverse order
    op.drop_table("slack_channel_config")
    op.drop_table("slack_bot")



================================================
FILE: backend/alembic/versions/50b683a8295c_add_additional_retrieval_controls_to_.py
================================================
"""Add additional retrieval controls to Persona

Revision ID: 50b683a8295c
Revises: 7da0ae5ad583
Create Date: 2023-11-27 17:23:29.668422

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "50b683a8295c"
down_revision = "7da0ae5ad583"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("persona", sa.Column("num_chunks", sa.Integer(), nullable=True))
    op.add_column(
        "persona",
        sa.Column("apply_llm_relevance_filter", sa.Boolean(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("persona", "apply_llm_relevance_filter")
    op.drop_column("persona", "num_chunks")



================================================
FILE: backend/alembic/versions/52a219fb5233_add_last_synced_and_last_modified_to_document_table.py
================================================
"""Add last synced and last modified to document table

Revision ID: 52a219fb5233
Revises: f7e58d357687
Create Date: 2024-08-28 17:40:46.077470

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision = "52a219fb5233"
down_revision = "f7e58d357687"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # last modified represents the last time anything needing syncing to vespa changed
    # including row metadata and the document itself. This obviously does not include
    # the last_synced column.
    op.add_column(
        "document",
        sa.Column(
            "last_modified",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
    )

    # last synced represents the last time this document was synced to Vespa
    op.add_column(
        "document",
        sa.Column("last_synced", sa.DateTime(timezone=True), nullable=True),
    )

    # Set last_synced to the same value as last_modified for existing rows
    op.execute(
        """
        UPDATE document
        SET last_synced = last_modified
        """
    )

    op.create_index(
        op.f("ix_document_last_modified"),
        "document",
        ["last_modified"],
        unique=False,
    )

    op.create_index(
        op.f("ix_document_last_synced"),
        "document",
        ["last_synced"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(op.f("ix_document_last_synced"), table_name="document")
    op.drop_index(op.f("ix_document_last_modified"), table_name="document")
    op.drop_column("document", "last_synced")
    op.drop_column("document", "last_modified")



================================================
FILE: backend/alembic/versions/54a74a0417fc_danswerbot_onyxbot.py
================================================
"""danswerbot -> onyxbot

Revision ID: 54a74a0417fc
Revises: 94dc3d0236f8
Create Date: 2024-12-11 18:05:05.490737

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "54a74a0417fc"
down_revision = "94dc3d0236f8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.alter_column("chat_session", "danswerbot_flow", new_column_name="onyxbot_flow")


def downgrade() -> None:
    op.alter_column("chat_session", "onyxbot_flow", new_column_name="danswerbot_flow")



================================================
FILE: backend/alembic/versions/55546a7967ee_assistant_rework.py
================================================
"""assistant_rework

Revision ID: 55546a7967ee
Revises: 61ff3651add4
Create Date: 2024-09-18 17:00:23.755399

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = "55546a7967ee"
down_revision = "61ff3651add4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Reworking persona and user tables for new assistant features
    # keep track of user's chosen assistants separate from their `ordering`
    op.add_column("persona", sa.Column("builtin_persona", sa.Boolean(), nullable=True))
    op.execute("UPDATE persona SET builtin_persona = default_persona")
    op.alter_column("persona", "builtin_persona", nullable=False)
    op.drop_index("_default_persona_name_idx", table_name="persona")
    op.create_index(
        "_builtin_persona_name_idx",
        "persona",
        ["name"],
        unique=True,
        postgresql_where=sa.text("builtin_persona = true"),
    )

    op.add_column(
        "user", sa.Column("visible_assistants", postgresql.JSONB(), nullable=True)
    )
    op.add_column(
        "user", sa.Column("hidden_assistants", postgresql.JSONB(), nullable=True)
    )
    op.execute(
        "UPDATE \"user\" SET visible_assistants = '[]'::jsonb, hidden_assistants = '[]'::jsonb"
    )
    op.alter_column(
        "user",
        "visible_assistants",
        nullable=False,
        server_default=sa.text("'[]'::jsonb"),
    )
    op.alter_column(
        "user",
        "hidden_assistants",
        nullable=False,
        server_default=sa.text("'[]'::jsonb"),
    )
    op.drop_column("persona", "default_persona")
    op.add_column(
        "persona", sa.Column("is_default_persona", sa.Boolean(), nullable=True)
    )


def downgrade() -> None:
    # Reverting changes made in upgrade
    op.drop_column("user", "hidden_assistants")
    op.drop_column("user", "visible_assistants")
    op.drop_index("_builtin_persona_name_idx", table_name="persona")

    op.drop_column("persona", "is_default_persona")
    op.add_column("persona", sa.Column("default_persona", sa.Boolean(), nullable=True))
    op.execute("UPDATE persona SET default_persona = builtin_persona")
    op.alter_column("persona", "default_persona", nullable=False)
    op.drop_column("persona", "builtin_persona")
    op.create_index(
        "_default_persona_name_idx",
        "persona",
        ["name"],
        unique=True,
        postgresql_where=sa.text("default_persona = true"),
    )



================================================
FILE: backend/alembic/versions/570282d33c49_track_onyxbot_explicitly.py
================================================
"""Track Onyxbot Explicitly

Revision ID: 570282d33c49
Revises: 7547d982db8f
Create Date: 2024-05-04 17:49:28.568109

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "570282d33c49"
down_revision = "7547d982db8f"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_session", sa.Column("danswerbot_flow", sa.Boolean(), nullable=True)
    )
    op.execute("UPDATE chat_session SET danswerbot_flow = one_shot")
    op.alter_column("chat_session", "danswerbot_flow", nullable=False)


def downgrade() -> None:
    op.drop_column("chat_session", "danswerbot_flow")



================================================
FILE: backend/alembic/versions/57b53544726e_add_document_set_tables.py
================================================
"""Add document set tables

Revision ID: 57b53544726e
Revises: 800f48024ae9
Create Date: 2023-09-20 16:59:39.097177

"""

from alembic import op
import fastapi_users_db_sqlalchemy
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "57b53544726e"
down_revision = "800f48024ae9"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "document_set",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=False),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
        sa.Column("is_up_to_date", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "document_set__connector_credential_pair",
        sa.Column("document_set_id", sa.Integer(), nullable=False),
        sa.Column("connector_credential_pair_id", sa.Integer(), nullable=False),
        sa.Column("is_current", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["connector_credential_pair_id"],
            ["connector_credential_pair.id"],
        ),
        sa.ForeignKeyConstraint(
            ["document_set_id"],
            ["document_set.id"],
        ),
        sa.PrimaryKeyConstraint(
            "document_set_id", "connector_credential_pair_id", "is_current"
        ),
    )


def downgrade() -> None:
    op.drop_table("document_set__connector_credential_pair")
    op.drop_table("document_set")



================================================
FILE: backend/alembic/versions/5809c0787398_add_chat_sessions.py
================================================
"""Add Chat Sessions

Revision ID: 5809c0787398
Revises: d929f0c1c6af
Create Date: 2023-09-04 15:29:44.002164

"""

import fastapi_users_db_sqlalchemy
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "5809c0787398"
down_revision = "d929f0c1c6af"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "chat_session",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column("deleted", sa.Boolean(), nullable=False),
        sa.Column(
            "time_updated",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "time_created",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "chat_message",
        sa.Column("chat_session_id", sa.Integer(), nullable=False),
        sa.Column("message_number", sa.Integer(), nullable=False),
        sa.Column("edit_number", sa.Integer(), nullable=False),
        sa.Column("parent_edit_number", sa.Integer(), nullable=True),
        sa.Column("latest", sa.Boolean(), nullable=False),
        sa.Column("message", sa.Text(), nullable=False),
        sa.Column(
            "message_type",
            sa.Enum(
                "SYSTEM",
                "USER",
                "ASSISTANT",
                "DANSWER",
                name="messagetype",
                native_enum=False,
            ),
            nullable=False,
        ),
        sa.Column(
            "time_sent",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["chat_session_id"],
            ["chat_session.id"],
        ),
        sa.PrimaryKeyConstraint("chat_session_id", "message_number", "edit_number"),
    )


def downgrade() -> None:
    op.drop_table("chat_message")
    op.drop_table("chat_session")



================================================
FILE: backend/alembic/versions/58c50ef19f08_add_stale_column_to_user__external_user_.py
================================================
"""add stale column to external user group tables

Revision ID: 58c50ef19f08
Revises: 7b9b952abdf6
Create Date: 2025-06-25 14:08:14.162380

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "58c50ef19f08"
down_revision = "7b9b952abdf6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add the stale column with default value False to user__external_user_group_id
    op.add_column(
        "user__external_user_group_id",
        sa.Column("stale", sa.Boolean(), nullable=False, server_default="false"),
    )

    # Create index for efficient querying of stale rows by cc_pair_id
    op.create_index(
        "ix_user__external_user_group_id_cc_pair_id_stale",
        "user__external_user_group_id",
        ["cc_pair_id", "stale"],
        unique=False,
    )

    # Create index for efficient querying of all stale rows
    op.create_index(
        "ix_user__external_user_group_id_stale",
        "user__external_user_group_id",
        ["stale"],
        unique=False,
    )

    # Add the stale column with default value False to public_external_user_group
    op.add_column(
        "public_external_user_group",
        sa.Column("stale", sa.Boolean(), nullable=False, server_default="false"),
    )

    # Create index for efficient querying of stale rows by cc_pair_id
    op.create_index(
        "ix_public_external_user_group_cc_pair_id_stale",
        "public_external_user_group",
        ["cc_pair_id", "stale"],
        unique=False,
    )

    # Create index for efficient querying of all stale rows
    op.create_index(
        "ix_public_external_user_group_stale",
        "public_external_user_group",
        ["stale"],
        unique=False,
    )


def downgrade() -> None:
    # Drop the indices for public_external_user_group first
    op.drop_index(
        "ix_public_external_user_group_stale", table_name="public_external_user_group"
    )
    op.drop_index(
        "ix_public_external_user_group_cc_pair_id_stale",
        table_name="public_external_user_group",
    )

    # Drop the stale column from public_external_user_group
    op.drop_column("public_external_user_group", "stale")

    # Drop the indices for user__external_user_group_id
    op.drop_index(
        "ix_user__external_user_group_id_stale",
        table_name="user__external_user_group_id",
    )
    op.drop_index(
        "ix_user__external_user_group_id_cc_pair_id_stale",
        table_name="user__external_user_group_id",
    )

    # Drop the stale column from user__external_user_group_id
    op.drop_column("user__external_user_group_id", "stale")



================================================
FILE: backend/alembic/versions/5b29123cd710_nullable_search_settings_for_historic_.py
================================================
"""nullable search settings for historic index attempts

Revision ID: 5b29123cd710
Revises: 949b4a92a401
Create Date: 2024-10-30 19:37:59.630704

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "5b29123cd710"
down_revision = "949b4a92a401"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop the existing foreign key constraint
    op.drop_constraint(
        "fk_index_attempt_search_settings", "index_attempt", type_="foreignkey"
    )

    # Modify the column to be nullable
    op.alter_column(
        "index_attempt", "search_settings_id", existing_type=sa.INTEGER(), nullable=True
    )

    # Add back the foreign key with ON DELETE SET NULL
    op.create_foreign_key(
        "fk_index_attempt_search_settings",
        "index_attempt",
        "search_settings",
        ["search_settings_id"],
        ["id"],
        ondelete="SET NULL",
    )


def downgrade() -> None:
    # Warning: This will delete all index attempts that don't have search settings
    op.execute(
        """
        DELETE FROM index_attempt
        WHERE search_settings_id IS NULL
    """
    )

    # Drop foreign key constraint
    op.drop_constraint(
        "fk_index_attempt_search_settings", "index_attempt", type_="foreignkey"
    )

    # Modify the column to be not nullable
    op.alter_column(
        "index_attempt",
        "search_settings_id",
        existing_type=sa.INTEGER(),
        nullable=False,
    )

    # Add back the foreign key without ON DELETE SET NULL
    op.create_foreign_key(
        "fk_index_attempt_search_settings",
        "index_attempt",
        "search_settings",
        ["search_settings_id"],
        ["id"],
    )



================================================
FILE: backend/alembic/versions/5c448911b12f_add_content_type_to_userfile.py
================================================
"""Add content type to UserFile

Revision ID: 5c448911b12f
Revises: 47a07e1a38f1
Create Date: 2025-04-25 16:59:48.182672

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5c448911b12f"
down_revision = "47a07e1a38f1"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("user_file", sa.Column("content_type", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("user_file", "content_type")



================================================
FILE: backend/alembic/versions/5c7fdadae813_match_any_keywords_flag_for_standard_.py
================================================
"""match_any_keywords flag for standard answers

Revision ID: 5c7fdadae813
Revises: efb35676026c
Create Date: 2024-09-13 18:52:59.256478

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5c7fdadae813"
down_revision = "efb35676026c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "standard_answer",
        sa.Column(
            "match_any_keywords",
            sa.Boolean(),
            nullable=False,
            server_default=sa.false(),
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("standard_answer", "match_any_keywords")
    # ### end Alembic commands ###



================================================
FILE: backend/alembic/versions/5d12a446f5c0_add_api_version_and_deployment_name_to_.py
================================================
"""add api_version and deployment_name to search settings

Revision ID: 5d12a446f5c0
Revises: e4334d5b33ba
Create Date: 2024-10-08 15:56:07.975636

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "5d12a446f5c0"
down_revision = "e4334d5b33ba"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "embedding_provider", sa.Column("api_version", sa.String(), nullable=True)
    )
    op.add_column(
        "embedding_provider", sa.Column("deployment_name", sa.String(), nullable=True)
    )


def downgrade() -> None:
    op.drop_column("embedding_provider", "deployment_name")
    op.drop_column("embedding_provider", "api_version")



================================================
FILE: backend/alembic/versions/5e84129c8be3_add_docs_indexed_column_to_index_.py
================================================
"""Add docs_indexed_column + time_started to index_attempt table

Revision ID: 5e84129c8be3
Revises: e6a4bbc13fe4
Create Date: 2023-08-10 21:43:09.069523

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "5e84129c8be3"
down_revision = "e6a4bbc13fe4"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "index_attempt",
        sa.Column("num_docs_indexed", sa.Integer()),
    )
    op.add_column(
        "index_attempt",
        sa.Column(
            "time_started",
            sa.DateTime(timezone=True),
            nullable=True,
        ),
    )


def downgrade() -> None:
    op.drop_column("index_attempt", "time_started")
    op.drop_column("index_attempt", "num_docs_indexed")



================================================
FILE: backend/alembic/versions/5f4b8568a221_add_removed_documents_to_index_attempt.py
================================================
"""add removed documents to index_attempt

Revision ID: 5f4b8568a221
Revises: dbaa756c2ccf
Create Date: 2024-02-16 15:02:03.319907

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5f4b8568a221"
down_revision = "8987770549c0"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "index_attempt",
        sa.Column("docs_removed_from_index", sa.Integer()),
    )
    op.execute("UPDATE index_attempt SET docs_removed_from_index = 0")


def downgrade() -> None:
    op.drop_column("index_attempt", "docs_removed_from_index")



================================================
FILE: backend/alembic/versions/5fc1f54cc252_hybrid_enum.py
================================================
"""hybrid-enum

Revision ID: 5fc1f54cc252
Revises: 1d6ad76d1f37
Create Date: 2024-08-06 15:35:40.278485

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5fc1f54cc252"
down_revision = "1d6ad76d1f37"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.drop_column("persona", "search_type")


def downgrade() -> None:
    op.add_column("persona", sa.Column("search_type", sa.String(), nullable=True))
    op.execute("UPDATE persona SET search_type = 'SEMANTIC'")
    op.alter_column("persona", "search_type", nullable=False)



================================================
FILE: backend/alembic/versions/61ff3651add4_add_permission_syncing.py
================================================
"""Add Permission Syncing

Revision ID: 61ff3651add4
Revises: 1b8206b29c5d
Create Date: 2024-09-05 13:57:11.770413

"""

import fastapi_users_db_sqlalchemy

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "61ff3651add4"
down_revision = "1b8206b29c5d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Admin user who set up connectors will lose access to the docs temporarily
    # only way currently to give back access is to rerun from beginning
    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "access_type",
            sa.String(),
            nullable=True,
        ),
    )
    op.execute(
        "UPDATE connector_credential_pair SET access_type = 'PUBLIC' WHERE is_public = true"
    )
    op.execute(
        "UPDATE connector_credential_pair SET access_type = 'PRIVATE' WHERE is_public = false"
    )
    op.alter_column("connector_credential_pair", "access_type", nullable=False)

    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "auto_sync_options",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )
    op.add_column(
        "connector_credential_pair",
        sa.Column("last_time_perm_sync", sa.DateTime(timezone=True), nullable=True),
    )
    op.drop_column("connector_credential_pair", "is_public")

    op.add_column(
        "document",
        sa.Column("external_user_emails", postgresql.ARRAY(sa.String()), nullable=True),
    )
    op.add_column(
        "document",
        sa.Column(
            "external_user_group_ids", postgresql.ARRAY(sa.String()), nullable=True
        ),
    )
    op.add_column(
        "document",
        sa.Column("is_public", sa.Boolean(), nullable=True),
    )

    op.create_table(
        "user__external_user_group_id",
        sa.Column(
            "user_id", fastapi_users_db_sqlalchemy.generics.GUID(), nullable=False
        ),
        sa.Column("external_user_group_id", sa.String(), nullable=False),
        sa.Column("cc_pair_id", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("user_id"),
    )

    op.drop_column("external_permission", "user_id")
    op.drop_column("email_to_external_user_cache", "user_id")
    op.drop_table("permission_sync_run")
    op.drop_table("external_permission")
    op.drop_table("email_to_external_user_cache")


def downgrade() -> None:
    op.add_column(
        "connector_credential_pair",
        sa.Column("is_public", sa.BOOLEAN(), nullable=True),
    )
    op.execute(
        "UPDATE connector_credential_pair SET is_public = (access_type = 'PUBLIC')"
    )
    op.alter_column("connector_credential_pair", "is_public", nullable=False)

    op.drop_column("connector_credential_pair", "auto_sync_options")
    op.drop_column("connector_credential_pair", "access_type")
    op.drop_column("connector_credential_pair", "last_time_perm_sync")
    op.drop_column("document", "external_user_emails")
    op.drop_column("document", "external_user_group_ids")
    op.drop_column("document", "is_public")

    op.drop_table("user__external_user_group_id")

    # Drop the enum type at the end of the downgrade
    op.create_table(
        "permission_sync_run",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "source_type",
            sa.String(),
            nullable=False,
        ),
        sa.Column("update_type", sa.String(), nullable=False),
        sa.Column("cc_pair_id", sa.Integer(), nullable=True),
        sa.Column(
            "status",
            sa.String(),
            nullable=False,
        ),
        sa.Column("error_msg", sa.Text(), nullable=True),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["cc_pair_id"],
            ["connector_credential_pair.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "external_permission",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=True),
        sa.Column("user_email", sa.String(), nullable=False),
        sa.Column(
            "source_type",
            sa.String(),
            nullable=False,
        ),
        sa.Column("external_permission_group", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "email_to_external_user_cache",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("external_user_id", sa.String(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=True),
        sa.Column("user_email", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )



================================================
FILE: backend/alembic/versions/643a84a42a33_add_user_configured_names_to_llmprovider.py
================================================
"""Add user-configured names to LLMProvider

Revision ID: 643a84a42a33
Revises: 0a98909f2757
Create Date: 2024-05-07 14:54:55.493100

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "643a84a42a33"
down_revision = "0a98909f2757"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("llm_provider", sa.Column("provider", sa.String(), nullable=True))
    # move "name" -> "provider" to match the new schema
    op.execute("UPDATE llm_provider SET provider = name")
    # pretty up display name
    op.execute("UPDATE llm_provider SET name = 'OpenAI' WHERE name = 'openai'")
    op.execute("UPDATE llm_provider SET name = 'Anthropic' WHERE name = 'anthropic'")
    op.execute("UPDATE llm_provider SET name = 'Azure OpenAI' WHERE name = 'azure'")
    op.execute("UPDATE llm_provider SET name = 'AWS Bedrock' WHERE name = 'bedrock'")

    # update personas to use the new provider names
    op.execute(
        "UPDATE persona SET llm_model_provider_override = 'OpenAI' WHERE llm_model_provider_override = 'openai'"
    )
    op.execute(
        "UPDATE persona SET llm_model_provider_override = 'Anthropic' WHERE llm_model_provider_override = 'anthropic'"
    )
    op.execute(
        "UPDATE persona SET llm_model_provider_override = 'Azure OpenAI' WHERE llm_model_provider_override = 'azure'"
    )
    op.execute(
        "UPDATE persona SET llm_model_provider_override = 'AWS Bedrock' WHERE llm_model_provider_override = 'bedrock'"
    )


def downgrade() -> None:
    op.execute("UPDATE llm_provider SET name = provider")
    op.drop_column("llm_provider", "provider")



================================================
FILE: backend/alembic/versions/65bc6e0f8500_remove_kg_subtype_from_db.py
================================================
"""remove kg subtype from db

Revision ID: 65bc6e0f8500
Revises: cec7ec36c505
Create Date: 2025-06-13 10:04:27.705976

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "65bc6e0f8500"
down_revision = "cec7ec36c505"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_column("kg_entity", "entity_class")
    op.drop_column("kg_entity", "entity_subtype")
    op.drop_column("kg_entity_extraction_staging", "entity_class")
    op.drop_column("kg_entity_extraction_staging", "entity_subtype")


def downgrade() -> None:
    op.add_column(
        "kg_entity_extraction_staging",
        sa.Column("entity_subtype", sa.String(), nullable=True, index=True),
    )
    op.add_column(
        "kg_entity_extraction_staging",
        sa.Column("entity_class", sa.String(), nullable=True, index=True),
    )
    op.add_column(
        "kg_entity", sa.Column("entity_subtype", sa.String(), nullable=True, index=True)
    )
    op.add_column(
        "kg_entity", sa.Column("entity_class", sa.String(), nullable=True, index=True)
    )



================================================
FILE: backend/alembic/versions/6756efa39ada_id_uuid_for_chat_session.py
================================================
"""Migrate chat_session and chat_message tables to use UUID primary keys

Revision ID: 6756efa39ada
Revises: 5d12a446f5c0
Create Date: 2024-10-15 17:47:44.108537

"""

from alembic import op
import sqlalchemy as sa

revision = "6756efa39ada"
down_revision = "5d12a446f5c0"
branch_labels = None
depends_on = None

"""
This script:
1. Adds UUID columns to chat_session and chat_message
2. Populates new columns with UUIDs
3. Updates foreign key relationships
4. Removes old integer ID columns

Note: Downgrade will assign new integer IDs, not restore original ones.
"""


def upgrade() -> None:
    op.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto;")

    op.add_column(
        "chat_session",
        sa.Column(
            "new_id",
            sa.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
    )

    op.execute("UPDATE chat_session SET new_id = gen_random_uuid();")

    op.add_column(
        "chat_message",
        sa.Column("new_chat_session_id", sa.UUID(as_uuid=True), nullable=True),
    )

    op.execute(
        """
        UPDATE chat_message
        SET new_chat_session_id = cs.new_id
        FROM chat_session cs
        WHERE chat_message.chat_session_id = cs.id;
        """
    )

    op.drop_constraint(
        "chat_message_chat_session_id_fkey", "chat_message", type_="foreignkey"
    )

    op.drop_column("chat_message", "chat_session_id")
    op.alter_column(
        "chat_message", "new_chat_session_id", new_column_name="chat_session_id"
    )

    op.drop_constraint("chat_session_pkey", "chat_session", type_="primary")
    op.drop_column("chat_session", "id")
    op.alter_column("chat_session", "new_id", new_column_name="id")

    op.create_primary_key("chat_session_pkey", "chat_session", ["id"])

    op.create_foreign_key(
        "chat_message_chat_session_id_fkey",
        "chat_message",
        "chat_session",
        ["chat_session_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    op.drop_constraint(
        "chat_message_chat_session_id_fkey", "chat_message", type_="foreignkey"
    )

    op.add_column(
        "chat_session",
        sa.Column("old_id", sa.Integer, autoincrement=True, nullable=True),
    )

    op.execute("CREATE SEQUENCE chat_session_old_id_seq OWNED BY chat_session.old_id;")
    op.execute(
        "ALTER TABLE chat_session ALTER COLUMN old_id SET DEFAULT nextval('chat_session_old_id_seq');"
    )

    op.execute(
        "UPDATE chat_session SET old_id = nextval('chat_session_old_id_seq') WHERE old_id IS NULL;"
    )

    op.alter_column("chat_session", "old_id", nullable=False)

    op.drop_constraint("chat_session_pkey", "chat_session", type_="primary")
    op.create_primary_key("chat_session_pkey", "chat_session", ["old_id"])

    op.add_column(
        "chat_message",
        sa.Column("old_chat_session_id", sa.Integer, nullable=True),
    )

    op.execute(
        """
        UPDATE chat_message
        SET old_chat_session_id = cs.old_id
        FROM chat_session cs
        WHERE chat_message.chat_session_id = cs.id;
        """
    )

    op.drop_column("chat_message", "chat_session_id")
    op.alter_column(
        "chat_message", "old_chat_session_id", new_column_name="chat_session_id"
    )

    op.create_foreign_key(
        "chat_message_chat_session_id_fkey",
        "chat_message",
        "chat_session",
        ["chat_session_id"],
        ["old_id"],
        ondelete="CASCADE",
    )

    op.drop_column("chat_session", "id")
    op.alter_column("chat_session", "old_id", new_column_name="id")

    op.alter_column(
        "chat_session",
        "id",
        type_=sa.Integer(),
        existing_type=sa.Integer(),
        existing_nullable=False,
        existing_server_default=False,
    )

    # Rename the sequence
    op.execute("ALTER SEQUENCE chat_session_old_id_seq RENAME TO chat_session_id_seq;")

    # Update the default value to use the renamed sequence
    op.alter_column(
        "chat_session",
        "id",
        server_default=sa.text("nextval('chat_session_id_seq'::regclass)"),
    )



================================================
FILE: backend/alembic/versions/6a804aeb4830_duplicated_no_harm_user_file_migration.py
================================================
"""duplicated no-harm user file migration

Revision ID: 6a804aeb4830
Revises: 8e1ac4f39a9f
Create Date: 2025-04-01 07:26:10.539362

"""

# revision identifiers, used by Alembic.
revision = "6a804aeb4830"
down_revision = "8e1ac4f39a9f"
branch_labels = None
depends_on = None


# Leaving this around only because some people might be on this migration
# originally was a duplicate of the user files migration
def upgrade() -> None:
    pass


def downgrade() -> None:
    pass



================================================
FILE: backend/alembic/versions/6d387b3196c2_basic_auth.py
================================================
"""Basic Auth

Revision ID: 6d387b3196c2
Revises: 47433d30de82
Create Date: 2023-05-05 14:40:10.242502

"""

import fastapi_users_db_sqlalchemy
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "6d387b3196c2"
down_revision = "47433d30de82"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "user",
        sa.Column("id", fastapi_users_db_sqlalchemy.generics.GUID(), nullable=False),
        sa.Column("email", sa.String(length=320), nullable=False),
        sa.Column("hashed_password", sa.String(length=1024), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_superuser", sa.Boolean(), nullable=False),
        sa.Column("is_verified", sa.Boolean(), nullable=False),
        sa.Column(
            "role",
            sa.Enum("BASIC", "ADMIN", name="userrole", native_enum=False),
            default="BASIC",
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_email"), "user", ["email"], unique=True)
    op.create_table(
        "accesstoken",
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=False,
        ),
        sa.Column("token", sa.String(length=43), nullable=False),
        sa.Column(
            "created_at",
            fastapi_users_db_sqlalchemy.generics.TIMESTAMPAware(timezone=True),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="cascade"),
        sa.PrimaryKeyConstraint("token"),
    )
    op.create_index(
        op.f("ix_accesstoken_created_at"),
        "accesstoken",
        ["created_at"],
        unique=False,
    )
    op.alter_column(
        "index_attempt",
        "time_created",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
        existing_server_default=sa.text("now()"),  # type: ignore
    )
    op.alter_column(
        "index_attempt",
        "time_updated",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
    )


def downgrade() -> None:
    op.alter_column(
        "index_attempt",
        "time_updated",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
    )
    op.alter_column(
        "index_attempt",
        "time_created",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),  # type: ignore
    )
    op.drop_index(op.f("ix_accesstoken_created_at"), table_name="accesstoken")
    op.drop_table("accesstoken")
    op.drop_index(op.f("ix_user_email"), table_name="user")
    op.drop_table("user")



================================================
FILE: backend/alembic/versions/6d562f86c78b_remove_default_bot.py
================================================
"""remove default bot

Revision ID: 6d562f86c78b
Revises: 177de57c21c9
Create Date: 2024-11-22 11:51:29.331336

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6d562f86c78b"
down_revision = "177de57c21c9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        sa.text(
            """
            DELETE FROM slack_bot
            WHERE name = 'Default Bot'
            AND bot_token = ''
            AND app_token = ''
            AND NOT EXISTS (
                SELECT 1 FROM slack_channel_config
                WHERE slack_channel_config.slack_bot_id = slack_bot.id
            )
            """
        )
    )


def downgrade() -> None:
    op.execute(
        sa.text(
            """
            INSERT INTO slack_bot (name, enabled, bot_token, app_token)
            SELECT 'Default Bot', true, '', ''
            WHERE NOT EXISTS (SELECT 1 FROM slack_bot)
            RETURNING id;
            """
        )
    )



================================================
FILE: backend/alembic/versions/6fc7886d665d_make_categories_labels_and_many_to_many.py
================================================
"""make categories labels and many to many

Revision ID: 6fc7886d665d
Revises: 3c6531f32351
Create Date: 2025-01-13 18:12:18.029112

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "6fc7886d665d"
down_revision = "3c6531f32351"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Rename persona_category table to persona_label
    op.rename_table("persona_category", "persona_label")

    # Create the new association table
    op.create_table(
        "persona__persona_label",
        sa.Column("persona_id", sa.Integer(), nullable=False),
        sa.Column("persona_label_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["persona_id"],
            ["persona.id"],
        ),
        sa.ForeignKeyConstraint(
            ["persona_label_id"],
            ["persona_label.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("persona_id", "persona_label_id"),
    )

    # Copy existing relationships to the new table
    op.execute(
        """
        INSERT INTO persona__persona_label (persona_id, persona_label_id)
        SELECT id, category_id FROM persona WHERE category_id IS NOT NULL
    """
    )

    # Remove the old category_id column from persona table
    op.drop_column("persona", "category_id")


def downgrade() -> None:
    # Rename persona_label table back to persona_category
    op.rename_table("persona_label", "persona_category")

    # Add back the category_id column to persona table
    op.add_column("persona", sa.Column("category_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "persona_category_id_fkey",
        "persona",
        "persona_category",
        ["category_id"],
        ["id"],
    )

    # Copy the first label relationship back to the persona table
    op.execute(
        """
        UPDATE persona
        SET category_id = (
            SELECT persona_label_id
            FROM persona__persona_label
            WHERE persona__persona_label.persona_id = persona.id
            LIMIT 1
        )
    """
    )

    # Drop the association table
    op.drop_table("persona__persona_label")



================================================
FILE: backend/alembic/versions/703313b75876_add_tokenratelimit_tables.py
================================================
"""Add TokenRateLimit Tables

Revision ID: 703313b75876
Revises: fad14119fb92
Create Date: 2024-04-15 01:36:02.952809

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "703313b75876"
down_revision = "fad14119fb92"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "token_rate_limit",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("enabled", sa.Boolean(), nullable=False),
        sa.Column("token_budget", sa.Integer(), nullable=False),
        sa.Column("period_hours", sa.Integer(), nullable=False),
        sa.Column(
            "scope",
            sa.String(length=10),
            nullable=False,
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "token_rate_limit__user_group",
        sa.Column("rate_limit_id", sa.Integer(), nullable=False),
        sa.Column("user_group_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rate_limit_id"],
            ["token_rate_limit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_group_id"],
            ["user_group.id"],
        ),
        sa.PrimaryKeyConstraint("rate_limit_id", "user_group_id"),
    )

    # NOTE: rate limit settings used to be stored in the "token_budget_settings" key in the
    # KeyValueStore. This will now be lost. The KV store works differently than it used to
    # so the migration is fairly complicated and likely not worth it to support (pretty much
    # nobody will have it set)


def downgrade() -> None:
    op.drop_table("token_rate_limit__user_group")
    op.drop_table("token_rate_limit")



================================================
FILE: backend/alembic/versions/70f00c45c0f2_more_descriptive_filestore.py
================================================
"""More Descriptive Filestore

Revision ID: 70f00c45c0f2
Revises: 3879338f8ba1
Create Date: 2024-05-17 17:51:41.926893

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "70f00c45c0f2"
down_revision = "3879338f8ba1"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("file_store", sa.Column("display_name", sa.String(), nullable=True))
    op.add_column(
        "file_store",
        sa.Column(
            "file_origin",
            sa.String(),
            nullable=False,
            server_default="connector",  # Default to connector
        ),
    )
    op.add_column(
        "file_store",
        sa.Column(
            "file_type", sa.String(), nullable=False, server_default="text/plain"
        ),
    )
    op.add_column(
        "file_store",
        sa.Column(
            "file_metadata",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )

    op.execute(
        """
        UPDATE file_store
        SET file_origin = CASE
            WHEN file_name LIKE 'chat__%' THEN 'chat_upload'
            ELSE 'connector'
        END,
        file_name = CASE
            WHEN file_name LIKE 'chat__%' THEN SUBSTR(file_name, 7)
            ELSE file_name
        END,
        file_type = CASE
            WHEN file_name LIKE 'chat__%' THEN 'image/png'
            ELSE 'text/plain'
        END
    """
    )


def downgrade() -> None:
    op.drop_column("file_store", "file_metadata")
    op.drop_column("file_store", "file_type")
    op.drop_column("file_store", "file_origin")
    op.drop_column("file_store", "display_name")



================================================
FILE: backend/alembic/versions/72bdc9929a46_permission_auto_sync_framework.py
================================================
"""Permission Auto Sync Framework

Revision ID: 72bdc9929a46
Revises: 475fcefe8826
Create Date: 2024-04-14 21:15:28.659634

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "72bdc9929a46"
down_revision = "475fcefe8826"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "email_to_external_user_cache",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("external_user_id", sa.String(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=True),
        sa.Column("user_email", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "external_permission",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=True),
        sa.Column("user_email", sa.String(), nullable=False),
        sa.Column(
            "source_type",
            sa.String(),
            nullable=False,
        ),
        sa.Column("external_permission_group", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "permission_sync_run",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "source_type",
            sa.String(),
            nullable=False,
        ),
        sa.Column("update_type", sa.String(), nullable=False),
        sa.Column("cc_pair_id", sa.Integer(), nullable=True),
        sa.Column(
            "status",
            sa.String(),
            nullable=False,
        ),
        sa.Column("error_msg", sa.Text(), nullable=True),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["cc_pair_id"],
            ["connector_credential_pair.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("permission_sync_run")
    op.drop_table("external_permission")
    op.drop_table("email_to_external_user_cache")



================================================
FILE: backend/alembic/versions/7477a5f5d728_added_model_defaults_for_users.py
================================================
"""Added model defaults for users

Revision ID: 7477a5f5d728
Revises: 213fd978c6d8
Create Date: 2024-08-04 19:00:04.512634

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7477a5f5d728"
down_revision = "213fd978c6d8"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("user", sa.Column("default_model", sa.Text(), nullable=True))


def downgrade() -> None:
    op.drop_column("user", "default_model")



================================================
FILE: backend/alembic/versions/7547d982db8f_chat_folders.py
================================================
"""Chat Folders

Revision ID: 7547d982db8f
Revises: ef7da92f7213
Create Date: 2024-05-02 15:18:56.573347

"""

from alembic import op
import sqlalchemy as sa
import fastapi_users_db_sqlalchemy

# revision identifiers, used by Alembic.
revision = "7547d982db8f"
down_revision = "ef7da92f7213"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "chat_folder",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("display_priority", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column("chat_session", sa.Column("folder_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "chat_session_chat_folder_fk",
        "chat_session",
        "chat_folder",
        ["folder_id"],
        ["id"],
    )


def downgrade() -> None:
    op.drop_constraint(
        "chat_session_chat_folder_fk", "chat_session", type_="foreignkey"
    )
    op.drop_column("chat_session", "folder_id")
    op.drop_table("chat_folder")



================================================
FILE: backend/alembic/versions/767f1c2a00eb_count_chat_tokens.py
================================================
"""Count Chat Tokens

Revision ID: 767f1c2a00eb
Revises: dba7f71618f5
Create Date: 2023-09-21 10:03:21.509899

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "767f1c2a00eb"
down_revision = "dba7f71618f5"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_message", sa.Column("token_count", sa.Integer(), nullable=False)
    )


def downgrade() -> None:
    op.drop_column("chat_message", "token_count")



================================================
FILE: backend/alembic/versions/76b60d407dfb_cc_pair_name_not_unique.py
================================================
"""CC-Pair Name not Unique

Revision ID: 76b60d407dfb
Revises: b156fa702355
Create Date: 2023-12-22 21:42:10.018804

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "76b60d407dfb"
down_revision = "b156fa702355"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.execute("DELETE FROM connector_credential_pair WHERE name IS NULL")
    op.drop_constraint(
        "connector_credential_pair__name__key",
        "connector_credential_pair",
        type_="unique",
    )
    op.alter_column(
        "connector_credential_pair", "name", existing_type=sa.String(), nullable=False
    )


def downgrade() -> None:
    op.create_unique_constraint(
        "connector_credential_pair__name__key", "connector_credential_pair", ["name"]
    )
    op.alter_column(
        "connector_credential_pair", "name", existing_type=sa.String(), nullable=True
    )



================================================
FILE: backend/alembic/versions/776b3bbe9092_remove_remaining_enums.py
================================================
"""Remove Remaining Enums

Revision ID: 776b3bbe9092
Revises: 4738e4b3bae1
Create Date: 2024-03-22 21:34:27.629444

"""

from alembic import op
import sqlalchemy as sa

from onyx.db.models import IndexModelStatus
from onyx.context.search.enums import RecencyBiasSetting
from onyx.context.search.enums import SearchType

# revision identifiers, used by Alembic.
revision = "776b3bbe9092"
down_revision = "4738e4b3bae1"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.alter_column(
        "persona",
        "search_type",
        type_=sa.String,
        existing_type=sa.Enum(SearchType, native_enum=False),
        existing_nullable=False,
    )
    op.alter_column(
        "persona",
        "recency_bias",
        type_=sa.String,
        existing_type=sa.Enum(RecencyBiasSetting, native_enum=False),
        existing_nullable=False,
    )

    # Because the indexmodelstatus enum does not have a mapping to a string type
    # we need this workaround instead of directly changing the type
    op.add_column("embedding_model", sa.Column("temp_status", sa.String))
    op.execute("UPDATE embedding_model SET temp_status = status::text")
    op.drop_column("embedding_model", "status")
    op.alter_column("embedding_model", "temp_status", new_column_name="status")

    op.execute("DROP TYPE IF EXISTS searchtype")
    op.execute("DROP TYPE IF EXISTS recencybiassetting")
    op.execute("DROP TYPE IF EXISTS indexmodelstatus")


def downgrade() -> None:
    op.alter_column(
        "persona",
        "search_type",
        type_=sa.Enum(SearchType, native_enum=False),
        existing_type=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "persona",
        "recency_bias",
        type_=sa.Enum(RecencyBiasSetting, native_enum=False),
        existing_type=sa.String(length=50),
        existing_nullable=False,
    )
    op.alter_column(
        "embedding_model",
        "status",
        type_=sa.Enum(IndexModelStatus, native_enum=False),
        existing_type=sa.String(length=50),
        existing_nullable=False,
    )



================================================
FILE: backend/alembic/versions/77d07dffae64_forcibly_remove_more_enum_types_from_.py
================================================
"""forcibly remove more enum types from postgres

Revision ID: 77d07dffae64
Revises: d61e513bef0a
Create Date: 2023-11-01 12:33:01.999617

"""

from alembic import op
from sqlalchemy import String


# revision identifiers, used by Alembic.
revision = "77d07dffae64"
down_revision = "d61e513bef0a"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # In a PR:
    # https://github.com/onyx-dot-app/onyx/pull/397/files#diff-f05fb341f6373790b91852579631b64ca7645797a190837156a282b67e5b19c2
    # we directly changed some previous migrations. This caused some users to have native enums
    # while others wouldn't. This has caused some issues when adding new fields to these enums.
    # This migration manually changes the enum types to ensure that nobody uses native enums.
    op.alter_column("query_event", "selected_search_flow", type_=String)
    op.alter_column("query_event", "feedback", type_=String)
    op.alter_column("document_retrieval_feedback", "feedback", type_=String)
    op.execute("DROP TYPE IF EXISTS searchtype")
    op.execute("DROP TYPE IF EXISTS qafeedbacktype")
    op.execute("DROP TYPE IF EXISTS searchfeedbacktype")


def downgrade() -> None:
    # We don't want Native Enums, do nothing
    pass



================================================
FILE: backend/alembic/versions/78dbe7e38469_task_tracking.py
================================================
"""Task Tracking

Revision ID: 78dbe7e38469
Revises: 7ccea01261f6
Create Date: 2023-10-15 23:40:50.593262

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "78dbe7e38469"
down_revision = "7ccea01261f6"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "task_queue_jobs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.String(), nullable=False),
        sa.Column("task_name", sa.String(), nullable=False),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING",
                "STARTED",
                "SUCCESS",
                "FAILURE",
                name="taskstatus",
                native_enum=False,
            ),
            nullable=False,
        ),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "register_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("task_queue_jobs")



================================================
FILE: backend/alembic/versions/795b20b85b4b_add_llm_group_permissions_control.py
================================================
"""add_llm_group_permissions_control

Revision ID: 795b20b85b4b
Revises: 05c07bf07c00
Create Date: 2024-07-19 11:54:35.701558

"""

from alembic import op
import sqlalchemy as sa


revision = "795b20b85b4b"
down_revision = "05c07bf07c00"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "llm_provider__user_group",
        sa.Column("llm_provider_id", sa.Integer(), nullable=False),
        sa.Column("user_group_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["llm_provider_id"],
            ["llm_provider.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_group_id"],
            ["user_group.id"],
        ),
        sa.PrimaryKeyConstraint("llm_provider_id", "user_group_id"),
    )
    op.add_column(
        "llm_provider",
        sa.Column("is_public", sa.Boolean(), nullable=False, server_default="true"),
    )


def downgrade() -> None:
    op.drop_table("llm_provider__user_group")
    op.drop_column("llm_provider", "is_public")



================================================
FILE: backend/alembic/versions/797089dfb4d2_persona_start_date.py
================================================
"""persona_start_date

Revision ID: 797089dfb4d2
Revises: 55546a7967ee
Create Date: 2024-09-11 14:51:49.785835

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "797089dfb4d2"
down_revision = "55546a7967ee"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "persona",
        sa.Column("search_start_date", sa.DateTime(timezone=True), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("persona", "search_start_date")



================================================
FILE: backend/alembic/versions/79acd316403a_add_api_key_table.py
================================================
"""Add api_key table

Revision ID: 79acd316403a
Revises: 904e5138fffb
Create Date: 2024-01-11 17:56:37.934381

"""

from alembic import op
import fastapi_users_db_sqlalchemy
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "79acd316403a"
down_revision = "904e5138fffb"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "api_key",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("hashed_api_key", sa.String(), nullable=False),
        sa.Column("api_key_display", sa.String(), nullable=False),
        sa.Column(
            "user_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=False,
        ),
        sa.Column(
            "owner_id",
            fastapi_users_db_sqlalchemy.generics.GUID(),
            nullable=True,
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("api_key_display"),
        sa.UniqueConstraint("hashed_api_key"),
    )


def downgrade() -> None:
    op.drop_table("api_key")



================================================
FILE: backend/alembic/versions/7a70b7664e37_add_model_configuration_table.py
================================================
"""Add model-configuration table

Revision ID: 7a70b7664e37
Revises: d961aca62eb3
Create Date: 2025-04-10 15:00:35.984669

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from onyx.llm.llm_provider_options import (
    fetch_model_names_for_provider_as_set,
    fetch_visible_model_names_for_provider_as_set,
)

# revision identifiers, used by Alembic.
revision = "7a70b7664e37"
down_revision = "d961aca62eb3"
branch_labels = None
depends_on = None


def _resolve(
    provider_name: str,
    model_names: list[str] | None,
    display_model_names: list[str] | None,
    default_model_name: str,
    fast_default_model_name: str | None,
) -> set[tuple[str, bool]]:
    models = set(model_names) if model_names else None
    display_models = set(display_model_names) if display_model_names else None

    # If both are defined, we need to make sure that `model_names` is a superset of `display_model_names`.
    if models and display_models:
        models = display_models.union(models)

    # If only `model_names` is defined, then:
    #   - If default-model-names are available for the `provider_name`, then set `display_model_names` to it
    #     and set `model_names` to the union of those default-model-names with itself.
    #   - If no default-model-names are available, then set `display_models` to `models`.
    #
    # This preserves the invariant that `display_models` is a subset of `models`.
    elif models and not display_models:
        visible_default_models = fetch_visible_model_names_for_provider_as_set(
            provider_name=provider_name
        )
        if visible_default_models:
            display_models = set(visible_default_models)
            models = display_models.union(models)
        else:
            display_models = set(models)

    # If only the `display_model_names` are defined, then set `models` to the union of `display_model_names`
    # and the default-model-names for that provider.
    #
    # This will also preserve the invariant that `display_models` is a subset of `models`.
    elif not models and display_models:
        default_models = fetch_model_names_for_provider_as_set(
            provider_name=provider_name
        )
        if default_models:
            models = display_models.union(default_models)
        else:
            models = set(display_models)

    # If neither are defined, then set `models` and `display_models` to the default-model-names for the given provider.
    #
    # This will also preserve the invariant that `display_models` is a subset of `models`.
    else:
        default_models = fetch_model_names_for_provider_as_set(
            provider_name=provider_name
        )
        visible_default_models = fetch_visible_model_names_for_provider_as_set(
            provider_name=provider_name
        )

        if default_models:
            if not visible_default_models:
                raise RuntimeError
                raise RuntimeError(
                    "If `default_models` is non-None, `visible_default_models` must be non-None too."
                )
            models = default_models
            display_models = visible_default_models

        # This is not a well-known llm-provider; we can't provide any model suggestions.
        # Therefore, we set to the empty set and continue
        else:
            models = set()
            display_models = set()

    # It is possible that `default_model_name` is not in `models` and is not in `display_models`.
    # It is also possible that `fast_default_model_name` is not in `models` and is not in `display_models`.
    models.add(default_model_name)
    if fast_default_model_name:
        models.add(fast_default_model_name)
    display_models.add(default_model_name)
    if fast_default_model_name:
        display_models.add(fast_default_model_name)

    return set([(model, model in display_models) for model in models])


def upgrade() -> None:
    op.create_table(
        "model_configuration",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("llm_provider_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("is_visible", sa.Boolean(), nullable=False),
        sa.Column("max_input_tokens", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["llm_provider_id"], ["llm_provider.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("llm_provider_id", "name"),
    )

    # Create temporary sqlalchemy references to tables for data migration
    llm_provider_table = sa.sql.table(
        "llm_provider",
        sa.column("id", sa.Integer),
        sa.column("provider", sa.Integer),
        sa.column("model_names", postgresql.ARRAY(sa.String)),
        sa.column("display_model_names", postgresql.ARRAY(sa.String)),
        sa.column("default_model_name", sa.String),
        sa.column("fast_default_model_name", sa.String),
    )
    model_configuration_table = sa.sql.table(
        "model_configuration",
        sa.column("id", sa.Integer),
        sa.column("llm_provider_id", sa.Integer),
        sa.column("name", sa.String),
        sa.column("is_visible", sa.Boolean),
        sa.column("max_input_tokens", sa.Integer),
    )
    connection = op.get_bind()
    llm_providers = connection.execute(
        sa.select(
            llm_provider_table.c.id,
            llm_provider_table.c.provider,
            llm_provider_table.c.model_names,
            llm_provider_table.c.display_model_names,
            llm_provider_table.c.default_model_name,
            llm_provider_table.c.fast_default_model_name,
        )
    ).fetchall()

    for llm_provider in llm_providers:
        provider_id = llm_provider[0]
        provider_name = llm_provider[1]
        model_names = llm_provider[2]
        display_model_names = llm_provider[3]
        default_model_name = llm_provider[4]
        fast_default_model_name = llm_provider[5]

        model_configurations = _resolve(
            provider_name=provider_name,
            model_names=model_names,
            display_model_names=display_model_names,
            default_model_name=default_model_name,
            fast_default_model_name=fast_default_model_name,
        )

        for model_name, is_visible in model_configurations:
            connection.execute(
                model_configuration_table.insert().values(
                    llm_provider_id=provider_id,
                    name=model_name,
                    is_visible=is_visible,
                    max_input_tokens=None,
                )
            )

    op.drop_column("llm_provider", "model_names")
    op.drop_column("llm_provider", "display_model_names")


def downgrade() -> None:
    llm_provider = sa.table(
        "llm_provider",
        sa.column("id", sa.Integer),
        sa.column("model_names", postgresql.ARRAY(sa.String)),
        sa.column("display_model_names", postgresql.ARRAY(sa.String)),
    )

    model_configuration = sa.table(
        "model_configuration",
        sa.column("id", sa.Integer),
        sa.column("llm_provider_id", sa.Integer),
        sa.column("name", sa.String),
        sa.column("is_visible", sa.Boolean),
        sa.column("max_input_tokens", sa.Integer),
    )
    op.add_column(
        "llm_provider",
        sa.Column(
            "model_names",
            postgresql.ARRAY(sa.VARCHAR()),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "llm_provider",
        sa.Column(
            "display_model_names",
            postgresql.ARRAY(sa.VARCHAR()),
            autoincrement=False,
            nullable=True,
        ),
    )

    connection = op.get_bind()
    provider_ids = connection.execute(sa.select(llm_provider.c.id)).fetchall()

    for (provider_id,) in provider_ids:
        # Get all models for this provider
        models = connection.execute(
            sa.select(
                model_configuration.c.name, model_configuration.c.is_visible
            ).where(model_configuration.c.llm_provider_id == provider_id)
        ).fetchall()

        all_models = [model[0] for model in models]
        visible_models = [model[0] for model in models if model[1]]

        # Update provider with arrays
        op.execute(
            llm_provider.update()
            .where(llm_provider.c.id == provider_id)
            .values(model_names=all_models, display_model_names=visible_models)
        )

    op.drop_table("model_configuration")



================================================
FILE: backend/alembic/versions/7aea705850d5_added_slack_auto_filter.py
================================================
"""added slack_auto_filter

Revision ID: 7aea705850d5
Revises: 4505fd7302e1
Create Date: 2024-07-10 11:01:23.581015

"""

from alembic import op
import sqlalchemy as sa

revision = "7aea705850d5"
down_revision = "4505fd7302e1"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "slack_bot_config",
        sa.Column("enable_auto_filters", sa.Boolean(), nullable=True),
    )
    op.execute(
        "UPDATE slack_bot_config SET enable_auto_filters = FALSE WHERE enable_auto_filters IS NULL"
    )
    op.alter_column(
        "slack_bot_config",
        "enable_auto_filters",
        existing_type=sa.Boolean(),
        nullable=False,
        server_default=sa.false(),
    )


def downgrade() -> None:
    op.drop_column("slack_bot_config", "enable_auto_filters")



================================================
FILE: backend/alembic/versions/7b9b952abdf6_update_entities.py
================================================
"""update-entities

Revision ID: 7b9b952abdf6
Revises: 36e9220ab794
Create Date: 2025-06-23 20:24:08.139201

"""

import json

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "7b9b952abdf6"
down_revision = "36e9220ab794"
branch_labels = None
depends_on = None


def upgrade() -> None:
    conn = op.get_bind()

    # new entity type metadata_attribute_conversion
    new_entity_type_conversion = {
        "LINEAR": {
            "team": {"name": "team", "keep": True, "implication_property": None},
            "state": {"name": "state", "keep": True, "implication_property": None},
            "priority": {
                "name": "priority",
                "keep": True,
                "implication_property": None,
            },
            "estimate": {
                "name": "estimate",
                "keep": True,
                "implication_property": None,
            },
            "created_at": {
                "name": "created_at",
                "keep": True,
                "implication_property": None,
            },
            "started_at": {
                "name": "started_at",
                "keep": True,
                "implication_property": None,
            },
            "completed_at": {
                "name": "completed_at",
                "keep": True,
                "implication_property": None,
            },
            "due_date": {
                "name": "due_date",
                "keep": True,
                "implication_property": None,
            },
            "creator": {
                "name": "creator",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_creator_of",
                },
            },
            "assignee": {
                "name": "assignee",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_assignee_of",
                },
            },
        },
        "JIRA": {
            "issuetype": {
                "name": "subtype",
                "keep": True,
                "implication_property": None,
            },
            "status": {"name": "status", "keep": True, "implication_property": None},
            "priority": {
                "name": "priority",
                "keep": True,
                "implication_property": None,
            },
            "project_name": {
                "name": "project",
                "keep": True,
                "implication_property": None,
            },
            "created": {
                "name": "created_at",
                "keep": True,
                "implication_property": None,
            },
            "updated": {
                "name": "updated_at",
                "keep": True,
                "implication_property": None,
            },
            "resolution_date": {
                "name": "completed_at",
                "keep": True,
                "implication_property": None,
            },
            "duedate": {"name": "due_date", "keep": True, "implication_property": None},
            "reporter_email": {
                "name": "creator",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_creator_of",
                },
            },
            "assignee_email": {
                "name": "assignee",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_assignee_of",
                },
            },
            "key": {"name": "key", "keep": True, "implication_property": None},
            "parent": {"name": "parent", "keep": True, "implication_property": None},
        },
        "GITHUB_PR": {
            "repo": {"name": "repository", "keep": True, "implication_property": None},
            "state": {"name": "state", "keep": True, "implication_property": None},
            "num_commits": {
                "name": "num_commits",
                "keep": True,
                "implication_property": None,
            },
            "num_files_changed": {
                "name": "num_files_changed",
                "keep": True,
                "implication_property": None,
            },
            "labels": {"name": "labels", "keep": True, "implication_property": None},
            "merged": {"name": "merged", "keep": True, "implication_property": None},
            "merged_at": {
                "name": "merged_at",
                "keep": True,
                "implication_property": None,
            },
            "closed_at": {
                "name": "closed_at",
                "keep": True,
                "implication_property": None,
            },
            "created_at": {
                "name": "created_at",
                "keep": True,
                "implication_property": None,
            },
            "updated_at": {
                "name": "updated_at",
                "keep": True,
                "implication_property": None,
            },
            "user": {
                "name": "creator",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_creator_of",
                },
            },
            "assignees": {
                "name": "assignees",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_assignee_of",
                },
            },
        },
        "GITHUB_ISSUE": {
            "repo": {"name": "repository", "keep": True, "implication_property": None},
            "state": {"name": "state", "keep": True, "implication_property": None},
            "labels": {"name": "labels", "keep": True, "implication_property": None},
            "closed_at": {
                "name": "closed_at",
                "keep": True,
                "implication_property": None,
            },
            "created_at": {
                "name": "created_at",
                "keep": True,
                "implication_property": None,
            },
            "updated_at": {
                "name": "updated_at",
                "keep": True,
                "implication_property": None,
            },
            "user": {
                "name": "creator",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_creator_of",
                },
            },
            "assignees": {
                "name": "assignees",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "from_email",
                    "implied_relationship_name": "is_assignee_of",
                },
            },
        },
        "FIREFLIES": {},
        "ACCOUNT": {},
        "OPPORTUNITY": {
            "name": {"name": "name", "keep": True, "implication_property": None},
            "stage_name": {"name": "stage", "keep": True, "implication_property": None},
            "type": {"name": "type", "keep": True, "implication_property": None},
            "amount": {"name": "amount", "keep": True, "implication_property": None},
            "fiscal_year": {
                "name": "fiscal_year",
                "keep": True,
                "implication_property": None,
            },
            "fiscal_quarter": {
                "name": "fiscal_quarter",
                "keep": True,
                "implication_property": None,
            },
            "is_closed": {
                "name": "is_closed",
                "keep": True,
                "implication_property": None,
            },
            "close_date": {
                "name": "close_date",
                "keep": True,
                "implication_property": None,
            },
            "probability": {
                "name": "close_probability",
                "keep": True,
                "implication_property": None,
            },
            "created_date": {
                "name": "created_at",
                "keep": True,
                "implication_property": None,
            },
            "last_modified_date": {
                "name": "updated_at",
                "keep": True,
                "implication_property": None,
            },
            "account": {
                "name": "account",
                "keep": False,
                "implication_property": {
                    "implied_entity_type": "ACCOUNT",
                    "implied_relationship_name": "is_account_of",
                },
            },
        },
        "VENDOR": {},
        "EMPLOYEE": {},
    }

    current_entity_types = conn.execute(
        sa.text("SELECT id_name, attributes from kg_entity_type")
    ).all()
    for entity_type, attributes in current_entity_types:
        # delete removed entity types
        if entity_type not in new_entity_type_conversion:
            op.execute(
                sa.text(f"DELETE FROM kg_entity_type WHERE id_name = '{entity_type}'")
            )
            continue

        # update entity type attributes
        if "metadata_attributes" in attributes:
            del attributes["metadata_attributes"]
        attributes["metadata_attribute_conversion"] = new_entity_type_conversion[
            entity_type
        ]
        attributes_str = json.dumps(attributes).replace("'", "''")
        op.execute(
            sa.text(
                f"UPDATE kg_entity_type SET attributes = '{attributes_str}'"
                f"WHERE id_name = '{entity_type}'"
            ),
        )


def downgrade() -> None:
    conn = op.get_bind()

    current_entity_types = conn.execute(
        sa.text("SELECT id_name, attributes from kg_entity_type")
    ).all()
    for entity_type, attributes in current_entity_types:
        conversion = {}
        if "metadata_attribute_conversion" in attributes:
            conversion = attributes.pop("metadata_attribute_conversion")
        attributes["metadata_attributes"] = {
            attr: prop["name"] for attr, prop in conversion.items() if prop["keep"]
        }

        attributes_str = json.dumps(attributes).replace("'", "''")
        op.execute(
            sa.text(
                f"UPDATE kg_entity_type SET attributes = '{attributes_str}'"
                f"WHERE id_name = '{entity_type}'"
            ),
        )



================================================
FILE: backend/alembic/versions/7ccea01261f6_store_chat_retrieval_docs.py
================================================
"""Store Chat Retrieval Docs

Revision ID: 7ccea01261f6
Revises: a570b80a5f20
Create Date: 2023-10-15 10:39:23.317453

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "7ccea01261f6"
down_revision = "a570b80a5f20"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_message",
        sa.Column(
            "reference_docs",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )


def downgrade() -> None:
    op.drop_column("chat_message", "reference_docs")



================================================
FILE: backend/alembic/versions/7da0ae5ad583_add_description_to_persona.py
================================================
"""Add description to persona

Revision ID: 7da0ae5ad583
Revises: e86866a9c78a
Create Date: 2023-11-27 00:16:19.959414

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7da0ae5ad583"
down_revision = "e86866a9c78a"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column("persona", sa.Column("description", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("persona", "description")



================================================
FILE: backend/alembic/versions/7da543f5672f_add_slackbotconfig_table.py
================================================
"""Add SlackBotConfig table

Revision ID: 7da543f5672f
Revises: febe9eaa0644
Create Date: 2023-09-24 16:34:17.526128

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "7da543f5672f"
down_revision = "febe9eaa0644"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_table(
        "slack_bot_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("persona_id", sa.Integer(), nullable=True),
        sa.Column(
            "channel_config",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["persona_id"],
            ["persona.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("slack_bot_config")



================================================
FILE: backend/alembic/versions/7f726bad5367_slack_followup.py
================================================
"""Slack Followup

Revision ID: 7f726bad5367
Revises: 79acd316403a
Create Date: 2024-01-15 00:19:55.991224

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7f726bad5367"
down_revision = "79acd316403a"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.add_column(
        "chat_feedback",
        sa.Column("required_followup", sa.Boolean(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("chat_feedback", "required_followup")



================================================
FILE: backend/alembic/versions/7f99be1cb9f5_add_index_for_getting_documents_just_by_.py
================================================
"""Add index for getting documents just by connector id / credential id

Revision ID: 7f99be1cb9f5
Revises: 78dbe7e38469
Create Date: 2023-10-15 22:48:15.487762

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "7f99be1cb9f5"
down_revision = "78dbe7e38469"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    op.create_index(
        op.f(
            "ix_document_by_connector_credential_pair_pkey__connector_id__credential_id"
        ),
        "document_by_connector_credential_pair",
        ["connector_id", "credential_id"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(
        op.f(
            "ix_document_by_connector_credential_pair_pkey__connector_id__credential_id"
        ),
        table_name="document_by_connector_credential_pair",
    )



================================================
FILE: backend/alembic/versions/800f48024ae9_add_id_to_connectorcredentialpair.py
================================================
"""Add ID to ConnectorCredentialPair

Revision ID: 800f48024ae9
Revises: 767f1c2a00eb
Create Date: 2023-09-19 16:13:42.299715

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.schema import Sequence, CreateSequence

# revision identifiers, used by Alembic.
revision = "800f48024ae9"
down_revision = "767f1c2a00eb"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    sequence = Sequence("connector_credential_pair_id_seq")
    op.execute(CreateSequence(sequence))  # type: ignore
    op.add_column(
        "connector_credential_pair",
        sa.Column(
            "id", sa.Integer(), nullable=True, server_default=sequence.next_value()
        ),
    )
    op.add_column(
        "connector_credential_pair",
        sa.Column("name", sa.String(), nullable=True),
    )

    # fill in IDs for existing rows
    op.execute(
        "UPDATE connector_credential_pair SET id = nextval('connector_credential_pair_id_seq') WHERE id IS NULL"
    )
    op.alter_column("connector_credential_pair", "id", nullable=False)

    op.create_unique_constraint(
        "connector_credential_pair__name__key", "connector_credential_pair", ["name"]
    )
    op.create_unique_constraint(
        "connector_credential_pair__id__key", "connector_credential_pair", ["id"]
    )


def downgrade() -> None:
    op.drop_constraint(
        "connector_credential_pair__name__key",
        "connector_credential_pair",
        type_="unique",
    )
    o