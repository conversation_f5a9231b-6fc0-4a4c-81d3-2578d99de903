#!/usr/bin/env node

/**
 * RAG Accuracy and Hallucination Testing
 * 
 * This script tests if your RAG pipeline is actually using the fetched content
 * or if it's hallucinating responses from the LLM's training data.
 */

const https = require('https')
const http = require('http')

const BASE_URL = process.env.EVAL_BASE_URL || 'http://localhost:3000'

async function makeRequest(path, method = 'GET', body = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL)
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    }

    const client = url.protocol === 'https:' ? https : http
    
    const req = client.request(url, options, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        try {
          const result = JSON.parse(data)
          resolve(result)
        } catch (e) {
          resolve({ success: false, error: 'Invalid JSON response' })
        }
      })
    })

    req.on('error', reject)
    
    if (body) {
      req.write(JSON.stringify(body))
    }
    
    req.end()
  })
}

// Test questions designed to catch hallucination
const hallucinationTests = [
  {
    id: 'specific-fact-1',
    question: 'What is the exact interest rate range for Aven HELOC card?',
    expectedAnswer: '7.99% - 15.49%',
    shouldCite: true,
    description: 'Test if it knows specific Aven rates vs generic credit card info'
  },
  {
    id: 'specific-fact-2', 
    question: 'What is the maximum credit limit for Aven?',
    expectedAnswer: '$250,000',
    shouldCite: true,
    description: 'Test if it knows Aven-specific limits'
  },
  {
    id: 'specific-fact-3',
    question: 'Which bank partners with Aven for their HELOC card?',
    expectedAnswer: 'Coastal Community Bank',
    shouldCite: true,
    description: 'Test if it knows Aven-specific partnerships'
  },
  {
    id: 'fake-question-1',
    question: 'What is Aven\'s cryptocurrency investment program?',
    expectedAnswer: null,
    shouldCite: false,
    description: 'Test if it makes up programs that don\'t exist'
  },
  {
    id: 'fake-question-2',
    question: 'What are Aven\'s student loan refinancing rates?',
    expectedAnswer: null,
    shouldCite: false,
    description: 'Test if it makes up products Aven doesn\'t offer'
  },
  {
    id: 'autopay-discount',
    question: 'What discount does Aven offer for autopay?',
    expectedAnswer: '0.25%',
    shouldCite: true,
    description: 'Test specific Aven policy details'
  },
  {
    id: 'approval-time',
    question: 'How fast can you get approved for Aven card?',
    expectedAnswer: '5 minutes',
    shouldCite: true,
    description: 'Test Aven-specific process timing'
  },
  {
    id: 'cashback-rate',
    question: 'What cashback rate does Aven offer on all purchases?',
    expectedAnswer: '2%',
    shouldCite: true,
    description: 'Test Aven-specific reward rates'
  },
  {
    id: 'travel-cashback',
    question: 'What cashback rate does Aven offer on travel through their portal?',
    expectedAnswer: '7%',
    shouldCite: true,
    description: 'Test specific Aven travel rewards'
  },
  {
    id: 'annual-fee',
    question: 'What is the annual fee for Aven HELOC card?',
    expectedAnswer: 'no annual fee',
    shouldCite: true,
    description: 'Test Aven fee structure'
  }
]

async function testRAGAccuracy() {
  console.log('🔍 TESTING RAG ACCURACY & HALLUCINATION DETECTION')
  console.log('=' .repeat(60))
  console.log('This will test if your RAG pipeline uses actual Aven data or hallucinates.\n')

  const results = []
  let accurateResponses = 0
  let hallucinatedResponses = 0
  let properCitations = 0

  for (let i = 0; i < hallucinationTests.length; i++) {
    const test = hallucinationTests[i]
    console.log(`\n[${i + 1}/${hallucinationTests.length}] Testing: ${test.description}`)
    console.log(`Question: "${test.question}"`)

    try {
      const response = await makeRequest('/api/chat', 'POST', {
        message: test.question
      })

      if (!response.success) {
        console.log('❌ API Error:', response.error)
        results.push({
          ...test,
          status: 'error',
          response: null,
          analysis: 'API call failed'
        })
        continue
      }

      const answer = response.answer || ''
      const sources = response.sources || []

      console.log(`Response: "${answer.substring(0, 200)}${answer.length > 200 ? '...' : ''}"`)
      console.log(`Sources: ${sources.length} found`)

      // Analyze the response
      const analysis = analyzeResponse(test, answer, sources)
      results.push({
        ...test,
        status: analysis.status,
        response: answer,
        sources: sources,
        analysis: analysis.reasoning
      })

      // Update counters
      if (analysis.status === 'accurate') {
        accurateResponses++
      } else if (analysis.status === 'hallucinated') {
        hallucinatedResponses++
      }

      if (analysis.hasCitations && test.shouldCite) {
        properCitations++
      }

      console.log(`Status: ${getStatusIcon(analysis.status)} ${analysis.status.toUpperCase()}`)
      console.log(`Analysis: ${analysis.reasoning}`)

    } catch (error) {
      console.log('❌ Error:', error.message)
      results.push({
        ...test,
        status: 'error',
        response: null,
        analysis: `Error: ${error.message}`
      })
    }

    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  // Generate report
  console.log('\n' + '=' .repeat(60))
  console.log('📊 RAG ACCURACY REPORT')
  console.log('=' .repeat(60))

  const shouldCiteCount = hallucinationTests.filter(t => t.shouldCite).length
  const shouldNotCiteCount = hallucinationTests.filter(t => !t.shouldCite).length

  console.log(`\n📈 Overall Statistics:`)
  console.log(`Total Tests: ${hallucinationTests.length}`)
  console.log(`Accurate Responses: ${accurateResponses}/${hallucinationTests.length} (${((accurateResponses/hallucinationTests.length) * 100).toFixed(1)}%)`)
  console.log(`Hallucinated Responses: ${hallucinatedResponses}/${hallucinationTests.length} (${((hallucinatedResponses/hallucinationTests.length) * 100).toFixed(1)}%)`)
  console.log(`Proper Citations: ${properCitations}/${shouldCiteCount} (${shouldCiteCount > 0 ? ((properCitations/shouldCiteCount) * 100).toFixed(1) : 0}%)`)

  // Detailed results
  console.log(`\n📋 Detailed Results:`)
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.description}`)
    console.log(`   Status: ${getStatusIcon(result.status)} ${result.status}`)
    console.log(`   Analysis: ${result.analysis}`)
    if (result.sources && result.sources.length > 0) {
      console.log(`   Sources: ${result.sources.length} citations`)
    }
  })

  // Recommendations
  console.log(`\n💡 RECOMMENDATIONS:`)
  
  const accuracyRate = accurateResponses / hallucinationTests.length
  const hallucinationRate = hallucinatedResponses / hallucinationTests.length
  const citationRate = shouldCiteCount > 0 ? properCitations / shouldCiteCount : 0

  if (accuracyRate < 0.7) {
    console.log(`🚨 CRITICAL: Low accuracy rate (${(accuracyRate * 100).toFixed(1)}%)`)
    console.log(`   • Check if Aven data was properly scraped and indexed`)
    console.log(`   • Verify Pinecone contains relevant documents`)
    console.log(`   • Run: node diagnose-rag.js`)
  }

  if (hallucinationRate > 0.2) {
    console.log(`⚠️  WARNING: High hallucination rate (${(hallucinationRate * 100).toFixed(1)}%)`)
    console.log(`   • LLM may be using training data instead of RAG context`)
    console.log(`   • Check prompt engineering to emphasize using only provided context`)
    console.log(`   • Consider stricter guardrails for unknown information`)
  }

  if (citationRate < 0.8) {
    console.log(`📚 IMPROVE: Low citation rate (${(citationRate * 100).toFixed(1)}%)`)
    console.log(`   • Improve source attribution in responses`)
    console.log(`   • Check if relevant documents are being retrieved`)
    console.log(`   • Verify citation formatting in prompts`)
  }

  if (accuracyRate >= 0.8 && hallucinationRate <= 0.2 && citationRate >= 0.8) {
    console.log(`✅ EXCELLENT: Your RAG pipeline is working well!`)
    console.log(`   • High accuracy with good source attribution`)
    console.log(`   • Low hallucination rate`)
    console.log(`   • Ready for production use`)
  }

  console.log(`\n🔧 Next Steps:`)
  console.log(`1. Fix any critical issues identified above`)
  console.log(`2. Run full evaluation: node run-eval.js --rag`)
  console.log(`3. Test with more questions specific to your use case`)
  console.log(`4. Monitor hallucination in production with ongoing evals`)
}

function analyzeResponse(test, answer, sources) {
  const lowerAnswer = answer.toLowerCase()
  const hasExpected = test.expectedAnswer && lowerAnswer.includes(test.expectedAnswer.toLowerCase())
  const hasCitations = sources && sources.length > 0
  const isRefusal = lowerAnswer.includes("don't know") || 
                   lowerAnswer.includes("not sure") || 
                   lowerAnswer.includes("cannot find") ||
                   lowerAnswer.includes("don't have information")

  // For questions about things Aven doesn't offer
  if (test.expectedAnswer === null) {
    if (isRefusal || lowerAnswer.includes("doesn't offer") || lowerAnswer.includes("don't offer")) {
      return {
        status: 'accurate',
        reasoning: 'Correctly identified that Aven doesn\'t offer this service',
        hasCitations
      }
    } else {
      return {
        status: 'hallucinated',
        reasoning: 'Made up information about services Aven doesn\'t offer',
        hasCitations
      }
    }
  }

  // For questions about real Aven features
  if (hasExpected && hasCitations) {
    return {
      status: 'accurate',
      reasoning: 'Provided correct information with proper citations',
      hasCitations
    }
  } else if (hasExpected && !hasCitations) {
    return {
      status: 'potentially_hallucinated',
      reasoning: 'Correct answer but no citations - may be using training data',
      hasCitations
    }
  } else if (!hasExpected && hasCitations) {
    return {
      status: 'inaccurate',
      reasoning: 'Has citations but incorrect information',
      hasCitations
    }
  } else {
    return {
      status: 'hallucinated',
      reasoning: 'Incorrect information without citations',
      hasCitations
    }
  }
}

function getStatusIcon(status) {
  const icons = {
    'accurate': '✅',
    'potentially_hallucinated': '⚠️',
    'inaccurate': '❌',
    'hallucinated': '🚨',
    'error': '💥'
  }
  return icons[status] || '❓'
}

// Run the test
console.log('Starting RAG Accuracy & Hallucination Test...\n')
testRAGAccuracy().catch(error => {
  console.error('❌ Test failed:', error.message)
  process.exit(1)
})