"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@sevinf";
exports.ids = ["vendor-chunks/@sevinf"];
exports.modules = {

/***/ "(rsc)/./node_modules/@sevinf/maybe/dist/esm/maybe.js":
/*!******************************************************!*\
  !*** ./node_modules/@sevinf/maybe/dist/esm/maybe.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMaybe: () => (/* binding */ isMaybe),\n/* harmony export */   maybe: () => (/* binding */ maybe),\n/* harmony export */   none: () => (/* binding */ none),\n/* harmony export */   some: () => (/* binding */ some)\n/* harmony export */ });\nvar none = {\n    isNone: function () {\n        return true;\n    },\n    orElse: function (fallback) {\n        return fallback;\n    },\n    orCall: function (getFallback) {\n        return getFallback();\n    },\n    orNull: function () {\n        return null;\n    },\n    orThrow: function (message) {\n        if (message === void 0) { message = 'Unexpected null value'; }\n        throw new TypeError(message);\n    },\n    map: function () {\n        return none;\n    },\n    get: function () {\n        return none;\n    }\n};\nvar Some = /** @class */ (function () {\n    function Some(value) {\n        this.value = value;\n    }\n    Some.prototype.isNone = function () {\n        return false;\n    };\n    Some.prototype.orElse = function () {\n        return this.value;\n    };\n    Some.prototype.orCall = function () {\n        return this.value;\n    };\n    Some.prototype.orNull = function () {\n        return this.value;\n    };\n    Some.prototype.orThrow = function () {\n        return this.value;\n    };\n    Some.prototype.map = function (f) {\n        return maybe(f(this.value));\n    };\n    Some.prototype.get = function (key) {\n        return this.map(function (obj) { return obj[key]; });\n    };\n    return Some;\n}());\nfunction isMaybe(value) {\n    return value === none || value instanceof Some;\n}\nfunction maybe(value) {\n    if (isMaybe(value)) {\n        return value;\n    }\n    if (value == null) {\n        return none;\n    }\n    return some(value);\n}\nfunction some(value) {\n    if (value == null) {\n        throw new TypeError('some() does not accept null or undefined');\n    }\n    return new Some(value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sevinf/maybe/dist/esm/maybe.js\n");

/***/ })

};
;