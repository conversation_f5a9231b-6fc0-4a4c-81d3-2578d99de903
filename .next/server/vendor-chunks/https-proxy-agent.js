"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/https-proxy-agent";
exports.ids = ["vendor-chunks/https-proxy-agent"];
exports.modules = {

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HttpsProxyAgent = void 0;\nconst net = __importStar(__webpack_require__(/*! net */ \"net\"));\nconst tls = __importStar(__webpack_require__(/*! tls */ \"tls\"));\nconst assert_1 = __importDefault(__webpack_require__(/*! assert */ \"assert\"));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst agent_base_1 = __webpack_require__(/*! agent-base */ \"(rsc)/./node_modules/agent-base/dist/index.js\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst parse_proxy_response_1 = __webpack_require__(/*! ./parse-proxy-response */ \"(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\");\nconst debug = (0, debug_1.default)('https-proxy-agent');\nconst setServernameFromNonIpHost = (options) => {\n    if (options.servername === undefined &&\n        options.host &&\n        !net.isIP(options.host)) {\n        return {\n            ...options,\n            servername: options.host,\n        };\n    }\n    return options;\n};\n/**\n * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to\n * the specified \"HTTP(s) proxy server\" in order to proxy HTTPS requests.\n *\n * Outgoing HTTP requests are first tunneled through the proxy server using the\n * `CONNECT` HTTP request method to establish a connection to the proxy server,\n * and then the proxy server connects to the destination target and issues the\n * HTTP request from the proxy server.\n *\n * `https:` requests have their socket connection upgraded to TLS once\n * the connection to the proxy server has been established.\n */\nclass HttpsProxyAgent extends agent_base_1.Agent {\n    constructor(proxy, opts) {\n        super(opts);\n        this.options = { path: undefined };\n        this.proxy = typeof proxy === 'string' ? new url_1.URL(proxy) : proxy;\n        this.proxyHeaders = opts?.headers ?? {};\n        debug('Creating new HttpsProxyAgent instance: %o', this.proxy.href);\n        // Trim off the brackets from IPv6 addresses\n        const host = (this.proxy.hostname || this.proxy.host).replace(/^\\[|\\]$/g, '');\n        const port = this.proxy.port\n            ? parseInt(this.proxy.port, 10)\n            : this.proxy.protocol === 'https:'\n                ? 443\n                : 80;\n        this.connectOpts = {\n            // Attempt to negotiate http/1.1 for proxy servers that support http/2\n            ALPNProtocols: ['http/1.1'],\n            ...(opts ? omit(opts, 'headers') : null),\n            host,\n            port,\n        };\n    }\n    /**\n     * Called when the node-core HTTP client library is creating a\n     * new HTTP request.\n     */\n    async connect(req, opts) {\n        const { proxy } = this;\n        if (!opts.host) {\n            throw new TypeError('No \"host\" provided');\n        }\n        // Create a socket connection to the proxy server.\n        let socket;\n        if (proxy.protocol === 'https:') {\n            debug('Creating `tls.Socket`: %o', this.connectOpts);\n            socket = tls.connect(setServernameFromNonIpHost(this.connectOpts));\n        }\n        else {\n            debug('Creating `net.Socket`: %o', this.connectOpts);\n            socket = net.connect(this.connectOpts);\n        }\n        const headers = typeof this.proxyHeaders === 'function'\n            ? this.proxyHeaders()\n            : { ...this.proxyHeaders };\n        const host = net.isIPv6(opts.host) ? `[${opts.host}]` : opts.host;\n        let payload = `CONNECT ${host}:${opts.port} HTTP/1.1\\r\\n`;\n        // Inject the `Proxy-Authorization` header if necessary.\n        if (proxy.username || proxy.password) {\n            const auth = `${decodeURIComponent(proxy.username)}:${decodeURIComponent(proxy.password)}`;\n            headers['Proxy-Authorization'] = `Basic ${Buffer.from(auth).toString('base64')}`;\n        }\n        headers.Host = `${host}:${opts.port}`;\n        if (!headers['Proxy-Connection']) {\n            headers['Proxy-Connection'] = this.keepAlive\n                ? 'Keep-Alive'\n                : 'close';\n        }\n        for (const name of Object.keys(headers)) {\n            payload += `${name}: ${headers[name]}\\r\\n`;\n        }\n        const proxyResponsePromise = (0, parse_proxy_response_1.parseProxyResponse)(socket);\n        socket.write(`${payload}\\r\\n`);\n        const { connect, buffered } = await proxyResponsePromise;\n        req.emit('proxyConnect', connect);\n        this.emit('proxyConnect', connect, req);\n        if (connect.statusCode === 200) {\n            req.once('socket', resume);\n            if (opts.secureEndpoint) {\n                // The proxy is connecting to a TLS server, so upgrade\n                // this socket connection to a TLS connection.\n                debug('Upgrading socket connection to TLS');\n                return tls.connect({\n                    ...omit(setServernameFromNonIpHost(opts), 'host', 'path', 'port'),\n                    socket,\n                });\n            }\n            return socket;\n        }\n        // Some other status code that's not 200... need to re-play the HTTP\n        // header \"data\" events onto the socket once the HTTP machinery is\n        // attached so that the node core `http` can parse and handle the\n        // error status code.\n        // Close the original socket, and a new \"fake\" socket is returned\n        // instead, so that the proxy doesn't get the HTTP request\n        // written to it (which may contain `Authorization` headers or other\n        // sensitive data).\n        //\n        // See: https://hackerone.com/reports/541502\n        socket.destroy();\n        const fakeSocket = new net.Socket({ writable: false });\n        fakeSocket.readable = true;\n        // Need to wait for the \"socket\" event to re-play the \"data\" events.\n        req.once('socket', (s) => {\n            debug('Replaying proxy buffer for failed request');\n            (0, assert_1.default)(s.listenerCount('data') > 0);\n            // Replay the \"buffered\" Buffer onto the fake `socket`, since at\n            // this point the HTTP module machinery has been hooked up for\n            // the user.\n            s.push(buffered);\n            s.push(null);\n        });\n        return fakeSocket;\n    }\n}\nHttpsProxyAgent.protocols = ['http', 'https'];\nexports.HttpsProxyAgent = HttpsProxyAgent;\nfunction resume(socket) {\n    socket.resume();\n}\nfunction omit(obj, ...keys) {\n    const ret = {};\n    let key;\n    for (key in obj) {\n        if (!keys.includes(key)) {\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js":
/*!*********************************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/parse-proxy-response.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseProxyResponse = void 0;\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst debug = (0, debug_1.default)('https-proxy-agent:parse-proxy-response');\nfunction parseProxyResponse(socket) {\n    return new Promise((resolve, reject) => {\n        // we need to buffer any HTTP traffic that happens with the proxy before we get\n        // the CONNECT response, so that if the response is anything other than an \"200\"\n        // response code, then we can re-play the \"data\" events on the socket once the\n        // HTTP parser is hooked up...\n        let buffersLength = 0;\n        const buffers = [];\n        function read() {\n            const b = socket.read();\n            if (b)\n                ondata(b);\n            else\n                socket.once('readable', read);\n        }\n        function cleanup() {\n            socket.removeListener('end', onend);\n            socket.removeListener('error', onerror);\n            socket.removeListener('readable', read);\n        }\n        function onend() {\n            cleanup();\n            debug('onend');\n            reject(new Error('Proxy connection ended before receiving CONNECT response'));\n        }\n        function onerror(err) {\n            cleanup();\n            debug('onerror %o', err);\n            reject(err);\n        }\n        function ondata(b) {\n            buffers.push(b);\n            buffersLength += b.length;\n            const buffered = Buffer.concat(buffers, buffersLength);\n            const endOfHeaders = buffered.indexOf('\\r\\n\\r\\n');\n            if (endOfHeaders === -1) {\n                // keep buffering\n                debug('have not received end of HTTP headers yet...');\n                read();\n                return;\n            }\n            const headerParts = buffered\n                .slice(0, endOfHeaders)\n                .toString('ascii')\n                .split('\\r\\n');\n            const firstLine = headerParts.shift();\n            if (!firstLine) {\n                socket.destroy();\n                return reject(new Error('No header received from proxy CONNECT response'));\n            }\n            const firstLineParts = firstLine.split(' ');\n            const statusCode = +firstLineParts[1];\n            const statusText = firstLineParts.slice(2).join(' ');\n            const headers = {};\n            for (const header of headerParts) {\n                if (!header)\n                    continue;\n                const firstColon = header.indexOf(':');\n                if (firstColon === -1) {\n                    socket.destroy();\n                    return reject(new Error(`Invalid header from proxy CONNECT response: \"${header}\"`));\n                }\n                const key = header.slice(0, firstColon).toLowerCase();\n                const value = header.slice(firstColon + 1).trimStart();\n                const current = headers[key];\n                if (typeof current === 'string') {\n                    headers[key] = [current, value];\n                }\n                else if (Array.isArray(current)) {\n                    current.push(value);\n                }\n                else {\n                    headers[key] = value;\n                }\n            }\n            debug('got proxy server response: %o %o', firstLine, headers);\n            cleanup();\n            resolve({\n                connect: {\n                    statusCode,\n                    statusText,\n                    headers,\n                },\n                buffered,\n            });\n        }\n        socket.on('error', onerror);\n        socket.on('end', onend);\n        read();\n    });\n}\nexports.parseProxyResponse = parseProxyResponse;\n//# sourceMappingURL=parse-proxy-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\n");

/***/ })

};
;