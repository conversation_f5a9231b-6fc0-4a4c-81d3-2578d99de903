"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vapi-ai";
exports.ids = ["vendor-chunks/@vapi-ai"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vapi-ai/web/dist/api.js":
/*!***********************************************!*\
  !*** ./node_modules/@vapi-ai/web/dist/api.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/* eslint-disable */\n/* tslint:disable */\n// @ts-nocheck\n/*\n * ---------------------------------------------------------------\n * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##\n * ##                                                           ##\n * ## AUTHOR: acacode                                           ##\n * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##\n * ---------------------------------------------------------------\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Api = exports.HttpClient = exports.ContentType = void 0;\nvar ContentType;\n(function (ContentType) {\n    ContentType[\"Json\"] = \"application/json\";\n    ContentType[\"JsonApi\"] = \"application/vnd.api+json\";\n    ContentType[\"FormData\"] = \"multipart/form-data\";\n    ContentType[\"UrlEncoded\"] = \"application/x-www-form-urlencoded\";\n    ContentType[\"Text\"] = \"text/plain\";\n})(ContentType || (exports.ContentType = ContentType = {}));\nclass HttpClient {\n    baseUrl = \"https://api.vapi.ai\";\n    securityData = null;\n    securityWorker;\n    abortControllers = new Map();\n    customFetch = (...fetchParams) => fetch(...fetchParams);\n    baseApiParams = {\n        credentials: \"same-origin\",\n        headers: {},\n        redirect: \"follow\",\n        referrerPolicy: \"no-referrer\",\n    };\n    constructor(apiConfig = {}) {\n        Object.assign(this, apiConfig);\n    }\n    setSecurityData = (data) => {\n        this.securityData = data;\n    };\n    encodeQueryParam(key, value) {\n        const encodedKey = encodeURIComponent(key);\n        return `${encodedKey}=${encodeURIComponent(typeof value === \"number\" ? value : `${value}`)}`;\n    }\n    addQueryParam(query, key) {\n        return this.encodeQueryParam(key, query[key]);\n    }\n    addArrayQueryParam(query, key) {\n        const value = query[key];\n        return value.map((v) => this.encodeQueryParam(key, v)).join(\"&\");\n    }\n    toQueryString(rawQuery) {\n        const query = rawQuery || {};\n        const keys = Object.keys(query).filter((key) => \"undefined\" !== typeof query[key]);\n        return keys\n            .map((key) => Array.isArray(query[key])\n            ? this.addArrayQueryParam(query, key)\n            : this.addQueryParam(query, key))\n            .join(\"&\");\n    }\n    addQueryParams(rawQuery) {\n        const queryString = this.toQueryString(rawQuery);\n        return queryString ? `?${queryString}` : \"\";\n    }\n    contentFormatters = {\n        [ContentType.Json]: (input) => input !== null && (typeof input === \"object\" || typeof input === \"string\")\n            ? JSON.stringify(input)\n            : input,\n        [ContentType.JsonApi]: (input) => input !== null && (typeof input === \"object\" || typeof input === \"string\")\n            ? JSON.stringify(input)\n            : input,\n        [ContentType.Text]: (input) => input !== null && typeof input !== \"string\"\n            ? JSON.stringify(input)\n            : input,\n        [ContentType.FormData]: (input) => Object.keys(input || {}).reduce((formData, key) => {\n            const property = input[key];\n            formData.append(key, property instanceof Blob\n                ? property\n                : typeof property === \"object\" && property !== null\n                    ? JSON.stringify(property)\n                    : `${property}`);\n            return formData;\n        }, new FormData()),\n        [ContentType.UrlEncoded]: (input) => this.toQueryString(input),\n    };\n    mergeRequestParams(params1, params2) {\n        return {\n            ...this.baseApiParams,\n            ...params1,\n            ...(params2 || {}),\n            headers: {\n                ...(this.baseApiParams.headers || {}),\n                ...(params1.headers || {}),\n                ...((params2 && params2.headers) || {}),\n            },\n        };\n    }\n    createAbortSignal = (cancelToken) => {\n        if (this.abortControllers.has(cancelToken)) {\n            const abortController = this.abortControllers.get(cancelToken);\n            if (abortController) {\n                return abortController.signal;\n            }\n            return void 0;\n        }\n        const abortController = new AbortController();\n        this.abortControllers.set(cancelToken, abortController);\n        return abortController.signal;\n    };\n    abortRequest = (cancelToken) => {\n        const abortController = this.abortControllers.get(cancelToken);\n        if (abortController) {\n            abortController.abort();\n            this.abortControllers.delete(cancelToken);\n        }\n    };\n    request = async ({ body, secure, path, type, query, format, baseUrl, cancelToken, ...params }) => {\n        const secureParams = ((typeof secure === \"boolean\" ? secure : this.baseApiParams.secure) &&\n            this.securityWorker &&\n            (await this.securityWorker(this.securityData))) ||\n            {};\n        const requestParams = this.mergeRequestParams(params, secureParams);\n        const queryString = query && this.toQueryString(query);\n        const payloadFormatter = this.contentFormatters[type || ContentType.Json];\n        const responseFormat = format || requestParams.format;\n        return this.customFetch(`${baseUrl || this.baseUrl || \"\"}${path}${queryString ? `?${queryString}` : \"\"}`, {\n            ...requestParams,\n            headers: {\n                ...(requestParams.headers || {}),\n                ...(type && type !== ContentType.FormData\n                    ? { \"Content-Type\": type }\n                    : {}),\n            },\n            signal: (cancelToken\n                ? this.createAbortSignal(cancelToken)\n                : requestParams.signal) || null,\n            body: typeof body === \"undefined\" || body === null\n                ? null\n                : payloadFormatter(body),\n        }).then(async (response) => {\n            const r = response.clone();\n            r.data = null;\n            r.error = null;\n            const data = !responseFormat\n                ? r\n                : await response[responseFormat]()\n                    .then((data) => {\n                    if (r.ok) {\n                        r.data = data;\n                    }\n                    else {\n                        r.error = data;\n                    }\n                    return r;\n                })\n                    .catch((e) => {\n                    r.error = e;\n                    return r;\n                });\n            if (cancelToken) {\n                this.abortControllers.delete(cancelToken);\n            }\n            if (!response.ok)\n                throw data;\n            return data;\n        });\n    };\n}\nexports.HttpClient = HttpClient;\n/**\n * @title Vapi API\n * @version 1.0\n * @baseUrl https://api.vapi.ai\n * @contact\n *\n * Voice AI for developers.\n */\nclass Api extends HttpClient {\n    call = {\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerCreate\n         * @summary Create Call\n         * @request POST:/call\n         * @secure\n         */\n        callControllerCreate: (data, params = {}) => this.request({\n            path: `/call`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerFindAll\n         * @summary List Calls\n         * @request GET:/call\n         * @secure\n         */\n        callControllerFindAll: (query, params = {}) => this.request({\n            path: `/call`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerFindOne\n         * @summary Get Call\n         * @request GET:/call/{id}\n         * @secure\n         */\n        callControllerFindOne: (id, params = {}) => this.request({\n            path: `/call/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerUpdate\n         * @summary Update Call\n         * @request PATCH:/call/{id}\n         * @secure\n         */\n        callControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/call/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerDeleteCallData\n         * @summary Delete Call Data\n         * @request DELETE:/call/{id}\n         * @secure\n         */\n        callControllerDeleteCallData: (id, params = {}) => this.request({\n            path: `/call/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerCreatePhoneCall\n         * @summary Create Phone Call\n         * @request POST:/call/phone\n         * @deprecated\n         * @secure\n         */\n        callControllerCreatePhoneCall: (data, params = {}) => this.request({\n            path: `/call/phone`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerCreateWebCall\n         * @summary Create Web Call\n         * @request POST:/call/web\n         * @secure\n         */\n        callControllerCreateWebCall: (data, params = {}) => this.request({\n            path: `/call/web`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    v2 = {\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerExportCalls\n         * @summary Export Calls to CSV\n         * @request GET:/v2/call/export\n         * @secure\n         */\n        callControllerExportCalls: (query, params = {}) => this.request({\n            path: `/v2/call/export`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerFindAllPaginated\n         * @summary List Calls\n         * @request GET:/v2/call\n         * @secure\n         */\n        callControllerFindAllPaginated: (query, params = {}) => this.request({\n            path: `/v2/call`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Calls\n         * @name CallControllerFindAllMetadataPaginated\n         * @summary List Call Metadata\n         * @request GET:/v2/call/metadata\n         * @secure\n         */\n        callControllerFindAllMetadataPaginated: (query, params = {}) => this.request({\n            path: `/v2/call/metadata`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerFindAllPaginated\n         * @summary List Assistants with pagination\n         * @request GET:/v2/assistant\n         * @secure\n         */\n        assistantControllerFindAllPaginated: (query, params = {}) => this.request({\n            path: `/v2/assistant`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerFindAllPaginated\n         * @summary List Phone Numbers\n         * @request GET:/v2/phone-number\n         * @secure\n         */\n        phoneNumberControllerFindAllPaginated: (query, params = {}) => this.request({\n            path: `/v2/phone-number`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    chat = {\n        /**\n         * No description\n         *\n         * @tags Chats\n         * @name ChatControllerListChats\n         * @summary List Chats\n         * @request GET:/chat\n         * @secure\n         */\n        chatControllerListChats: (query, params = {}) => this.request({\n            path: `/chat`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * @description Creates a new chat. Requires at least one of: assistantId/assistant, sessionId, or previousChatId. Note: sessionId and previousChatId are mutually exclusive.\n         *\n         * @tags Chats\n         * @name ChatControllerCreateChat\n         * @summary Create Chat\n         * @request POST:/chat\n         * @secure\n         */\n        chatControllerCreateChat: (data, params = {}) => this.request({\n            path: `/chat`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Chats\n         * @name ChatControllerGetChat\n         * @summary Get Chat\n         * @request GET:/chat/{id}\n         * @secure\n         */\n        chatControllerGetChat: (id, params = {}) => this.request({\n            path: `/chat/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Chats\n         * @name ChatControllerDeleteChat\n         * @summary Delete Chat\n         * @request DELETE:/chat/{id}\n         * @secure\n         */\n        chatControllerDeleteChat: (id, params = {}) => this.request({\n            path: `/chat/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Chats\n         * @name ChatControllerCreateOpenAiChat\n         * @summary Create Chat (OpenAI Compatible)\n         * @request POST:/chat/responses\n         * @secure\n         */\n        chatControllerCreateOpenAiChat: (data, params = {}) => this.request({\n            path: `/chat/responses`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Chats\n         * @name ChatControllerCreateWebChat\n         * @summary Create WebChat\n         * @request POST:/chat/web\n         * @secure\n         */\n        chatControllerCreateWebChat: (data, params = {}) => this.request({\n            path: `/chat/web`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Chats\n         * @name ChatControllerCreateOpenAiWebChat\n         * @summary Create WebChat (OpenAI Compatible)\n         * @request POST:/chat/web/responses\n         * @secure\n         */\n        chatControllerCreateOpenAiWebChat: (data, params = {}) => this.request({\n            path: `/chat/web/responses`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    campaign = {\n        /**\n         * No description\n         *\n         * @tags Campaigns\n         * @name CampaignControllerCreate\n         * @summary Create Campaign\n         * @request POST:/campaign\n         * @secure\n         */\n        campaignControllerCreate: (data, params = {}) => this.request({\n            path: `/campaign`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Campaigns\n         * @name CampaignControllerFindAll\n         * @summary List Campaigns\n         * @request GET:/campaign\n         * @secure\n         */\n        campaignControllerFindAll: (query, params = {}) => this.request({\n            path: `/campaign`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Campaigns\n         * @name CampaignControllerFindAllPaginated\n         * @summary List Campaigns with pagination\n         * @request GET:/campaign/paginated\n         * @secure\n         */\n        campaignControllerFindAllPaginated: (query, params = {}) => this.request({\n            path: `/campaign/paginated`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Campaigns\n         * @name CampaignControllerFindOne\n         * @summary Get Campaign\n         * @request GET:/campaign/{id}\n         * @secure\n         */\n        campaignControllerFindOne: (id, params = {}) => this.request({\n            path: `/campaign/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Campaigns\n         * @name CampaignControllerUpdate\n         * @summary Update Campaign\n         * @request PATCH:/campaign/{id}\n         * @secure\n         */\n        campaignControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/campaign/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Campaigns\n         * @name CampaignControllerRemove\n         * @summary Delete Campaign\n         * @request DELETE:/campaign/{id}\n         * @secure\n         */\n        campaignControllerRemove: (id, params = {}) => this.request({\n            path: `/campaign/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    session = {\n        /**\n         * No description\n         *\n         * @tags Sessions\n         * @name SessionControllerCreate\n         * @summary Create Session\n         * @request POST:/session\n         * @secure\n         */\n        sessionControllerCreate: (data, params = {}) => this.request({\n            path: `/session`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Sessions\n         * @name SessionControllerFindAllPaginated\n         * @summary List Sessions\n         * @request GET:/session\n         * @secure\n         */\n        sessionControllerFindAllPaginated: (query, params = {}) => this.request({\n            path: `/session`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Sessions\n         * @name SessionControllerFindOne\n         * @summary Get Session\n         * @request GET:/session/{id}\n         * @secure\n         */\n        sessionControllerFindOne: (id, params = {}) => this.request({\n            path: `/session/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Sessions\n         * @name SessionControllerUpdate\n         * @summary Update Session\n         * @request PATCH:/session/{id}\n         * @secure\n         */\n        sessionControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/session/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Sessions\n         * @name SessionControllerRemove\n         * @summary Delete Session\n         * @request DELETE:/session/{id}\n         * @secure\n         */\n        sessionControllerRemove: (id, params = {}) => this.request({\n            path: `/session/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    support = {\n        /**\n         * No description\n         *\n         * @tags Support\n         * @name SupportControllerCreateTicket\n         * @summary Create Support Ticket\n         * @request POST:/support/ticket\n         * @secure\n         */\n        supportControllerCreateTicket: (data, params = {}) => this.request({\n            path: `/support/ticket`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    assistant = {\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerCreate\n         * @summary Create Assistant\n         * @request POST:/assistant\n         * @secure\n         */\n        assistantControllerCreate: (data, params = {}) => this.request({\n            path: `/assistant`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerFindAll\n         * @summary List Assistants\n         * @request GET:/assistant\n         * @secure\n         */\n        assistantControllerFindAll: (query, params = {}) => this.request({\n            path: `/assistant`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerFindOne\n         * @summary Get Assistant\n         * @request GET:/assistant/{id}\n         * @secure\n         */\n        assistantControllerFindOne: (id, params = {}) => this.request({\n            path: `/assistant/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerUpdate\n         * @summary Update Assistant\n         * @request PATCH:/assistant/{id}\n         * @secure\n         */\n        assistantControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/assistant/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerReplace\n         * @summary Replace Assistant\n         * @request PUT:/assistant/{id}\n         * @secure\n         */\n        assistantControllerReplace: (id, data, params = {}) => this.request({\n            path: `/assistant/${id}`,\n            method: \"PUT\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerRemove\n         * @summary Delete Assistant\n         * @request DELETE:/assistant/{id}\n         * @secure\n         */\n        assistantControllerRemove: (id, params = {}) => this.request({\n            path: `/assistant/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Assistants\n         * @name AssistantControllerFindVersions\n         * @summary List Assistant Versions\n         * @request GET:/assistant/{id}/version\n         * @secure\n         */\n        assistantControllerFindVersions: (id, query, params = {}) => this.request({\n            path: `/assistant/${id}/version`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    phoneNumber = {\n        /**\n         * @description Use POST /phone-number instead.\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerImportTwilio\n         * @summary Import Twilio Number\n         * @request POST:/phone-number/import/twilio\n         * @deprecated\n         * @secure\n         */\n        phoneNumberControllerImportTwilio: (data, params = {}) => this.request({\n            path: `/phone-number/import/twilio`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * @description Use POST /phone-number instead.\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerImportVonage\n         * @summary Import Vonage Number\n         * @request POST:/phone-number/import/vonage\n         * @deprecated\n         * @secure\n         */\n        phoneNumberControllerImportVonage: (data, params = {}) => this.request({\n            path: `/phone-number/import/vonage`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerCreate\n         * @summary Create Phone Number\n         * @request POST:/phone-number\n         * @secure\n         */\n        phoneNumberControllerCreate: (data, params = {}) => this.request({\n            path: `/phone-number`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerFindAll\n         * @summary List Phone Numbers\n         * @request GET:/phone-number\n         * @secure\n         */\n        phoneNumberControllerFindAll: (query, params = {}) => this.request({\n            path: `/phone-number`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerFindOne\n         * @summary Get Phone Number\n         * @request GET:/phone-number/{id}\n         * @secure\n         */\n        phoneNumberControllerFindOne: (id, params = {}) => this.request({\n            path: `/phone-number/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerUpdate\n         * @summary Update Phone Number\n         * @request PATCH:/phone-number/{id}\n         * @secure\n         */\n        phoneNumberControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/phone-number/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Phone Numbers\n         * @name PhoneNumberControllerRemove\n         * @summary Delete Phone Number\n         * @request DELETE:/phone-number/{id}\n         * @secure\n         */\n        phoneNumberControllerRemove: (id, params = {}) => this.request({\n            path: `/phone-number/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    tool = {\n        /**\n         * No description\n         *\n         * @tags Tools\n         * @name ToolControllerCreate\n         * @summary Create Tool\n         * @request POST:/tool\n         * @secure\n         */\n        toolControllerCreate: (data, params = {}) => this.request({\n            path: `/tool`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tools\n         * @name ToolControllerFindAll\n         * @summary List Tools\n         * @request GET:/tool\n         * @secure\n         */\n        toolControllerFindAll: (query, params = {}) => this.request({\n            path: `/tool`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tools\n         * @name ToolControllerFindOne\n         * @summary Get Tool\n         * @request GET:/tool/{id}\n         * @secure\n         */\n        toolControllerFindOne: (id, params = {}) => this.request({\n            path: `/tool/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tools\n         * @name ToolControllerUpdate\n         * @summary Update Tool\n         * @request PATCH:/tool/{id}\n         * @secure\n         */\n        toolControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/tool/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tools\n         * @name ToolControllerRemove\n         * @summary Delete Tool\n         * @request DELETE:/tool/{id}\n         * @secure\n         */\n        toolControllerRemove: (id, params = {}) => this.request({\n            path: `/tool/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    file = {\n        /**\n         * @description Use POST /file instead.\n         *\n         * @tags Files\n         * @name FileControllerCreateDeprecated\n         * @summary Upload File\n         * @request POST:/file/upload\n         * @deprecated\n         * @secure\n         */\n        fileControllerCreateDeprecated: (data, params = {}) => this.request({\n            path: `/file/upload`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.FormData,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Files\n         * @name FileControllerCreate\n         * @summary Upload File\n         * @request POST:/file\n         * @secure\n         */\n        fileControllerCreate: (data, params = {}) => this.request({\n            path: `/file`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.FormData,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Files\n         * @name FileControllerFindAll\n         * @summary List Files\n         * @request GET:/file\n         * @secure\n         */\n        fileControllerFindAll: (params = {}) => this.request({\n            path: `/file`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Files\n         * @name FileControllerFindOne\n         * @summary Get File\n         * @request GET:/file/{id}\n         * @secure\n         */\n        fileControllerFindOne: (id, params = {}) => this.request({\n            path: `/file/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Files\n         * @name FileControllerUpdate\n         * @summary Update File\n         * @request PATCH:/file/{id}\n         * @secure\n         */\n        fileControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/file/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Files\n         * @name FileControllerRemove\n         * @summary Delete File\n         * @request DELETE:/file/{id}\n         * @secure\n         */\n        fileControllerRemove: (id, params = {}) => this.request({\n            path: `/file/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    knowledgeBase = {\n        /**\n         * No description\n         *\n         * @tags Knowledge Base\n         * @name KnowledgeBaseControllerCreate\n         * @summary Create Knowledge Base\n         * @request POST:/knowledge-base\n         * @secure\n         */\n        knowledgeBaseControllerCreate: (data, params = {}) => this.request({\n            path: `/knowledge-base`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Knowledge Base\n         * @name KnowledgeBaseControllerFindAll\n         * @summary List Knowledge Bases\n         * @request GET:/knowledge-base\n         * @secure\n         */\n        knowledgeBaseControllerFindAll: (query, params = {}) => this.request({\n            path: `/knowledge-base`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Knowledge Base\n         * @name KnowledgeBaseControllerFindOne\n         * @summary Get Knowledge Base\n         * @request GET:/knowledge-base/{id}\n         * @secure\n         */\n        knowledgeBaseControllerFindOne: (id, params = {}) => this.request({\n            path: `/knowledge-base/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Knowledge Base\n         * @name KnowledgeBaseControllerUpdate\n         * @summary Update Knowledge Base\n         * @request PATCH:/knowledge-base/{id}\n         * @secure\n         */\n        knowledgeBaseControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/knowledge-base/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Knowledge Base\n         * @name KnowledgeBaseControllerRemove\n         * @summary Delete Knowledge Base\n         * @request DELETE:/knowledge-base/{id}\n         * @secure\n         */\n        knowledgeBaseControllerRemove: (id, params = {}) => this.request({\n            path: `/knowledge-base/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    workflow = {\n        /**\n         * No description\n         *\n         * @tags Workflow\n         * @name WorkflowControllerFindAll\n         * @summary Get Workflows\n         * @request GET:/workflow\n         * @secure\n         */\n        workflowControllerFindAll: (params = {}) => this.request({\n            path: `/workflow`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Workflow\n         * @name WorkflowControllerCreate\n         * @summary Create Workflow\n         * @request POST:/workflow\n         * @secure\n         */\n        workflowControllerCreate: (data, params = {}) => this.request({\n            path: `/workflow`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Workflow\n         * @name WorkflowControllerFindOne\n         * @summary Get Workflow\n         * @request GET:/workflow/{id}\n         * @secure\n         */\n        workflowControllerFindOne: (id, params = {}) => this.request({\n            path: `/workflow/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Workflow\n         * @name WorkflowControllerDelete\n         * @summary Delete Workflow\n         * @request DELETE:/workflow/{id}\n         * @secure\n         */\n        workflowControllerDelete: (id, params = {}) => this.request({\n            path: `/workflow/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Workflow\n         * @name WorkflowControllerUpdate\n         * @summary Update Workflow\n         * @request PATCH:/workflow/{id}\n         * @secure\n         */\n        workflowControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/workflow/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Workflow\n         * @name WorkflowControllerGenerateFromTranscripts\n         * @summary Generate Workflow JSON from Transcript Files\n         * @request POST:/workflow/generate\n         * @secure\n         */\n        workflowControllerGenerateFromTranscripts: (data, params = {}) => this.request({\n            path: `/workflow/generate`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    squad = {\n        /**\n         * No description\n         *\n         * @tags Squads\n         * @name SquadControllerCreate\n         * @summary Create Squad\n         * @request POST:/squad\n         * @secure\n         */\n        squadControllerCreate: (data, params = {}) => this.request({\n            path: `/squad`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Squads\n         * @name SquadControllerFindAll\n         * @summary List Squads\n         * @request GET:/squad\n         * @secure\n         */\n        squadControllerFindAll: (query, params = {}) => this.request({\n            path: `/squad`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Squads\n         * @name SquadControllerFindOne\n         * @summary Get Squad\n         * @request GET:/squad/{id}\n         * @secure\n         */\n        squadControllerFindOne: (id, params = {}) => this.request({\n            path: `/squad/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Squads\n         * @name SquadControllerUpdate\n         * @summary Update Squad\n         * @request PATCH:/squad/{id}\n         * @secure\n         */\n        squadControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/squad/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Squads\n         * @name SquadControllerRemove\n         * @summary Delete Squad\n         * @request DELETE:/squad/{id}\n         * @secure\n         */\n        squadControllerRemove: (id, params = {}) => this.request({\n            path: `/squad/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    testSuite = {\n        /**\n         * No description\n         *\n         * @tags Test Suites\n         * @name TestSuiteControllerFindAllPaginated\n         * @summary List Test Suites\n         * @request GET:/test-suite\n         * @secure\n         */\n        testSuiteControllerFindAllPaginated: (query, params = {}) => this.request({\n            path: `/test-suite`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suites\n         * @name TestSuiteControllerCreate\n         * @summary Create Test Suite\n         * @request POST:/test-suite\n         * @secure\n         */\n        testSuiteControllerCreate: (data, params = {}) => this.request({\n            path: `/test-suite`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suites\n         * @name TestSuiteControllerFindOne\n         * @summary Get Test Suite\n         * @request GET:/test-suite/{id}\n         * @secure\n         */\n        testSuiteControllerFindOne: (id, params = {}) => this.request({\n            path: `/test-suite/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suites\n         * @name TestSuiteControllerUpdate\n         * @summary Update Test Suite\n         * @request PATCH:/test-suite/{id}\n         * @secure\n         */\n        testSuiteControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/test-suite/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suites\n         * @name TestSuiteControllerRemove\n         * @summary Delete Test Suite\n         * @request DELETE:/test-suite/{id}\n         * @secure\n         */\n        testSuiteControllerRemove: (id, params = {}) => this.request({\n            path: `/test-suite/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Tests\n         * @name TestSuiteTestControllerFindAllPaginated\n         * @summary List Tests\n         * @request GET:/test-suite/{testSuiteId}/test\n         * @secure\n         */\n        testSuiteTestControllerFindAllPaginated: (testSuiteId, query, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/test`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Tests\n         * @name TestSuiteTestControllerCreate\n         * @summary Create Test\n         * @request POST:/test-suite/{testSuiteId}/test\n         * @secure\n         */\n        testSuiteTestControllerCreate: (testSuiteId, data, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/test`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Tests\n         * @name TestSuiteTestControllerFindOne\n         * @summary Get Test\n         * @request GET:/test-suite/{testSuiteId}/test/{id}\n         * @secure\n         */\n        testSuiteTestControllerFindOne: (testSuiteId, id, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/test/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Tests\n         * @name TestSuiteTestControllerUpdate\n         * @summary Update Test\n         * @request PATCH:/test-suite/{testSuiteId}/test/{id}\n         * @secure\n         */\n        testSuiteTestControllerUpdate: (testSuiteId, id, data, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/test/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Tests\n         * @name TestSuiteTestControllerRemove\n         * @summary Delete Test\n         * @request DELETE:/test-suite/{testSuiteId}/test/{id}\n         * @secure\n         */\n        testSuiteTestControllerRemove: (testSuiteId, id, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/test/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Runs\n         * @name TestSuiteRunControllerFindAllPaginated\n         * @summary List Test Suite Runs\n         * @request GET:/test-suite/{testSuiteId}/run\n         * @secure\n         */\n        testSuiteRunControllerFindAllPaginated: (testSuiteId, query, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/run`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Runs\n         * @name TestSuiteRunControllerCreate\n         * @summary Create Test Suite Run\n         * @request POST:/test-suite/{testSuiteId}/run\n         * @secure\n         */\n        testSuiteRunControllerCreate: (testSuiteId, data, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/run`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Runs\n         * @name TestSuiteRunControllerFindOne\n         * @summary Get Test Suite Run\n         * @request GET:/test-suite/{testSuiteId}/run/{id}\n         * @secure\n         */\n        testSuiteRunControllerFindOne: (testSuiteId, id, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/run/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Runs\n         * @name TestSuiteRunControllerUpdate\n         * @summary Update Test Suite Run\n         * @request PATCH:/test-suite/{testSuiteId}/run/{id}\n         * @secure\n         */\n        testSuiteRunControllerUpdate: (testSuiteId, id, data, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/run/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Test Suite Runs\n         * @name TestSuiteRunControllerRemove\n         * @summary Delete Test Suite Run\n         * @request DELETE:/test-suite/{testSuiteId}/run/{id}\n         * @secure\n         */\n        testSuiteRunControllerRemove: (testSuiteId, id, params = {}) => this.request({\n            path: `/test-suite/${testSuiteId}/run/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    analytics = {\n        /**\n         * No description\n         *\n         * @tags Analytics\n         * @name AnalyticsControllerQuery\n         * @summary Create Analytics Queries\n         * @request POST:/analytics\n         * @secure\n         */\n        analyticsControllerQuery: (data, params = {}) => this.request({\n            path: `/analytics`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    log = {\n        /**\n         * No description\n         *\n         * @tags Logs\n         * @name LoggingControllerCallLogsQuery\n         * @summary Get Call Logs\n         * @request GET:/log\n         * @secure\n         */\n        loggingControllerCallLogsQuery: (query, params = {}) => this.request({\n            path: `/log`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Logs\n         * @name LoggingControllerCallLogsDeleteQuery\n         * @summary Delete Call Logs\n         * @request DELETE:/log\n         * @secure\n         */\n        loggingControllerCallLogsDeleteQuery: (query, params = {}) => this.request({\n            path: `/log`,\n            method: \"DELETE\",\n            query: query,\n            secure: true,\n            ...params,\n        }),\n    };\n    logs = {\n        /**\n         * No description\n         *\n         * @tags Logs\n         * @name LoggingControllerLogsQuery\n         * @summary Get Logs\n         * @request GET:/logs\n         * @deprecated\n         * @secure\n         */\n        loggingControllerLogsQuery: (query, params = {}) => this.request({\n            path: `/logs`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Logs\n         * @name LoggingControllerLogsDeleteQuery\n         * @summary Delete Logs\n         * @request DELETE:/logs\n         * @deprecated\n         * @secure\n         */\n        loggingControllerLogsDeleteQuery: (query, params = {}) => this.request({\n            path: `/logs`,\n            method: \"DELETE\",\n            query: query,\n            secure: true,\n            ...params,\n        }),\n    };\n    org = {\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerCreate\n         * @summary Create Org\n         * @request POST:/org\n         * @secure\n         */\n        orgControllerCreate: (data, params = {}) => this.request({\n            path: `/org`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerFindAll\n         * @summary List Orgs\n         * @request GET:/org\n         * @secure\n         */\n        orgControllerFindAll: (params = {}) => this.request({\n            path: `/org`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerFindOne\n         * @summary Get Org\n         * @request GET:/org/{id}\n         * @secure\n         */\n        orgControllerFindOne: (id, params = {}) => this.request({\n            path: `/org/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerUpdate\n         * @summary Update Org\n         * @request PATCH:/org/{id}\n         * @secure\n         */\n        orgControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/org/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerDeleteOrg\n         * @summary Delete Org\n         * @request DELETE:/org/{id}\n         * @secure\n         */\n        orgControllerDeleteOrg: (id, params = {}) => this.request({\n            path: `/org/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerFindAllUsers\n         * @summary List Users\n         * @request GET:/org/{id}/user\n         * @secure\n         */\n        orgControllerFindAllUsers: (id, params = {}) => this.request({\n            path: `/org/${id}/user`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerOrgLeave\n         * @summary Leave Org\n         * @request DELETE:/org/{id}/leave\n         * @secure\n         */\n        orgControllerOrgLeave: (id, params = {}) => this.request({\n            path: `/org/${id}/leave`,\n            method: \"DELETE\",\n            secure: true,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerOrgRemoveUser\n         * @summary Remove Org Member\n         * @request DELETE:/org/{id}/member/{memberId}/leave\n         * @secure\n         */\n        orgControllerOrgRemoveUser: (id, memberId, params = {}) => this.request({\n            path: `/org/${id}/member/${memberId}/leave`,\n            method: \"DELETE\",\n            secure: true,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerUsersInvite\n         * @summary Invite User\n         * @request POST:/org/{id}/invite\n         * @secure\n         */\n        orgControllerUsersInvite: (id, data, params = {}) => this.request({\n            path: `/org/${id}/invite`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerUserUpdate\n         * @summary Update User Role\n         * @request PATCH:/org/{id}/role\n         * @secure\n         */\n        orgControllerUserUpdate: (id, data, params = {}) => this.request({\n            path: `/org/${id}/role`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Orgs\n         * @name OrgControllerOrgToken\n         * @summary Generate User Org JWT\n         * @request GET:/org/{id}/auth\n         * @secure\n         */\n        orgControllerOrgToken: (id, params = {}) => this.request({\n            path: `/org/${id}/auth`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    token = {\n        /**\n         * No description\n         *\n         * @tags Tokens\n         * @name TokenControllerCreate\n         * @summary Create Token\n         * @request POST:/token\n         * @secure\n         */\n        tokenControllerCreate: (data, params = {}) => this.request({\n            path: `/token`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tokens\n         * @name TokenControllerFindAll\n         * @summary List Tokens\n         * @request GET:/token\n         * @secure\n         */\n        tokenControllerFindAll: (query, params = {}) => this.request({\n            path: `/token`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tokens\n         * @name TokenControllerFindOne\n         * @summary Get Token\n         * @request GET:/token/{id}\n         * @secure\n         */\n        tokenControllerFindOne: (id, params = {}) => this.request({\n            path: `/token/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tokens\n         * @name TokenControllerUpdate\n         * @summary Update Token\n         * @request PATCH:/token/{id}\n         * @secure\n         */\n        tokenControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/token/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Tokens\n         * @name TokenControllerRemove\n         * @summary Delete Token\n         * @request DELETE:/token/{id}\n         * @secure\n         */\n        tokenControllerRemove: (id, params = {}) => this.request({\n            path: `/token/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    credential = {\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerCreate\n         * @summary Create Credential\n         * @request POST:/credential\n         * @secure\n         */\n        credentialControllerCreate: (data, params = {}) => this.request({\n            path: `/credential`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerFindAll\n         * @summary List Credentials\n         * @request GET:/credential\n         * @secure\n         */\n        credentialControllerFindAll: (query, params = {}) => this.request({\n            path: `/credential`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerFindOne\n         * @summary Get Credential\n         * @request GET:/credential/{id}\n         * @secure\n         */\n        credentialControllerFindOne: (id, params = {}) => this.request({\n            path: `/credential/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerUpdate\n         * @summary Update Credential\n         * @request PATCH:/credential/{id}\n         * @secure\n         */\n        credentialControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/credential/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerRemove\n         * @summary Delete Credential\n         * @request DELETE:/credential/{id}\n         * @secure\n         */\n        credentialControllerRemove: (id, params = {}) => this.request({\n            path: `/credential/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerGenerateSession\n         * @summary Generate a credential session\n         * @request POST:/credential/session\n         * @secure\n         */\n        credentialControllerGenerateSession: (data, params = {}) => this.request({\n            path: `/credential/session`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerHandleWebhook\n         * @summary Handle credential webhook\n         * @request POST:/credential/webhook\n         */\n        credentialControllerHandleWebhook: (data, params = {}) => this.request({\n            path: `/credential/webhook`,\n            method: \"POST\",\n            body: data,\n            type: ContentType.Json,\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Credentials\n         * @name CredentialControllerCredentialActionTrigger\n         * @summary Trigger a credential action\n         * @request POST:/credential/trigger\n         * @secure\n         */\n        credentialControllerCredentialActionTrigger: (data, params = {}) => this.request({\n            path: `/credential/trigger`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            ...params,\n        }),\n    };\n    template = {\n        /**\n         * No description\n         *\n         * @tags Templates\n         * @name TemplateControllerCreate\n         * @summary Create Template\n         * @request POST:/template\n         * @secure\n         */\n        templateControllerCreate: (data, params = {}) => this.request({\n            path: `/template`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Templates\n         * @name TemplateControllerFindAll\n         * @summary List Templates\n         * @request GET:/template\n         * @secure\n         */\n        templateControllerFindAll: (query, params = {}) => this.request({\n            path: `/template`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Templates\n         * @name TemplateControllerFindAllPinned\n         * @summary List Templates\n         * @request GET:/template/pinned\n         * @secure\n         */\n        templateControllerFindAllPinned: (params = {}) => this.request({\n            path: `/template/pinned`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Templates\n         * @name TemplateControllerFindOne\n         * @summary Get Template\n         * @request GET:/template/{id}\n         * @secure\n         */\n        templateControllerFindOne: (id, params = {}) => this.request({\n            path: `/template/${id}`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Templates\n         * @name TemplateControllerUpdate\n         * @summary Update Template\n         * @request PATCH:/template/{id}\n         * @secure\n         */\n        templateControllerUpdate: (id, data, params = {}) => this.request({\n            path: `/template/${id}`,\n            method: \"PATCH\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Templates\n         * @name TemplateControllerRemove\n         * @summary Delete Template\n         * @request DELETE:/template/{id}\n         * @secure\n         */\n        templateControllerRemove: (id, params = {}) => this.request({\n            path: `/template/${id}`,\n            method: \"DELETE\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    voiceLibrary = {\n        /**\n         * No description\n         *\n         * @tags Voice Library\n         * @name VoiceLibraryControllerVoiceGetByProvider\n         * @summary Get voices in Voice Library by Provider\n         * @request GET:/voice-library/{provider}\n         * @secure\n         */\n        voiceLibraryControllerVoiceGetByProvider: (provider, query, params = {}) => this.request({\n            path: `/voice-library/${provider}`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Voice Library\n         * @name VoiceLibraryControllerVoiceGetAccentsByProvider\n         * @summary Get accents in Voice Library by Provider\n         * @request GET:/voice-library/{provider}/accents\n         * @secure\n         */\n        voiceLibraryControllerVoiceGetAccentsByProvider: (provider, params = {}) => this.request({\n            path: `/voice-library/${provider}/accents`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Voice Library\n         * @name VoiceLibraryControllerVoiceLibrarySyncByProvider\n         * @summary Sync Private voices in Voice Library by Provider\n         * @request POST:/voice-library/sync/{provider}\n         * @secure\n         */\n        voiceLibraryControllerVoiceLibrarySyncByProvider: (provider, params = {}) => this.request({\n            path: `/voice-library/sync/${provider}`,\n            method: \"POST\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Voice Library\n         * @name VoiceLibraryControllerVoiceLibrarySyncDefaultVoices\n         * @summary Sync Default voices in Voice Library by Providers\n         * @request POST:/voice-library/sync\n         * @secure\n         */\n        voiceLibraryControllerVoiceLibrarySyncDefaultVoices: (data, params = {}) => this.request({\n            path: `/voice-library/sync`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Voice Library\n         * @name VoiceLibraryControllerVoiceLibraryCreateSesameVoice\n         * @summary Create a new voice in the Voice Library using Sesame\n         * @request POST:/voice-library/create-sesame-voice\n         * @secure\n         */\n        voiceLibraryControllerVoiceLibraryCreateSesameVoice: (data, params = {}) => this.request({\n            path: `/voice-library/create-sesame-voice`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            ...params,\n        }),\n    };\n    provider = {\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name ProviderControllerGetWorkflows\n         * @request GET:/{provider}/workflows\n         * @secure\n         */\n        providerControllerGetWorkflows: (provider, query, params = {}) => this.request({\n            path: `/${provider}/workflows`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name ProviderControllerGetWorkflowTriggerHook\n         * @request GET:/{provider}/workflows/{workflowId}/hooks\n         * @secure\n         */\n        providerControllerGetWorkflowTriggerHook: (provider, workflowId, params = {}) => this.request({\n            path: `/${provider}/workflows/${workflowId}/hooks`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name ProviderControllerGetLocations\n         * @request GET:/{provider}/locations\n         * @secure\n         */\n        providerControllerGetLocations: (provider, params = {}) => this.request({\n            path: `/${provider}/locations`,\n            method: \"GET\",\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name VoiceProviderControllerSearchVoices\n         * @summary Search Voice from Provider Voice Library.\n         * @request GET:/{provider}/voices/search\n         * @deprecated\n         * @secure\n         */\n        voiceProviderControllerSearchVoices: (provider, query, params = {}) => this.request({\n            path: `/${provider}/voices/search`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name VoiceProviderControllerSearchVoice\n         * @summary Search Voice from Provider Voice Library.\n         * @request GET:/{provider}/voice/search\n         * @secure\n         */\n        voiceProviderControllerSearchVoice: (provider, query, params = {}) => this.request({\n            path: `/${provider}/voice/search`,\n            method: \"GET\",\n            query: query,\n            secure: true,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name VoiceProviderControllerAddVoices\n         * @summary Add Shared Voice to your Provider Account.\n         * @request POST:/{provider}/voices/add\n         * @deprecated\n         * @secure\n         */\n        voiceProviderControllerAddVoices: (provider, data, params = {}) => this.request({\n            path: `/${provider}/voices/add`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name VoiceProviderControllerAddVoice\n         * @summary Add Shared Voice to your Provider Account.\n         * @request POST:/{provider}/voice/add\n         * @secure\n         */\n        voiceProviderControllerAddVoice: (provider, data, params = {}) => this.request({\n            path: `/${provider}/voice/add`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.Json,\n            format: \"json\",\n            ...params,\n        }),\n    };\n    v11Labs = {\n        /**\n         * No description\n         *\n         * @tags Providers\n         * @name VoiceProviderControllerCloneVoices\n         * @summary Clone a voice to the provider account and add to Vapi Voice Library.\n         * @request POST:/11labs/voice/clone\n         * @secure\n         */\n        voiceProviderControllerCloneVoices: (data, params = {}) => this.request({\n            path: `/11labs/voice/clone`,\n            method: \"POST\",\n            body: data,\n            secure: true,\n            type: ContentType.FormData,\n            ...params,\n        }),\n    };\n}\nexports.Api = Api;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vapi-ai/web/dist/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@vapi-ai/web/dist/client.js":
/*!**************************************************!*\
  !*** ./node_modules/@vapi-ai/web/dist/client.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.client = void 0;\nconst api_1 = __webpack_require__(/*! ./api */ \"(ssr)/./node_modules/@vapi-ai/web/dist/api.js\");\nconst api = new api_1.Api({\n    baseUrl: \"https://api.vapi.ai\",\n    baseApiParams: {\n        secure: true,\n    },\n    securityWorker: async (securityData) => {\n        if (securityData) {\n            return {\n                headers: {\n                    Authorization: `Bearer ${securityData}`,\n                },\n            };\n        }\n    },\n});\nexports.client = api;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHZhcGktYWkvd2ViL2Rpc3QvY2xpZW50LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZCxjQUFjLG1CQUFPLENBQUMsNERBQU87QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsYUFBYTtBQUMxRCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLEtBQUs7QUFDTCxDQUFDO0FBQ0QsY0FBYyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL0B2YXBpLWFpL3dlYi9kaXN0L2NsaWVudC5qcz8xMjU4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jbGllbnQgPSB2b2lkIDA7XG5jb25zdCBhcGlfMSA9IHJlcXVpcmUoXCIuL2FwaVwiKTtcbmNvbnN0IGFwaSA9IG5ldyBhcGlfMS5BcGkoe1xuICAgIGJhc2VVcmw6IFwiaHR0cHM6Ly9hcGkudmFwaS5haVwiLFxuICAgIGJhc2VBcGlQYXJhbXM6IHtcbiAgICAgICAgc2VjdXJlOiB0cnVlLFxuICAgIH0sXG4gICAgc2VjdXJpdHlXb3JrZXI6IGFzeW5jIChzZWN1cml0eURhdGEpID0+IHtcbiAgICAgICAgaWYgKHNlY3VyaXR5RGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHtzZWN1cml0eURhdGF9YCxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH0sXG59KTtcbmV4cG9ydHMuY2xpZW50ID0gYXBpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vapi-ai/web/dist/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@vapi-ai/web/dist/vapi.js":
/*!************************************************!*\
  !*** ./node_modules/@vapi-ai/web/dist/vapi.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst daily_js_1 = __importDefault(__webpack_require__(/*! @daily-co/daily-js */ \"(ssr)/./node_modules/@daily-co/daily-js/dist/daily-esm.js\"));\nconst events_1 = __importDefault(__webpack_require__(/*! events */ \"events\"));\nconst client_1 = __webpack_require__(/*! ./client */ \"(ssr)/./node_modules/@vapi-ai/web/dist/client.js\");\nasync function startAudioPlayer(player, track) {\n    player.muted = false;\n    player.autoplay = true;\n    if (track != null) {\n        player.srcObject = new MediaStream([track]);\n        await player.play();\n    }\n}\nasync function buildAudioPlayer(track, participantId) {\n    const player = document.createElement('audio');\n    player.dataset.participantId = participantId;\n    document.body.appendChild(player);\n    await startAudioPlayer(player, track);\n    return player;\n}\nfunction destroyAudioPlayer(participantId) {\n    const player = document.querySelector(`audio[data-participant-id=\"${participantId}\"]`);\n    player?.remove();\n}\nfunction subscribeToTracks(e, call, isVideoRecordingEnabled, isVideoEnabled) {\n    if (e.participant.local)\n        return;\n    call.updateParticipant(e.participant.session_id, {\n        setSubscribedTracks: {\n            audio: true,\n            video: isVideoRecordingEnabled || isVideoEnabled,\n        },\n    });\n}\nclass VapiEventEmitter extends events_1.default {\n    on(event, listener) {\n        super.on(event, listener);\n        return this;\n    }\n    once(event, listener) {\n        super.once(event, listener);\n        return this;\n    }\n    emit(event, ...args) {\n        return super.emit(event, ...args);\n    }\n    removeListener(event, listener) {\n        super.removeListener(event, listener);\n        return this;\n    }\n    removeAllListeners(event) {\n        super.removeAllListeners(event);\n        return this;\n    }\n}\nclass Vapi extends VapiEventEmitter {\n    started = false;\n    call = null;\n    speakingTimeout = null;\n    dailyCallConfig = {};\n    dailyCallObject = {};\n    hasEmittedCallEndedStatus = false;\n    constructor(apiToken, apiBaseUrl, dailyCallConfig, dailyCallObject) {\n        super();\n        client_1.client.baseUrl = apiBaseUrl ?? 'https://api.vapi.ai';\n        client_1.client.setSecurityData(apiToken);\n        this.dailyCallConfig = dailyCallConfig ?? {};\n        this.dailyCallObject = dailyCallObject ?? {};\n    }\n    cleanup() {\n        this.started = false;\n        this.hasEmittedCallEndedStatus = false;\n        this.call?.destroy();\n        this.call = null;\n        this.speakingTimeout = null;\n    }\n    isMobileDevice() {\n        if (typeof navigator === 'undefined') {\n            return false;\n        }\n        const userAgent = navigator.userAgent;\n        return /android|iphone|ipad|ipod|iemobile|blackberry|bada/i.test(userAgent.toLowerCase());\n    }\n    async sleep(ms) {\n        return new Promise((resolve) => setTimeout(resolve, ms));\n    }\n    async start(assistant, assistantOverrides, squad, workflow, workflowOverrides) {\n        const startTime = Date.now();\n        // Input validation with detailed error messages\n        if (!assistant && !squad && !workflow) {\n            const error = new Error('Assistant or Squad or Workflow must be provided.');\n            this.emit('error', {\n                type: 'validation-error',\n                stage: 'input-validation',\n                message: error.message,\n                timestamp: new Date().toISOString()\n            });\n            throw error;\n        }\n        if (this.started) {\n            this.emit('call-start-progress', {\n                stage: 'validation',\n                status: 'failed',\n                timestamp: new Date().toISOString(),\n                metadata: { reason: 'already-started' }\n            });\n            return null;\n        }\n        this.emit('call-start-progress', {\n            stage: 'initialization',\n            status: 'started',\n            timestamp: new Date().toISOString(),\n            metadata: {\n                hasAssistant: !!assistant,\n                hasSquad: !!squad,\n                hasWorkflow: !!workflow\n            }\n        });\n        this.started = true;\n        try {\n            // Stage 1: Create web call\n            this.emit('call-start-progress', {\n                stage: 'web-call-creation',\n                status: 'started',\n                timestamp: new Date().toISOString()\n            });\n            const webCallStartTime = Date.now();\n            const webCall = (await client_1.client.call.callControllerCreateWebCall({\n                assistant: typeof assistant === 'string' ? undefined : assistant,\n                assistantId: typeof assistant === 'string' ? assistant : undefined,\n                assistantOverrides,\n                squad: typeof squad === 'string' ? undefined : squad,\n                squadId: typeof squad === 'string' ? squad : undefined,\n                workflow: typeof workflow === 'string' ? undefined : workflow,\n                workflowId: typeof workflow === 'string' ? workflow : undefined,\n                workflowOverrides,\n            })).data;\n            const webCallDuration = Date.now() - webCallStartTime;\n            this.emit('call-start-progress', {\n                stage: 'web-call-creation',\n                status: 'completed',\n                duration: webCallDuration,\n                timestamp: new Date().toISOString(),\n                metadata: {\n                    callId: webCall?.id || 'unknown',\n                    videoRecordingEnabled: webCall?.artifactPlan?.videoRecordingEnabled ?? false,\n                    voiceProvider: webCall?.assistant?.voice?.provider || 'unknown'\n                }\n            });\n            if (this.call) {\n                this.emit('call-start-progress', {\n                    stage: 'daily-call-object-creation',\n                    status: 'started',\n                    timestamp: new Date().toISOString(),\n                    metadata: { action: 'cleanup-existing' }\n                });\n                this.cleanup();\n            }\n            const isVideoRecordingEnabled = webCall?.artifactPlan?.videoRecordingEnabled ?? false;\n            const isVideoEnabled = webCall?.assistant?.voice?.provider === 'tavus';\n            // Stage 2: Create Daily call object\n            this.emit('call-start-progress', {\n                stage: 'daily-call-object-creation',\n                status: 'started',\n                timestamp: new Date().toISOString(),\n                metadata: {\n                    audioSource: this.dailyCallObject.audioSource ?? true,\n                    videoSource: this.dailyCallObject.videoSource ?? isVideoRecordingEnabled,\n                    isVideoRecordingEnabled,\n                    isVideoEnabled\n                }\n            });\n            const dailyCallStartTime = Date.now();\n            try {\n                this.call = daily_js_1.default.createCallObject({\n                    audioSource: this.dailyCallObject.audioSource ?? true,\n                    videoSource: this.dailyCallObject.videoSource ?? isVideoRecordingEnabled,\n                    dailyConfig: this.dailyCallConfig,\n                });\n                const dailyCallDuration = Date.now() - dailyCallStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'daily-call-object-creation',\n                    status: 'completed',\n                    duration: dailyCallDuration,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            catch (error) {\n                const dailyCallDuration = Date.now() - dailyCallStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'daily-call-object-creation',\n                    status: 'failed',\n                    duration: dailyCallDuration,\n                    timestamp: new Date().toISOString(),\n                    metadata: { error: error?.toString() }\n                });\n                this.emit('error', {\n                    type: 'daily-call-object-creation-error',\n                    stage: 'daily-call-object-creation',\n                    error,\n                    timestamp: new Date().toISOString()\n                });\n                throw error;\n            }\n            this.call.iframe()?.style.setProperty('display', 'none');\n            this.call.on('left-meeting', () => {\n                this.emit('call-end');\n                if (!this.hasEmittedCallEndedStatus) {\n                    this.emit('message', {\n                        type: 'status-update',\n                        status: 'ended',\n                        'endedReason': 'customer-ended-call',\n                    });\n                    this.hasEmittedCallEndedStatus = true;\n                }\n                if (isVideoRecordingEnabled) {\n                    this.call?.stopRecording();\n                }\n                this.cleanup();\n            });\n            this.call.on('error', (error) => {\n                this.emit('error', error);\n                if (isVideoRecordingEnabled) {\n                    this.call?.stopRecording();\n                }\n            });\n            this.call.on('camera-error', (error) => {\n                this.emit('camera-error', error);\n            });\n            this.call.on('network-quality-change', (event) => {\n                this.emit('network-quality-change', event);\n            });\n            this.call.on('network-connection', (event) => {\n                this.emit('network-connection', event);\n            });\n            this.call.on('track-started', async (e) => {\n                if (!e || !e.participant) {\n                    return;\n                }\n                if (e.participant?.local) {\n                    return;\n                }\n                if (e.participant?.user_name !== 'Vapi Speaker') {\n                    return;\n                }\n                if (e.track.kind === 'video') {\n                    this.emit('video', e.track);\n                }\n                if (e.track.kind === 'audio') {\n                    await buildAudioPlayer(e.track, e.participant.session_id);\n                }\n                this.call?.sendAppMessage('playable');\n            });\n            this.call.on('participant-joined', (e) => {\n                if (!e || !this.call)\n                    return;\n                subscribeToTracks(e, this.call, isVideoRecordingEnabled, isVideoEnabled);\n            });\n            this.call.on('participant-updated', (e) => {\n                if (!e) {\n                    return;\n                }\n                this.emit('daily-participant-updated', e.participant);\n            });\n            this.call.on('participant-left', (e) => {\n                if (!e) {\n                    return;\n                }\n                destroyAudioPlayer(e.participant.session_id);\n            });\n            // Stage 3: Mobile device handling and permissions\n            const isMobile = this.isMobileDevice();\n            this.emit('call-start-progress', {\n                stage: 'mobile-permissions',\n                status: 'started',\n                timestamp: new Date().toISOString(),\n                metadata: { isMobile }\n            });\n            if (isMobile) {\n                const mobileWaitStartTime = Date.now();\n                await this.sleep(1000);\n                const mobileWaitDuration = Date.now() - mobileWaitStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'mobile-permissions',\n                    status: 'completed',\n                    duration: mobileWaitDuration,\n                    timestamp: new Date().toISOString(),\n                    metadata: { action: 'permissions-wait' }\n                });\n            }\n            else {\n                this.emit('call-start-progress', {\n                    stage: 'mobile-permissions',\n                    status: 'completed',\n                    timestamp: new Date().toISOString(),\n                    metadata: { action: 'skipped-not-mobile' }\n                });\n            }\n            // Stage 4: Join the call\n            this.emit('call-start-progress', {\n                stage: 'daily-call-join',\n                status: 'started',\n                timestamp: new Date().toISOString()\n            });\n            const joinStartTime = Date.now();\n            try {\n                await this.call.join({\n                    // @ts-expect-error This exists\n                    url: webCall.webCallUrl,\n                    subscribeToTracksAutomatically: false,\n                });\n                const joinDuration = Date.now() - joinStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'daily-call-join',\n                    status: 'completed',\n                    duration: joinDuration,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            catch (error) {\n                const joinDuration = Date.now() - joinStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'daily-call-join',\n                    status: 'failed',\n                    duration: joinDuration,\n                    timestamp: new Date().toISOString(),\n                    metadata: { error: error?.toString() }\n                });\n                this.emit('error', {\n                    type: 'daily-call-join-error',\n                    stage: 'daily-call-join',\n                    error,\n                    duration: joinDuration,\n                    timestamp: new Date().toISOString()\n                });\n                throw error;\n            }\n            // Stage 5: Video recording setup (if enabled)\n            if (isVideoRecordingEnabled) {\n                this.emit('call-start-progress', {\n                    stage: 'video-recording-setup',\n                    status: 'started',\n                    timestamp: new Date().toISOString()\n                });\n                const recordingRequestedTime = new Date().getTime();\n                const recordingStartTime = Date.now();\n                try {\n                    this.call.startRecording({\n                        width: 1280,\n                        height: 720,\n                        backgroundColor: '#FF1F2D3D',\n                        layout: {\n                            preset: 'default',\n                        },\n                    });\n                    const recordingSetupDuration = Date.now() - recordingStartTime;\n                    this.emit('call-start-progress', {\n                        stage: 'video-recording-setup',\n                        status: 'completed',\n                        duration: recordingSetupDuration,\n                        timestamp: new Date().toISOString()\n                    });\n                    this.call.on('recording-started', () => {\n                        const totalRecordingDelay = (new Date().getTime() - recordingRequestedTime) / 1000;\n                        this.emit('call-start-progress', {\n                            stage: 'video-recording-started',\n                            status: 'completed',\n                            timestamp: new Date().toISOString(),\n                            metadata: { delaySeconds: totalRecordingDelay }\n                        });\n                        this.send({\n                            type: 'control',\n                            control: 'say-first-message',\n                            videoRecordingStartDelaySeconds: totalRecordingDelay,\n                        });\n                    });\n                }\n                catch (error) {\n                    const recordingSetupDuration = Date.now() - recordingStartTime;\n                    this.emit('call-start-progress', {\n                        stage: 'video-recording-setup',\n                        status: 'failed',\n                        duration: recordingSetupDuration,\n                        timestamp: new Date().toISOString(),\n                        metadata: { error: error?.toString() }\n                    });\n                    this.emit('error', {\n                        type: 'video-recording-setup-error',\n                        stage: 'video-recording-setup',\n                        error,\n                        timestamp: new Date().toISOString()\n                    });\n                    // Don't throw here, video recording is optional\n                }\n            }\n            else {\n                this.emit('call-start-progress', {\n                    stage: 'video-recording-setup',\n                    status: 'completed',\n                    timestamp: new Date().toISOString(),\n                    metadata: { action: 'skipped-not-enabled' }\n                });\n            }\n            // Stage 6: Audio level observer setup\n            this.emit('call-start-progress', {\n                stage: 'audio-observer-setup',\n                status: 'started',\n                timestamp: new Date().toISOString()\n            });\n            const audioObserverStartTime = Date.now();\n            try {\n                this.call.startRemoteParticipantsAudioLevelObserver(100);\n                const audioObserverDuration = Date.now() - audioObserverStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'audio-observer-setup',\n                    status: 'completed',\n                    duration: audioObserverDuration,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            catch (error) {\n                const audioObserverDuration = Date.now() - audioObserverStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'audio-observer-setup',\n                    status: 'failed',\n                    duration: audioObserverDuration,\n                    timestamp: new Date().toISOString(),\n                    metadata: { error: error?.toString() }\n                });\n                this.emit('error', {\n                    type: 'audio-observer-setup-error',\n                    stage: 'audio-observer-setup',\n                    error,\n                    timestamp: new Date().toISOString()\n                });\n                // Don't throw here, this is non-critical\n            }\n            this.call.on('remote-participants-audio-level', (e) => {\n                if (e)\n                    this.handleRemoteParticipantsAudioLevel(e);\n            });\n            this.call.on('app-message', (e) => this.onAppMessage(e));\n            this.call.on('nonfatal-error', (e) => {\n                // https://docs.daily.co/reference/daily-js/events/meeting-events#type-audio-processor-error\n                if (e?.type === 'audio-processor-error') {\n                    this.call\n                        ?.updateInputSettings({\n                        audio: {\n                            processor: {\n                                type: 'none',\n                            },\n                        },\n                    })\n                        .then(() => {\n                        this.call?.setLocalAudio(true);\n                    });\n                }\n            });\n            // Stage 7: Audio processing setup\n            this.emit('call-start-progress', {\n                stage: 'audio-processing-setup',\n                status: 'started',\n                timestamp: new Date().toISOString()\n            });\n            const audioProcessingStartTime = Date.now();\n            try {\n                this.call.updateInputSettings({\n                    audio: {\n                        processor: {\n                            type: 'noise-cancellation',\n                        },\n                    },\n                });\n                const audioProcessingDuration = Date.now() - audioProcessingStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'audio-processing-setup',\n                    status: 'completed',\n                    duration: audioProcessingDuration,\n                    timestamp: new Date().toISOString()\n                });\n            }\n            catch (error) {\n                const audioProcessingDuration = Date.now() - audioProcessingStartTime;\n                this.emit('call-start-progress', {\n                    stage: 'audio-processing-setup',\n                    status: 'failed',\n                    duration: audioProcessingDuration,\n                    timestamp: new Date().toISOString(),\n                    metadata: { error: error?.toString() }\n                });\n                this.emit('error', {\n                    type: 'audio-processing-setup-error',\n                    stage: 'audio-processing-setup',\n                    error,\n                    timestamp: new Date().toISOString()\n                });\n                // Don't throw here, this is non-critical\n            }\n            const totalDuration = Date.now() - startTime;\n            this.emit('call-start-success', {\n                totalDuration,\n                callId: webCall?.id || 'unknown',\n                timestamp: new Date().toISOString()\n            });\n            return webCall;\n        }\n        catch (e) {\n            const totalDuration = Date.now() - startTime;\n            this.emit('call-start-failed', {\n                stage: 'unknown',\n                totalDuration,\n                error: e?.toString() || 'Unknown error occurred',\n                errorStack: e instanceof Error ? e.stack : 'No stack trace available',\n                timestamp: new Date().toISOString(),\n                context: {\n                    hasAssistant: !!assistant,\n                    hasSquad: !!squad,\n                    hasWorkflow: !!workflow,\n                    isMobile: this.isMobileDevice()\n                }\n            });\n            // Also emit the generic error event for backward compatibility\n            this.emit('error', {\n                type: 'start-method-error',\n                stage: 'unknown',\n                error: e,\n                totalDuration,\n                timestamp: new Date().toISOString(),\n                context: {\n                    hasAssistant: !!assistant,\n                    hasSquad: !!squad,\n                    hasWorkflow: !!workflow,\n                    isMobile: this.isMobileDevice()\n                }\n            });\n            this.cleanup();\n            return null;\n        }\n    }\n    onAppMessage(e) {\n        if (!e) {\n            return;\n        }\n        try {\n            if (e.data === 'listening') {\n                return this.emit('call-start');\n            }\n            else {\n                try {\n                    const parsedMessage = JSON.parse(e.data);\n                    this.emit('message', parsedMessage);\n                    if (parsedMessage && 'type' in parsedMessage && 'status' in parsedMessage && parsedMessage.type === 'status-update' && parsedMessage.status === 'ended') {\n                        this.hasEmittedCallEndedStatus = true;\n                    }\n                }\n                catch (parseError) {\n                    console.log('Error parsing message data: ', parseError);\n                }\n            }\n        }\n        catch (e) {\n            console.error(e);\n        }\n    }\n    handleRemoteParticipantsAudioLevel(e) {\n        const speechLevel = Object.values(e.participantsAudioLevel).reduce((a, b) => a + b, 0);\n        this.emit('volume-level', Math.min(1, speechLevel / 0.15));\n        const isSpeaking = speechLevel > 0.01;\n        if (!isSpeaking) {\n            return;\n        }\n        if (this.speakingTimeout) {\n            clearTimeout(this.speakingTimeout);\n            this.speakingTimeout = null;\n        }\n        else {\n            this.emit('speech-start');\n        }\n        this.speakingTimeout = setTimeout(() => {\n            this.emit('speech-end');\n            this.speakingTimeout = null;\n        }, 1000);\n    }\n    stop() {\n        this.started = false;\n        this.call?.destroy();\n        this.call = null;\n    }\n    send(message) {\n        this.call?.sendAppMessage(JSON.stringify(message));\n    }\n    setMuted(mute) {\n        if (!this.call) {\n            throw new Error('Call object is not available.');\n        }\n        this.call.setLocalAudio(!mute);\n    }\n    isMuted() {\n        if (!this.call) {\n            return false;\n        }\n        return this.call.localAudio() === false;\n    }\n    say(message, endCallAfterSpoken, interruptionsEnabled, interruptAssistantEnabled) {\n        this.send({\n            type: 'say',\n            message,\n            endCallAfterSpoken,\n            interruptionsEnabled: interruptionsEnabled ?? false,\n            interruptAssistantEnabled: interruptAssistantEnabled ?? false,\n        });\n    }\n    setInputDevicesAsync(options) {\n        this.call?.setInputDevicesAsync(options);\n    }\n    async increaseMicLevel(gain) {\n        if (!this.call) {\n            throw new Error('Call object is not available.');\n        }\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n            const audioContext = new AudioContext();\n            const source = audioContext.createMediaStreamSource(stream);\n            const gainNode = audioContext.createGain();\n            gainNode.gain.value = gain;\n            source.connect(gainNode);\n            const destination = audioContext.createMediaStreamDestination();\n            gainNode.connect(destination);\n            const [boostedTrack] = destination.stream.getAudioTracks();\n            await this.call.setInputDevicesAsync({ audioSource: boostedTrack });\n        }\n        catch (error) {\n            console.error(\"Error adjusting microphone level:\", error);\n        }\n    }\n    setOutputDeviceAsync(options) {\n        this.call?.setOutputDeviceAsync(options);\n    }\n    getDailyCallObject() {\n        return this.call;\n    }\n    startScreenSharing(displayMediaOptions, screenVideoSendSettings) {\n        this.call?.startScreenShare({\n            displayMediaOptions,\n            screenVideoSendSettings,\n        });\n    }\n    stopScreenSharing() {\n        this.call?.stopScreenShare();\n    }\n}\nexports[\"default\"] = Vapi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHZhcGktYWkvd2ViL2Rpc3QvdmFwaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1DQUFtQyxtQkFBTyxDQUFDLHFGQUFvQjtBQUMvRCxpQ0FBaUMsbUJBQU8sQ0FBQyxzQkFBUTtBQUNqRCxpQkFBaUIsbUJBQU8sQ0FBQyxrRUFBVTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdFQUF3RSxjQUFjO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEMseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DO0FBQ3BDLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEMsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RUFBdUUsYUFBYTtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELDJCQUEyQjtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9ub2RlX21vZHVsZXMvQHZhcGktYWkvd2ViL2Rpc3QvdmFwaS5qcz84ZDZmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgZGFpbHlfanNfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiQGRhaWx5LWNvL2RhaWx5LWpzXCIpKTtcbmNvbnN0IGV2ZW50c18xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJldmVudHNcIikpO1xuY29uc3QgY2xpZW50XzEgPSByZXF1aXJlKFwiLi9jbGllbnRcIik7XG5hc3luYyBmdW5jdGlvbiBzdGFydEF1ZGlvUGxheWVyKHBsYXllciwgdHJhY2spIHtcbiAgICBwbGF5ZXIubXV0ZWQgPSBmYWxzZTtcbiAgICBwbGF5ZXIuYXV0b3BsYXkgPSB0cnVlO1xuICAgIGlmICh0cmFjayAhPSBudWxsKSB7XG4gICAgICAgIHBsYXllci5zcmNPYmplY3QgPSBuZXcgTWVkaWFTdHJlYW0oW3RyYWNrXSk7XG4gICAgICAgIGF3YWl0IHBsYXllci5wbGF5KCk7XG4gICAgfVxufVxuYXN5bmMgZnVuY3Rpb24gYnVpbGRBdWRpb1BsYXllcih0cmFjaywgcGFydGljaXBhbnRJZCkge1xuICAgIGNvbnN0IHBsYXllciA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2F1ZGlvJyk7XG4gICAgcGxheWVyLmRhdGFzZXQucGFydGljaXBhbnRJZCA9IHBhcnRpY2lwYW50SWQ7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChwbGF5ZXIpO1xuICAgIGF3YWl0IHN0YXJ0QXVkaW9QbGF5ZXIocGxheWVyLCB0cmFjayk7XG4gICAgcmV0dXJuIHBsYXllcjtcbn1cbmZ1bmN0aW9uIGRlc3Ryb3lBdWRpb1BsYXllcihwYXJ0aWNpcGFudElkKSB7XG4gICAgY29uc3QgcGxheWVyID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihgYXVkaW9bZGF0YS1wYXJ0aWNpcGFudC1pZD1cIiR7cGFydGljaXBhbnRJZH1cIl1gKTtcbiAgICBwbGF5ZXI/LnJlbW92ZSgpO1xufVxuZnVuY3Rpb24gc3Vic2NyaWJlVG9UcmFja3MoZSwgY2FsbCwgaXNWaWRlb1JlY29yZGluZ0VuYWJsZWQsIGlzVmlkZW9FbmFibGVkKSB7XG4gICAgaWYgKGUucGFydGljaXBhbnQubG9jYWwpXG4gICAgICAgIHJldHVybjtcbiAgICBjYWxsLnVwZGF0ZVBhcnRpY2lwYW50KGUucGFydGljaXBhbnQuc2Vzc2lvbl9pZCwge1xuICAgICAgICBzZXRTdWJzY3JpYmVkVHJhY2tzOiB7XG4gICAgICAgICAgICBhdWRpbzogdHJ1ZSxcbiAgICAgICAgICAgIHZpZGVvOiBpc1ZpZGVvUmVjb3JkaW5nRW5hYmxlZCB8fCBpc1ZpZGVvRW5hYmxlZCxcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbmNsYXNzIFZhcGlFdmVudEVtaXR0ZXIgZXh0ZW5kcyBldmVudHNfMS5kZWZhdWx0IHtcbiAgICBvbihldmVudCwgbGlzdGVuZXIpIHtcbiAgICAgICAgc3VwZXIub24oZXZlbnQsIGxpc3RlbmVyKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIG9uY2UoZXZlbnQsIGxpc3RlbmVyKSB7XG4gICAgICAgIHN1cGVyLm9uY2UoZXZlbnQsIGxpc3RlbmVyKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGVtaXQoZXZlbnQsIC4uLmFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIHN1cGVyLmVtaXQoZXZlbnQsIC4uLmFyZ3MpO1xuICAgIH1cbiAgICByZW1vdmVMaXN0ZW5lcihldmVudCwgbGlzdGVuZXIpIHtcbiAgICAgICAgc3VwZXIucmVtb3ZlTGlzdGVuZXIoZXZlbnQsIGxpc3RlbmVyKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHJlbW92ZUFsbExpc3RlbmVycyhldmVudCkge1xuICAgICAgICBzdXBlci5yZW1vdmVBbGxMaXN0ZW5lcnMoZXZlbnQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG59XG5jbGFzcyBWYXBpIGV4dGVuZHMgVmFwaUV2ZW50RW1pdHRlciB7XG4gICAgc3RhcnRlZCA9IGZhbHNlO1xuICAgIGNhbGwgPSBudWxsO1xuICAgIHNwZWFraW5nVGltZW91dCA9IG51bGw7XG4gICAgZGFpbHlDYWxsQ29uZmlnID0ge307XG4gICAgZGFpbHlDYWxsT2JqZWN0ID0ge307XG4gICAgaGFzRW1pdHRlZENhbGxFbmRlZFN0YXR1cyA9IGZhbHNlO1xuICAgIGNvbnN0cnVjdG9yKGFwaVRva2VuLCBhcGlCYXNlVXJsLCBkYWlseUNhbGxDb25maWcsIGRhaWx5Q2FsbE9iamVjdCkge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICBjbGllbnRfMS5jbGllbnQuYmFzZVVybCA9IGFwaUJhc2VVcmwgPz8gJ2h0dHBzOi8vYXBpLnZhcGkuYWknO1xuICAgICAgICBjbGllbnRfMS5jbGllbnQuc2V0U2VjdXJpdHlEYXRhKGFwaVRva2VuKTtcbiAgICAgICAgdGhpcy5kYWlseUNhbGxDb25maWcgPSBkYWlseUNhbGxDb25maWcgPz8ge307XG4gICAgICAgIHRoaXMuZGFpbHlDYWxsT2JqZWN0ID0gZGFpbHlDYWxsT2JqZWN0ID8/IHt9O1xuICAgIH1cbiAgICBjbGVhbnVwKCkge1xuICAgICAgICB0aGlzLnN0YXJ0ZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5oYXNFbWl0dGVkQ2FsbEVuZGVkU3RhdHVzID0gZmFsc2U7XG4gICAgICAgIHRoaXMuY2FsbD8uZGVzdHJveSgpO1xuICAgICAgICB0aGlzLmNhbGwgPSBudWxsO1xuICAgICAgICB0aGlzLnNwZWFraW5nVGltZW91dCA9IG51bGw7XG4gICAgfVxuICAgIGlzTW9iaWxlRGV2aWNlKCkge1xuICAgICAgICBpZiAodHlwZW9mIG5hdmlnYXRvciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1c2VyQWdlbnQgPSBuYXZpZ2F0b3IudXNlckFnZW50O1xuICAgICAgICByZXR1cm4gL2FuZHJvaWR8aXBob25lfGlwYWR8aXBvZHxpZW1vYmlsZXxibGFja2JlcnJ5fGJhZGEvaS50ZXN0KHVzZXJBZ2VudC50b0xvd2VyQ2FzZSgpKTtcbiAgICB9XG4gICAgYXN5bmMgc2xlZXAobXMpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIG1zKSk7XG4gICAgfVxuICAgIGFzeW5jIHN0YXJ0KGFzc2lzdGFudCwgYXNzaXN0YW50T3ZlcnJpZGVzLCBzcXVhZCwgd29ya2Zsb3csIHdvcmtmbG93T3ZlcnJpZGVzKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgICAgIC8vIElucHV0IHZhbGlkYXRpb24gd2l0aCBkZXRhaWxlZCBlcnJvciBtZXNzYWdlc1xuICAgICAgICBpZiAoIWFzc2lzdGFudCAmJiAhc3F1YWQgJiYgIXdvcmtmbG93KSB7XG4gICAgICAgICAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcignQXNzaXN0YW50IG9yIFNxdWFkIG9yIFdvcmtmbG93IG11c3QgYmUgcHJvdmlkZWQuJyk7XG4gICAgICAgICAgICB0aGlzLmVtaXQoJ2Vycm9yJywge1xuICAgICAgICAgICAgICAgIHR5cGU6ICd2YWxpZGF0aW9uLWVycm9yJyxcbiAgICAgICAgICAgICAgICBzdGFnZTogJ2lucHV0LXZhbGlkYXRpb24nLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuc3RhcnRlZCkge1xuICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgIHN0YWdlOiAndmFsaWRhdGlvbicsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnZmFpbGVkJyxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyByZWFzb246ICdhbHJlYWR5LXN0YXJ0ZWQnIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgc3RhZ2U6ICdpbml0aWFsaXphdGlvbicsXG4gICAgICAgICAgICBzdGF0dXM6ICdzdGFydGVkJyxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgICAgICAgICBoYXNBc3Npc3RhbnQ6ICEhYXNzaXN0YW50LFxuICAgICAgICAgICAgICAgIGhhc1NxdWFkOiAhIXNxdWFkLFxuICAgICAgICAgICAgICAgIGhhc1dvcmtmbG93OiAhIXdvcmtmbG93XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLnN0YXJ0ZWQgPSB0cnVlO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gU3RhZ2UgMTogQ3JlYXRlIHdlYiBjYWxsXG4gICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtcHJvZ3Jlc3MnLCB7XG4gICAgICAgICAgICAgICAgc3RhZ2U6ICd3ZWItY2FsbC1jcmVhdGlvbicsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnc3RhcnRlZCcsXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc3Qgd2ViQ2FsbFN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgICAgICAgICBjb25zdCB3ZWJDYWxsID0gKGF3YWl0IGNsaWVudF8xLmNsaWVudC5jYWxsLmNhbGxDb250cm9sbGVyQ3JlYXRlV2ViQ2FsbCh7XG4gICAgICAgICAgICAgICAgYXNzaXN0YW50OiB0eXBlb2YgYXNzaXN0YW50ID09PSAnc3RyaW5nJyA/IHVuZGVmaW5lZCA6IGFzc2lzdGFudCxcbiAgICAgICAgICAgICAgICBhc3Npc3RhbnRJZDogdHlwZW9mIGFzc2lzdGFudCA9PT0gJ3N0cmluZycgPyBhc3Npc3RhbnQgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgYXNzaXN0YW50T3ZlcnJpZGVzLFxuICAgICAgICAgICAgICAgIHNxdWFkOiB0eXBlb2Ygc3F1YWQgPT09ICdzdHJpbmcnID8gdW5kZWZpbmVkIDogc3F1YWQsXG4gICAgICAgICAgICAgICAgc3F1YWRJZDogdHlwZW9mIHNxdWFkID09PSAnc3RyaW5nJyA/IHNxdWFkIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgIHdvcmtmbG93OiB0eXBlb2Ygd29ya2Zsb3cgPT09ICdzdHJpbmcnID8gdW5kZWZpbmVkIDogd29ya2Zsb3csXG4gICAgICAgICAgICAgICAgd29ya2Zsb3dJZDogdHlwZW9mIHdvcmtmbG93ID09PSAnc3RyaW5nJyA/IHdvcmtmbG93IDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgIHdvcmtmbG93T3ZlcnJpZGVzLFxuICAgICAgICAgICAgfSkpLmRhdGE7XG4gICAgICAgICAgICBjb25zdCB3ZWJDYWxsRHVyYXRpb24gPSBEYXRlLm5vdygpIC0gd2ViQ2FsbFN0YXJ0VGltZTtcbiAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FsbC1zdGFydC1wcm9ncmVzcycsIHtcbiAgICAgICAgICAgICAgICBzdGFnZTogJ3dlYi1jYWxsLWNyZWF0aW9uJyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiB3ZWJDYWxsRHVyYXRpb24sXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgICAgICAgICAgICAgY2FsbElkOiB3ZWJDYWxsPy5pZCB8fCAndW5rbm93bicsXG4gICAgICAgICAgICAgICAgICAgIHZpZGVvUmVjb3JkaW5nRW5hYmxlZDogd2ViQ2FsbD8uYXJ0aWZhY3RQbGFuPy52aWRlb1JlY29yZGluZ0VuYWJsZWQgPz8gZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIHZvaWNlUHJvdmlkZXI6IHdlYkNhbGw/LmFzc2lzdGFudD8udm9pY2U/LnByb3ZpZGVyIHx8ICd1bmtub3duJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHRoaXMuY2FsbCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FsbC1zdGFydC1wcm9ncmVzcycsIHtcbiAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICdkYWlseS1jYWxsLW9iamVjdC1jcmVhdGlvbicsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ3N0YXJ0ZWQnLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICAgICAgbWV0YWRhdGE6IHsgYWN0aW9uOiAnY2xlYW51cC1leGlzdGluZycgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHRoaXMuY2xlYW51cCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaXNWaWRlb1JlY29yZGluZ0VuYWJsZWQgPSB3ZWJDYWxsPy5hcnRpZmFjdFBsYW4/LnZpZGVvUmVjb3JkaW5nRW5hYmxlZCA/PyBmYWxzZTtcbiAgICAgICAgICAgIGNvbnN0IGlzVmlkZW9FbmFibGVkID0gd2ViQ2FsbD8uYXNzaXN0YW50Py52b2ljZT8ucHJvdmlkZXIgPT09ICd0YXZ1cyc7XG4gICAgICAgICAgICAvLyBTdGFnZSAyOiBDcmVhdGUgRGFpbHkgY2FsbCBvYmplY3RcbiAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FsbC1zdGFydC1wcm9ncmVzcycsIHtcbiAgICAgICAgICAgICAgICBzdGFnZTogJ2RhaWx5LWNhbGwtb2JqZWN0LWNyZWF0aW9uJyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdzdGFydGVkJyxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICBtZXRhZGF0YToge1xuICAgICAgICAgICAgICAgICAgICBhdWRpb1NvdXJjZTogdGhpcy5kYWlseUNhbGxPYmplY3QuYXVkaW9Tb3VyY2UgPz8gdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgdmlkZW9Tb3VyY2U6IHRoaXMuZGFpbHlDYWxsT2JqZWN0LnZpZGVvU291cmNlID8/IGlzVmlkZW9SZWNvcmRpbmdFbmFibGVkLFxuICAgICAgICAgICAgICAgICAgICBpc1ZpZGVvUmVjb3JkaW5nRW5hYmxlZCxcbiAgICAgICAgICAgICAgICAgICAgaXNWaWRlb0VuYWJsZWRcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IGRhaWx5Q2FsbFN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHRoaXMuY2FsbCA9IGRhaWx5X2pzXzEuZGVmYXVsdC5jcmVhdGVDYWxsT2JqZWN0KHtcbiAgICAgICAgICAgICAgICAgICAgYXVkaW9Tb3VyY2U6IHRoaXMuZGFpbHlDYWxsT2JqZWN0LmF1ZGlvU291cmNlID8/IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIHZpZGVvU291cmNlOiB0aGlzLmRhaWx5Q2FsbE9iamVjdC52aWRlb1NvdXJjZSA/PyBpc1ZpZGVvUmVjb3JkaW5nRW5hYmxlZCxcbiAgICAgICAgICAgICAgICAgICAgZGFpbHlDb25maWc6IHRoaXMuZGFpbHlDYWxsQ29uZmlnLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhaWx5Q2FsbER1cmF0aW9uID0gRGF0ZS5ub3coKSAtIGRhaWx5Q2FsbFN0YXJ0VGltZTtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtcHJvZ3Jlc3MnLCB7XG4gICAgICAgICAgICAgICAgICAgIHN0YWdlOiAnZGFpbHktY2FsbC1vYmplY3QtY3JlYXRpb24nLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogZGFpbHlDYWxsRHVyYXRpb24sXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYWlseUNhbGxEdXJhdGlvbiA9IERhdGUubm93KCkgLSBkYWlseUNhbGxTdGFydFRpbWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2RhaWx5LWNhbGwtb2JqZWN0LWNyZWF0aW9uJyxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnZmFpbGVkJyxcbiAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IGRhaWx5Q2FsbER1cmF0aW9uLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICAgICAgbWV0YWRhdGE6IHsgZXJyb3I6IGVycm9yPy50b1N0cmluZygpIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2Vycm9yJywge1xuICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZGFpbHktY2FsbC1vYmplY3QtY3JlYXRpb24tZXJyb3InLFxuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2RhaWx5LWNhbGwtb2JqZWN0LWNyZWF0aW9uJyxcbiAgICAgICAgICAgICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhbGwuaWZyYW1lKCk/LnN0eWxlLnNldFByb3BlcnR5KCdkaXNwbGF5JywgJ25vbmUnKTtcbiAgICAgICAgICAgIHRoaXMuY2FsbC5vbignbGVmdC1tZWV0aW5nJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FsbC1lbmQnKTtcbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuaGFzRW1pdHRlZENhbGxFbmRlZFN0YXR1cykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ21lc3NhZ2UnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnc3RhdHVzLXVwZGF0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdlbmRlZCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAnZW5kZWRSZWFzb24nOiAnY3VzdG9tZXItZW5kZWQtY2FsbCcsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmhhc0VtaXR0ZWRDYWxsRW5kZWRTdGF0dXMgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoaXNWaWRlb1JlY29yZGluZ0VuYWJsZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYWxsPy5zdG9wUmVjb3JkaW5nKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuY2xlYW51cCgpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLmNhbGwub24oJ2Vycm9yJywgKGVycm9yKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIGVycm9yKTtcbiAgICAgICAgICAgICAgICBpZiAoaXNWaWRlb1JlY29yZGluZ0VuYWJsZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYWxsPy5zdG9wUmVjb3JkaW5nKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLmNhbGwub24oJ2NhbWVyYS1lcnJvcicsIChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FtZXJhLWVycm9yJywgZXJyb3IpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLmNhbGwub24oJ25ldHdvcmstcXVhbGl0eS1jaGFuZ2UnLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ25ldHdvcmstcXVhbGl0eS1jaGFuZ2UnLCBldmVudCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuY2FsbC5vbignbmV0d29yay1jb25uZWN0aW9uJywgKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCduZXR3b3JrLWNvbm5lY3Rpb24nLCBldmVudCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuY2FsbC5vbigndHJhY2stc3RhcnRlZCcsIGFzeW5jIChlKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFlIHx8ICFlLnBhcnRpY2lwYW50KSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGUucGFydGljaXBhbnQ/LmxvY2FsKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGUucGFydGljaXBhbnQ/LnVzZXJfbmFtZSAhPT0gJ1ZhcGkgU3BlYWtlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoZS50cmFjay5raW5kID09PSAndmlkZW8nKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgndmlkZW8nLCBlLnRyYWNrKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGUudHJhY2sua2luZCA9PT0gJ2F1ZGlvJykge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCBidWlsZEF1ZGlvUGxheWVyKGUudHJhY2ssIGUucGFydGljaXBhbnQuc2Vzc2lvbl9pZCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuY2FsbD8uc2VuZEFwcE1lc3NhZ2UoJ3BsYXlhYmxlJyk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuY2FsbC5vbigncGFydGljaXBhbnQtam9pbmVkJywgKGUpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIWUgfHwgIXRoaXMuY2FsbClcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIHN1YnNjcmliZVRvVHJhY2tzKGUsIHRoaXMuY2FsbCwgaXNWaWRlb1JlY29yZGluZ0VuYWJsZWQsIGlzVmlkZW9FbmFibGVkKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy5jYWxsLm9uKCdwYXJ0aWNpcGFudC11cGRhdGVkJywgKGUpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIWUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2RhaWx5LXBhcnRpY2lwYW50LXVwZGF0ZWQnLCBlLnBhcnRpY2lwYW50KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy5jYWxsLm9uKCdwYXJ0aWNpcGFudC1sZWZ0JywgKGUpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIWUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBkZXN0cm95QXVkaW9QbGF5ZXIoZS5wYXJ0aWNpcGFudC5zZXNzaW9uX2lkKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgLy8gU3RhZ2UgMzogTW9iaWxlIGRldmljZSBoYW5kbGluZyBhbmQgcGVybWlzc2lvbnNcbiAgICAgICAgICAgIGNvbnN0IGlzTW9iaWxlID0gdGhpcy5pc01vYmlsZURldmljZSgpO1xuICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgIHN0YWdlOiAnbW9iaWxlLXBlcm1pc3Npb25zJyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdzdGFydGVkJyxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBpc01vYmlsZSB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChpc01vYmlsZSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IG1vYmlsZVdhaXRTdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgICAgICAgICAgICAgIGF3YWl0IHRoaXMuc2xlZXAoMTAwMCk7XG4gICAgICAgICAgICAgICAgY29uc3QgbW9iaWxlV2FpdER1cmF0aW9uID0gRGF0ZS5ub3coKSAtIG1vYmlsZVdhaXRTdGFydFRpbWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ21vYmlsZS1wZXJtaXNzaW9ucycsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiBtb2JpbGVXYWl0RHVyYXRpb24sXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBhY3Rpb246ICdwZXJtaXNzaW9ucy13YWl0JyB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtcHJvZ3Jlc3MnLCB7XG4gICAgICAgICAgICAgICAgICAgIHN0YWdlOiAnbW9iaWxlLXBlcm1pc3Npb25zJyxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgICAgIG1ldGFkYXRhOiB7IGFjdGlvbjogJ3NraXBwZWQtbm90LW1vYmlsZScgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gU3RhZ2UgNDogSm9pbiB0aGUgY2FsbFxuICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgIHN0YWdlOiAnZGFpbHktY2FsbC1qb2luJyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdzdGFydGVkJyxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zdCBqb2luU3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgdGhpcy5jYWxsLmpvaW4oe1xuICAgICAgICAgICAgICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIFRoaXMgZXhpc3RzXG4gICAgICAgICAgICAgICAgICAgIHVybDogd2ViQ2FsbC53ZWJDYWxsVXJsLFxuICAgICAgICAgICAgICAgICAgICBzdWJzY3JpYmVUb1RyYWNrc0F1dG9tYXRpY2FsbHk6IGZhbHNlLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGpvaW5EdXJhdGlvbiA9IERhdGUubm93KCkgLSBqb2luU3RhcnRUaW1lO1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FsbC1zdGFydC1wcm9ncmVzcycsIHtcbiAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICdkYWlseS1jYWxsLWpvaW4nLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogam9pbkR1cmF0aW9uLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgam9pbkR1cmF0aW9uID0gRGF0ZS5ub3coKSAtIGpvaW5TdGFydFRpbWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2RhaWx5LWNhbGwtam9pbicsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ2ZhaWxlZCcsXG4gICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiBqb2luRHVyYXRpb24sXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBlcnJvcjogZXJyb3I/LnRvU3RyaW5nKCkgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCB7XG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICdkYWlseS1jYWxsLWpvaW4tZXJyb3InLFxuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2RhaWx5LWNhbGwtam9pbicsXG4gICAgICAgICAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogam9pbkR1cmF0aW9uLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gU3RhZ2UgNTogVmlkZW8gcmVjb3JkaW5nIHNldHVwIChpZiBlbmFibGVkKVxuICAgICAgICAgICAgaWYgKGlzVmlkZW9SZWNvcmRpbmdFbmFibGVkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ3ZpZGVvLXJlY29yZGluZy1zZXR1cCcsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ3N0YXJ0ZWQnLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlY29yZGluZ1JlcXVlc3RlZFRpbWUgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTtcbiAgICAgICAgICAgICAgICBjb25zdCByZWNvcmRpbmdTdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2FsbC5zdGFydFJlY29yZGluZyh7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTI4MCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogNzIwLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI0ZGMUYyRDNEJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxheW91dDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByZXNldDogJ2RlZmF1bHQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlY29yZGluZ1NldHVwRHVyYXRpb24gPSBEYXRlLm5vdygpIC0gcmVjb3JkaW5nU3RhcnRUaW1lO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtcHJvZ3Jlc3MnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ3ZpZGVvLXJlY29yZGluZy1zZXR1cCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IHJlY29yZGluZ1NldHVwRHVyYXRpb24sXG4gICAgICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYWxsLm9uKCdyZWNvcmRpbmctc3RhcnRlZCcsICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvdGFsUmVjb3JkaW5nRGVsYXkgPSAobmV3IERhdGUoKS5nZXRUaW1lKCkgLSByZWNvcmRpbmdSZXF1ZXN0ZWRUaW1lKSAvIDEwMDA7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtcHJvZ3Jlc3MnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICd2aWRlby1yZWNvcmRpbmctc3RhcnRlZCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBkZWxheVNlY29uZHM6IHRvdGFsUmVjb3JkaW5nRGVsYXkgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNlbmQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdjb250cm9sJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sOiAnc2F5LWZpcnN0LW1lc3NhZ2UnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZGVvUmVjb3JkaW5nU3RhcnREZWxheVNlY29uZHM6IHRvdGFsUmVjb3JkaW5nRGVsYXksXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZWNvcmRpbmdTZXR1cER1cmF0aW9uID0gRGF0ZS5ub3coKSAtIHJlY29yZGluZ1N0YXJ0VGltZTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICd2aWRlby1yZWNvcmRpbmctc2V0dXAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnZmFpbGVkJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiByZWNvcmRpbmdTZXR1cER1cmF0aW9uLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBlcnJvcjogZXJyb3I/LnRvU3RyaW5nKCkgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICd2aWRlby1yZWNvcmRpbmctc2V0dXAtZXJyb3InLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICd2aWRlby1yZWNvcmRpbmctc2V0dXAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgLy8gRG9uJ3QgdGhyb3cgaGVyZSwgdmlkZW8gcmVjb3JkaW5nIGlzIG9wdGlvbmFsXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ3ZpZGVvLXJlY29yZGluZy1zZXR1cCcsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBhY3Rpb246ICdza2lwcGVkLW5vdC1lbmFibGVkJyB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBTdGFnZSA2OiBBdWRpbyBsZXZlbCBvYnNlcnZlciBzZXR1cFxuICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgIHN0YWdlOiAnYXVkaW8tb2JzZXJ2ZXItc2V0dXAnLFxuICAgICAgICAgICAgICAgIHN0YXR1czogJ3N0YXJ0ZWQnLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IGF1ZGlvT2JzZXJ2ZXJTdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICB0aGlzLmNhbGwuc3RhcnRSZW1vdGVQYXJ0aWNpcGFudHNBdWRpb0xldmVsT2JzZXJ2ZXIoMTAwKTtcbiAgICAgICAgICAgICAgICBjb25zdCBhdWRpb09ic2VydmVyRHVyYXRpb24gPSBEYXRlLm5vdygpIC0gYXVkaW9PYnNlcnZlclN0YXJ0VGltZTtcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtcHJvZ3Jlc3MnLCB7XG4gICAgICAgICAgICAgICAgICAgIHN0YWdlOiAnYXVkaW8tb2JzZXJ2ZXItc2V0dXAnLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogYXVkaW9PYnNlcnZlckR1cmF0aW9uLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYXVkaW9PYnNlcnZlckR1cmF0aW9uID0gRGF0ZS5ub3coKSAtIGF1ZGlvT2JzZXJ2ZXJTdGFydFRpbWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2F1ZGlvLW9ic2VydmVyLXNldHVwJyxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiAnZmFpbGVkJyxcbiAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IGF1ZGlvT2JzZXJ2ZXJEdXJhdGlvbixcbiAgICAgICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgICAgIG1ldGFkYXRhOiB7IGVycm9yOiBlcnJvcj8udG9TdHJpbmcoKSB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIHtcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2F1ZGlvLW9ic2VydmVyLXNldHVwLWVycm9yJyxcbiAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICdhdWRpby1vYnNlcnZlci1zZXR1cCcsXG4gICAgICAgICAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIC8vIERvbid0IHRocm93IGhlcmUsIHRoaXMgaXMgbm9uLWNyaXRpY2FsXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhbGwub24oJ3JlbW90ZS1wYXJ0aWNpcGFudHMtYXVkaW8tbGV2ZWwnLCAoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChlKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZVJlbW90ZVBhcnRpY2lwYW50c0F1ZGlvTGV2ZWwoZSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuY2FsbC5vbignYXBwLW1lc3NhZ2UnLCAoZSkgPT4gdGhpcy5vbkFwcE1lc3NhZ2UoZSkpO1xuICAgICAgICAgICAgdGhpcy5jYWxsLm9uKCdub25mYXRhbC1lcnJvcicsIChlKSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gaHR0cHM6Ly9kb2NzLmRhaWx5LmNvL3JlZmVyZW5jZS9kYWlseS1qcy9ldmVudHMvbWVldGluZy1ldmVudHMjdHlwZS1hdWRpby1wcm9jZXNzb3ItZXJyb3JcbiAgICAgICAgICAgICAgICBpZiAoZT8udHlwZSA9PT0gJ2F1ZGlvLXByb2Nlc3Nvci1lcnJvcicpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYWxsXG4gICAgICAgICAgICAgICAgICAgICAgICA/LnVwZGF0ZUlucHV0U2V0dGluZ3Moe1xuICAgICAgICAgICAgICAgICAgICAgICAgYXVkaW86IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9jZXNzb3I6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jYWxsPy5zZXRMb2NhbEF1ZGlvKHRydWUpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIFN0YWdlIDc6IEF1ZGlvIHByb2Nlc3Npbmcgc2V0dXBcbiAgICAgICAgICAgIHRoaXMuZW1pdCgnY2FsbC1zdGFydC1wcm9ncmVzcycsIHtcbiAgICAgICAgICAgICAgICBzdGFnZTogJ2F1ZGlvLXByb2Nlc3Npbmctc2V0dXAnLFxuICAgICAgICAgICAgICAgIHN0YXR1czogJ3N0YXJ0ZWQnLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IGF1ZGlvUHJvY2Vzc2luZ1N0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHRoaXMuY2FsbC51cGRhdGVJbnB1dFNldHRpbmdzKHtcbiAgICAgICAgICAgICAgICAgICAgYXVkaW86IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2Nlc3Nvcjoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdub2lzZS1jYW5jZWxsYXRpb24nLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCBhdWRpb1Byb2Nlc3NpbmdEdXJhdGlvbiA9IERhdGUubm93KCkgLSBhdWRpb1Byb2Nlc3NpbmdTdGFydFRpbWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2F1ZGlvLXByb2Nlc3Npbmctc2V0dXAnLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogYXVkaW9Qcm9jZXNzaW5nRHVyYXRpb24sXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBhdWRpb1Byb2Nlc3NpbmdEdXJhdGlvbiA9IERhdGUubm93KCkgLSBhdWRpb1Byb2Nlc3NpbmdTdGFydFRpbWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXByb2dyZXNzJywge1xuICAgICAgICAgICAgICAgICAgICBzdGFnZTogJ2F1ZGlvLXByb2Nlc3Npbmctc2V0dXAnLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdmYWlsZWQnLFxuICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogYXVkaW9Qcm9jZXNzaW5nRHVyYXRpb24sXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICBtZXRhZGF0YTogeyBlcnJvcjogZXJyb3I/LnRvU3RyaW5nKCkgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCB7XG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICdhdWRpby1wcm9jZXNzaW5nLXNldHVwLWVycm9yJyxcbiAgICAgICAgICAgICAgICAgICAgc3RhZ2U6ICdhdWRpby1wcm9jZXNzaW5nLXNldHVwJyxcbiAgICAgICAgICAgICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gRG9uJ3QgdGhyb3cgaGVyZSwgdGhpcyBpcyBub24tY3JpdGljYWxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHRvdGFsRHVyYXRpb24gPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgICAgICAgICAgdGhpcy5lbWl0KCdjYWxsLXN0YXJ0LXN1Y2Nlc3MnLCB7XG4gICAgICAgICAgICAgICAgdG90YWxEdXJhdGlvbixcbiAgICAgICAgICAgICAgICBjYWxsSWQ6IHdlYkNhbGw/LmlkIHx8ICd1bmtub3duJyxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gd2ViQ2FsbDtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgY29uc3QgdG90YWxEdXJhdGlvbiA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgICAgICAgICB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQtZmFpbGVkJywge1xuICAgICAgICAgICAgICAgIHN0YWdlOiAndW5rbm93bicsXG4gICAgICAgICAgICAgICAgdG90YWxEdXJhdGlvbixcbiAgICAgICAgICAgICAgICBlcnJvcjogZT8udG9TdHJpbmcoKSB8fCAnVW5rbm93biBlcnJvciBvY2N1cnJlZCcsXG4gICAgICAgICAgICAgICAgZXJyb3JTdGFjazogZSBpbnN0YW5jZW9mIEVycm9yID8gZS5zdGFjayA6ICdObyBzdGFjayB0cmFjZSBhdmFpbGFibGUnLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIGNvbnRleHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgaGFzQXNzaXN0YW50OiAhIWFzc2lzdGFudCxcbiAgICAgICAgICAgICAgICAgICAgaGFzU3F1YWQ6ICEhc3F1YWQsXG4gICAgICAgICAgICAgICAgICAgIGhhc1dvcmtmbG93OiAhIXdvcmtmbG93LFxuICAgICAgICAgICAgICAgICAgICBpc01vYmlsZTogdGhpcy5pc01vYmlsZURldmljZSgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAvLyBBbHNvIGVtaXQgdGhlIGdlbmVyaWMgZXJyb3IgZXZlbnQgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbiAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCB7XG4gICAgICAgICAgICAgICAgdHlwZTogJ3N0YXJ0LW1ldGhvZC1lcnJvcicsXG4gICAgICAgICAgICAgICAgc3RhZ2U6ICd1bmtub3duJyxcbiAgICAgICAgICAgICAgICBlcnJvcjogZSxcbiAgICAgICAgICAgICAgICB0b3RhbER1cmF0aW9uLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIGNvbnRleHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgaGFzQXNzaXN0YW50OiAhIWFzc2lzdGFudCxcbiAgICAgICAgICAgICAgICAgICAgaGFzU3F1YWQ6ICEhc3F1YWQsXG4gICAgICAgICAgICAgICAgICAgIGhhc1dvcmtmbG93OiAhIXdvcmtmbG93LFxuICAgICAgICAgICAgICAgICAgICBpc01vYmlsZTogdGhpcy5pc01vYmlsZURldmljZSgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLmNsZWFudXAoKTtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgfVxuICAgIG9uQXBwTWVzc2FnZShlKSB7XG4gICAgICAgIGlmICghZSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBpZiAoZS5kYXRhID09PSAnbGlzdGVuaW5nJykge1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmVtaXQoJ2NhbGwtc3RhcnQnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcnNlZE1lc3NhZ2UgPSBKU09OLnBhcnNlKGUuZGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnbWVzc2FnZScsIHBhcnNlZE1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgICAgICBpZiAocGFyc2VkTWVzc2FnZSAmJiAndHlwZScgaW4gcGFyc2VkTWVzc2FnZSAmJiAnc3RhdHVzJyBpbiBwYXJzZWRNZXNzYWdlICYmIHBhcnNlZE1lc3NhZ2UudHlwZSA9PT0gJ3N0YXR1cy11cGRhdGUnICYmIHBhcnNlZE1lc3NhZ2Uuc3RhdHVzID09PSAnZW5kZWQnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmhhc0VtaXR0ZWRDYWxsRW5kZWRTdGF0dXMgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdFcnJvciBwYXJzaW5nIG1lc3NhZ2UgZGF0YTogJywgcGFyc2VFcnJvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGhhbmRsZVJlbW90ZVBhcnRpY2lwYW50c0F1ZGlvTGV2ZWwoZSkge1xuICAgICAgICBjb25zdCBzcGVlY2hMZXZlbCA9IE9iamVjdC52YWx1ZXMoZS5wYXJ0aWNpcGFudHNBdWRpb0xldmVsKS5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKTtcbiAgICAgICAgdGhpcy5lbWl0KCd2b2x1bWUtbGV2ZWwnLCBNYXRoLm1pbigxLCBzcGVlY2hMZXZlbCAvIDAuMTUpKTtcbiAgICAgICAgY29uc3QgaXNTcGVha2luZyA9IHNwZWVjaExldmVsID4gMC4wMTtcbiAgICAgICAgaWYgKCFpc1NwZWFraW5nKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuc3BlYWtpbmdUaW1lb3V0KSB7XG4gICAgICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5zcGVha2luZ1RpbWVvdXQpO1xuICAgICAgICAgICAgdGhpcy5zcGVha2luZ1RpbWVvdXQgPSBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5lbWl0KCdzcGVlY2gtc3RhcnQnKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnNwZWFraW5nVGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5lbWl0KCdzcGVlY2gtZW5kJyk7XG4gICAgICAgICAgICB0aGlzLnNwZWFraW5nVGltZW91dCA9IG51bGw7XG4gICAgICAgIH0sIDEwMDApO1xuICAgIH1cbiAgICBzdG9wKCkge1xuICAgICAgICB0aGlzLnN0YXJ0ZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5jYWxsPy5kZXN0cm95KCk7XG4gICAgICAgIHRoaXMuY2FsbCA9IG51bGw7XG4gICAgfVxuICAgIHNlbmQobWVzc2FnZSkge1xuICAgICAgICB0aGlzLmNhbGw/LnNlbmRBcHBNZXNzYWdlKEpTT04uc3RyaW5naWZ5KG1lc3NhZ2UpKTtcbiAgICB9XG4gICAgc2V0TXV0ZWQobXV0ZSkge1xuICAgICAgICBpZiAoIXRoaXMuY2FsbCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYWxsIG9iamVjdCBpcyBub3QgYXZhaWxhYmxlLicpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY2FsbC5zZXRMb2NhbEF1ZGlvKCFtdXRlKTtcbiAgICB9XG4gICAgaXNNdXRlZCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLmNhbGwpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5jYWxsLmxvY2FsQXVkaW8oKSA9PT0gZmFsc2U7XG4gICAgfVxuICAgIHNheShtZXNzYWdlLCBlbmRDYWxsQWZ0ZXJTcG9rZW4sIGludGVycnVwdGlvbnNFbmFibGVkLCBpbnRlcnJ1cHRBc3Npc3RhbnRFbmFibGVkKSB7XG4gICAgICAgIHRoaXMuc2VuZCh7XG4gICAgICAgICAgICB0eXBlOiAnc2F5JyxcbiAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgICBlbmRDYWxsQWZ0ZXJTcG9rZW4sXG4gICAgICAgICAgICBpbnRlcnJ1cHRpb25zRW5hYmxlZDogaW50ZXJydXB0aW9uc0VuYWJsZWQgPz8gZmFsc2UsXG4gICAgICAgICAgICBpbnRlcnJ1cHRBc3Npc3RhbnRFbmFibGVkOiBpbnRlcnJ1cHRBc3Npc3RhbnRFbmFibGVkID8/IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgc2V0SW5wdXREZXZpY2VzQXN5bmMob3B0aW9ucykge1xuICAgICAgICB0aGlzLmNhbGw/LnNldElucHV0RGV2aWNlc0FzeW5jKG9wdGlvbnMpO1xuICAgIH1cbiAgICBhc3luYyBpbmNyZWFzZU1pY0xldmVsKGdhaW4pIHtcbiAgICAgICAgaWYgKCF0aGlzLmNhbGwpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2FsbCBvYmplY3QgaXMgbm90IGF2YWlsYWJsZS4nKTtcbiAgICAgICAgfVxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3Qgc3RyZWFtID0gYXdhaXQgbmF2aWdhdG9yLm1lZGlhRGV2aWNlcy5nZXRVc2VyTWVkaWEoeyBhdWRpbzogdHJ1ZSB9KTtcbiAgICAgICAgICAgIGNvbnN0IGF1ZGlvQ29udGV4dCA9IG5ldyBBdWRpb0NvbnRleHQoKTtcbiAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IGF1ZGlvQ29udGV4dC5jcmVhdGVNZWRpYVN0cmVhbVNvdXJjZShzdHJlYW0pO1xuICAgICAgICAgICAgY29uc3QgZ2Fpbk5vZGUgPSBhdWRpb0NvbnRleHQuY3JlYXRlR2FpbigpO1xuICAgICAgICAgICAgZ2Fpbk5vZGUuZ2Fpbi52YWx1ZSA9IGdhaW47XG4gICAgICAgICAgICBzb3VyY2UuY29ubmVjdChnYWluTm9kZSk7XG4gICAgICAgICAgICBjb25zdCBkZXN0aW5hdGlvbiA9IGF1ZGlvQ29udGV4dC5jcmVhdGVNZWRpYVN0cmVhbURlc3RpbmF0aW9uKCk7XG4gICAgICAgICAgICBnYWluTm9kZS5jb25uZWN0KGRlc3RpbmF0aW9uKTtcbiAgICAgICAgICAgIGNvbnN0IFtib29zdGVkVHJhY2tdID0gZGVzdGluYXRpb24uc3RyZWFtLmdldEF1ZGlvVHJhY2tzKCk7XG4gICAgICAgICAgICBhd2FpdCB0aGlzLmNhbGwuc2V0SW5wdXREZXZpY2VzQXN5bmMoeyBhdWRpb1NvdXJjZTogYm9vc3RlZFRyYWNrIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFkanVzdGluZyBtaWNyb3Bob25lIGxldmVsOlwiLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc2V0T3V0cHV0RGV2aWNlQXN5bmMob3B0aW9ucykge1xuICAgICAgICB0aGlzLmNhbGw/LnNldE91dHB1dERldmljZUFzeW5jKG9wdGlvbnMpO1xuICAgIH1cbiAgICBnZXREYWlseUNhbGxPYmplY3QoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNhbGw7XG4gICAgfVxuICAgIHN0YXJ0U2NyZWVuU2hhcmluZyhkaXNwbGF5TWVkaWFPcHRpb25zLCBzY3JlZW5WaWRlb1NlbmRTZXR0aW5ncykge1xuICAgICAgICB0aGlzLmNhbGw/LnN0YXJ0U2NyZWVuU2hhcmUoe1xuICAgICAgICAgICAgZGlzcGxheU1lZGlhT3B0aW9ucyxcbiAgICAgICAgICAgIHNjcmVlblZpZGVvU2VuZFNldHRpbmdzLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgc3RvcFNjcmVlblNoYXJpbmcoKSB7XG4gICAgICAgIHRoaXMuY2FsbD8uc3RvcFNjcmVlblNoYXJlKCk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gVmFwaTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vapi-ai/web/dist/vapi.js\n");

/***/ })

};
;