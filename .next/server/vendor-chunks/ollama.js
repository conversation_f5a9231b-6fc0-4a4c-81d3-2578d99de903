"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ollama";
exports.ids = ["vendor-chunks/ollama"];
exports.modules = {

/***/ "(rsc)/./node_modules/ollama/dist/browser.mjs":
/*!**********************************************!*\
  !*** ./node_modules/ollama/dist/browser.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ollama: () => (/* binding */ Ollama$1),\n/* harmony export */   \"default\": () => (/* binding */ browser)\n/* harmony export */ });\n/* harmony import */ var whatwg_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! whatwg-fetch */ \"(rsc)/./node_modules/whatwg-fetch/fetch.js\");\n\n\nconst defaultPort = \"11434\";\nconst defaultHost = `http://127.0.0.1:${defaultPort}`;\n\nconst version = \"0.5.16\";\n\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => {\n  __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass ResponseError extends Error {\n  constructor(error, status_code) {\n    super(error);\n    this.error = error;\n    this.status_code = status_code;\n    this.name = \"ResponseError\";\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ResponseError);\n    }\n  }\n}\nclass AbortableAsyncIterator {\n  constructor(abortController, itr, doneCallback) {\n    __publicField$1(this, \"abortController\");\n    __publicField$1(this, \"itr\");\n    __publicField$1(this, \"doneCallback\");\n    this.abortController = abortController;\n    this.itr = itr;\n    this.doneCallback = doneCallback;\n  }\n  abort() {\n    this.abortController.abort();\n  }\n  async *[Symbol.asyncIterator]() {\n    for await (const message of this.itr) {\n      if (\"error\" in message) {\n        throw new Error(message.error);\n      }\n      yield message;\n      if (message.done || message.status === \"success\") {\n        this.doneCallback();\n        return;\n      }\n    }\n    throw new Error(\"Did not receive done or success response in stream.\");\n  }\n}\nconst checkOk = async (response) => {\n  if (response.ok) {\n    return;\n  }\n  let message = `Error ${response.status}: ${response.statusText}`;\n  let errorData = null;\n  if (response.headers.get(\"content-type\")?.includes(\"application/json\")) {\n    try {\n      errorData = await response.json();\n      message = errorData.error || message;\n    } catch (error) {\n      console.log(\"Failed to parse error response as JSON\");\n    }\n  } else {\n    try {\n      console.log(\"Getting text from response\");\n      const textResponse = await response.text();\n      message = textResponse || message;\n    } catch (error) {\n      console.log(\"Failed to get text from error response\");\n    }\n  }\n  throw new ResponseError(message, response.status);\n};\nfunction getPlatform() {\n  if (typeof window !== \"undefined\" && window.navigator) {\n    const nav = navigator;\n    if (\"userAgentData\" in nav && nav.userAgentData?.platform) {\n      return `${nav.userAgentData.platform.toLowerCase()} Browser/${navigator.userAgent};`;\n    }\n    if (navigator.platform) {\n      return `${navigator.platform.toLowerCase()} Browser/${navigator.userAgent};`;\n    }\n    return `unknown Browser/${navigator.userAgent};`;\n  } else if (typeof process !== \"undefined\") {\n    return `${process.arch} ${process.platform} Node.js/${process.version}`;\n  }\n  return \"\";\n}\nfunction normalizeHeaders(headers) {\n  if (headers instanceof Headers) {\n    const obj = {};\n    headers.forEach((value, key) => {\n      obj[key] = value;\n    });\n    return obj;\n  } else if (Array.isArray(headers)) {\n    return Object.fromEntries(headers);\n  } else {\n    return headers || {};\n  }\n}\nconst fetchWithHeaders = async (fetch, url, options = {}) => {\n  const defaultHeaders = {\n    \"Content-Type\": \"application/json\",\n    Accept: \"application/json\",\n    \"User-Agent\": `ollama-js/${version} (${getPlatform()})`\n  };\n  options.headers = normalizeHeaders(options.headers);\n  const customHeaders = Object.fromEntries(\n    Object.entries(options.headers).filter(([key]) => !Object.keys(defaultHeaders).some((defaultKey) => defaultKey.toLowerCase() === key.toLowerCase()))\n  );\n  options.headers = {\n    ...defaultHeaders,\n    ...customHeaders\n  };\n  return fetch(url, options);\n};\nconst get = async (fetch, host, options) => {\n  const response = await fetchWithHeaders(fetch, host, {\n    headers: options?.headers\n  });\n  await checkOk(response);\n  return response;\n};\nconst post = async (fetch, host, data, options) => {\n  const isRecord = (input) => {\n    return input !== null && typeof input === \"object\" && !Array.isArray(input);\n  };\n  const formattedData = isRecord(data) ? JSON.stringify(data) : data;\n  const response = await fetchWithHeaders(fetch, host, {\n    method: \"POST\",\n    body: formattedData,\n    signal: options?.signal,\n    headers: options?.headers\n  });\n  await checkOk(response);\n  return response;\n};\nconst del = async (fetch, host, data, options) => {\n  const response = await fetchWithHeaders(fetch, host, {\n    method: \"DELETE\",\n    body: JSON.stringify(data),\n    headers: options?.headers\n  });\n  await checkOk(response);\n  return response;\n};\nconst parseJSON = async function* (itr) {\n  const decoder = new TextDecoder(\"utf-8\");\n  let buffer = \"\";\n  const reader = itr.getReader();\n  while (true) {\n    const { done, value: chunk } = await reader.read();\n    if (done) {\n      break;\n    }\n    buffer += decoder.decode(chunk);\n    const parts = buffer.split(\"\\n\");\n    buffer = parts.pop() ?? \"\";\n    for (const part of parts) {\n      try {\n        yield JSON.parse(part);\n      } catch (error) {\n        console.warn(\"invalid json: \", part);\n      }\n    }\n  }\n  for (const part of buffer.split(\"\\n\").filter((p) => p !== \"\")) {\n    try {\n      yield JSON.parse(part);\n    } catch (error) {\n      console.warn(\"invalid json: \", part);\n    }\n  }\n};\nconst formatHost = (host) => {\n  if (!host) {\n    return defaultHost;\n  }\n  let isExplicitProtocol = host.includes(\"://\");\n  if (host.startsWith(\":\")) {\n    host = `http://127.0.0.1${host}`;\n    isExplicitProtocol = true;\n  }\n  if (!isExplicitProtocol) {\n    host = `http://${host}`;\n  }\n  const url = new URL(host);\n  let port = url.port;\n  if (!port) {\n    if (!isExplicitProtocol) {\n      port = defaultPort;\n    } else {\n      port = url.protocol === \"https:\" ? \"443\" : \"80\";\n    }\n  }\n  let auth = \"\";\n  if (url.username) {\n    auth = url.username;\n    if (url.password) {\n      auth += `:${url.password}`;\n    }\n    auth += \"@\";\n  }\n  let formattedHost = `${url.protocol}//${auth}${url.hostname}:${port}${url.pathname}`;\n  if (formattedHost.endsWith(\"/\")) {\n    formattedHost = formattedHost.slice(0, -1);\n  }\n  return formattedHost;\n};\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nlet Ollama$1 = class Ollama {\n  constructor(config) {\n    __publicField(this, \"config\");\n    __publicField(this, \"fetch\");\n    __publicField(this, \"ongoingStreamedRequests\", []);\n    this.config = {\n      host: \"\",\n      headers: config?.headers\n    };\n    if (!config?.proxy) {\n      this.config.host = formatHost(config?.host ?? defaultHost);\n    }\n    this.fetch = config?.fetch ?? fetch;\n  }\n  // Abort any ongoing streamed requests to Ollama\n  abort() {\n    for (const request of this.ongoingStreamedRequests) {\n      request.abort();\n    }\n    this.ongoingStreamedRequests.length = 0;\n  }\n  /**\n   * Processes a request to the Ollama server. If the request is streamable, it will return a\n   * AbortableAsyncIterator that yields the response messages. Otherwise, it will return the response\n   * object.\n   * @param endpoint {string} - The endpoint to send the request to.\n   * @param request {object} - The request object to send to the endpoint.\n   * @protected {T | AbortableAsyncIterator<T>} - The response object or a AbortableAsyncIterator that yields\n   * response messages.\n   * @throws {Error} - If the response body is missing or if the response is an error.\n   * @returns {Promise<T | AbortableAsyncIterator<T>>} - The response object or a AbortableAsyncIterator that yields the streamed response.\n   */\n  async processStreamableRequest(endpoint, request) {\n    request.stream = request.stream ?? false;\n    const host = `${this.config.host}/api/${endpoint}`;\n    if (request.stream) {\n      const abortController = new AbortController();\n      const response2 = await post(this.fetch, host, request, {\n        signal: abortController.signal,\n        headers: this.config.headers\n      });\n      if (!response2.body) {\n        throw new Error(\"Missing body\");\n      }\n      const itr = parseJSON(response2.body);\n      const abortableAsyncIterator = new AbortableAsyncIterator(\n        abortController,\n        itr,\n        () => {\n          const i = this.ongoingStreamedRequests.indexOf(abortableAsyncIterator);\n          if (i > -1) {\n            this.ongoingStreamedRequests.splice(i, 1);\n          }\n        }\n      );\n      this.ongoingStreamedRequests.push(abortableAsyncIterator);\n      return abortableAsyncIterator;\n    }\n    const response = await post(this.fetch, host, request, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Encodes an image to base64 if it is a Uint8Array.\n   * @param image {Uint8Array | string} - The image to encode.\n   * @returns {Promise<string>} - The base64 encoded image.\n   */\n  async encodeImage(image) {\n    if (typeof image !== \"string\") {\n      const uint8Array = new Uint8Array(image);\n      let byteString = \"\";\n      const len = uint8Array.byteLength;\n      for (let i = 0; i < len; i++) {\n        byteString += String.fromCharCode(uint8Array[i]);\n      }\n      return btoa(byteString);\n    }\n    return image;\n  }\n  /**\n   * Generates a response from a text prompt.\n   * @param request {GenerateRequest} - The request object.\n   * @returns {Promise<GenerateResponse | AbortableAsyncIterator<GenerateResponse>>} - The response object or\n   * an AbortableAsyncIterator that yields response messages.\n   */\n  async generate(request) {\n    if (request.images) {\n      request.images = await Promise.all(request.images.map(this.encodeImage.bind(this)));\n    }\n    return this.processStreamableRequest(\"generate\", request);\n  }\n  /**\n   * Chats with the model. The request object can contain messages with images that are either\n   * Uint8Arrays or base64 encoded strings. The images will be base64 encoded before sending the\n   * request.\n   * @param request {ChatRequest} - The request object.\n   * @returns {Promise<ChatResponse | AbortableAsyncIterator<ChatResponse>>} - The response object or an\n   * AbortableAsyncIterator that yields response messages.\n   */\n  async chat(request) {\n    if (request.messages) {\n      for (const message of request.messages) {\n        if (message.images) {\n          message.images = await Promise.all(\n            message.images.map(this.encodeImage.bind(this))\n          );\n        }\n      }\n    }\n    return this.processStreamableRequest(\"chat\", request);\n  }\n  /**\n   * Creates a new model from a stream of data.\n   * @param request {CreateRequest} - The request object.\n   * @returns {Promise<ProgressResponse | AbortableAsyncIterator<ProgressResponse>>} - The response object or a stream of progress responses.\n   */\n  async create(request) {\n    return this.processStreamableRequest(\"create\", {\n      ...request\n    });\n  }\n  /**\n   * Pulls a model from the Ollama registry. The request object can contain a stream flag to indicate if the\n   * response should be streamed.\n   * @param request {PullRequest} - The request object.\n   * @returns {Promise<ProgressResponse | AbortableAsyncIterator<ProgressResponse>>} - The response object or\n   * an AbortableAsyncIterator that yields response messages.\n   */\n  async pull(request) {\n    return this.processStreamableRequest(\"pull\", {\n      name: request.model,\n      stream: request.stream,\n      insecure: request.insecure\n    });\n  }\n  /**\n   * Pushes a model to the Ollama registry. The request object can contain a stream flag to indicate if the\n   * response should be streamed.\n   * @param request {PushRequest} - The request object.\n   * @returns {Promise<ProgressResponse | AbortableAsyncIterator<ProgressResponse>>} - The response object or\n   * an AbortableAsyncIterator that yields response messages.\n   */\n  async push(request) {\n    return this.processStreamableRequest(\"push\", {\n      name: request.model,\n      stream: request.stream,\n      insecure: request.insecure\n    });\n  }\n  /**\n   * Deletes a model from the server. The request object should contain the name of the model to\n   * delete.\n   * @param request {DeleteRequest} - The request object.\n   * @returns {Promise<StatusResponse>} - The response object.\n   */\n  async delete(request) {\n    await del(\n      this.fetch,\n      `${this.config.host}/api/delete`,\n      { name: request.model },\n      { headers: this.config.headers }\n    );\n    return { status: \"success\" };\n  }\n  /**\n   * Copies a model from one name to another. The request object should contain the name of the\n   * model to copy and the new name.\n   * @param request {CopyRequest} - The request object.\n   * @returns {Promise<StatusResponse>} - The response object.\n   */\n  async copy(request) {\n    await post(this.fetch, `${this.config.host}/api/copy`, { ...request }, {\n      headers: this.config.headers\n    });\n    return { status: \"success\" };\n  }\n  /**\n   * Lists the models on the server.\n   * @returns {Promise<ListResponse>} - The response object.\n   * @throws {Error} - If the response body is missing.\n   */\n  async list() {\n    const response = await get(this.fetch, `${this.config.host}/api/tags`, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Shows the metadata of a model. The request object should contain the name of the model.\n   * @param request {ShowRequest} - The request object.\n   * @returns {Promise<ShowResponse>} - The response object.\n   */\n  async show(request) {\n    const response = await post(this.fetch, `${this.config.host}/api/show`, {\n      ...request\n    }, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Embeds text input into vectors.\n   * @param request {EmbedRequest} - The request object.\n   * @returns {Promise<EmbedResponse>} - The response object.\n   */\n  async embed(request) {\n    const response = await post(this.fetch, `${this.config.host}/api/embed`, {\n      ...request\n    }, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Embeds a text prompt into a vector.\n   * @param request {EmbeddingsRequest} - The request object.\n   * @returns {Promise<EmbeddingsResponse>} - The response object.\n   */\n  async embeddings(request) {\n    const response = await post(this.fetch, `${this.config.host}/api/embeddings`, {\n      ...request\n    }, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Lists the running models on the server\n   * @returns {Promise<ListResponse>} - The response object.\n   * @throws {Error} - If the response body is missing.\n   */\n  async ps() {\n    const response = await get(this.fetch, `${this.config.host}/api/ps`, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n};\nconst browser = new Ollama$1();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ollama/dist/browser.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/ollama/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/ollama/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ollama: () => (/* binding */ Ollama),\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:path */ \"node:path\");\n/* harmony import */ var _browser_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./browser.mjs */ \"(rsc)/./node_modules/ollama/dist/browser.mjs\");\n/* harmony import */ var whatwg_fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! whatwg-fetch */ \"(rsc)/./node_modules/whatwg-fetch/fetch.js\");\n\n\n\n\n\nclass Ollama extends _browser_mjs__WEBPACK_IMPORTED_MODULE_2__.Ollama {\n  async encodeImage(image) {\n    if (typeof image !== \"string\") {\n      return Buffer.from(image).toString(\"base64\");\n    }\n    try {\n      if (node_fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(image)) {\n        const fileBuffer = await node_fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile((0,node_path__WEBPACK_IMPORTED_MODULE_1__.resolve)(image));\n        return Buffer.from(fileBuffer).toString(\"base64\");\n      }\n    } catch {\n    }\n    return image;\n  }\n  /**\n   * checks if a file exists\n   * @param path {string} - The path to the file\n   * @private @internal\n   * @returns {Promise<boolean>} - Whether the file exists or not\n   */\n  async fileExists(path) {\n    try {\n      await node_fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(path);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n  async create(request) {\n    if (request.from && await this.fileExists((0,node_path__WEBPACK_IMPORTED_MODULE_1__.resolve)(request.from))) {\n      throw Error(\"Creating with a local path is not currently supported from ollama-js\");\n    }\n    if (request.stream) {\n      return super.create(request);\n    } else {\n      return super.create(request);\n    }\n  }\n}\nconst index = new Ollama();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ollama/dist/index.mjs\n");

/***/ })

};
;