"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-logging-utils";
exports.ids = ["vendor-chunks/google-logging-utils"];
exports.modules = {

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/colours.js":
/*!****************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/colours.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Colours = void 0;\n/**\n * Handles figuring out if we can use ANSI colours and handing out the escape codes.\n *\n * This is for package-internal use only, and may change at any time.\n *\n * @private\n * @internal\n */\nclass Colours {\n    /**\n     * @param stream The stream (e.g. process.stderr)\n     * @returns true if the stream should have colourization enabled\n     */\n    static isEnabled(stream) {\n        return (stream.isTTY &&\n            (typeof stream.getColorDepth === 'function'\n                ? stream.getColorDepth() > 2\n                : true));\n    }\n    static refresh() {\n        Colours.enabled = Colours.isEnabled(process.stderr);\n        if (!this.enabled) {\n            Colours.reset = '';\n            Colours.bright = '';\n            Colours.dim = '';\n            Colours.red = '';\n            Colours.green = '';\n            Colours.yellow = '';\n            Colours.blue = '';\n            Colours.magenta = '';\n            Colours.cyan = '';\n            Colours.white = '';\n            Colours.grey = '';\n        }\n        else {\n            Colours.reset = '\\u001b[0m';\n            Colours.bright = '\\u001b[1m';\n            Colours.dim = '\\u001b[2m';\n            Colours.red = '\\u001b[31m';\n            Colours.green = '\\u001b[32m';\n            Colours.yellow = '\\u001b[33m';\n            Colours.blue = '\\u001b[34m';\n            Colours.magenta = '\\u001b[35m';\n            Colours.cyan = '\\u001b[36m';\n            Colours.white = '\\u001b[37m';\n            Colours.grey = '\\u001b[90m';\n        }\n    }\n}\nexports.Colours = Colours;\nColours.enabled = false;\nColours.reset = '';\nColours.bright = '';\nColours.dim = '';\nColours.red = '';\nColours.green = '';\nColours.yellow = '';\nColours.blue = '';\nColours.magenta = '';\nColours.cyan = '';\nColours.white = '';\nColours.grey = '';\nColours.refresh();\n//# sourceMappingURL=colours.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/colours.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js":
/*!**********************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/logging-utils.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2021-2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.env = exports.DebugLogBackendBase = exports.placeholder = exports.AdhocDebugLogger = exports.LogSeverity = void 0;\nexports.getNodeBackend = getNodeBackend;\nexports.getDebugBackend = getDebugBackend;\nexports.getStructuredBackend = getStructuredBackend;\nexports.setBackend = setBackend;\nexports.log = log;\nconst node_events_1 = __webpack_require__(/*! node:events */ \"node:events\");\nconst process = __importStar(__webpack_require__(/*! node:process */ \"node:process\"));\nconst util = __importStar(__webpack_require__(/*! node:util */ \"node:util\"));\nconst colours_1 = __webpack_require__(/*! ./colours */ \"(rsc)/./node_modules/google-logging-utils/build/src/colours.js\");\n// Some functions (as noted) are based on the Node standard library, from\n// the following file:\n//\n// https://github.com/nodejs/node/blob/main/lib/internal/util/debuglog.js\n/**\n * This module defines an ad-hoc debug logger for Google Cloud Platform\n * client libraries in Node. An ad-hoc debug logger is a tool which lets\n * users use an external, unified interface (in this case, environment\n * variables) to determine what logging they want to see at runtime. This\n * isn't necessarily fed into the console, but is meant to be under the\n * control of the user. The kind of logging that will be produced by this\n * is more like \"call retry happened\", not \"event you'd want to record\n * in Cloud Logger\".\n *\n * More for Googlers implementing libraries with it:\n * go/cloud-client-logging-design\n */\n/**\n * Possible log levels. These are a subset of Cloud Observability levels.\n * https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#LogSeverity\n */\nvar LogSeverity;\n(function (LogSeverity) {\n    LogSeverity[\"DEFAULT\"] = \"DEFAULT\";\n    LogSeverity[\"DEBUG\"] = \"DEBUG\";\n    LogSeverity[\"INFO\"] = \"INFO\";\n    LogSeverity[\"WARNING\"] = \"WARNING\";\n    LogSeverity[\"ERROR\"] = \"ERROR\";\n})(LogSeverity || (exports.LogSeverity = LogSeverity = {}));\n/**\n * Our logger instance. This actually contains the meat of dealing\n * with log lines, including EventEmitter. This contains the function\n * that will be passed back to users of the package.\n */\nclass AdhocDebugLogger extends node_events_1.EventEmitter {\n    /**\n     * @param upstream The backend will pass a function that will be\n     *   called whenever our logger function is invoked.\n     */\n    constructor(namespace, upstream) {\n        super();\n        this.namespace = namespace;\n        this.upstream = upstream;\n        this.func = Object.assign(this.invoke.bind(this), {\n            // Also add an instance pointer back to us.\n            instance: this,\n            // And pull over the EventEmitter functionality.\n            on: (event, listener) => this.on(event, listener),\n        });\n        // Convenience methods for log levels.\n        this.func.debug = (...args) => this.invokeSeverity(LogSeverity.DEBUG, ...args);\n        this.func.info = (...args) => this.invokeSeverity(LogSeverity.INFO, ...args);\n        this.func.warn = (...args) => this.invokeSeverity(LogSeverity.WARNING, ...args);\n        this.func.error = (...args) => this.invokeSeverity(LogSeverity.ERROR, ...args);\n        this.func.sublog = (namespace) => log(namespace, this.func);\n    }\n    invoke(fields, ...args) {\n        // Push out any upstream logger first.\n        if (this.upstream) {\n            this.upstream(fields, ...args);\n        }\n        // Emit sink events.\n        this.emit('log', fields, args);\n    }\n    invokeSeverity(severity, ...args) {\n        this.invoke({ severity }, ...args);\n    }\n}\nexports.AdhocDebugLogger = AdhocDebugLogger;\n/**\n * This can be used in place of a real logger while waiting for Promises or disabling logging.\n */\nexports.placeholder = new AdhocDebugLogger('', () => { }).func;\n/**\n * The base class for debug logging backends. It's possible to use this, but the\n * same non-guarantees above still apply (unstable interface, etc).\n *\n * @private\n * @internal\n */\nclass DebugLogBackendBase {\n    constructor() {\n        var _a;\n        this.cached = new Map();\n        this.filters = [];\n        this.filtersSet = false;\n        // Look for the Node config variable for what systems to enable. We'll store\n        // these for the log method below, which will call setFilters() once.\n        let nodeFlag = (_a = process.env[exports.env.nodeEnables]) !== null && _a !== void 0 ? _a : '*';\n        if (nodeFlag === 'all') {\n            nodeFlag = '*';\n        }\n        this.filters = nodeFlag.split(',');\n    }\n    log(namespace, fields, ...args) {\n        try {\n            if (!this.filtersSet) {\n                this.setFilters();\n                this.filtersSet = true;\n            }\n            let logger = this.cached.get(namespace);\n            if (!logger) {\n                logger = this.makeLogger(namespace);\n                this.cached.set(namespace, logger);\n            }\n            logger(fields, ...args);\n        }\n        catch (e) {\n            // Silently ignore all errors; we don't want them to interfere with\n            // the user's running app.\n            // e;\n            console.error(e);\n        }\n    }\n}\nexports.DebugLogBackendBase = DebugLogBackendBase;\n// The basic backend. This one definitely works, but it's less feature-filled.\n//\n// Rather than using util.debuglog, this implements the same basic logic directly.\n// The reason for this decision is that debuglog checks the value of the\n// NODE_DEBUG environment variable before any user code runs; we therefore\n// can't pipe our own enables into it (and util.debuglog will never print unless\n// the user duplicates it into NODE_DEBUG, which isn't reasonable).\n//\nclass NodeBackend extends DebugLogBackendBase {\n    constructor() {\n        super(...arguments);\n        // Default to allowing all systems, since we gate earlier based on whether the\n        // variable is empty.\n        this.enabledRegexp = /.*/g;\n    }\n    isEnabled(namespace) {\n        return this.enabledRegexp.test(namespace);\n    }\n    makeLogger(namespace) {\n        if (!this.enabledRegexp.test(namespace)) {\n            return () => { };\n        }\n        return (fields, ...args) => {\n            var _a;\n            // TODO: `fields` needs to be turned into a string here, one way or another.\n            const nscolour = `${colours_1.Colours.green}${namespace}${colours_1.Colours.reset}`;\n            const pid = `${colours_1.Colours.yellow}${process.pid}${colours_1.Colours.reset}`;\n            let level;\n            switch (fields.severity) {\n                case LogSeverity.ERROR:\n                    level = `${colours_1.Colours.red}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.INFO:\n                    level = `${colours_1.Colours.magenta}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.WARNING:\n                    level = `${colours_1.Colours.yellow}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                default:\n                    level = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.DEFAULT;\n                    break;\n            }\n            const msg = util.formatWithOptions({ colors: colours_1.Colours.enabled }, ...args);\n            const filteredFields = Object.assign({}, fields);\n            delete filteredFields.severity;\n            const fieldsJson = Object.getOwnPropertyNames(filteredFields).length\n                ? JSON.stringify(filteredFields)\n                : '';\n            const fieldsColour = fieldsJson\n                ? `${colours_1.Colours.grey}${fieldsJson}${colours_1.Colours.reset}`\n                : '';\n            console.error('%s [%s|%s] %s%s', pid, nscolour, level, msg, fieldsJson ? ` ${fieldsColour}` : '');\n        };\n    }\n    // Regexp patterns below are from here:\n    // https://github.com/nodejs/node/blob/c0aebed4b3395bd65d54b18d1fd00f071002ac20/lib/internal/util/debuglog.js#L36\n    setFilters() {\n        const totalFilters = this.filters.join(',');\n        const regexp = totalFilters\n            .replace(/[|\\\\{}()[\\]^$+?.]/g, '\\\\$&')\n            .replace(/\\*/g, '.*')\n            .replace(/,/g, '$|^');\n        this.enabledRegexp = new RegExp(`^${regexp}$`, 'i');\n    }\n}\n/**\n * @returns A backend based on Node util.debuglog; this is the default.\n */\nfunction getNodeBackend() {\n    return new NodeBackend();\n}\nclass DebugBackend extends DebugLogBackendBase {\n    constructor(pkg) {\n        super();\n        this.debugPkg = pkg;\n    }\n    makeLogger(namespace) {\n        const debugLogger = this.debugPkg(namespace);\n        return (fields, ...args) => {\n            // TODO: `fields` needs to be turned into a string here.\n            debugLogger(args[0], ...args.slice(1));\n        };\n    }\n    setFilters() {\n        var _a;\n        const existingFilters = (_a = process.env['NODE_DEBUG']) !== null && _a !== void 0 ? _a : '';\n        process.env['NODE_DEBUG'] = `${existingFilters}${existingFilters ? ',' : ''}${this.filters.join(',')}`;\n    }\n}\n/**\n * Creates a \"debug\" package backend. The user must call require('debug') and pass\n * the resulting object to this function.\n *\n * ```\n *  setBackend(getDebugBackend(require('debug')))\n * ```\n *\n * https://www.npmjs.com/package/debug\n *\n * Note: Google does not explicitly endorse or recommend this package; it's just\n * being provided as an option.\n *\n * @returns A backend based on the npm \"debug\" package.\n */\nfunction getDebugBackend(debugPkg) {\n    return new DebugBackend(debugPkg);\n}\n/**\n * This pretty much works like the Node logger, but it outputs structured\n * logging JSON matching Google Cloud's ingestion specs. Rather than handling\n * its own output, it wraps another backend. The passed backend must be a subclass\n * of `DebugLogBackendBase` (any of the backends exposed by this package will work).\n */\nclass StructuredBackend extends DebugLogBackendBase {\n    constructor(upstream) {\n        var _a;\n        super();\n        this.upstream = (_a = upstream) !== null && _a !== void 0 ? _a : new NodeBackend();\n    }\n    makeLogger(namespace) {\n        const debugLogger = this.upstream.makeLogger(namespace);\n        return (fields, ...args) => {\n            var _a;\n            const severity = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.INFO;\n            const json = Object.assign({\n                severity,\n                message: util.format(...args),\n            }, fields);\n            const jsonString = JSON.stringify(json);\n            debugLogger(fields, jsonString);\n        };\n    }\n    setFilters() {\n        this.upstream.setFilters();\n    }\n}\n/**\n * Creates a \"structured logging\" backend. This pretty much works like the\n * Node logger, but it outputs structured logging JSON matching Google\n * Cloud's ingestion specs instead of plain text.\n *\n * ```\n *  setBackend(getStructuredBackend())\n * ```\n *\n * @param upstream If you want to use something besides the Node backend to\n *   write the actual log lines into, pass that here.\n * @returns A backend based on Google Cloud structured logging.\n */\nfunction getStructuredBackend(upstream) {\n    return new StructuredBackend(upstream);\n}\n/**\n * The environment variables that we standardized on, for all ad-hoc logging.\n */\nexports.env = {\n    /**\n     * Filter wildcards specific to the Node syntax, and similar to the built-in\n     * utils.debuglog() environment variable. If missing, disables logging.\n     */\n    nodeEnables: 'GOOGLE_SDK_NODE_LOGGING',\n};\n// Keep a copy of all namespaced loggers so users can reliably .on() them.\n// Note that these cached functions will need to deal with changes in the backend.\nconst loggerCache = new Map();\n// Our current global backend. This might be:\nlet cachedBackend = undefined;\n/**\n * Set the backend to use for our log output.\n * - A backend object\n * - null to disable logging\n * - undefined for \"nothing yet\", defaults to the Node backend\n *\n * @param backend Results from one of the get*Backend() functions.\n */\nfunction setBackend(backend) {\n    cachedBackend = backend;\n    loggerCache.clear();\n}\n/**\n * Creates a logging function. Multiple calls to this with the same namespace\n * will produce the same logger, with the same event emitter hooks.\n *\n * Namespaces can be a simple string (\"system\" name), or a qualified string\n * (system:subsystem), which can be used for filtering, or for \"system:*\".\n *\n * @param namespace The namespace, a descriptive text string.\n * @returns A function you can call that works similar to console.log().\n */\nfunction log(namespace, parent) {\n    // If the enable flag isn't set, do nothing.\n    const enablesFlag = process.env[exports.env.nodeEnables];\n    if (!enablesFlag) {\n        return exports.placeholder;\n    }\n    // This might happen mostly if the typings are dropped in a user's code,\n    // or if they're calling from JavaScript.\n    if (!namespace) {\n        return exports.placeholder;\n    }\n    // Handle sub-loggers.\n    if (parent) {\n        namespace = `${parent.instance.namespace}:${namespace}`;\n    }\n    // Reuse loggers so things like event sinks are persistent.\n    const existing = loggerCache.get(namespace);\n    if (existing) {\n        return existing.func;\n    }\n    // Do we have a backend yet?\n    if (cachedBackend === null) {\n        // Explicitly disabled.\n        return exports.placeholder;\n    }\n    else if (cachedBackend === undefined) {\n        // One hasn't been made yet, so default to Node.\n        cachedBackend = getNodeBackend();\n    }\n    // The logger is further wrapped so we can handle the backend changing out.\n    const logger = (() => {\n        let previousBackend = undefined;\n        const newLogger = new AdhocDebugLogger(namespace, (fields, ...args) => {\n            if (previousBackend !== cachedBackend) {\n                // Did the user pass a custom backend?\n                if (cachedBackend === null) {\n                    // Explicitly disabled.\n                    return;\n                }\n                else if (cachedBackend === undefined) {\n                    // One hasn't been made yet, so default to Node.\n                    cachedBackend = getNodeBackend();\n                }\n                previousBackend = cachedBackend;\n            }\n            cachedBackend === null || cachedBackend === void 0 ? void 0 : cachedBackend.log(namespace, fields, ...args);\n        });\n        return newLogger;\n    })();\n    loggerCache.set(namespace, logger);\n    return logger.func;\n}\n//# sourceMappingURL=logging-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\n");

/***/ })

};
;