/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/exa-js";
exports.ids = ["vendor-chunks/exa-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/exa-js/node_modules/cross-fetch/dist/node-ponyfill.js":
/*!****************************************************************************!*\
  !*** ./node_modules/exa-js/node_modules/cross-fetch/dist/node-ponyfill.js ***!
  \****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("const nodeFetch = __webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\")\nconst realFetch = nodeFetch.default || nodeFetch\n\nconst fetch = function (url, options) {\n  // Support schemaless URIs on the server for parity with the browser.\n  // Ex: //github.com/ -> https://github.com/\n  if (/^\\/\\//.test(url)) {\n    url = 'https:' + url\n  }\n  return realFetch.call(this, url, options)\n}\n\nfetch.ponyfill = true\n\nmodule.exports = exports = fetch\nexports.fetch = fetch\nexports.Headers = nodeFetch.Headers\nexports.Request = nodeFetch.Request\nexports.Response = nodeFetch.Response\n\n// Needed for TypeScript consumers without esModuleInterop.\nexports[\"default\"] = fetch\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZXhhLWpzL25vZGVfbW9kdWxlcy9jcm9zcy1mZXRjaC9kaXN0L25vZGUtcG9ueWZpbGwuanMiLCJtYXBwaW5ncyI6IkFBQUEsa0JBQWtCLG1CQUFPLENBQUMsaUVBQVk7QUFDdEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLGFBQWE7QUFDYixlQUFlO0FBQ2YsZUFBZTtBQUNmLGdCQUFnQjs7QUFFaEI7QUFDQSxrQkFBZSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL2V4YS1qcy9ub2RlX21vZHVsZXMvY3Jvc3MtZmV0Y2gvZGlzdC9ub2RlLXBvbnlmaWxsLmpzPzhhMGIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgbm9kZUZldGNoID0gcmVxdWlyZSgnbm9kZS1mZXRjaCcpXG5jb25zdCByZWFsRmV0Y2ggPSBub2RlRmV0Y2guZGVmYXVsdCB8fCBub2RlRmV0Y2hcblxuY29uc3QgZmV0Y2ggPSBmdW5jdGlvbiAodXJsLCBvcHRpb25zKSB7XG4gIC8vIFN1cHBvcnQgc2NoZW1hbGVzcyBVUklzIG9uIHRoZSBzZXJ2ZXIgZm9yIHBhcml0eSB3aXRoIHRoZSBicm93c2VyLlxuICAvLyBFeDogLy9naXRodWIuY29tLyAtPiBodHRwczovL2dpdGh1Yi5jb20vXG4gIGlmICgvXlxcL1xcLy8udGVzdCh1cmwpKSB7XG4gICAgdXJsID0gJ2h0dHBzOicgKyB1cmxcbiAgfVxuICByZXR1cm4gcmVhbEZldGNoLmNhbGwodGhpcywgdXJsLCBvcHRpb25zKVxufVxuXG5mZXRjaC5wb255ZmlsbCA9IHRydWVcblxubW9kdWxlLmV4cG9ydHMgPSBleHBvcnRzID0gZmV0Y2hcbmV4cG9ydHMuZmV0Y2ggPSBmZXRjaFxuZXhwb3J0cy5IZWFkZXJzID0gbm9kZUZldGNoLkhlYWRlcnNcbmV4cG9ydHMuUmVxdWVzdCA9IG5vZGVGZXRjaC5SZXF1ZXN0XG5leHBvcnRzLlJlc3BvbnNlID0gbm9kZUZldGNoLlJlc3BvbnNlXG5cbi8vIE5lZWRlZCBmb3IgVHlwZVNjcmlwdCBjb25zdW1lcnMgd2l0aG91dCBlc01vZHVsZUludGVyb3AuXG5leHBvcnRzLmRlZmF1bHQgPSBmZXRjaFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/exa-js/node_modules/cross-fetch/dist/node-ponyfill.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/exa-js/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/exa-js/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateEnrichmentParametersFormat: () => (/* binding */ CreateEnrichmentParametersFormat),\n/* harmony export */   CreateImportParametersFormat: () => (/* binding */ CreateImportParametersFormat),\n/* harmony export */   CreateWebsetParametersImportSource: () => (/* binding */ CreateWebsetParametersImportSource),\n/* harmony export */   CreateWebsetParametersSearchExcludeSource: () => (/* binding */ CreateWebsetParametersSearchExcludeSource),\n/* harmony export */   CreateWebsetSearchParametersExcludeSource: () => (/* binding */ CreateWebsetSearchParametersExcludeSource),\n/* harmony export */   EventType: () => (/* binding */ EventType),\n/* harmony export */   EventsClient: () => (/* binding */ EventsClient),\n/* harmony export */   Exa: () => (/* binding */ Exa2),\n/* harmony export */   ExaError: () => (/* binding */ ExaError),\n/* harmony export */   HttpStatusCode: () => (/* binding */ HttpStatusCode),\n/* harmony export */   ImportFailedReason: () => (/* binding */ ImportFailedReason),\n/* harmony export */   ImportFormat: () => (/* binding */ ImportFormat),\n/* harmony export */   ImportObject: () => (/* binding */ ImportObject),\n/* harmony export */   ImportStatus: () => (/* binding */ ImportStatus),\n/* harmony export */   ImportsClient: () => (/* binding */ ImportsClient),\n/* harmony export */   MonitorObject: () => (/* binding */ MonitorObject),\n/* harmony export */   MonitorRunObject: () => (/* binding */ MonitorRunObject),\n/* harmony export */   MonitorRunStatus: () => (/* binding */ MonitorRunStatus),\n/* harmony export */   MonitorRunType: () => (/* binding */ MonitorRunType),\n/* harmony export */   MonitorStatus: () => (/* binding */ MonitorStatus),\n/* harmony export */   ResearchClient: () => (/* binding */ ResearchClient),\n/* harmony export */   UpdateMonitorStatus: () => (/* binding */ UpdateMonitorStatus),\n/* harmony export */   WebhookStatus: () => (/* binding */ WebhookStatus),\n/* harmony export */   WebsetEnrichmentFormat: () => (/* binding */ WebsetEnrichmentFormat),\n/* harmony export */   WebsetEnrichmentStatus: () => (/* binding */ WebsetEnrichmentStatus),\n/* harmony export */   WebsetEnrichmentsClient: () => (/* binding */ WebsetEnrichmentsClient),\n/* harmony export */   WebsetItemEvaluationSatisfied: () => (/* binding */ WebsetItemEvaluationSatisfied),\n/* harmony export */   WebsetItemSource: () => (/* binding */ WebsetItemSource),\n/* harmony export */   WebsetItemsClient: () => (/* binding */ WebsetItemsClient),\n/* harmony export */   WebsetMonitorsClient: () => (/* binding */ WebsetMonitorsClient),\n/* harmony export */   WebsetSearchBehavior: () => (/* binding */ WebsetSearchBehavior),\n/* harmony export */   WebsetSearchCanceledReason: () => (/* binding */ WebsetSearchCanceledReason),\n/* harmony export */   WebsetSearchStatus: () => (/* binding */ WebsetSearchStatus),\n/* harmony export */   WebsetSearchesClient: () => (/* binding */ WebsetSearchesClient),\n/* harmony export */   WebsetStatus: () => (/* binding */ WebsetStatus),\n/* harmony export */   WebsetWebhooksClient: () => (/* binding */ WebsetWebhooksClient),\n/* harmony export */   WebsetsClient: () => (/* binding */ WebsetsClient),\n/* harmony export */   \"default\": () => (/* binding */ index_default)\n/* harmony export */ });\n/* harmony import */ var cross_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cross-fetch */ \"(rsc)/./node_modules/exa-js/node_modules/cross-fetch/dist/node-ponyfill.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(rsc)/./node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/errors.ts\nvar HttpStatusCode = /* @__PURE__ */ ((HttpStatusCode2) => {\n  HttpStatusCode2[HttpStatusCode2[\"BadRequest\"] = 400] = \"BadRequest\";\n  HttpStatusCode2[HttpStatusCode2[\"NotFound\"] = 404] = \"NotFound\";\n  HttpStatusCode2[HttpStatusCode2[\"Unauthorized\"] = 401] = \"Unauthorized\";\n  HttpStatusCode2[HttpStatusCode2[\"Forbidden\"] = 403] = \"Forbidden\";\n  HttpStatusCode2[HttpStatusCode2[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n  HttpStatusCode2[HttpStatusCode2[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n  HttpStatusCode2[HttpStatusCode2[\"InternalServerError\"] = 500] = \"InternalServerError\";\n  HttpStatusCode2[HttpStatusCode2[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n  return HttpStatusCode2;\n})(HttpStatusCode || {});\nvar ExaError = class extends Error {\n  /**\n   * Create a new ExaError\n   * @param message Error message\n   * @param statusCode HTTP status code\n   * @param timestamp ISO timestamp from API\n   * @param path Path that caused the error\n   */\n  constructor(message, statusCode, timestamp, path) {\n    super(message);\n    this.name = \"ExaError\";\n    this.statusCode = statusCode;\n    this.timestamp = timestamp ?? (/* @__PURE__ */ new Date()).toISOString();\n    this.path = path;\n  }\n};\n\n// src/websets/base.ts\nvar WebsetsBaseClient = class {\n  /**\n   * Initialize a new Websets base client\n   * @param client The Exa client instance\n   */\n  constructor(client) {\n    this.client = client;\n  }\n  /**\n   * Make a request to the Websets API\n   * @param endpoint The endpoint path\n   * @param method The HTTP method\n   * @param data Optional request body data\n   * @param params Optional query parameters\n   * @returns The response JSON\n   * @throws ExaError with API error details if the request fails\n   */\n  async request(endpoint, method = \"POST\", data, params) {\n    return this.client.request(`/websets${endpoint}`, method, data, params);\n  }\n  /**\n   * Helper to build pagination parameters\n   * @param pagination The pagination parameters\n   * @returns QueryParams object with pagination parameters\n   */\n  buildPaginationParams(pagination) {\n    const params = {};\n    if (!pagination) return params;\n    if (pagination.cursor) params.cursor = pagination.cursor;\n    if (pagination.limit) params.limit = pagination.limit;\n    return params;\n  }\n};\n\n// src/websets/enrichments.ts\nvar WebsetEnrichmentsClient = class extends WebsetsBaseClient {\n  /**\n   * Create an Enrichment for a Webset\n   * @param websetId The ID of the Webset\n   * @param params The enrichment parameters\n   * @returns The created Webset Enrichment\n   */\n  async create(websetId, params) {\n    return this.request(\n      `/v0/websets/${websetId}/enrichments`,\n      \"POST\",\n      params\n    );\n  }\n  /**\n   * Get an Enrichment by ID\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Enrichment\n   * @returns The Webset Enrichment\n   */\n  async get(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/enrichments/${id}`,\n      \"GET\"\n    );\n  }\n  /**\n   * Delete an Enrichment\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Enrichment\n   * @returns The deleted Webset Enrichment\n   */\n  async delete(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/enrichments/${id}`,\n      \"DELETE\"\n    );\n  }\n  /**\n   * Cancel a running Enrichment\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Enrichment\n   * @returns The canceled Webset Enrichment\n   */\n  async cancel(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/enrichments/${id}/cancel`,\n      \"POST\"\n    );\n  }\n};\n\n// src/websets/events.ts\nvar EventsClient = class extends WebsetsBaseClient {\n  /**\n   * Initialize a new Events client\n   * @param client The Exa client instance\n   */\n  constructor(client) {\n    super(client);\n  }\n  /**\n   * List all Events\n   * @param options Optional filtering and pagination options\n   * @returns The list of Events\n   */\n  async list(options) {\n    const params = {\n      cursor: options?.cursor,\n      limit: options?.limit,\n      types: options?.types\n    };\n    return this.request(\n      \"/v0/events\",\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Get an Event by ID\n   * @param id The ID of the Event\n   * @returns The Event\n   */\n  async get(id) {\n    return this.request(`/v0/events/${id}`, \"GET\");\n  }\n};\n\n// src/websets/openapi.ts\nvar CreateEnrichmentParametersFormat = /* @__PURE__ */ ((CreateEnrichmentParametersFormat2) => {\n  CreateEnrichmentParametersFormat2[\"text\"] = \"text\";\n  CreateEnrichmentParametersFormat2[\"date\"] = \"date\";\n  CreateEnrichmentParametersFormat2[\"number\"] = \"number\";\n  CreateEnrichmentParametersFormat2[\"options\"] = \"options\";\n  CreateEnrichmentParametersFormat2[\"email\"] = \"email\";\n  CreateEnrichmentParametersFormat2[\"phone\"] = \"phone\";\n  return CreateEnrichmentParametersFormat2;\n})(CreateEnrichmentParametersFormat || {});\nvar CreateImportParametersFormat = /* @__PURE__ */ ((CreateImportParametersFormat2) => {\n  CreateImportParametersFormat2[\"csv\"] = \"csv\";\n  return CreateImportParametersFormat2;\n})(CreateImportParametersFormat || {});\nvar CreateWebsetParametersImportSource = /* @__PURE__ */ ((CreateWebsetParametersImportSource2) => {\n  CreateWebsetParametersImportSource2[\"import\"] = \"import\";\n  CreateWebsetParametersImportSource2[\"webset\"] = \"webset\";\n  return CreateWebsetParametersImportSource2;\n})(CreateWebsetParametersImportSource || {});\nvar CreateWebsetParametersSearchExcludeSource = /* @__PURE__ */ ((CreateWebsetParametersSearchExcludeSource2) => {\n  CreateWebsetParametersSearchExcludeSource2[\"import\"] = \"import\";\n  CreateWebsetParametersSearchExcludeSource2[\"webset\"] = \"webset\";\n  return CreateWebsetParametersSearchExcludeSource2;\n})(CreateWebsetParametersSearchExcludeSource || {});\nvar CreateWebsetSearchParametersExcludeSource = /* @__PURE__ */ ((CreateWebsetSearchParametersExcludeSource2) => {\n  CreateWebsetSearchParametersExcludeSource2[\"import\"] = \"import\";\n  CreateWebsetSearchParametersExcludeSource2[\"webset\"] = \"webset\";\n  return CreateWebsetSearchParametersExcludeSource2;\n})(CreateWebsetSearchParametersExcludeSource || {});\nvar EventType = /* @__PURE__ */ ((EventType2) => {\n  EventType2[\"webset_created\"] = \"webset.created\";\n  EventType2[\"webset_deleted\"] = \"webset.deleted\";\n  EventType2[\"webset_paused\"] = \"webset.paused\";\n  EventType2[\"webset_idle\"] = \"webset.idle\";\n  EventType2[\"webset_search_created\"] = \"webset.search.created\";\n  EventType2[\"webset_search_canceled\"] = \"webset.search.canceled\";\n  EventType2[\"webset_search_completed\"] = \"webset.search.completed\";\n  EventType2[\"webset_search_updated\"] = \"webset.search.updated\";\n  EventType2[\"import_created\"] = \"import.created\";\n  EventType2[\"import_completed\"] = \"import.completed\";\n  EventType2[\"import_processing\"] = \"import.processing\";\n  EventType2[\"webset_item_created\"] = \"webset.item.created\";\n  EventType2[\"webset_item_enriched\"] = \"webset.item.enriched\";\n  EventType2[\"webset_export_created\"] = \"webset.export.created\";\n  EventType2[\"webset_export_completed\"] = \"webset.export.completed\";\n  return EventType2;\n})(EventType || {});\nvar ImportFailedReason = /* @__PURE__ */ ((ImportFailedReason2) => {\n  ImportFailedReason2[\"invalid_format\"] = \"invalid_format\";\n  ImportFailedReason2[\"invalid_file_content\"] = \"invalid_file_content\";\n  ImportFailedReason2[\"missing_identifier\"] = \"missing_identifier\";\n  return ImportFailedReason2;\n})(ImportFailedReason || {});\nvar ImportFormat = /* @__PURE__ */ ((ImportFormat2) => {\n  ImportFormat2[\"csv\"] = \"csv\";\n  ImportFormat2[\"webset\"] = \"webset\";\n  return ImportFormat2;\n})(ImportFormat || {});\nvar ImportObject = /* @__PURE__ */ ((ImportObject2) => {\n  ImportObject2[\"import\"] = \"import\";\n  return ImportObject2;\n})(ImportObject || {});\nvar ImportStatus = /* @__PURE__ */ ((ImportStatus2) => {\n  ImportStatus2[\"pending\"] = \"pending\";\n  ImportStatus2[\"processing\"] = \"processing\";\n  ImportStatus2[\"completed\"] = \"completed\";\n  ImportStatus2[\"failed\"] = \"failed\";\n  return ImportStatus2;\n})(ImportStatus || {});\nvar MonitorObject = /* @__PURE__ */ ((MonitorObject2) => {\n  MonitorObject2[\"monitor\"] = \"monitor\";\n  return MonitorObject2;\n})(MonitorObject || {});\nvar MonitorStatus = /* @__PURE__ */ ((MonitorStatus2) => {\n  MonitorStatus2[\"enabled\"] = \"enabled\";\n  MonitorStatus2[\"disabled\"] = \"disabled\";\n  return MonitorStatus2;\n})(MonitorStatus || {});\nvar MonitorRunObject = /* @__PURE__ */ ((MonitorRunObject2) => {\n  MonitorRunObject2[\"monitor_run\"] = \"monitor_run\";\n  return MonitorRunObject2;\n})(MonitorRunObject || {});\nvar MonitorRunStatus = /* @__PURE__ */ ((MonitorRunStatus2) => {\n  MonitorRunStatus2[\"created\"] = \"created\";\n  MonitorRunStatus2[\"running\"] = \"running\";\n  MonitorRunStatus2[\"completed\"] = \"completed\";\n  MonitorRunStatus2[\"canceled\"] = \"canceled\";\n  MonitorRunStatus2[\"failed\"] = \"failed\";\n  return MonitorRunStatus2;\n})(MonitorRunStatus || {});\nvar MonitorRunType = /* @__PURE__ */ ((MonitorRunType2) => {\n  MonitorRunType2[\"search\"] = \"search\";\n  MonitorRunType2[\"refresh\"] = \"refresh\";\n  return MonitorRunType2;\n})(MonitorRunType || {});\nvar UpdateMonitorStatus = /* @__PURE__ */ ((UpdateMonitorStatus2) => {\n  UpdateMonitorStatus2[\"enabled\"] = \"enabled\";\n  UpdateMonitorStatus2[\"disabled\"] = \"disabled\";\n  return UpdateMonitorStatus2;\n})(UpdateMonitorStatus || {});\nvar WebhookStatus = /* @__PURE__ */ ((WebhookStatus2) => {\n  WebhookStatus2[\"active\"] = \"active\";\n  WebhookStatus2[\"inactive\"] = \"inactive\";\n  return WebhookStatus2;\n})(WebhookStatus || {});\nvar WebsetStatus = /* @__PURE__ */ ((WebsetStatus2) => {\n  WebsetStatus2[\"idle\"] = \"idle\";\n  WebsetStatus2[\"running\"] = \"running\";\n  WebsetStatus2[\"paused\"] = \"paused\";\n  return WebsetStatus2;\n})(WebsetStatus || {});\nvar WebsetEnrichmentStatus = /* @__PURE__ */ ((WebsetEnrichmentStatus2) => {\n  WebsetEnrichmentStatus2[\"pending\"] = \"pending\";\n  WebsetEnrichmentStatus2[\"canceled\"] = \"canceled\";\n  WebsetEnrichmentStatus2[\"completed\"] = \"completed\";\n  return WebsetEnrichmentStatus2;\n})(WebsetEnrichmentStatus || {});\nvar WebsetEnrichmentFormat = /* @__PURE__ */ ((WebsetEnrichmentFormat2) => {\n  WebsetEnrichmentFormat2[\"text\"] = \"text\";\n  WebsetEnrichmentFormat2[\"date\"] = \"date\";\n  WebsetEnrichmentFormat2[\"number\"] = \"number\";\n  WebsetEnrichmentFormat2[\"options\"] = \"options\";\n  WebsetEnrichmentFormat2[\"email\"] = \"email\";\n  WebsetEnrichmentFormat2[\"phone\"] = \"phone\";\n  return WebsetEnrichmentFormat2;\n})(WebsetEnrichmentFormat || {});\nvar WebsetItemSource = /* @__PURE__ */ ((WebsetItemSource2) => {\n  WebsetItemSource2[\"search\"] = \"search\";\n  WebsetItemSource2[\"import\"] = \"import\";\n  return WebsetItemSource2;\n})(WebsetItemSource || {});\nvar WebsetItemEvaluationSatisfied = /* @__PURE__ */ ((WebsetItemEvaluationSatisfied2) => {\n  WebsetItemEvaluationSatisfied2[\"yes\"] = \"yes\";\n  WebsetItemEvaluationSatisfied2[\"no\"] = \"no\";\n  WebsetItemEvaluationSatisfied2[\"unclear\"] = \"unclear\";\n  return WebsetItemEvaluationSatisfied2;\n})(WebsetItemEvaluationSatisfied || {});\nvar WebsetSearchStatus = /* @__PURE__ */ ((WebsetSearchStatus2) => {\n  WebsetSearchStatus2[\"created\"] = \"created\";\n  WebsetSearchStatus2[\"running\"] = \"running\";\n  WebsetSearchStatus2[\"completed\"] = \"completed\";\n  WebsetSearchStatus2[\"canceled\"] = \"canceled\";\n  return WebsetSearchStatus2;\n})(WebsetSearchStatus || {});\nvar WebsetSearchBehavior = /* @__PURE__ */ ((WebsetSearchBehavior2) => {\n  WebsetSearchBehavior2[\"override\"] = \"override\";\n  WebsetSearchBehavior2[\"append\"] = \"append\";\n  return WebsetSearchBehavior2;\n})(WebsetSearchBehavior || {});\nvar WebsetSearchCanceledReason = /* @__PURE__ */ ((WebsetSearchCanceledReason2) => {\n  WebsetSearchCanceledReason2[\"webset_deleted\"] = \"webset_deleted\";\n  WebsetSearchCanceledReason2[\"webset_canceled\"] = \"webset_canceled\";\n  return WebsetSearchCanceledReason2;\n})(WebsetSearchCanceledReason || {});\n\n// src/websets/imports.ts\nvar ImportsClient = class extends WebsetsBaseClient {\n  async create(params, csv) {\n    if (csv === void 0) {\n      return this.request(\n        \"/v0/imports\",\n        \"POST\",\n        params\n      );\n    }\n    let csvBuffer;\n    if (typeof csv === \"string\") {\n      csvBuffer = Buffer.from(csv, \"utf8\");\n    } else if (Buffer.isBuffer(csv)) {\n      csvBuffer = csv;\n    } else {\n      throw new ExaError(\n        \"Invalid CSV data input. Must be string or Buffer\",\n        400 /* BadRequest */\n      );\n    }\n    const sizeInBytes = csvBuffer.length;\n    const sizeInMB = Math.max(1, Math.ceil(sizeInBytes / (1024 * 1024)));\n    const csvText = csvBuffer.toString(\"utf8\");\n    const lines = csvText.split(\"\\n\").filter((line) => line.trim().length > 0);\n    const recordCount = Math.max(0, lines.length - 1);\n    if (sizeInMB > 50) {\n      throw new ExaError(\n        `CSV file too large: ${sizeInMB}MB. Maximum size is 50MB.`,\n        400 /* BadRequest */\n      );\n    }\n    if (recordCount === 0) {\n      throw new ExaError(\n        \"CSV file appears to have no data records (only header or empty)\",\n        400 /* BadRequest */\n      );\n    }\n    const createParams = {\n      title: params.title,\n      format: \"csv\" /* csv */,\n      entity: params.entity,\n      size: sizeInBytes,\n      count: recordCount,\n      metadata: params.metadata,\n      csv: params.csv\n    };\n    const importResponse = await this.request(\n      \"/v0/imports\",\n      \"POST\",\n      createParams\n    );\n    try {\n      const uploadResponse = await fetch(importResponse.uploadUrl, {\n        method: \"PUT\",\n        body: csvBuffer\n      });\n      if (!uploadResponse.ok) {\n        const errorText = await uploadResponse.text();\n        throw new ExaError(\n          `Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}. ${errorText}`,\n          400 /* BadRequest */\n        );\n      }\n    } catch (error) {\n      if (error instanceof ExaError) {\n        throw error;\n      }\n      throw new ExaError(\n        `Failed to upload CSV data: ${error.message}`,\n        400 /* BadRequest */\n      );\n    }\n    return importResponse;\n  }\n  /**\n   * Get an Import by ID\n   * @param id The ID of the Import\n   * @returns The Import\n   */\n  async get(id) {\n    return this.request(`/v0/imports/${id}`, \"GET\");\n  }\n  /**\n   * List all Imports\n   * @param options Pagination options\n   * @returns The list of Imports\n   */\n  async list(options) {\n    const params = this.buildPaginationParams(options);\n    return this.request(\n      \"/v0/imports\",\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Update an Import\n   * @param id The ID of the Import\n   * @param params The import update parameters\n   * @returns The updated Import\n   */\n  async update(id, params) {\n    return this.request(`/v0/imports/${id}`, \"PATCH\", params);\n  }\n  /**\n   * Delete an Import\n   * @param id The ID of the Import\n   * @returns The deleted Import\n   */\n  async delete(id) {\n    return this.request(`/v0/imports/${id}`, \"DELETE\");\n  }\n  /**\n   * Wait until an Import is completed or failed\n   * @param id The ID of the Import\n   * @param options Configuration options for timeout and polling\n   * @returns The Import once it reaches a final state (completed or failed)\n   * @throws Error if the Import does not complete within the timeout or fails\n   */\n  async waitUntilCompleted(id, options) {\n    const timeout = options?.timeout ?? 30 * 60 * 1e3;\n    const pollInterval = options?.pollInterval ?? 2e3;\n    const onPoll = options?.onPoll;\n    const startTime = Date.now();\n    while (true) {\n      const importItem = await this.get(id);\n      if (onPoll) {\n        onPoll(importItem.status);\n      }\n      if (importItem.status === \"completed\" /* completed */) {\n        return importItem;\n      }\n      if (importItem.status === \"failed\" /* failed */) {\n        throw new ExaError(\n          `Import ${id} failed: ${importItem.failedMessage || \"Unknown error\"}`,\n          400 /* BadRequest */\n        );\n      }\n      if (Date.now() - startTime > timeout) {\n        throw new ExaError(\n          `Import ${id} did not complete within ${timeout}ms. Current status: ${importItem.status}`,\n          408 /* RequestTimeout */\n        );\n      }\n      await new Promise((resolve) => setTimeout(resolve, pollInterval));\n    }\n  }\n};\n\n// src/websets/items.ts\nvar WebsetItemsClient = class extends WebsetsBaseClient {\n  /**\n   * List all Items for a Webset\n   * @param websetId The ID of the Webset\n   * @param params - Optional pagination and filtering parameters\n   * @returns A promise that resolves with the list of Items\n   */\n  list(websetId, params) {\n    const queryParams = {\n      ...this.buildPaginationParams(params),\n      sourceId: params?.sourceId\n    };\n    return this.request(\n      `/v0/websets/${websetId}/items`,\n      \"GET\",\n      void 0,\n      queryParams\n    );\n  }\n  /**\n   * Iterate through all Items in a Webset, handling pagination automatically\n   * @param websetId The ID of the Webset\n   * @param options Pagination options\n   * @returns Async generator of Webset Items\n   */\n  async *listAll(websetId, options) {\n    let cursor = void 0;\n    const pageOptions = options ? { ...options } : {};\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.list(websetId, pageOptions);\n      for (const item of response.data) {\n        yield item;\n      }\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n      cursor = response.nextCursor;\n    }\n  }\n  /**\n   * Collect all items from a Webset into an array\n   * @param websetId The ID of the Webset\n   * @param options Pagination options\n   * @returns Promise resolving to an array of all Webset Items\n   */\n  async getAll(websetId, options) {\n    const items = [];\n    for await (const item of this.listAll(websetId, options)) {\n      items.push(item);\n    }\n    return items;\n  }\n  /**\n   * Get an Item by ID\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Item\n   * @returns The Webset Item\n   */\n  async get(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/items/${id}`,\n      \"GET\"\n    );\n  }\n  /**\n   * Delete an Item\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Item\n   * @returns The deleted Webset Item\n   */\n  async delete(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/items/${id}`,\n      \"DELETE\"\n    );\n  }\n};\n\n// src/websets/monitors.ts\nvar WebsetMonitorRunsClient = class extends WebsetsBaseClient {\n  /**\n   * List all runs for a Monitor\n   * @param monitorId The ID of the Monitor\n   * @param options Pagination options\n   * @returns The list of Monitor runs\n   */\n  async list(monitorId, options) {\n    const params = this.buildPaginationParams(options);\n    return this.request(\n      `/v0/monitors/${monitorId}/runs`,\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Get a specific Monitor run\n   * @param monitorId The ID of the Monitor\n   * @param runId The ID of the Monitor run\n   * @returns The Monitor run\n   */\n  async get(monitorId, runId) {\n    return this.request(\n      `/v0/monitors/${monitorId}/runs/${runId}`,\n      \"GET\"\n    );\n  }\n};\nvar WebsetMonitorsClient = class extends WebsetsBaseClient {\n  constructor(client) {\n    super(client);\n    this.runs = new WebsetMonitorRunsClient(client);\n  }\n  /**\n   * Create a Monitor\n   * @param params The monitor parameters\n   * @returns The created Monitor\n   */\n  async create(params) {\n    return this.request(\"/v0/monitors\", \"POST\", params);\n  }\n  /**\n   * Get a Monitor by ID\n   * @param id The ID of the Monitor\n   * @returns The Monitor\n   */\n  async get(id) {\n    return this.request(`/v0/monitors/${id}`, \"GET\");\n  }\n  /**\n   * List all Monitors\n   * @param options Pagination and filtering options\n   * @returns The list of Monitors\n   */\n  async list(options) {\n    const params = {\n      cursor: options?.cursor,\n      limit: options?.limit,\n      websetId: options?.websetId\n    };\n    return this.request(\n      \"/v0/monitors\",\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Update a Monitor\n   * @param id The ID of the Monitor\n   * @param params The monitor update parameters (status, metadata)\n   * @returns The updated Monitor\n   */\n  async update(id, params) {\n    return this.request(`/v0/monitors/${id}`, \"PATCH\", params);\n  }\n  /**\n   * Delete a Monitor\n   * @param id The ID of the Monitor\n   * @returns The deleted Monitor\n   */\n  async delete(id) {\n    return this.request(`/v0/monitors/${id}`, \"DELETE\");\n  }\n};\n\n// src/websets/searches.ts\nvar WebsetSearchesClient = class extends WebsetsBaseClient {\n  /**\n   * Create a new Search for the Webset\n   * @param websetId The ID of the Webset\n   * @param params The search parameters\n   * @returns The created Webset Search\n   */\n  async create(websetId, params) {\n    return this.request(\n      `/v0/websets/${websetId}/searches`,\n      \"POST\",\n      params\n    );\n  }\n  /**\n   * Get a Search by ID\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Search\n   * @returns The Webset Search\n   */\n  async get(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/searches/${id}`,\n      \"GET\"\n    );\n  }\n  /**\n   * Cancel a running Search\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Search\n   * @returns The canceled Webset Search\n   */\n  async cancel(websetId, id) {\n    return this.request(\n      `/v0/websets/${websetId}/searches/${id}/cancel`,\n      \"POST\"\n    );\n  }\n};\n\n// src/websets/webhooks.ts\nvar WebsetWebhooksClient = class extends WebsetsBaseClient {\n  /**\n   * Create a Webhook\n   * @param params The webhook parameters\n   * @returns The created Webhook\n   */\n  async create(params) {\n    return this.request(\"/v0/webhooks\", \"POST\", params);\n  }\n  /**\n   * Get a Webhook by ID\n   * @param id The ID of the Webhook\n   * @returns The Webhook\n   */\n  async get(id) {\n    return this.request(`/v0/webhooks/${id}`, \"GET\");\n  }\n  /**\n   * List all Webhooks\n   * @param options Pagination options\n   * @returns The list of Webhooks\n   */\n  async list(options) {\n    const params = this.buildPaginationParams(options);\n    return this.request(\n      \"/v0/webhooks\",\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Iterate through all Webhooks, handling pagination automatically\n   * @param options Pagination options\n   * @returns Async generator of Webhooks\n   */\n  async *listAll(options) {\n    let cursor = void 0;\n    const pageOptions = options ? { ...options } : {};\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.list(pageOptions);\n      for (const webhook of response.data) {\n        yield webhook;\n      }\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n      cursor = response.nextCursor;\n    }\n  }\n  /**\n   * Collect all Webhooks into an array\n   * @param options Pagination options\n   * @returns Promise resolving to an array of all Webhooks\n   */\n  async getAll(options) {\n    const webhooks = [];\n    for await (const webhook of this.listAll(options)) {\n      webhooks.push(webhook);\n    }\n    return webhooks;\n  }\n  /**\n   * Update a Webhook\n   * @param id The ID of the Webhook\n   * @param params The webhook update parameters (events, metadata, url)\n   * @returns The updated Webhook\n   */\n  async update(id, params) {\n    return this.request(`/v0/webhooks/${id}`, \"PATCH\", params);\n  }\n  /**\n   * Delete a Webhook\n   * @param id The ID of the Webhook\n   * @returns The deleted Webhook\n   */\n  async delete(id) {\n    return this.request(`/v0/webhooks/${id}`, \"DELETE\");\n  }\n  /**\n   * List all attempts for a Webhook\n   * @param id The ID of the Webhook\n   * @param options Pagination and filtering options\n   * @returns The list of Webhook attempts\n   */\n  async listAttempts(id, options) {\n    const params = {\n      cursor: options?.cursor,\n      limit: options?.limit,\n      eventType: options?.eventType,\n      successful: options?.successful\n    };\n    return this.request(\n      `/v0/webhooks/${id}/attempts`,\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Iterate through all attempts for a Webhook, handling pagination automatically\n   * @param id The ID of the Webhook\n   * @param options Pagination and filtering options\n   * @returns Async generator of Webhook attempts\n   */\n  async *listAllAttempts(id, options) {\n    let cursor = void 0;\n    const pageOptions = options ? { ...options } : {};\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.listAttempts(id, pageOptions);\n      for (const attempt of response.data) {\n        yield attempt;\n      }\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n      cursor = response.nextCursor;\n    }\n  }\n  /**\n   * Collect all attempts for a Webhook into an array\n   * @param id The ID of the Webhook\n   * @param options Pagination and filtering options\n   * @returns Promise resolving to an array of all Webhook attempts\n   */\n  async getAllAttempts(id, options) {\n    const attempts = [];\n    for await (const attempt of this.listAllAttempts(id, options)) {\n      attempts.push(attempt);\n    }\n    return attempts;\n  }\n};\n\n// src/websets/client.ts\nvar WebsetsClient = class extends WebsetsBaseClient {\n  /**\n   * Initialize a new Websets client\n   * @param client The Exa client instance\n   */\n  constructor(client) {\n    super(client);\n    this.events = new EventsClient(client);\n    this.imports = new ImportsClient(client);\n    this.items = new WebsetItemsClient(client);\n    this.searches = new WebsetSearchesClient(client);\n    this.enrichments = new WebsetEnrichmentsClient(client);\n    this.monitors = new WebsetMonitorsClient(client);\n    this.webhooks = new WebsetWebhooksClient(client);\n  }\n  /**\n   * Create a new Webset\n   * @param params The Webset creation parameters\n   * @returns The created Webset\n   */\n  async create(params) {\n    return this.request(\"/v0/websets\", \"POST\", params);\n  }\n  /**\n   * Get a Webset by ID\n   * @param id The ID of the Webset\n   * @param expand Optional array of relations to expand\n   * @returns The Webset\n   */\n  async get(id, expand) {\n    const params = {};\n    if (expand) {\n      params.expand = expand;\n    }\n    return this.request(\n      `/v0/websets/${id}`,\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * List all Websets\n   * @param options Pagination options (filtering by status is not supported by API)\n   * @returns The list of Websets\n   */\n  async list(options) {\n    const params = this.buildPaginationParams(options);\n    return this.request(\n      \"/v0/websets\",\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n  /**\n   * Iterate through all Websets, handling pagination automatically\n   * @param options Pagination options\n   * @returns Async generator of Websets\n   */\n  async *listAll(options) {\n    let cursor = void 0;\n    const pageOptions = options ? { ...options } : {};\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.list(pageOptions);\n      for (const webset of response.data) {\n        yield webset;\n      }\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n      cursor = response.nextCursor;\n    }\n  }\n  /**\n   * Collect all Websets into an array\n   * @param options Pagination options\n   * @returns Promise resolving to an array of all Websets\n   */\n  async getAll(options) {\n    const websets = [];\n    for await (const webset of this.listAll(options)) {\n      websets.push(webset);\n    }\n    return websets;\n  }\n  /**\n   * Update a Webset\n   * @param id The ID of the Webset\n   * @param params The Webset update parameters\n   * @returns The updated Webset\n   */\n  async update(id, params) {\n    return this.request(`/v0/websets/${id}`, \"POST\", params);\n  }\n  /**\n   * Delete a Webset\n   * @param id The ID of the Webset\n   * @returns The deleted Webset\n   */\n  async delete(id) {\n    return this.request(`/v0/websets/${id}`, \"DELETE\");\n  }\n  /**\n   * Cancel a running Webset\n   * @param id The ID or external ID of the Webset\n   * @returns The canceled Webset (as returned by the API)\n   */\n  async cancel(id) {\n    return this.request(`/v0/websets/${id}/cancel`, \"POST\");\n  }\n  /**\n   * Wait until a Webset is idle\n   * @param id The ID of the Webset\n   * @param options Configuration options for timeout and polling\n   * @returns The Webset once it becomes idle\n   * @throws Error if the Webset does not become idle within the timeout\n   */\n  async waitUntilIdle(id, options) {\n    let timeout;\n    let pollInterval = 1e3;\n    let onPoll;\n    if (typeof options === \"number\") {\n      timeout = options;\n    } else if (options) {\n      timeout = options.timeout;\n      pollInterval = options.pollInterval || 1e3;\n      onPoll = options.onPoll;\n    }\n    const startTime = Date.now();\n    while (true) {\n      const webset = await this.get(id);\n      if (onPoll) {\n        onPoll(webset.status);\n      }\n      if (webset.status === \"idle\" /* idle */) {\n        return webset;\n      }\n      if (timeout && Date.now() - startTime > timeout) {\n        throw new ExaError(\n          `Webset ${id} did not reach idle state within ${timeout}ms. Current status: ${webset.status}`,\n          408 /* RequestTimeout */\n        );\n      }\n      await new Promise((resolve) => setTimeout(resolve, pollInterval));\n    }\n  }\n};\n\n// src/zod-utils.ts\n\n\nfunction isZodSchema(obj) {\n  return obj instanceof zod__WEBPACK_IMPORTED_MODULE_2__.ZodType;\n}\nfunction zodToJsonSchema(schema) {\n  return (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__.zodToJsonSchema)(schema, {\n    $refStrategy: \"none\"\n  });\n}\n\n// src/research/base.ts\nvar ResearchBaseClient = class {\n  /**\n   * Initialize a new Research base client\n   * @param client The Exa client instance\n   */\n  constructor(client) {\n    this.client = client;\n  }\n  /**\n   * Make a request to the Research API (prefixes all paths with `/research`).\n   * @param endpoint The endpoint path, beginning with a slash (e.g. \"/tasks\").\n   * @param method The HTTP method. Defaults to \"POST\".\n   * @param data Optional request body\n   * @param params Optional query parameters\n   * @returns The parsed JSON response\n   */\n  async request(endpoint, method = \"POST\", data, params) {\n    return this.client.request(\n      `/research/v0${endpoint}`,\n      method,\n      data,\n      params\n    );\n  }\n  /**\n   * Make a request to the Research API (prefixes all paths with `/research`).\n   * @param endpoint The endpoint path, beginning with a slash (e.g. \"/tasks\").\n   * @param method The HTTP method. Defaults to \"POST\".\n   * @param data Optional request body\n   * @param params Optional query parameters\n   * @returns The parsed JSON response\n   */\n  async rawRequest(endpoint, method = \"POST\", data, params) {\n    return this.client.rawRequest(\n      `/research/v0${endpoint}`,\n      method,\n      data,\n      params\n    );\n  }\n  /**\n   * Helper to build pagination parameters.\n   * @param pagination The pagination parameters\n   * @returns QueryParams object with pagination parameters\n   */\n  buildPaginationParams(pagination) {\n    const params = {};\n    if (!pagination) return params;\n    if (pagination.cursor) params.cursor = pagination.cursor;\n    if (pagination.limit) params.limit = pagination.limit;\n    return params;\n  }\n};\n\n// src/research/client.ts\nvar ResearchClient = class extends ResearchBaseClient {\n  constructor(client) {\n    super(client);\n  }\n  async createTask(params) {\n    const { instructions, model, output } = params;\n    let schema = output?.schema;\n    if (schema && isZodSchema(schema)) {\n      schema = zodToJsonSchema(schema);\n    }\n    const payload = {\n      instructions,\n      model: model ?? \"exa-research\",\n      output: output ? {\n        schema,\n        inferSchema: output.inferSchema ?? true\n      } : { inferSchema: true }\n    };\n    return this.request(\"/tasks\", \"POST\", payload);\n  }\n  getTask(id, options) {\n    if (options?.stream) {\n      const promise = async () => {\n        const resp = await this.rawRequest(`/tasks/${id}?stream=true`, \"GET\");\n        if (!resp.body) {\n          throw new Error(\"No response body for SSE stream\");\n        }\n        const reader = resp.body.getReader();\n        const decoder = new TextDecoder();\n        let buffer = \"\";\n        function processPart(part) {\n          const lines = part.split(\"\\n\");\n          let data = lines.slice(1).join(\"\\n\");\n          if (data.startsWith(\"data:\")) {\n            data = data.slice(5).trimStart();\n          }\n          try {\n            return JSON.parse(data);\n          } catch (e) {\n            return null;\n          }\n        }\n        async function* streamEvents() {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            buffer += decoder.decode(value, { stream: true });\n            let parts = buffer.split(\"\\n\\n\");\n            buffer = parts.pop() ?? \"\";\n            for (const part of parts) {\n              const processed = processPart(part);\n              if (processed) {\n                yield processed;\n              }\n            }\n          }\n          if (buffer.trim()) {\n            const processed = processPart(buffer.trim());\n            if (processed) {\n              yield processed;\n            }\n          }\n        }\n        return streamEvents();\n      };\n      return promise();\n    } else {\n      return this.request(`/tasks/${id}`, \"GET\");\n    }\n  }\n  /**\n   * @deprecated This method is deprecated and may be removed in a future release.\n   * @see getTask(id, {stream: true})\n   * Poll a research task until completion or failure.\n   * Polls every 1 second with a maximum timeout of 10 minutes.\n   * Resilient to up to 10 consecutive polling failures.\n   */\n  async pollTask(id) {\n    const pollingInterval = 1e3;\n    const maxPollingTime = 10 * 60 * 1e3;\n    const maxConsecutiveFailures = 10;\n    const startTime = Date.now();\n    let consecutiveFailures = 0;\n    while (true) {\n      try {\n        const task = await this.request(`/tasks/${id}`, \"GET\");\n        consecutiveFailures = 0;\n        if (task.status === \"completed\" || task.status === \"failed\") {\n          return task;\n        }\n      } catch (err) {\n        consecutiveFailures += 1;\n        if (consecutiveFailures >= maxConsecutiveFailures) {\n          throw new Error(\n            `Polling failed ${maxConsecutiveFailures} times in a row for task ${id}: ${err}`\n          );\n        }\n      }\n      if (Date.now() - startTime > maxPollingTime) {\n        throw new Error(\n          `Polling timeout: Task ${id} did not complete within 10 minutes`\n        );\n      }\n      await new Promise((resolve) => setTimeout(resolve, pollingInterval));\n    }\n  }\n  /**\n   * List research tasks\n   * @param options Pagination options\n   * @returns The paginated list of research tasks\n   */\n  async listTasks(options) {\n    const params = this.buildPaginationParams(options);\n    return this.request(\n      \"/tasks\",\n      \"GET\",\n      void 0,\n      params\n    );\n  }\n};\n\n// src/index.ts\nvar fetchImpl = typeof global !== \"undefined\" && global.fetch ? global.fetch : cross_fetch__WEBPACK_IMPORTED_MODULE_0__;\nvar HeadersImpl = typeof global !== \"undefined\" && global.Headers ? global.Headers : cross_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\nvar Exa2 = class {\n  /**\n   * Helper method to separate out the contents-specific options from the rest.\n   */\n  extractContentsOptions(options) {\n    const {\n      text,\n      highlights,\n      summary,\n      subpages,\n      subpageTarget,\n      extras,\n      livecrawl,\n      livecrawlTimeout,\n      context,\n      ...rest\n    } = options;\n    const contentsOptions = {};\n    if (text === void 0 && summary === void 0 && highlights === void 0 && extras === void 0) {\n      contentsOptions.text = true;\n    }\n    if (text !== void 0) contentsOptions.text = text;\n    if (summary !== void 0) {\n      if (typeof summary === \"object\" && summary !== null && \"schema\" in summary && summary.schema && isZodSchema(summary.schema)) {\n        contentsOptions.summary = {\n          ...summary,\n          schema: zodToJsonSchema(summary.schema)\n        };\n      } else {\n        contentsOptions.summary = summary;\n      }\n    }\n    if (highlights !== void 0) contentsOptions.highlights = highlights;\n    if (subpages !== void 0) contentsOptions.subpages = subpages;\n    if (subpageTarget !== void 0)\n      contentsOptions.subpageTarget = subpageTarget;\n    if (extras !== void 0) contentsOptions.extras = extras;\n    if (livecrawl !== void 0) contentsOptions.livecrawl = livecrawl;\n    if (livecrawlTimeout !== void 0)\n      contentsOptions.livecrawlTimeout = livecrawlTimeout;\n    if (context !== void 0) contentsOptions.context = context;\n    return {\n      contentsOptions,\n      restOptions: rest\n    };\n  }\n  /**\n   * Constructs the Exa API client.\n   * @param {string} apiKey - The API key for authentication.\n   * @param {string} [baseURL] - The base URL of the Exa API.\n   */\n  constructor(apiKey, baseURL = \"https://api.exa.ai\") {\n    this.baseURL = baseURL;\n    if (!apiKey) {\n      apiKey = process.env.EXASEARCH_API_KEY;\n      if (!apiKey) {\n        throw new ExaError(\n          \"API key must be provided as an argument or as an environment variable (EXASEARCH_API_KEY)\",\n          401 /* Unauthorized */\n        );\n      }\n    }\n    this.headers = new HeadersImpl({\n      \"x-api-key\": apiKey,\n      \"Content-Type\": \"application/json\",\n      \"User-Agent\": \"exa-node 1.4.0\"\n    });\n    this.websets = new WebsetsClient(this);\n    this.research = new ResearchClient(this);\n  }\n  /**\n   * Makes a request to the Exa API.\n   * @param {string} endpoint - The API endpoint to call.\n   * @param {string} method - The HTTP method to use.\n   * @param {any} [body] - The request body for POST requests.\n   * @param {Record<string, any>} [params] - The query parameters.\n   * @returns {Promise<any>} The response from the API.\n   * @throws {ExaError} When any API request fails with structured error information\n   */\n  async request(endpoint, method, body, params) {\n    let url = this.baseURL + endpoint;\n    if (params && Object.keys(params).length > 0) {\n      const searchParams = new URLSearchParams();\n      for (const [key, value] of Object.entries(params)) {\n        if (Array.isArray(value)) {\n          for (const item of value) {\n            searchParams.append(key, item);\n          }\n        } else if (value !== void 0) {\n          searchParams.append(key, String(value));\n        }\n      }\n      url += `?${searchParams.toString()}`;\n    }\n    const response = await fetchImpl(url, {\n      method,\n      headers: this.headers,\n      body: body ? JSON.stringify(body) : void 0\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      if (!errorData.statusCode) {\n        errorData.statusCode = response.status;\n      }\n      if (!errorData.timestamp) {\n        errorData.timestamp = (/* @__PURE__ */ new Date()).toISOString();\n      }\n      if (!errorData.path) {\n        errorData.path = endpoint;\n      }\n      let message = errorData.error || \"Unknown error\";\n      if (errorData.message) {\n        message += (message.length > 0 ? \". \" : \"\") + errorData.message;\n      }\n      throw new ExaError(\n        message,\n        response.status,\n        errorData.timestamp,\n        errorData.path\n      );\n    }\n    const contentType = response.headers.get(\"content-type\") || \"\";\n    if (contentType.includes(\"text/event-stream\")) {\n      return await this.parseSSEStream(response);\n    }\n    return await response.json();\n  }\n  async rawRequest(endpoint, method = \"POST\", body, queryParams) {\n    let url = this.baseURL + endpoint;\n    if (queryParams) {\n      const searchParams = new URLSearchParams();\n      for (const [key, value] of Object.entries(queryParams)) {\n        if (Array.isArray(value)) {\n          for (const item of value) {\n            searchParams.append(key, String(item));\n          }\n        } else if (value !== void 0) {\n          searchParams.append(key, String(value));\n        }\n      }\n      url += `?${searchParams.toString()}`;\n    }\n    const response = await fetchImpl(url, {\n      method,\n      headers: this.headers,\n      body: body ? JSON.stringify(body) : void 0\n    });\n    return response;\n  }\n  /**\n   * Performs a search with an Exa prompt-engineered query.\n   *\n   * @param {string} query - The query string.\n   * @param {RegularSearchOptions} [options] - Additional search options\n   * @returns {Promise<SearchResponse<{}>>} A list of relevant search results.\n   */\n  async search(query, options) {\n    return await this.request(\"/search\", \"POST\", { query, ...options });\n  }\n  /**\n   * Performs a search with an Exa prompt-engineered query and returns the contents of the documents.\n   *\n   * @param {string} query - The query string.\n   * @param {RegularSearchOptions & T} [options] - Additional search + contents options\n   * @returns {Promise<SearchResponse<T>>} A list of relevant search results with requested contents.\n   */\n  async searchAndContents(query, options) {\n    const { contentsOptions, restOptions } = options === void 0 ? { contentsOptions: { text: true }, restOptions: {} } : this.extractContentsOptions(options);\n    return await this.request(\"/search\", \"POST\", {\n      query,\n      contents: contentsOptions,\n      ...restOptions\n    });\n  }\n  /**\n   * Finds similar links to the provided URL.\n   * @param {string} url - The URL for which to find similar links.\n   * @param {FindSimilarOptions} [options] - Additional options for finding similar links.\n   * @returns {Promise<SearchResponse<{}>>} A list of similar search results.\n   */\n  async findSimilar(url, options) {\n    return await this.request(\"/findSimilar\", \"POST\", { url, ...options });\n  }\n  /**\n   * Finds similar links to the provided URL and returns the contents of the documents.\n   * @param {string} url - The URL for which to find similar links.\n   * @param {FindSimilarOptions & T} [options] - Additional options for finding similar links + contents.\n   * @returns {Promise<SearchResponse<T>>} A list of similar search results, including requested contents.\n   */\n  async findSimilarAndContents(url, options) {\n    const { contentsOptions, restOptions } = options === void 0 ? { contentsOptions: { text: true }, restOptions: {} } : this.extractContentsOptions(options);\n    return await this.request(\"/findSimilar\", \"POST\", {\n      url,\n      contents: contentsOptions,\n      ...restOptions\n    });\n  }\n  /**\n   * Retrieves contents of documents based on URLs.\n   * @param {string | string[] | SearchResult[]} urls - A URL or array of URLs, or an array of SearchResult objects.\n   * @param {ContentsOptions} [options] - Additional options for retrieving document contents.\n   * @returns {Promise<SearchResponse<T>>} A list of document contents for the requested URLs.\n   */\n  async getContents(urls, options) {\n    if (!urls || Array.isArray(urls) && urls.length === 0) {\n      throw new ExaError(\n        \"Must provide at least one URL\",\n        400 /* BadRequest */\n      );\n    }\n    let requestUrls;\n    if (typeof urls === \"string\") {\n      requestUrls = [urls];\n    } else if (typeof urls[0] === \"string\") {\n      requestUrls = urls;\n    } else {\n      requestUrls = urls.map((result) => result.url);\n    }\n    const payload = {\n      urls: requestUrls,\n      ...options\n    };\n    return await this.request(\"/contents\", \"POST\", payload);\n  }\n  async answer(query, options) {\n    if (options?.stream) {\n      throw new ExaError(\n        \"For streaming responses, please use streamAnswer() instead:\\n\\nfor await (const chunk of exa.streamAnswer(query)) {\\n  // Handle chunks\\n}\",\n        400 /* BadRequest */\n      );\n    }\n    let outputSchema = options?.outputSchema;\n    if (outputSchema && isZodSchema(outputSchema)) {\n      outputSchema = zodToJsonSchema(outputSchema);\n    }\n    const requestBody = {\n      query,\n      stream: false,\n      text: options?.text ?? false,\n      model: options?.model ?? \"exa\",\n      systemPrompt: options?.systemPrompt,\n      outputSchema\n    };\n    return await this.request(\"/answer\", \"POST\", requestBody);\n  }\n  async *streamAnswer(query, options) {\n    let outputSchema = options?.outputSchema;\n    if (outputSchema && isZodSchema(outputSchema)) {\n      outputSchema = zodToJsonSchema(outputSchema);\n    }\n    const body = {\n      query,\n      text: options?.text ?? false,\n      stream: true,\n      model: options?.model ?? \"exa\",\n      systemPrompt: options?.systemPrompt,\n      outputSchema\n    };\n    const response = await fetchImpl(this.baseURL + \"/answer\", {\n      method: \"POST\",\n      headers: this.headers,\n      body: JSON.stringify(body)\n    });\n    if (!response.ok) {\n      const message = await response.text();\n      throw new ExaError(message, response.status, (/* @__PURE__ */ new Date()).toISOString());\n    }\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new ExaError(\n        \"No response body available for streaming.\",\n        500,\n        (/* @__PURE__ */ new Date()).toISOString()\n      );\n    }\n    const decoder = new TextDecoder();\n    let buffer = \"\";\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split(\"\\n\");\n        buffer = lines.pop() || \"\";\n        for (const line of lines) {\n          if (!line.startsWith(\"data: \")) continue;\n          const jsonStr = line.replace(/^data:\\s*/, \"\").trim();\n          if (!jsonStr || jsonStr === \"[DONE]\") {\n            continue;\n          }\n          let chunkData;\n          try {\n            chunkData = JSON.parse(jsonStr);\n          } catch (err) {\n            continue;\n          }\n          const chunk = this.processChunk(chunkData);\n          if (chunk.content || chunk.citations) {\n            yield chunk;\n          }\n        }\n      }\n      if (buffer.startsWith(\"data: \")) {\n        const leftover = buffer.replace(/^data:\\s*/, \"\").trim();\n        if (leftover && leftover !== \"[DONE]\") {\n          try {\n            const chunkData = JSON.parse(leftover);\n            const chunk = this.processChunk(chunkData);\n            if (chunk.content || chunk.citations) {\n              yield chunk;\n            }\n          } catch (e) {\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  }\n  processChunk(chunkData) {\n    let content;\n    let citations;\n    if (chunkData.choices && chunkData.choices[0] && chunkData.choices[0].delta) {\n      content = chunkData.choices[0].delta.content;\n    }\n    if (chunkData.citations && chunkData.citations !== \"null\") {\n      citations = chunkData.citations.map((c) => ({\n        id: c.id,\n        url: c.url,\n        title: c.title,\n        publishedDate: c.publishedDate,\n        author: c.author,\n        text: c.text\n      }));\n    }\n    return { content, citations };\n  }\n  async parseSSEStream(response) {\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new ExaError(\n        \"No response body available for streaming.\",\n        500,\n        (/* @__PURE__ */ new Date()).toISOString()\n      );\n    }\n    const decoder = new TextDecoder();\n    let buffer = \"\";\n    return new Promise(async (resolve, reject) => {\n      try {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n          buffer += decoder.decode(value, { stream: true });\n          const lines = buffer.split(\"\\n\");\n          buffer = lines.pop() || \"\";\n          for (const line of lines) {\n            if (!line.startsWith(\"data: \")) continue;\n            const jsonStr = line.replace(/^data:\\s*/, \"\").trim();\n            if (!jsonStr || jsonStr === \"[DONE]\") {\n              continue;\n            }\n            let chunk;\n            try {\n              chunk = JSON.parse(jsonStr);\n            } catch {\n              continue;\n            }\n            switch (chunk.tag) {\n              case \"complete\":\n                reader.releaseLock();\n                resolve(chunk.data);\n                return;\n              case \"error\": {\n                const message = chunk.error?.message || \"Unknown error\";\n                reader.releaseLock();\n                reject(\n                  new ExaError(\n                    message,\n                    500 /* InternalServerError */,\n                    (/* @__PURE__ */ new Date()).toISOString()\n                  )\n                );\n                return;\n              }\n              // 'progress' and any other tags are ignored for the blocking variant\n              default:\n                break;\n            }\n          }\n        }\n        reject(\n          new ExaError(\n            \"Stream ended without a completion event.\",\n            500 /* InternalServerError */,\n            (/* @__PURE__ */ new Date()).toISOString()\n          )\n        );\n      } catch (err) {\n        reject(err);\n      } finally {\n        try {\n          reader.releaseLock();\n        } catch {\n        }\n      }\n    });\n  }\n};\nvar index_default = Exa2;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/exa-js/dist/index.mjs\n");

/***/ })

};
;