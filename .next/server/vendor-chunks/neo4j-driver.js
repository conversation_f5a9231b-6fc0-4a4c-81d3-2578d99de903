"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/neo4j-driver";
exports.ids = ["vendor-chunks/neo4j-driver"];
exports.modules = {

/***/ "(rsc)/./node_modules/neo4j-driver/lib/driver.js":
/*!*************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/driver.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.WRITE = exports.READ = exports.Driver = void 0;\nvar neo4j_driver_core_1 = __webpack_require__(/*! neo4j-driver-core */ \"(rsc)/./node_modules/neo4j-driver-core/lib/index.js\");\nvar session_rx_1 = __importDefault(__webpack_require__(/*! ./session-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/session-rx.js\"));\nvar FETCH_ALL = neo4j_driver_core_1.internal.constants.FETCH_ALL;\nvar READ = neo4j_driver_core_1.driver.READ, WRITE = neo4j_driver_core_1.driver.WRITE;\nexports.READ = READ;\nexports.WRITE = WRITE;\n/**\n * A driver maintains one or more {@link Session}s with a remote\n * Neo4j instance. Through the {@link Session}s you can send queries\n * and retrieve results from the database.\n *\n * Drivers are reasonably expensive to create - you should strive to keep one\n * driver instance around per Neo4j Instance you connect to.\n *\n * @access public\n */\nvar Driver = /** @class */ (function (_super) {\n    __extends(Driver, _super);\n    function Driver() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * Acquire a reactive session to communicate with the database. The session will\n     * borrow connections from the underlying connection pool as required and\n     * should be considered lightweight and disposable.\n     *\n     * This comes with some responsibility - make sure you always call\n     * {@link close} when you are done using a session, and likewise,\n     * make sure you don't close your session before you are done using it. Once\n     * it is closed, the underlying connection will be released to the connection\n     * pool and made available for others to use.\n     *\n     * @public\n     * @param {SessionConfig} config\n     * @returns {RxSession} new reactive session.\n     */\n    Driver.prototype.rxSession = function (_a) {\n        var _b = _a === void 0 ? {} : _a, _c = _b.defaultAccessMode, defaultAccessMode = _c === void 0 ? WRITE : _c, bookmarks = _b.bookmarks, _d = _b.database, database = _d === void 0 ? '' : _d, fetchSize = _b.fetchSize, impersonatedUser = _b.impersonatedUser, bookmarkManager = _b.bookmarkManager, notificationFilter = _b.notificationFilter, auth = _b.auth;\n        return new session_rx_1.default({\n            session: this._newSession({\n                defaultAccessMode: defaultAccessMode,\n                bookmarkOrBookmarks: bookmarks,\n                database: database,\n                impersonatedUser: impersonatedUser,\n                auth: auth,\n                reactive: false,\n                fetchSize: validateFetchSizeValue(fetchSize, this._config.fetchSize),\n                bookmarkManager: bookmarkManager,\n                notificationFilter: notificationFilter,\n                log: this._log\n            }),\n            config: this._config,\n            log: this._log\n        });\n    };\n    return Driver;\n}(neo4j_driver_core_1.Driver));\nexports.Driver = Driver;\n/**\n * @private\n */\nfunction validateFetchSizeValue(rawValue, defaultWhenAbsent) {\n    var fetchSize = parseInt(rawValue, 10);\n    if (fetchSize > 0 || fetchSize === FETCH_ALL) {\n        return fetchSize;\n    }\n    else if (fetchSize === 0 || fetchSize < 0) {\n        throw new Error(\"The fetch size can only be a positive value or \".concat(FETCH_ALL, \" for ALL. However fetchSize = \").concat(fetchSize));\n    }\n    else {\n        return defaultWhenAbsent;\n    }\n}\nexports[\"default\"] = Driver;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/driver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UnboundRelationship = exports.Relationship = exports.Node = exports.Record = exports.ServerInfo = exports.GqlStatusObject = exports.Notification = exports.QueryStatistics = exports.ProfiledPlan = exports.Plan = exports.ResultSummary = exports.RxResult = exports.RxManagedTransaction = exports.RxTransaction = exports.RxSession = exports.EagerResult = exports.Result = exports.ManagedTransaction = exports.Transaction = exports.Session = exports.Driver = exports.temporal = exports.spatial = exports.graph = exports.error = exports.routing = exports.session = exports.types = exports.logging = exports.auth = exports.isRetryableError = exports.Neo4jError = exports.integer = exports.isUnboundRelationship = exports.isRelationship = exports.isPathSegment = exports.isPath = exports.isNode = exports.isDateTime = exports.isLocalDateTime = exports.isDate = exports.isTime = exports.isLocalTime = exports.isDuration = exports.isPoint = exports.isInt = exports.int = exports.hasReachableServer = exports.driver = exports.authTokenManagers = void 0;\nexports.clientCertificateProviders = exports.notificationFilterMinimumSeverityLevel = exports.notificationFilterDisabledClassification = exports.notificationFilterDisabledCategory = exports.notificationSeverityLevel = exports.notificationClassification = exports.notificationCategory = exports.resultTransformers = exports.bookmarkManager = exports.DateTime = exports.LocalDateTime = exports.Date = exports.Time = exports.LocalTime = exports.Duration = exports.Integer = exports.Point = exports.PathSegment = exports.Path = void 0;\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar driver_1 = __webpack_require__(/*! ./driver */ \"(rsc)/./node_modules/neo4j-driver/lib/driver.js\");\nObject.defineProperty(exports, \"Driver\", ({ enumerable: true, get: function () { return driver_1.Driver; } }));\nvar version_1 = __importDefault(__webpack_require__(/*! ./version */ \"(rsc)/./node_modules/neo4j-driver/lib/version.js\"));\nvar neo4j_driver_core_1 = __webpack_require__(/*! neo4j-driver-core */ \"(rsc)/./node_modules/neo4j-driver-core/lib/index.js\");\nObject.defineProperty(exports, \"authTokenManagers\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.authTokenManagers; } }));\nObject.defineProperty(exports, \"Neo4jError\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Neo4jError; } }));\nObject.defineProperty(exports, \"isRetryableError\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isRetryableError; } }));\nObject.defineProperty(exports, \"error\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.error; } }));\nObject.defineProperty(exports, \"Integer\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Integer; } }));\nObject.defineProperty(exports, \"int\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.int; } }));\nObject.defineProperty(exports, \"isInt\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isInt; } }));\nObject.defineProperty(exports, \"isPoint\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isPoint; } }));\nObject.defineProperty(exports, \"Point\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Point; } }));\nObject.defineProperty(exports, \"Date\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Date; } }));\nObject.defineProperty(exports, \"DateTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.DateTime; } }));\nObject.defineProperty(exports, \"Duration\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Duration; } }));\nObject.defineProperty(exports, \"isDate\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isDate; } }));\nObject.defineProperty(exports, \"isDateTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isDateTime; } }));\nObject.defineProperty(exports, \"isDuration\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isDuration; } }));\nObject.defineProperty(exports, \"isLocalDateTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isLocalDateTime; } }));\nObject.defineProperty(exports, \"isLocalTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isLocalTime; } }));\nObject.defineProperty(exports, \"isNode\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isNode; } }));\nObject.defineProperty(exports, \"isPath\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isPath; } }));\nObject.defineProperty(exports, \"isPathSegment\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isPathSegment; } }));\nObject.defineProperty(exports, \"isRelationship\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isRelationship; } }));\nObject.defineProperty(exports, \"isTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isTime; } }));\nObject.defineProperty(exports, \"isUnboundRelationship\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.isUnboundRelationship; } }));\nObject.defineProperty(exports, \"LocalDateTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.LocalDateTime; } }));\nObject.defineProperty(exports, \"LocalTime\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.LocalTime; } }));\nObject.defineProperty(exports, \"Time\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Time; } }));\nObject.defineProperty(exports, \"Node\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Node; } }));\nObject.defineProperty(exports, \"Path\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Path; } }));\nObject.defineProperty(exports, \"PathSegment\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.PathSegment; } }));\nObject.defineProperty(exports, \"Relationship\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Relationship; } }));\nObject.defineProperty(exports, \"UnboundRelationship\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.UnboundRelationship; } }));\nObject.defineProperty(exports, \"Record\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Record; } }));\nObject.defineProperty(exports, \"ResultSummary\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.ResultSummary; } }));\nObject.defineProperty(exports, \"Plan\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Plan; } }));\nObject.defineProperty(exports, \"ProfiledPlan\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.ProfiledPlan; } }));\nObject.defineProperty(exports, \"QueryStatistics\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.QueryStatistics; } }));\nObject.defineProperty(exports, \"Notification\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Notification; } }));\nObject.defineProperty(exports, \"GqlStatusObject\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.GqlStatusObject; } }));\nObject.defineProperty(exports, \"ServerInfo\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.ServerInfo; } }));\nObject.defineProperty(exports, \"Result\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Result; } }));\nObject.defineProperty(exports, \"EagerResult\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.EagerResult; } }));\nObject.defineProperty(exports, \"auth\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.auth; } }));\nObject.defineProperty(exports, \"Session\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Session; } }));\nObject.defineProperty(exports, \"Transaction\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.Transaction; } }));\nObject.defineProperty(exports, \"ManagedTransaction\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.ManagedTransaction; } }));\nObject.defineProperty(exports, \"bookmarkManager\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.bookmarkManager; } }));\nObject.defineProperty(exports, \"routing\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.routing; } }));\nObject.defineProperty(exports, \"resultTransformers\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.resultTransformers; } }));\nObject.defineProperty(exports, \"notificationCategory\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.notificationCategory; } }));\nObject.defineProperty(exports, \"notificationClassification\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.notificationClassification; } }));\nObject.defineProperty(exports, \"notificationSeverityLevel\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.notificationSeverityLevel; } }));\nObject.defineProperty(exports, \"notificationFilterDisabledCategory\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.notificationFilterDisabledCategory; } }));\nObject.defineProperty(exports, \"notificationFilterDisabledClassification\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.notificationFilterDisabledClassification; } }));\nObject.defineProperty(exports, \"notificationFilterMinimumSeverityLevel\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.notificationFilterMinimumSeverityLevel; } }));\nObject.defineProperty(exports, \"clientCertificateProviders\", ({ enumerable: true, get: function () { return neo4j_driver_core_1.clientCertificateProviders; } }));\nvar neo4j_driver_bolt_connection_1 = __webpack_require__(/*! neo4j-driver-bolt-connection */ \"(rsc)/./node_modules/neo4j-driver-bolt-connection/lib/index.js\");\nvar session_rx_1 = __importDefault(__webpack_require__(/*! ./session-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/session-rx.js\"));\nexports.RxSession = session_rx_1.default;\nvar transaction_rx_1 = __importDefault(__webpack_require__(/*! ./transaction-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/transaction-rx.js\"));\nexports.RxTransaction = transaction_rx_1.default;\nvar transaction_managed_rx_1 = __importDefault(__webpack_require__(/*! ./transaction-managed-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/transaction-managed-rx.js\"));\nexports.RxManagedTransaction = transaction_managed_rx_1.default;\nvar result_rx_1 = __importDefault(__webpack_require__(/*! ./result-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/result-rx.js\"));\nexports.RxResult = result_rx_1.default;\nvar _a = neo4j_driver_core_1.internal.util, ENCRYPTION_ON = _a.ENCRYPTION_ON, assertString = _a.assertString, isEmptyObjectOrNull = _a.isEmptyObjectOrNull, ServerAddress = neo4j_driver_core_1.internal.serverAddress.ServerAddress, urlUtil = neo4j_driver_core_1.internal.urlUtil;\nvar USER_AGENT = 'neo4j-javascript/' + version_1.default;\nfunction isAuthTokenManager(value) {\n    return typeof value === 'object' &&\n        value != null &&\n        'getToken' in value &&\n        'handleSecurityException' in value &&\n        typeof value.getToken === 'function' &&\n        typeof value.handleSecurityException === 'function';\n}\nfunction createAuthManager(authTokenOrManager) {\n    if (isAuthTokenManager(authTokenOrManager)) {\n        return authTokenOrManager;\n    }\n    var authToken = authTokenOrManager;\n    // Sanitize authority token. Nicer error from server when a scheme is set.\n    authToken = authToken || {};\n    authToken.scheme = authToken.scheme || 'none';\n    return (0, neo4j_driver_core_1.staticAuthTokenManager)({ authToken: authToken });\n}\n/**\n * Construct a new Neo4j Driver. This is your main entry point for this\n * library.\n *\n * @param {string} url The URL for the Neo4j database, for instance \"neo4j://localhost\" and/or \"bolt://localhost\"\n * @param {Map<string,string>} authToken Authentication credentials. See {@link auth} for helpers.\n * @param {Config} config Configuration object.\n * @returns {Driver}\n */\nfunction driver(url, authToken, config) {\n    if (config === void 0) { config = {}; }\n    assertString(url, 'Bolt URL');\n    var parsedUrl = urlUtil.parseDatabaseUrl(url);\n    // Determine encryption/trust options from the URL.\n    var routing = false;\n    var encrypted = false;\n    var trust;\n    switch (parsedUrl.scheme) {\n        case 'bolt':\n            break;\n        case 'bolt+s':\n            encrypted = true;\n            trust = 'TRUST_SYSTEM_CA_SIGNED_CERTIFICATES';\n            break;\n        case 'bolt+ssc':\n            encrypted = true;\n            trust = 'TRUST_ALL_CERTIFICATES';\n            break;\n        case 'neo4j':\n            routing = true;\n            break;\n        case 'neo4j+s':\n            encrypted = true;\n            trust = 'TRUST_SYSTEM_CA_SIGNED_CERTIFICATES';\n            routing = true;\n            break;\n        case 'neo4j+ssc':\n            encrypted = true;\n            trust = 'TRUST_ALL_CERTIFICATES';\n            routing = true;\n            break;\n        default:\n            throw new Error(\"Unknown scheme: \".concat(parsedUrl.scheme));\n    }\n    // Encryption enabled on URL, propagate trust to the config.\n    if (encrypted) {\n        // Check for configuration conflict between URL and config.\n        if ('encrypted' in config || 'trust' in config) {\n            throw new Error('Encryption/trust can only be configured either through URL or config, not both');\n        }\n        config.encrypted = ENCRYPTION_ON;\n        config.trust = trust;\n        config.clientCertificate = (0, neo4j_driver_core_1.resolveCertificateProvider)(config.clientCertificate);\n    }\n    var authTokenManager = createAuthManager(authToken);\n    // Use default user agent or user agent specified by user.\n    config.userAgent = config.userAgent || USER_AGENT;\n    config.boltAgent = neo4j_driver_core_1.internal.boltAgent.fromVersion(version_1.default);\n    var address = ServerAddress.fromUrl(parsedUrl.hostAndPort);\n    var meta = {\n        address: address,\n        typename: routing ? 'Routing' : 'Direct',\n        routing: routing\n    };\n    return new driver_1.Driver(meta, config, createConnectionProviderFunction());\n    function createConnectionProviderFunction() {\n        if (routing) {\n            return function (id, config, log, hostNameResolver) {\n                return new neo4j_driver_bolt_connection_1.RoutingConnectionProvider({\n                    id: id,\n                    config: config,\n                    log: log,\n                    hostNameResolver: hostNameResolver,\n                    authTokenManager: authTokenManager,\n                    address: address,\n                    userAgent: config.userAgent,\n                    boltAgent: config.boltAgent,\n                    routingContext: parsedUrl.query\n                });\n            };\n        }\n        else {\n            if (!isEmptyObjectOrNull(parsedUrl.query)) {\n                throw new Error(\"Parameters are not supported with none routed scheme. Given URL: '\".concat(url, \"'\"));\n            }\n            return function (id, config, log) {\n                return new neo4j_driver_bolt_connection_1.DirectConnectionProvider({\n                    id: id,\n                    config: config,\n                    log: log,\n                    authTokenManager: authTokenManager,\n                    address: address,\n                    userAgent: config.userAgent,\n                    boltAgent: config.boltAgent\n                });\n            };\n        }\n    }\n}\nexports.driver = driver;\n/**\n * Verifies if the driver can reach a server at the given url.\n *\n * @experimental\n * @since 5.0.0\n * @param {string} url The URL for the Neo4j database, for instance \"neo4j://localhost\" and/or \"bolt://localhost\"\n * @param {object} config Configuration object. See the {@link driver}\n * @returns {true} When the server is reachable\n * @throws {Error} When the server is not reachable or the url is invalid\n */\nfunction hasReachableServer(url, config) {\n    return __awaiter(this, void 0, void 0, function () {\n        var nonLoggedDriver;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    nonLoggedDriver = driver(url, { scheme: 'none', principal: '', credentials: '' }, config);\n                    _a.label = 1;\n                case 1:\n                    _a.trys.push([1, , 3, 5]);\n                    return [4 /*yield*/, nonLoggedDriver.getNegotiatedProtocolVersion()];\n                case 2:\n                    _a.sent();\n                    return [2 /*return*/, true];\n                case 3: return [4 /*yield*/, nonLoggedDriver.close()];\n                case 4:\n                    _a.sent();\n                    return [7 /*endfinally*/];\n                case 5: return [2 /*return*/];\n            }\n        });\n    });\n}\nexports.hasReachableServer = hasReachableServer;\n/**\n * Object containing predefined logging configurations. These are expected to be used as values of the driver config's `logging` property.\n * @property {function(level: ?string): object} console the function to create a logging config that prints all messages to `console.log` with\n * timestamp, level and message. It takes an optional `level` parameter which represents the maximum log level to be logged. Default value is 'info'.\n */\nvar logging = {\n    console: function (level) {\n        return {\n            level: level,\n            logger: function (level, message) {\n                return console.log(\"\".concat(global.Date.now(), \" \").concat(level.toUpperCase(), \" \").concat(message));\n            }\n        };\n    }\n};\nexports.logging = logging;\n/**\n * Object containing constructors for all neo4j types.\n */\nvar types = {\n    Node: neo4j_driver_core_1.Node,\n    Relationship: neo4j_driver_core_1.Relationship,\n    UnboundRelationship: neo4j_driver_core_1.UnboundRelationship,\n    PathSegment: neo4j_driver_core_1.PathSegment,\n    Path: neo4j_driver_core_1.Path,\n    Result: neo4j_driver_core_1.Result,\n    EagerResult: neo4j_driver_core_1.EagerResult,\n    ResultSummary: neo4j_driver_core_1.ResultSummary,\n    Record: neo4j_driver_core_1.Record,\n    Point: neo4j_driver_core_1.Point,\n    Date: neo4j_driver_core_1.Date,\n    DateTime: neo4j_driver_core_1.DateTime,\n    Duration: neo4j_driver_core_1.Duration,\n    LocalDateTime: neo4j_driver_core_1.LocalDateTime,\n    LocalTime: neo4j_driver_core_1.LocalTime,\n    Time: neo4j_driver_core_1.Time,\n    Integer: neo4j_driver_core_1.Integer\n};\nexports.types = types;\n/**\n * Object containing string constants representing session access modes.\n */\nvar session = {\n    READ: driver_1.READ,\n    WRITE: driver_1.WRITE\n};\nexports.session = session;\n/**\n * Object containing functions to work with {@link Integer} objects.\n */\nvar integer = {\n    toNumber: neo4j_driver_core_1.toNumber,\n    toString: neo4j_driver_core_1.toString,\n    inSafeRange: neo4j_driver_core_1.inSafeRange\n};\nexports.integer = integer;\n/**\n * Object containing functions to work with spatial types, like {@link Point}.\n */\nvar spatial = {\n    isPoint: neo4j_driver_core_1.isPoint\n};\nexports.spatial = spatial;\n/**\n * Object containing functions to work with temporal types, like {@link Time} or {@link Duration}.\n */\nvar temporal = {\n    isDuration: neo4j_driver_core_1.isDuration,\n    isLocalTime: neo4j_driver_core_1.isLocalTime,\n    isTime: neo4j_driver_core_1.isTime,\n    isDate: neo4j_driver_core_1.isDate,\n    isLocalDateTime: neo4j_driver_core_1.isLocalDateTime,\n    isDateTime: neo4j_driver_core_1.isDateTime\n};\nexports.temporal = temporal;\n/**\n * Object containing functions to work with graph types, like {@link Node} or {@link Relationship}.\n */\nvar graph = {\n    isNode: neo4j_driver_core_1.isNode,\n    isPath: neo4j_driver_core_1.isPath,\n    isPathSegment: neo4j_driver_core_1.isPathSegment,\n    isRelationship: neo4j_driver_core_1.isRelationship,\n    isUnboundRelationship: neo4j_driver_core_1.isUnboundRelationship\n};\nexports.graph = graph;\n/**\n * @private\n */\nvar forExport = {\n    authTokenManagers: neo4j_driver_core_1.authTokenManagers,\n    driver: driver,\n    hasReachableServer: hasReachableServer,\n    int: neo4j_driver_core_1.int,\n    isInt: neo4j_driver_core_1.isInt,\n    isPoint: neo4j_driver_core_1.isPoint,\n    isDuration: neo4j_driver_core_1.isDuration,\n    isLocalTime: neo4j_driver_core_1.isLocalTime,\n    isTime: neo4j_driver_core_1.isTime,\n    isDate: neo4j_driver_core_1.isDate,\n    isLocalDateTime: neo4j_driver_core_1.isLocalDateTime,\n    isDateTime: neo4j_driver_core_1.isDateTime,\n    isNode: neo4j_driver_core_1.isNode,\n    isPath: neo4j_driver_core_1.isPath,\n    isPathSegment: neo4j_driver_core_1.isPathSegment,\n    isRelationship: neo4j_driver_core_1.isRelationship,\n    isUnboundRelationship: neo4j_driver_core_1.isUnboundRelationship,\n    integer: integer,\n    Neo4jError: neo4j_driver_core_1.Neo4jError,\n    isRetryableError: neo4j_driver_core_1.isRetryableError,\n    auth: neo4j_driver_core_1.auth,\n    logging: logging,\n    types: types,\n    session: session,\n    routing: neo4j_driver_core_1.routing,\n    error: neo4j_driver_core_1.error,\n    graph: graph,\n    spatial: spatial,\n    temporal: temporal,\n    Driver: driver_1.Driver,\n    Session: neo4j_driver_core_1.Session,\n    Transaction: neo4j_driver_core_1.Transaction,\n    ManagedTransaction: neo4j_driver_core_1.ManagedTransaction,\n    Result: neo4j_driver_core_1.Result,\n    EagerResult: neo4j_driver_core_1.EagerResult,\n    RxSession: session_rx_1.default,\n    RxTransaction: transaction_rx_1.default,\n    RxManagedTransaction: transaction_managed_rx_1.default,\n    RxResult: result_rx_1.default,\n    ResultSummary: neo4j_driver_core_1.ResultSummary,\n    Plan: neo4j_driver_core_1.Plan,\n    ProfiledPlan: neo4j_driver_core_1.ProfiledPlan,\n    QueryStatistics: neo4j_driver_core_1.QueryStatistics,\n    Notification: neo4j_driver_core_1.Notification,\n    GqlStatusObject: neo4j_driver_core_1.GqlStatusObject,\n    ServerInfo: neo4j_driver_core_1.ServerInfo,\n    Record: neo4j_driver_core_1.Record,\n    Node: neo4j_driver_core_1.Node,\n    Relationship: neo4j_driver_core_1.Relationship,\n    UnboundRelationship: neo4j_driver_core_1.UnboundRelationship,\n    Path: neo4j_driver_core_1.Path,\n    PathSegment: neo4j_driver_core_1.PathSegment,\n    Point: neo4j_driver_core_1.Point,\n    Integer: neo4j_driver_core_1.Integer,\n    Duration: neo4j_driver_core_1.Duration,\n    LocalTime: neo4j_driver_core_1.LocalTime,\n    Time: neo4j_driver_core_1.Time,\n    Date: neo4j_driver_core_1.Date,\n    LocalDateTime: neo4j_driver_core_1.LocalDateTime,\n    DateTime: neo4j_driver_core_1.DateTime,\n    bookmarkManager: neo4j_driver_core_1.bookmarkManager,\n    resultTransformers: neo4j_driver_core_1.resultTransformers,\n    notificationCategory: neo4j_driver_core_1.notificationCategory,\n    notificationSeverityLevel: neo4j_driver_core_1.notificationSeverityLevel,\n    notificationFilterDisabledCategory: neo4j_driver_core_1.notificationFilterDisabledCategory,\n    notificationFilterMinimumSeverityLevel: neo4j_driver_core_1.notificationFilterMinimumSeverityLevel,\n    clientCertificateProviders: neo4j_driver_core_1.clientCertificateProviders\n};\nexports[\"default\"] = forExport;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/internal/retry-logic-rx.js":
/*!******************************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/internal/retry-logic-rx.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar neo4j_driver_core_1 = __webpack_require__(/*! neo4j-driver-core */ \"(rsc)/./node_modules/neo4j-driver-core/lib/index.js\");\n// eslint-disable-next-line no-unused-vars\nvar rxjs_1 = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/rxjs/dist/esm5/index.js\");\nvar operators_1 = __webpack_require__(/*! rxjs/operators */ \"(rsc)/./node_modules/rxjs/dist/cjs/operators/index.js\");\nvar \n// eslint-disable-next-line no-unused-vars\nLogger = neo4j_driver_core_1.internal.logger.Logger;\nvar SERVICE_UNAVAILABLE = neo4j_driver_core_1.error.SERVICE_UNAVAILABLE;\nvar DEFAULT_MAX_RETRY_TIME_MS = 30 * 1000; // 30 seconds\nvar DEFAULT_INITIAL_RETRY_DELAY_MS = 1000; // 1 seconds\nvar DEFAULT_RETRY_DELAY_MULTIPLIER = 2.0;\nvar DEFAULT_RETRY_DELAY_JITTER_FACTOR = 0.2;\nvar RxRetryLogic = /** @class */ (function () {\n    /**\n     *\n     * @param {Object} args\n     * @param {Logger} args.logger\n     */\n    function RxRetryLogic(_a) {\n        var _b = _a === void 0 ? {} : _a, _c = _b.maxRetryTimeout, maxRetryTimeout = _c === void 0 ? DEFAULT_MAX_RETRY_TIME_MS : _c, _d = _b.initialDelay, initialDelay = _d === void 0 ? DEFAULT_INITIAL_RETRY_DELAY_MS : _d, _e = _b.delayMultiplier, delayMultiplier = _e === void 0 ? DEFAULT_RETRY_DELAY_MULTIPLIER : _e, _f = _b.delayJitter, delayJitter = _f === void 0 ? DEFAULT_RETRY_DELAY_JITTER_FACTOR : _f, _g = _b.logger, logger = _g === void 0 ? null : _g;\n        this._maxRetryTimeout = valueOrDefault(maxRetryTimeout, DEFAULT_MAX_RETRY_TIME_MS);\n        this._initialDelay = valueOrDefault(initialDelay, DEFAULT_INITIAL_RETRY_DELAY_MS);\n        this._delayMultiplier = valueOrDefault(delayMultiplier, DEFAULT_RETRY_DELAY_MULTIPLIER);\n        this._delayJitter = valueOrDefault(delayJitter, DEFAULT_RETRY_DELAY_JITTER_FACTOR);\n        this._logger = logger;\n    }\n    /**\n     *\n     * @param {Observable<Any>} work\n     */\n    RxRetryLogic.prototype.retry = function (work) {\n        var _this = this;\n        return work.pipe((0, operators_1.retryWhen)(function (failedWork) {\n            var handledExceptions = [];\n            var startTime = Date.now();\n            var retryCount = 1;\n            var delayDuration = _this._initialDelay;\n            return failedWork.pipe((0, operators_1.mergeMap)(function (err) {\n                if (!(0, neo4j_driver_core_1.isRetriableError)(err)) {\n                    return (0, rxjs_1.throwError)(function () { return err; });\n                }\n                handledExceptions.push(err);\n                if (retryCount >= 2 &&\n                    Date.now() - startTime >= _this._maxRetryTimeout) {\n                    var error_1 = (0, neo4j_driver_core_1.newError)(\"Failed after retried for \".concat(retryCount, \" times in \").concat(_this._maxRetryTimeout, \" ms. Make sure that your database is online and retry again.\"), SERVICE_UNAVAILABLE);\n                    error_1.seenErrors = handledExceptions;\n                    return (0, rxjs_1.throwError)(function () { return error_1; });\n                }\n                var nextDelayDuration = _this._computeNextDelay(delayDuration);\n                delayDuration = delayDuration * _this._delayMultiplier;\n                retryCount++;\n                if (_this._logger) {\n                    _this._logger.warn(\"Transaction failed and will be retried in \".concat(nextDelayDuration));\n                }\n                return (0, rxjs_1.of)(1).pipe((0, operators_1.delay)(nextDelayDuration));\n            }));\n        }));\n    };\n    RxRetryLogic.prototype._computeNextDelay = function (delay) {\n        var jitter = delay * this._delayJitter;\n        return delay - jitter + 2 * jitter * Math.random();\n    };\n    return RxRetryLogic;\n}());\nexports[\"default\"] = RxRetryLogic;\nfunction valueOrDefault(value, defaultValue) {\n    if (value || value === 0) {\n        return value;\n    }\n    return defaultValue;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmVvNGotZHJpdmVyL2xpYi9pbnRlcm5hbC9yZXRyeS1sb2dpYy1yeC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQixtQkFBTyxDQUFDLDhFQUFtQjtBQUNyRDtBQUNBLGFBQWEsbUJBQU8sQ0FBQywwREFBTTtBQUMzQixrQkFBa0IsbUJBQU8sQ0FBQyw2RUFBZ0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0MsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0E7QUFDQSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsaUJBQWlCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLGFBQWE7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLGlCQUFpQjtBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsa0JBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9uZW80ai1kcml2ZXIvbGliL2ludGVybmFsL3JldHJ5LWxvZ2ljLXJ4LmpzPzUxYzkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKipcbiAqIENvcHlyaWdodCAoYykgXCJOZW80alwiXG4gKiBOZW80aiBTd2VkZW4gQUIgW2h0dHBzOi8vbmVvNGouY29tXVxuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xudmFyIG5lbzRqX2RyaXZlcl9jb3JlXzEgPSByZXF1aXJlKFwibmVvNGotZHJpdmVyLWNvcmVcIik7XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcbnZhciByeGpzXzEgPSByZXF1aXJlKFwicnhqc1wiKTtcbnZhciBvcGVyYXRvcnNfMSA9IHJlcXVpcmUoXCJyeGpzL29wZXJhdG9yc1wiKTtcbnZhciBcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtdmFyc1xuTG9nZ2VyID0gbmVvNGpfZHJpdmVyX2NvcmVfMS5pbnRlcm5hbC5sb2dnZXIuTG9nZ2VyO1xudmFyIFNFUlZJQ0VfVU5BVkFJTEFCTEUgPSBuZW80al9kcml2ZXJfY29yZV8xLmVycm9yLlNFUlZJQ0VfVU5BVkFJTEFCTEU7XG52YXIgREVGQVVMVF9NQVhfUkVUUllfVElNRV9NUyA9IDMwICogMTAwMDsgLy8gMzAgc2Vjb25kc1xudmFyIERFRkFVTFRfSU5JVElBTF9SRVRSWV9ERUxBWV9NUyA9IDEwMDA7IC8vIDEgc2Vjb25kc1xudmFyIERFRkFVTFRfUkVUUllfREVMQVlfTVVMVElQTElFUiA9IDIuMDtcbnZhciBERUZBVUxUX1JFVFJZX0RFTEFZX0pJVFRFUl9GQUNUT1IgPSAwLjI7XG52YXIgUnhSZXRyeUxvZ2ljID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xuICAgIC8qKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtPYmplY3R9IGFyZ3NcbiAgICAgKiBAcGFyYW0ge0xvZ2dlcn0gYXJncy5sb2dnZXJcbiAgICAgKi9cbiAgICBmdW5jdGlvbiBSeFJldHJ5TG9naWMoX2EpIHtcbiAgICAgICAgdmFyIF9iID0gX2EgPT09IHZvaWQgMCA/IHt9IDogX2EsIF9jID0gX2IubWF4UmV0cnlUaW1lb3V0LCBtYXhSZXRyeVRpbWVvdXQgPSBfYyA9PT0gdm9pZCAwID8gREVGQVVMVF9NQVhfUkVUUllfVElNRV9NUyA6IF9jLCBfZCA9IF9iLmluaXRpYWxEZWxheSwgaW5pdGlhbERlbGF5ID0gX2QgPT09IHZvaWQgMCA/IERFRkFVTFRfSU5JVElBTF9SRVRSWV9ERUxBWV9NUyA6IF9kLCBfZSA9IF9iLmRlbGF5TXVsdGlwbGllciwgZGVsYXlNdWx0aXBsaWVyID0gX2UgPT09IHZvaWQgMCA/IERFRkFVTFRfUkVUUllfREVMQVlfTVVMVElQTElFUiA6IF9lLCBfZiA9IF9iLmRlbGF5Sml0dGVyLCBkZWxheUppdHRlciA9IF9mID09PSB2b2lkIDAgPyBERUZBVUxUX1JFVFJZX0RFTEFZX0pJVFRFUl9GQUNUT1IgOiBfZiwgX2cgPSBfYi5sb2dnZXIsIGxvZ2dlciA9IF9nID09PSB2b2lkIDAgPyBudWxsIDogX2c7XG4gICAgICAgIHRoaXMuX21heFJldHJ5VGltZW91dCA9IHZhbHVlT3JEZWZhdWx0KG1heFJldHJ5VGltZW91dCwgREVGQVVMVF9NQVhfUkVUUllfVElNRV9NUyk7XG4gICAgICAgIHRoaXMuX2luaXRpYWxEZWxheSA9IHZhbHVlT3JEZWZhdWx0KGluaXRpYWxEZWxheSwgREVGQVVMVF9JTklUSUFMX1JFVFJZX0RFTEFZX01TKTtcbiAgICAgICAgdGhpcy5fZGVsYXlNdWx0aXBsaWVyID0gdmFsdWVPckRlZmF1bHQoZGVsYXlNdWx0aXBsaWVyLCBERUZBVUxUX1JFVFJZX0RFTEFZX01VTFRJUExJRVIpO1xuICAgICAgICB0aGlzLl9kZWxheUppdHRlciA9IHZhbHVlT3JEZWZhdWx0KGRlbGF5Sml0dGVyLCBERUZBVUxUX1JFVFJZX0RFTEFZX0pJVFRFUl9GQUNUT1IpO1xuICAgICAgICB0aGlzLl9sb2dnZXIgPSBsb2dnZXI7XG4gICAgfVxuICAgIC8qKlxuICAgICAqXG4gICAgICogQHBhcmFtIHtPYnNlcnZhYmxlPEFueT59IHdvcmtcbiAgICAgKi9cbiAgICBSeFJldHJ5TG9naWMucHJvdG90eXBlLnJldHJ5ID0gZnVuY3Rpb24gKHdvcmspIHtcbiAgICAgICAgdmFyIF90aGlzID0gdGhpcztcbiAgICAgICAgcmV0dXJuIHdvcmsucGlwZSgoMCwgb3BlcmF0b3JzXzEucmV0cnlXaGVuKShmdW5jdGlvbiAoZmFpbGVkV29yaykge1xuICAgICAgICAgICAgdmFyIGhhbmRsZWRFeGNlcHRpb25zID0gW107XG4gICAgICAgICAgICB2YXIgc3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgICAgICAgIHZhciByZXRyeUNvdW50ID0gMTtcbiAgICAgICAgICAgIHZhciBkZWxheUR1cmF0aW9uID0gX3RoaXMuX2luaXRpYWxEZWxheTtcbiAgICAgICAgICAgIHJldHVybiBmYWlsZWRXb3JrLnBpcGUoKDAsIG9wZXJhdG9yc18xLm1lcmdlTWFwKShmdW5jdGlvbiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgaWYgKCEoMCwgbmVvNGpfZHJpdmVyX2NvcmVfMS5pc1JldHJpYWJsZUVycm9yKShlcnIpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAoMCwgcnhqc18xLnRocm93RXJyb3IpKGZ1bmN0aW9uICgpIHsgcmV0dXJuIGVycjsgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGhhbmRsZWRFeGNlcHRpb25zLnB1c2goZXJyKTtcbiAgICAgICAgICAgICAgICBpZiAocmV0cnlDb3VudCA+PSAyICYmXG4gICAgICAgICAgICAgICAgICAgIERhdGUubm93KCkgLSBzdGFydFRpbWUgPj0gX3RoaXMuX21heFJldHJ5VGltZW91dCkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgZXJyb3JfMSA9ICgwLCBuZW80al9kcml2ZXJfY29yZV8xLm5ld0Vycm9yKShcIkZhaWxlZCBhZnRlciByZXRyaWVkIGZvciBcIi5jb25jYXQocmV0cnlDb3VudCwgXCIgdGltZXMgaW4gXCIpLmNvbmNhdChfdGhpcy5fbWF4UmV0cnlUaW1lb3V0LCBcIiBtcy4gTWFrZSBzdXJlIHRoYXQgeW91ciBkYXRhYmFzZSBpcyBvbmxpbmUgYW5kIHJldHJ5IGFnYWluLlwiKSwgU0VSVklDRV9VTkFWQUlMQUJMRSk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yXzEuc2VlbkVycm9ycyA9IGhhbmRsZWRFeGNlcHRpb25zO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKDAsIHJ4anNfMS50aHJvd0Vycm9yKShmdW5jdGlvbiAoKSB7IHJldHVybiBlcnJvcl8xOyB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdmFyIG5leHREZWxheUR1cmF0aW9uID0gX3RoaXMuX2NvbXB1dGVOZXh0RGVsYXkoZGVsYXlEdXJhdGlvbik7XG4gICAgICAgICAgICAgICAgZGVsYXlEdXJhdGlvbiA9IGRlbGF5RHVyYXRpb24gKiBfdGhpcy5fZGVsYXlNdWx0aXBsaWVyO1xuICAgICAgICAgICAgICAgIHJldHJ5Q291bnQrKztcbiAgICAgICAgICAgICAgICBpZiAoX3RoaXMuX2xvZ2dlcikge1xuICAgICAgICAgICAgICAgICAgICBfdGhpcy5fbG9nZ2VyLndhcm4oXCJUcmFuc2FjdGlvbiBmYWlsZWQgYW5kIHdpbGwgYmUgcmV0cmllZCBpbiBcIi5jb25jYXQobmV4dERlbGF5RHVyYXRpb24pKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuICgwLCByeGpzXzEub2YpKDEpLnBpcGUoKDAsIG9wZXJhdG9yc18xLmRlbGF5KShuZXh0RGVsYXlEdXJhdGlvbikpO1xuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9KSk7XG4gICAgfTtcbiAgICBSeFJldHJ5TG9naWMucHJvdG90eXBlLl9jb21wdXRlTmV4dERlbGF5ID0gZnVuY3Rpb24gKGRlbGF5KSB7XG4gICAgICAgIHZhciBqaXR0ZXIgPSBkZWxheSAqIHRoaXMuX2RlbGF5Sml0dGVyO1xuICAgICAgICByZXR1cm4gZGVsYXkgLSBqaXR0ZXIgKyAyICogaml0dGVyICogTWF0aC5yYW5kb20oKTtcbiAgICB9O1xuICAgIHJldHVybiBSeFJldHJ5TG9naWM7XG59KCkpO1xuZXhwb3J0cy5kZWZhdWx0ID0gUnhSZXRyeUxvZ2ljO1xuZnVuY3Rpb24gdmFsdWVPckRlZmF1bHQodmFsdWUsIGRlZmF1bHRWYWx1ZSkge1xuICAgIGlmICh2YWx1ZSB8fCB2YWx1ZSA9PT0gMCkge1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiBkZWZhdWx0VmFsdWU7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/internal/retry-logic-rx.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/result-rx.js":
/*!****************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/result-rx.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/* eslint-disable-next-line no-unused-vars */\nvar neo4j_driver_core_1 = __webpack_require__(/*! neo4j-driver-core */ \"(rsc)/./node_modules/neo4j-driver-core/lib/index.js\");\nvar rxjs_1 = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/rxjs/dist/esm5/index.js\");\nvar operators_1 = __webpack_require__(/*! rxjs/operators */ \"(rsc)/./node_modules/rxjs/dist/cjs/operators/index.js\");\nvar States = {\n    READY: 0,\n    STREAMING: 1,\n    COMPLETED: 2\n};\n/**\n * The reactive result interface.\n */\nvar RxResult = /** @class */ (function () {\n    /**\n     * @constructor\n     * @protected\n     * @param {Observable<Result>} result - An observable of single Result instance to relay requests.\n     * @param {number} state - The streaming state\n     */\n    function RxResult(result, state) {\n        var replayedResult = result.pipe((0, operators_1.publishReplay)(1), (0, operators_1.refCount)());\n        this._result = replayedResult;\n        this._keys = replayedResult.pipe((0, operators_1.mergeMap)(function (r) { return (0, rxjs_1.from)(r.keys()); }), (0, operators_1.publishReplay)(1), (0, operators_1.refCount)());\n        this._records = undefined;\n        this._controls = new StreamControl();\n        this._summary = new rxjs_1.ReplaySubject();\n        this._state = state || States.READY;\n    }\n    /**\n     * Returns an observable that exposes a single item containing field names\n     * returned by the executing query.\n     *\n     * Errors raised by actual query execution can surface on the returned\n     * observable stream.\n     *\n     * @public\n     * @returns {Observable<string[]>} - An observable stream (with exactly one element) of field names.\n     */\n    RxResult.prototype.keys = function () {\n        return this._keys;\n    };\n    /**\n     * Returns an observable that exposes each record returned by the executing query.\n     *\n     * Errors raised during the streaming phase can surface on the returned observable stream.\n     *\n     * @public\n     * @returns {Observable<Record>} - An observable stream of records.\n     */\n    RxResult.prototype.records = function () {\n        var _this = this;\n        var result = this._result.pipe((0, operators_1.mergeMap)(function (result) {\n            return new rxjs_1.Observable(function (recordsObserver) {\n                return _this._startStreaming({ result: result, recordsObserver: recordsObserver });\n            });\n        }));\n        result.push = function () { return _this._push(); };\n        return result;\n    };\n    /**\n     * Returns an observable that exposes a single item of {@link ResultSummary} that is generated by\n     * the server after the streaming of the executing query is completed.\n     *\n     * *Subscribing to this stream before subscribing to records() stream causes the results to be discarded on the server.*\n     *\n     * @public\n     * @returns {Observable<ResultSummary>} - An observable stream (with exactly one element) of result summary.\n     */\n    RxResult.prototype.consume = function () {\n        var _this = this;\n        return this._result.pipe((0, operators_1.mergeMap)(function (result) {\n            return new rxjs_1.Observable(function (summaryObserver) {\n                return _this._startStreaming({ result: result, summaryObserver: summaryObserver });\n            });\n        }));\n    };\n    /**\n     * Pauses the automatic streaming of records.\n     *\n     * This method provides a way of control the flow of records\n     *\n     * @experimental\n     */\n    RxResult.prototype.pause = function () {\n        this._controls.pause();\n    };\n    /**\n     * Resumes the automatic streaming of records.\n     *\n     * This method won't need to be called in normal stream operation. It only applies to the case when the stream is paused.\n     *\n     * This method is method won't start the consuming records if the {@link records} stream didn't get subscribed.\n     * @experimental\n     * @returns {Promise<void>} - A promise that resolves when the stream is resumed.\n     */\n    RxResult.prototype.resume = function () {\n        return this._controls.resume();\n    };\n    /**\n     * Pushes the next record to the stream.\n     *\n     * This method automatic pause the auto-streaming of records and then push next record to the stream.\n     *\n     * For returning the automatic streaming of records, use {@link resume} method.\n     *\n     * @experimental\n     * @returns {Promise<void>} - A promise that resolves when the push is completed.\n     */\n    RxResult.prototype.push = function () {\n        return this._controls.push();\n    };\n    RxResult.prototype._startStreaming = function (_a) {\n        var _b = _a === void 0 ? {} : _a, result = _b.result, _c = _b.recordsObserver, recordsObserver = _c === void 0 ? null : _c, _d = _b.summaryObserver, summaryObserver = _d === void 0 ? null : _d;\n        var subscriptions = [];\n        if (summaryObserver) {\n            subscriptions.push(this._summary.subscribe(summaryObserver));\n        }\n        if (this._state < States.STREAMING) {\n            this._state = States.STREAMING;\n            this._setupRecordsStream(result);\n            if (recordsObserver) {\n                subscriptions.push(this._records.subscribe(recordsObserver));\n            }\n            else {\n                result._cancel();\n            }\n            subscriptions.push({\n                unsubscribe: function () {\n                    if (result._cancel) {\n                        result._cancel();\n                    }\n                }\n            });\n        }\n        else if (recordsObserver) {\n            recordsObserver.error((0, neo4j_driver_core_1.newError)('Streaming has already started/consumed with a previous records or summary subscription.'));\n        }\n        return function () {\n            subscriptions.forEach(function (s) { return s.unsubscribe(); });\n        };\n    };\n    /**\n     * Create a {@link Observable} for the current {@link RxResult}\n     *\n     *\n     * @package\n     * @experimental\n     * @since 5.0\n     * @return {Observable<RxResult>}\n     */\n    RxResult.prototype._toObservable = function () {\n        var _this = this;\n        function wrap(result) {\n            return new rxjs_1.Observable(function (observer) {\n                observer.next(result);\n                observer.complete();\n            });\n        }\n        return new rxjs_1.Observable(function (observer) {\n            _this._result.subscribe({\n                complete: function () { return observer.complete(); },\n                next: function (result) { return observer.next(new RxResult(wrap(result)), _this._state); },\n                error: function (e) { return observer.error(e); }\n            });\n        });\n    };\n    RxResult.prototype._setupRecordsStream = function (result) {\n        var _this = this;\n        if (this._records) {\n            return this._records;\n        }\n        this._records = createFullyControlledSubject(result[Symbol.asyncIterator](), {\n            complete: function () { return __awaiter(_this, void 0, void 0, function () {\n                var _a, _b;\n                return __generator(this, function (_c) {\n                    switch (_c.label) {\n                        case 0:\n                            this._state = States.COMPLETED;\n                            _b = (_a = this._summary).next;\n                            return [4 /*yield*/, result.summary()];\n                        case 1:\n                            _b.apply(_a, [_c.sent()]);\n                            this._summary.complete();\n                            return [2 /*return*/];\n                    }\n                });\n            }); },\n            error: function (error) {\n                _this._state = States.COMPLETED;\n                _this._summary.error(error);\n            }\n        }, this._controls);\n        return this._records;\n    };\n    return RxResult;\n}());\nexports[\"default\"] = RxResult;\nfunction createFullyControlledSubject(iterator, completeObserver, streamControl) {\n    var _this = this;\n    if (streamControl === void 0) { streamControl = new StreamControl(); }\n    var subject = new rxjs_1.Subject();\n    var pushNextValue = function (result) { return __awaiter(_this, void 0, void 0, function () {\n        var _a, done, value, error_1;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    _b.trys.push([0, 2, 3, 4]);\n                    streamControl.pushing = true;\n                    return [4 /*yield*/, result];\n                case 1:\n                    _a = _b.sent(), done = _a.done, value = _a.value;\n                    if (done) {\n                        subject.complete();\n                        completeObserver.complete();\n                    }\n                    else {\n                        subject.next(value);\n                        if (!streamControl.paused) {\n                            pushNextValue(iterator.next())\n                                .catch(function () { });\n                        }\n                    }\n                    return [3 /*break*/, 4];\n                case 2:\n                    error_1 = _b.sent();\n                    subject.error(error_1);\n                    completeObserver.error(error_1);\n                    return [3 /*break*/, 4];\n                case 3:\n                    streamControl.pushing = false;\n                    return [7 /*endfinally*/];\n                case 4: return [2 /*return*/];\n            }\n        });\n    }); };\n    function push(value) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, pushNextValue(iterator.next(value))];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    }\n    streamControl.pusher = push;\n    push();\n    return subject;\n}\nvar StreamControl = /** @class */ (function () {\n    function StreamControl(push) {\n        if (push === void 0) { push = function () { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {\n            return [2 /*return*/];\n        }); }); }; }\n        var _this = this;\n        this._paused = false;\n        this._pushing = false;\n        this._push = push;\n    }\n    StreamControl.prototype.pause = function () {\n        this._paused = true;\n    };\n    Object.defineProperty(StreamControl.prototype, \"paused\", {\n        get: function () {\n            return this._paused;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(StreamControl.prototype, \"pushing\", {\n        get: function () {\n            return this._pushing;\n        },\n        set: function (pushing) {\n            this._pushing = pushing;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    StreamControl.prototype.resume = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var wasPaused;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        wasPaused = this._paused;\n                        this._paused = false;\n                        if (!(wasPaused && !this._pushing)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._push()];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    StreamControl.prototype.push = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.pause();\n                        return [4 /*yield*/, this._push()];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Object.defineProperty(StreamControl.prototype, \"pusher\", {\n        get: function () {\n            return this._push;\n        },\n        set: function (push) {\n            this._push = push;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return StreamControl;\n}());//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/result-rx.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/session-rx.js":
/*!*****************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/session-rx.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar rxjs_1 = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/rxjs/dist/esm5/index.js\");\nvar operators_1 = __webpack_require__(/*! rxjs/operators */ \"(rsc)/./node_modules/rxjs/dist/cjs/operators/index.js\");\nvar result_rx_1 = __importDefault(__webpack_require__(/*! ./result-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/result-rx.js\"));\n// eslint-disable-next-line no-unused-vars\nvar neo4j_driver_core_1 = __webpack_require__(/*! neo4j-driver-core */ \"(rsc)/./node_modules/neo4j-driver-core/lib/index.js\");\nvar transaction_rx_1 = __importDefault(__webpack_require__(/*! ./transaction-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/transaction-rx.js\"));\nvar transaction_managed_rx_1 = __importDefault(__webpack_require__(/*! ./transaction-managed-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/transaction-managed-rx.js\"));\nvar retry_logic_rx_1 = __importDefault(__webpack_require__(/*! ./internal/retry-logic-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/internal/retry-logic-rx.js\"));\nvar _a = neo4j_driver_core_1.internal.constants, ACCESS_MODE_READ = _a.ACCESS_MODE_READ, ACCESS_MODE_WRITE = _a.ACCESS_MODE_WRITE, TELEMETRY_APIS = _a.TELEMETRY_APIS, TxConfig = neo4j_driver_core_1.internal.txConfig.TxConfig;\n/**\n * A Reactive session, which provides the same functionality as {@link Session} but through a Reactive API.\n */\nvar RxSession = /** @class */ (function () {\n    /**\n     * Constructs a reactive session with given default session instance and provided driver configuration.\n     *\n     * @protected\n     * @param {Object} param - Object parameter\n     * @param {Session} param.session - The underlying session instance to relay requests\n     */\n    function RxSession(_a) {\n        var _b = _a === void 0 ? {} : _a, session = _b.session, config = _b.config, log = _b.log;\n        this._session = session;\n        this._retryLogic = _createRetryLogic(config);\n        this._log = log;\n    }\n    /**\n     * Creates a reactive result that will execute the  query with the provided parameters and the provided\n     * transaction configuration that applies to the underlying auto-commit transaction.\n     *\n     * @public\n     * @param {string} query - Query to be executed.\n     * @param {Object} parameters - Parameter values to use in query execution.\n     * @param {TransactionConfig} transactionConfig - Configuration for the new auto-commit transaction.\n     * @returns {RxResult} - A reactive result\n     */\n    RxSession.prototype.run = function (query, parameters, transactionConfig) {\n        var _this = this;\n        return new result_rx_1.default(new rxjs_1.Observable(function (observer) {\n            try {\n                observer.next(_this._session.run(query, parameters, transactionConfig));\n                observer.complete();\n            }\n            catch (err) {\n                observer.error(err);\n            }\n            return function () { };\n        }));\n    };\n    /**\n     * Starts a new explicit transaction with the provided transaction configuration.\n     *\n     * @public\n     * @param {TransactionConfig} transactionConfig - Configuration for the new transaction.\n     * @returns {Observable<RxTransaction>} - A reactive stream that will generate at most **one** RxTransaction instance.\n     */\n    RxSession.prototype.beginTransaction = function (transactionConfig) {\n        return this._beginTransaction(this._session._mode, transactionConfig, { api: TELEMETRY_APIS.UNMANAGED_TRANSACTION });\n    };\n    /**\n     * Executes the provided unit of work in a {@link READ} reactive transaction which is created with the provided\n     * transaction configuration.\n     * @public\n     * @deprecated This method will be removed in version 6.0. Please, use {@link RxSession#executeRead} instead.\n     * @param {function(txc: RxTransaction): Observable} work - A unit of work to be executed.\n     * @param {TransactionConfig} transactionConfig - Configuration for the enclosing transaction created by the driver.\n     * @returns {Observable} - A reactive stream returned by the unit of work.\n     */\n    RxSession.prototype.readTransaction = function (work, transactionConfig) {\n        return this._runTransaction(ACCESS_MODE_READ, work, transactionConfig);\n    };\n    /**\n     * Executes the provided unit of work in a {@link WRITE} reactive transaction which is created with the provided\n     * transaction configuration.\n     * @public\n     * @deprecated This method will be removed in version 6.0. Please, use {@link RxSession#executeWrite} instead.\n     * @param {function(txc: RxTransaction): Observable} work - A unit of work to be executed.\n     * @param {TransactionConfig} transactionConfig - Configuration for the enclosing transaction created by the driver.\n     * @returns {Observable} - A reactive stream returned by the unit of work.\n     */\n    RxSession.prototype.writeTransaction = function (work, transactionConfig) {\n        return this._runTransaction(ACCESS_MODE_WRITE, work, transactionConfig);\n    };\n    /**\n     * Executes the provided unit of work in a {@link READ} reactive transaction which is created with the provided\n     * transaction configuration.\n     * @public\n     * @param {function(txc: RxManagedTransaction): Observable} work - A unit of work to be executed.\n     * @param {TransactionConfig} transactionConfig - Configuration for the enclosing transaction created by the driver.\n     * @returns {Observable} - A reactive stream returned by the unit of work.\n     */\n    RxSession.prototype.executeRead = function (work, transactionConfig) {\n        return this._executeInTransaction(ACCESS_MODE_READ, work, transactionConfig);\n    };\n    /**\n     * Executes the provided unit of work in a {@link WRITE} reactive transaction which is created with the provided\n     * transaction configuration.\n     * @public\n     * @param {function(txc: RxManagedTransaction): Observable} work - A unit of work to be executed.\n     * @param {TransactionConfig} transactionConfig - Configuration for the enclosing transaction created by the driver.\n     * @returns {Observable} - A reactive stream returned by the unit of work.\n     */\n    RxSession.prototype.executeWrite = function (work, transactionConfig) {\n        return this._executeInTransaction(ACCESS_MODE_WRITE, work, transactionConfig);\n    };\n    /**\n     * @private\n     * @param {function(txc: RxManagedTransaction): Observable} work\n     * @param {TransactionConfig} transactionConfig\n     * @returns {Observable}\n     */\n    RxSession.prototype._executeInTransaction = function (accessMode, work, transactionConfig) {\n        var wrapper = function (txc) { return new transaction_managed_rx_1.default({\n            run: txc.run.bind(txc)\n        }); };\n        return this._runTransaction(accessMode, work, transactionConfig, wrapper);\n    };\n    /**\n     * Closes this reactive session.\n     *\n     * @public\n     * @returns {Observable} - An empty reactive stream\n     */\n    RxSession.prototype.close = function () {\n        var _this = this;\n        return new rxjs_1.Observable(function (observer) {\n            _this._session\n                .close()\n                .then(function () {\n                observer.complete();\n            })\n                .catch(function (err) { return observer.error(err); });\n        });\n    };\n    RxSession.prototype[Symbol.asyncDispose] = function () {\n        return this.close();\n    };\n    /**\n     * Returns the bookmarks received following the last successfully completed query, which is executed\n     * either in an {@link RxTransaction} obtained from this session instance or directly through one of\n     * the {@link RxSession#run} method of this session instance.\n     *\n     * If no bookmarks were received or if this transaction was rolled back, the bookmarks value will not be\n     * changed.\n     *\n     * @deprecated This method will be removed in 6.0 version. Please, use {@link RxSession#lastBookmarks} instead.\n     *\n     * @public\n     * @returns {string[]}\n     */\n    RxSession.prototype.lastBookmark = function () {\n        return this.lastBookmarks();\n    };\n    /**\n     * Returns the bookmarks received following the last successfully completed query, which is executed\n     * either in an {@link RxTransaction} obtained from this session instance or directly through one of\n     * the {@link RxSession#run} method of this session instance.\n     *\n     * If no bookmarks were received or if this transaction was rolled back, the bookmarks value will not be\n     * changed.\n     *\n     * @public\n     * @returns {string[]}\n     */\n    RxSession.prototype.lastBookmarks = function () {\n        return this._session.lastBookmarks();\n    };\n    /**\n     * @private\n     */\n    RxSession.prototype._beginTransaction = function (accessMode, transactionConfig, apiTelemetryConfig) {\n        var _this = this;\n        var txConfig = TxConfig.empty();\n        if (transactionConfig) {\n            txConfig = new TxConfig(transactionConfig, this._log);\n        }\n        return new rxjs_1.Observable(function (observer) {\n            try {\n                _this._session._beginTransaction(accessMode, txConfig, apiTelemetryConfig)\n                    .then(function (tx) {\n                    observer.next(new transaction_rx_1.default(tx));\n                    observer.complete();\n                })\n                    .catch(function (err) { return observer.error(err); });\n            }\n            catch (err) {\n                observer.error(err);\n            }\n            return function () { };\n        });\n    };\n    /**\n     * @private\n     */\n    RxSession.prototype._runTransaction = function (accessMode, work, transactionConfig, transactionWrapper) {\n        var _this = this;\n        if (transactionWrapper === void 0) { transactionWrapper = function (tx) { return tx; }; }\n        var txConfig = TxConfig.empty();\n        if (transactionConfig) {\n            txConfig = new TxConfig(transactionConfig);\n        }\n        var context = {\n            apiTelemetryConfig: {\n                api: TELEMETRY_APIS.MANAGED_TRANSACTION,\n                onTelemetrySuccess: function () {\n                    context.apiTelemetryConfig = undefined;\n                }\n            }\n        };\n        return this._retryLogic.retry((0, rxjs_1.of)(1).pipe((0, operators_1.mergeMap)(function () { return _this._beginTransaction(accessMode, txConfig, context.apiTelemetryConfig); }), (0, operators_1.mergeMap)(function (txc) {\n            return (0, rxjs_1.defer)(function () {\n                try {\n                    return work(transactionWrapper(txc));\n                }\n                catch (err) {\n                    return (0, rxjs_1.throwError)(function () { return err; });\n                }\n            }).pipe((0, operators_1.catchError)(function (err) { return txc.rollback().pipe((0, operators_1.concatWith)((0, rxjs_1.throwError)(function () { return err; }))); }), (0, operators_1.concatWith)(txc.commit()));\n        })));\n    };\n    return RxSession;\n}());\nexports[\"default\"] = RxSession;\nfunction _createRetryLogic(config) {\n    var maxRetryTimeout = config && config.maxTransactionRetryTime\n        ? config.maxTransactionRetryTime\n        : null;\n    return new retry_logic_rx_1.default({ maxRetryTimeout: maxRetryTimeout });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/session-rx.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/transaction-managed-rx.js":
/*!*****************************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/transaction-managed-rx.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line no-unused-vars\nvar result_rx_1 = __importDefault(__webpack_require__(/*! ./result-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/result-rx.js\"));\n// eslint-disable-next-line no-unused-vars\nvar transaction_rx_1 = __importDefault(__webpack_require__(/*! ./transaction-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/transaction-rx.js\"));\n/**\n * Represents a rx transaction that is managed by the transaction executor.\n *\n * @public\n */\nvar RxManagedTransaction = /** @class */ (function () {\n    /**\n     * @private\n     */\n    function RxManagedTransaction(_a) {\n        var run = _a.run;\n        this._run = run;\n    }\n    /**\n     * @private\n     * @param {RxTransaction} txc - The transaction to be wrapped\n     * @returns {RxManagedTransaction} The managed transaction\n     */\n    RxManagedTransaction.fromTransaction = function (txc) {\n        return new RxManagedTransaction({\n            run: txc.run.bind(txc)\n        });\n    };\n    /**\n     * Creates a reactive result that will execute the query in this transaction, with the provided parameters.\n     *\n     * @public\n     * @param {string} query - Query to be executed.\n     * @param {Object} parameters - Parameter values to use in query execution.\n     * @returns {RxResult} - A reactive result\n     */\n    RxManagedTransaction.prototype.run = function (query, parameters) {\n        return this._run(query, parameters);\n    };\n    return RxManagedTransaction;\n}());\nexports[\"default\"] = RxManagedTransaction;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmVvNGotZHJpdmVyL2xpYi90cmFuc2FjdGlvbi1tYW5hZ2VkLXJ4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxtQkFBTyxDQUFDLHVFQUFhO0FBQ3ZEO0FBQ0EsdUNBQXVDLG1CQUFPLENBQUMsaUZBQWtCO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZUFBZTtBQUM5QixpQkFBaUIsc0JBQXNCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsa0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9uZW80ai1kcml2ZXIvbGliL3RyYW5zYWN0aW9uLW1hbmFnZWQtcnguanM/NWVmZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2ltcG9ydERlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0RGVmYXVsdCkgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgXCJkZWZhdWx0XCI6IG1vZCB9O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8qKlxuICogQ29weXJpZ2h0IChjKSBcIk5lbzRqXCJcbiAqIE5lbzRqIFN3ZWRlbiBBQiBbaHR0cHM6Ly9uZW80ai5jb21dXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcbnZhciByZXN1bHRfcnhfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi9yZXN1bHQtcnhcIikpO1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC12YXJzXG52YXIgdHJhbnNhY3Rpb25fcnhfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi90cmFuc2FjdGlvbi1yeFwiKSk7XG4vKipcbiAqIFJlcHJlc2VudHMgYSByeCB0cmFuc2FjdGlvbiB0aGF0IGlzIG1hbmFnZWQgYnkgdGhlIHRyYW5zYWN0aW9uIGV4ZWN1dG9yLlxuICpcbiAqIEBwdWJsaWNcbiAqL1xudmFyIFJ4TWFuYWdlZFRyYW5zYWN0aW9uID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xuICAgIC8qKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgZnVuY3Rpb24gUnhNYW5hZ2VkVHJhbnNhY3Rpb24oX2EpIHtcbiAgICAgICAgdmFyIHJ1biA9IF9hLnJ1bjtcbiAgICAgICAgdGhpcy5fcnVuID0gcnVuO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqIEBwYXJhbSB7UnhUcmFuc2FjdGlvbn0gdHhjIC0gVGhlIHRyYW5zYWN0aW9uIHRvIGJlIHdyYXBwZWRcbiAgICAgKiBAcmV0dXJucyB7UnhNYW5hZ2VkVHJhbnNhY3Rpb259IFRoZSBtYW5hZ2VkIHRyYW5zYWN0aW9uXG4gICAgICovXG4gICAgUnhNYW5hZ2VkVHJhbnNhY3Rpb24uZnJvbVRyYW5zYWN0aW9uID0gZnVuY3Rpb24gKHR4Yykge1xuICAgICAgICByZXR1cm4gbmV3IFJ4TWFuYWdlZFRyYW5zYWN0aW9uKHtcbiAgICAgICAgICAgIHJ1bjogdHhjLnJ1bi5iaW5kKHR4YylcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGEgcmVhY3RpdmUgcmVzdWx0IHRoYXQgd2lsbCBleGVjdXRlIHRoZSBxdWVyeSBpbiB0aGlzIHRyYW5zYWN0aW9uLCB3aXRoIHRoZSBwcm92aWRlZCBwYXJhbWV0ZXJzLlxuICAgICAqXG4gICAgICogQHB1YmxpY1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBxdWVyeSAtIFF1ZXJ5IHRvIGJlIGV4ZWN1dGVkLlxuICAgICAqIEBwYXJhbSB7T2JqZWN0fSBwYXJhbWV0ZXJzIC0gUGFyYW1ldGVyIHZhbHVlcyB0byB1c2UgaW4gcXVlcnkgZXhlY3V0aW9uLlxuICAgICAqIEByZXR1cm5zIHtSeFJlc3VsdH0gLSBBIHJlYWN0aXZlIHJlc3VsdFxuICAgICAqL1xuICAgIFJ4TWFuYWdlZFRyYW5zYWN0aW9uLnByb3RvdHlwZS5ydW4gPSBmdW5jdGlvbiAocXVlcnksIHBhcmFtZXRlcnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3J1bihxdWVyeSwgcGFyYW1ldGVycyk7XG4gICAgfTtcbiAgICByZXR1cm4gUnhNYW5hZ2VkVHJhbnNhY3Rpb247XG59KCkpO1xuZXhwb3J0cy5kZWZhdWx0ID0gUnhNYW5hZ2VkVHJhbnNhY3Rpb247Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/transaction-managed-rx.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/transaction-rx.js":
/*!*********************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/transaction-rx.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar rxjs_1 = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/rxjs/dist/esm5/index.js\");\nvar result_rx_1 = __importDefault(__webpack_require__(/*! ./result-rx */ \"(rsc)/./node_modules/neo4j-driver/lib/result-rx.js\"));\n// eslint-disable-next-line no-unused-vars\nvar neo4j_driver_core_1 = __importDefault(__webpack_require__(/*! neo4j-driver-core */ \"(rsc)/./node_modules/neo4j-driver-core/lib/index.js\"));\n/**\n * A reactive transaction, which provides the same functionality as {@link Transaction} but through a Reactive API.\n */\nvar RxTransaction = /** @class */ (function () {\n    /**\n     * @constructor\n     * @protected\n     * @param {Transaction} txc - The underlying transaction instance to relay requests\n     */\n    function RxTransaction(txc) {\n        this._txc = txc;\n    }\n    /**\n     * Creates a reactive result that will execute the query in this transaction, with the provided parameters.\n     *\n     * @public\n     * @param {string} query - Query to be executed.\n     * @param {Object} parameters - Parameter values to use in query execution.\n     * @returns {RxResult} - A reactive result\n     */\n    RxTransaction.prototype.run = function (query, parameters) {\n        var _this = this;\n        return new result_rx_1.default(new rxjs_1.Observable(function (observer) {\n            try {\n                observer.next(_this._txc.run(query, parameters));\n                observer.complete();\n            }\n            catch (err) {\n                observer.error(err);\n            }\n            return function () { };\n        }));\n    };\n    /**\n     *  Commits the transaction.\n     *\n     * @public\n     * @returns {Observable} - An empty observable\n     */\n    RxTransaction.prototype.commit = function () {\n        var _this = this;\n        return new rxjs_1.Observable(function (observer) {\n            _this._txc\n                .commit()\n                .then(function () {\n                observer.complete();\n            })\n                .catch(function (err) { return observer.error(err); });\n        });\n    };\n    /**\n     *  Rolls back the transaction.\n     *\n     * @public\n     * @returns {Observable} - An empty observable\n     */\n    RxTransaction.prototype.rollback = function () {\n        var _this = this;\n        return new rxjs_1.Observable(function (observer) {\n            _this._txc\n                .rollback()\n                .then(function () {\n                observer.complete();\n            })\n                .catch(function (err) { return observer.error(err); });\n        });\n    };\n    /**\n     * Check if this transaction is active, which means commit and rollback did not happen.\n     * @return {boolean} `true` when not committed and not rolled back, `false` otherwise.\n     */\n    RxTransaction.prototype.isOpen = function () {\n        return this._txc.isOpen();\n    };\n    /**\n     * Closes the transaction\n     *\n     * This method will roll back the transaction if it is not already committed or rolled back.\n     *\n     * @returns {Observable} - An empty observable\n     */\n    RxTransaction.prototype.close = function () {\n        var _this = this;\n        return new rxjs_1.Observable(function (observer) {\n            _this._txc\n                .close()\n                .then(function () {\n                observer.complete();\n            })\n                .catch(function (err) { return observer.error(err); });\n        });\n    };\n    return RxTransaction;\n}());\nexports[\"default\"] = RxTransaction;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/transaction-rx.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/neo4j-driver/lib/version.js":
/*!**************************************************!*\
  !*** ./node_modules/neo4j-driver/lib/version.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * Copyright (c) \"Neo4j\"\n * Neo4j Sweden AB [https://neo4j.com]\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// DO NOT CHANGE THE VERSION BELOW HERE\n// This is set by the build system at release time, using\n//\n// gulp set --x <releaseversion>\n//\n// This is set up this way to keep the version in the code in\n// sync with the npm package version, and to allow the build\n// system to control version names at packaging time.\nexports[\"default\"] = '5.28.1';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmVvNGotZHJpdmVyL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9ub2RlX21vZHVsZXMvbmVvNGotZHJpdmVyL2xpYi92ZXJzaW9uLmpzP2YxZmQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKipcbiAqIENvcHlyaWdodCAoYykgXCJOZW80alwiXG4gKiBOZW80aiBTd2VkZW4gQUIgW2h0dHBzOi8vbmVvNGouY29tXVxuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8gRE8gTk9UIENIQU5HRSBUSEUgVkVSU0lPTiBCRUxPVyBIRVJFXG4vLyBUaGlzIGlzIHNldCBieSB0aGUgYnVpbGQgc3lzdGVtIGF0IHJlbGVhc2UgdGltZSwgdXNpbmdcbi8vXG4vLyBndWxwIHNldCAtLXggPHJlbGVhc2V2ZXJzaW9uPlxuLy9cbi8vIFRoaXMgaXMgc2V0IHVwIHRoaXMgd2F5IHRvIGtlZXAgdGhlIHZlcnNpb24gaW4gdGhlIGNvZGUgaW5cbi8vIHN5bmMgd2l0aCB0aGUgbnBtIHBhY2thZ2UgdmVyc2lvbiwgYW5kIHRvIGFsbG93IHRoZSBidWlsZFxuLy8gc3lzdGVtIHRvIGNvbnRyb2wgdmVyc2lvbiBuYW1lcyBhdCBwYWNrYWdpbmcgdGltZS5cbmV4cG9ydHMuZGVmYXVsdCA9ICc1LjI4LjEnOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/neo4j-driver/lib/version.js\n");

/***/ })

};
;