"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios";
exports.ids = ["vendor-chunks/gaxios"];
exports.modules = {

/***/ "(rsc)/./node_modules/gaxios/build/src/common.js":
/*!*************************************************!*\
  !*** ./node_modules/gaxios/build/src/common.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosError = exports.GAXIOS_ERROR_SYMBOL = void 0;\nexports.defaultErrorRedactor = defaultErrorRedactor;\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/gaxios/build/src/util.js\");\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\n/**\n * Support `instanceof` operator for `GaxiosError`s in different versions of this library.\n *\n * @see {@link GaxiosError[Symbol.hasInstance]}\n */\nexports.GAXIOS_ERROR_SYMBOL = Symbol.for(`${util_1.pkg.name}-gaxios-error`);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nclass GaxiosError extends Error {\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[GAXIOS_ERROR_SYMBOL]}\n     */\n    static [(_a = exports.GAXIOS_ERROR_SYMBOL, Symbol.hasInstance)](instance) {\n        if (instance &&\n            typeof instance === 'object' &&\n            exports.GAXIOS_ERROR_SYMBOL in instance &&\n            instance[exports.GAXIOS_ERROR_SYMBOL] === util_1.pkg.version) {\n            return true;\n        }\n        // fallback to native\n        return Function.prototype[Symbol.hasInstance].call(GaxiosError, instance);\n    }\n    constructor(message, config, response, error) {\n        var _b;\n        super(message);\n        this.config = config;\n        this.response = response;\n        this.error = error;\n        /**\n         * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n         *\n         * @see {@link GAXIOS_ERROR_SYMBOL}\n         * @see {@link GaxiosError[Symbol.hasInstance]}\n         * @see {@link https://github.com/microsoft/TypeScript/issues/13965#issuecomment-278570200}\n         * @see {@link https://stackoverflow.com/questions/46618852/require-and-instanceof}\n         * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/@@hasInstance#reverting_to_default_instanceof_behavior}\n         */\n        this[_a] = util_1.pkg.version;\n        // deep-copy config as we do not want to mutate\n        // the existing config for future retries/use\n        this.config = (0, extend_1.default)(true, {}, config);\n        if (this.response) {\n            this.response.config = (0, extend_1.default)(true, {}, this.response.config);\n        }\n        if (this.response) {\n            try {\n                this.response.data = translateData(this.config.responseType, (_b = this.response) === null || _b === void 0 ? void 0 : _b.data);\n            }\n            catch (_c) {\n                // best effort - don't throw an error within an error\n                // we could set `this.response.config.responseType = 'unknown'`, but\n                // that would mutate future calls with this config object.\n            }\n            this.status = this.response.status;\n        }\n        if (error && 'code' in error && error.code) {\n            this.code = error.code;\n        }\n        if (config.errorRedactor) {\n            config.errorRedactor({\n                config: this.config,\n                response: this.response,\n            });\n        }\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch (responseType) {\n        case 'stream':\n            return data;\n        case 'json':\n            return JSON.parse(JSON.stringify(data));\n        case 'arraybuffer':\n            return JSON.parse(Buffer.from(data).toString('utf8'));\n        case 'blob':\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n/**\n * An experimental error redactor.\n *\n * @param config Config to potentially redact properties of\n * @param response Config to potentially redact properties of\n *\n * @experimental\n */\nfunction defaultErrorRedactor(data) {\n    const REDACT = '<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.';\n    function redactHeaders(headers) {\n        if (!headers)\n            return;\n        for (const key of Object.keys(headers)) {\n            // any casing of `Authentication`\n            if (/^authentication$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // any casing of `Authorization`\n            if (/^authorization$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // anything containing secret, such as 'client secret'\n            if (/secret/i.test(key)) {\n                headers[key] = REDACT;\n            }\n        }\n    }\n    function redactString(obj, key) {\n        if (typeof obj === 'object' &&\n            obj !== null &&\n            typeof obj[key] === 'string') {\n            const text = obj[key];\n            if (/grant_type=/i.test(text) ||\n                /assertion=/i.test(text) ||\n                /secret/i.test(text)) {\n                obj[key] = REDACT;\n            }\n        }\n    }\n    function redactObject(obj) {\n        if (typeof obj === 'object' && obj !== null) {\n            if ('grant_type' in obj) {\n                obj['grant_type'] = REDACT;\n            }\n            if ('assertion' in obj) {\n                obj['assertion'] = REDACT;\n            }\n            if ('client_secret' in obj) {\n                obj['client_secret'] = REDACT;\n            }\n        }\n    }\n    if (data.config) {\n        redactHeaders(data.config.headers);\n        redactString(data.config, 'data');\n        redactObject(data.config.data);\n        redactString(data.config, 'body');\n        redactObject(data.config.body);\n        try {\n            const url = new url_1.URL('', data.config.url);\n            if (url.searchParams.has('token')) {\n                url.searchParams.set('token', REDACT);\n            }\n            if (url.searchParams.has('client_secret')) {\n                url.searchParams.set('client_secret', REDACT);\n            }\n            data.config.url = url.toString();\n        }\n        catch (_b) {\n            // ignore error - no need to parse an invalid URL\n        }\n    }\n    if (data.response) {\n        defaultErrorRedactor({ config: data.response.config });\n        redactHeaders(data.response.headers);\n        redactString(data.response, 'data');\n        redactObject(data.response.data);\n    }\n    return data;\n}\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/gaxios.js":
/*!*************************************************!*\
  !*** ./node_modules/gaxios/build/src/gaxios.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _Gaxios_instances, _a, _Gaxios_urlMayUseProxy, _Gaxios_applyRequestInterceptors, _Gaxios_applyResponseInterceptors, _Gaxios_prepareRequest, _Gaxios_proxyAgent, _Gaxios_getProxyAgent;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\"));\nconst querystring_1 = __importDefault(__webpack_require__(/*! querystring */ \"querystring\"));\nconst is_stream_1 = __importDefault(__webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/is-stream/index.js\"));\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nconst retry_1 = __webpack_require__(/*! ./retry */ \"(rsc)/./node_modules/gaxios/build/src/retry.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/index.js\");\nconst interceptor_1 = __webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/gaxios/build/src/interceptor.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst fetch = hasFetch() ? window.fetch : node_fetch_1.default;\nfunction hasWindow() {\n    return typeof window !== 'undefined' && !!window;\n}\nfunction hasFetch() {\n    return hasWindow() && !!window.fetch;\n}\nfunction hasBuffer() {\n    return typeof Buffer !== 'undefined';\n}\nfunction hasHeader(options, header) {\n    return !!getHeader(options, header);\n}\nfunction getHeader(options, header) {\n    header = header.toLowerCase();\n    for (const key of Object.keys((options === null || options === void 0 ? void 0 : options.headers) || {})) {\n        if (header === key.toLowerCase()) {\n            return options.headers[key];\n        }\n    }\n    return undefined;\n}\nclass Gaxios {\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */\n    constructor(defaults) {\n        _Gaxios_instances.add(this);\n        this.agentCache = new Map();\n        this.defaults = defaults || {};\n        this.interceptors = {\n            request: new interceptor_1.GaxiosInterceptorManager(),\n            response: new interceptor_1.GaxiosInterceptorManager(),\n        };\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async request(opts = {}) {\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_prepareRequest).call(this, opts);\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyRequestInterceptors).call(this, opts);\n        return __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyResponseInterceptors).call(this, this._request(opts));\n    }\n    async _defaultAdapter(opts) {\n        const fetchImpl = opts.fetchImplementation || fetch;\n        const res = (await fetchImpl(opts.url, opts));\n        const data = await this.getResponseData(opts, res);\n        return this.translateResponse(opts, res, data);\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async _request(opts = {}) {\n        var _b;\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            }\n            else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === 'stream') {\n                    let response = '';\n                    await new Promise(resolve => {\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on('data', chunk => {\n                            response += chunk;\n                        });\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on('end', resolve);\n                    });\n                    translatedResponse.data = response;\n                }\n                throw new common_1.GaxiosError(`Request failed with status code ${translatedResponse.status}`, opts, translatedResponse);\n            }\n            return translatedResponse;\n        }\n        catch (e) {\n            const err = e instanceof common_1.GaxiosError\n                ? e\n                : new common_1.GaxiosError(e.message, opts, undefined, e);\n            const { shouldRetry, config } = await (0, retry_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt =\n                    config.retryConfig.currentRetryAttempt;\n                // The error's config could be redacted - therefore we only want to\n                // copy the retry state over to the existing config\n                opts.retryConfig = (_b = err.config) === null || _b === void 0 ? void 0 : _b.retryConfig;\n                return this._request(opts);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        switch (opts.responseType) {\n            case 'stream':\n                return res.body;\n            case 'json': {\n                let data = await res.text();\n                try {\n                    data = JSON.parse(data);\n                }\n                catch (_b) {\n                    // continue\n                }\n                return data;\n            }\n            case 'arraybuffer':\n                return res.arrayBuffer();\n            case 'blob':\n                return res.blob();\n            case 'text':\n                return res.text();\n            default:\n                return this.getResponseDataFromContentType(res);\n        }\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */\n    validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Encode a set of key/value pars into a querystring format (?foo=bar&baz=boo)\n     * @param params key value pars to encode\n     */\n    paramsSerializer(params) {\n        return querystring_1.default.stringify(params);\n    }\n    translateResponse(opts, res, data) {\n        // headers need to be converted from a map to an obj\n        const headers = {};\n        res.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            config: opts,\n            data: data,\n            headers,\n            status: res.status,\n            statusText: res.statusText,\n            // XMLHttpRequestLike\n            request: {\n                responseURL: res.url,\n            },\n        };\n    }\n    /**\n     * Attempts to parse a response by looking at the Content-Type header.\n     * @param {FetchResponse} response the HTTP response.\n     * @returns {Promise<any>} a promise that resolves to the response data.\n     */\n    async getResponseDataFromContentType(response) {\n        let contentType = response.headers.get('Content-Type');\n        if (contentType === null) {\n            // Maintain existing functionality by calling text()\n            return response.text();\n        }\n        contentType = contentType.toLowerCase();\n        if (contentType.includes('application/json')) {\n            let data = await response.text();\n            try {\n                data = JSON.parse(data);\n            }\n            catch (_b) {\n                // continue\n            }\n            return data;\n        }\n        else if (contentType.match(/^text\\//)) {\n            return response.text();\n        }\n        else {\n            // If the content type is something not easily handled, just return the raw data (blob)\n            return response.blob();\n        }\n    }\n    /**\n     * Creates an async generator that yields the pieces of a multipart/related request body.\n     * This implementation follows the spec: https://www.ietf.org/rfc/rfc2387.txt. However, recursive\n     * multipart/related requests are not currently supported.\n     *\n     * @param {GaxioMultipartOptions[]} multipartOptions the pieces to turn into a multipart/related body.\n     * @param {string} boundary the boundary string to be placed between each part.\n     */\n    async *getMultipartRequest(multipartOptions, boundary) {\n        const finale = `--${boundary}--`;\n        for (const currentPart of multipartOptions) {\n            const partContentType = currentPart.headers['Content-Type'] || 'application/octet-stream';\n            const preamble = `--${boundary}\\r\\nContent-Type: ${partContentType}\\r\\n\\r\\n`;\n            yield preamble;\n            if (typeof currentPart.content === 'string') {\n                yield currentPart.content;\n            }\n            else {\n                yield* currentPart.content;\n            }\n            yield '\\r\\n';\n        }\n        yield finale;\n    }\n}\nexports.Gaxios = Gaxios;\n_a = Gaxios, _Gaxios_instances = new WeakSet(), _Gaxios_urlMayUseProxy = function _Gaxios_urlMayUseProxy(url, noProxy = []) {\n    var _b, _c;\n    const candidate = new url_1.URL(url);\n    const noProxyList = [...noProxy];\n    const noProxyEnvList = ((_c = ((_b = process.env.NO_PROXY) !== null && _b !== void 0 ? _b : process.env.no_proxy)) === null || _c === void 0 ? void 0 : _c.split(',')) || [];\n    for (const rule of noProxyEnvList) {\n        noProxyList.push(rule.trim());\n    }\n    for (const rule of noProxyList) {\n        // Match regex\n        if (rule instanceof RegExp) {\n            if (rule.test(candidate.toString())) {\n                return false;\n            }\n        }\n        // Match URL\n        else if (rule instanceof url_1.URL) {\n            if (rule.origin === candidate.origin) {\n                return false;\n            }\n        }\n        // Match string regex\n        else if (rule.startsWith('*.') || rule.startsWith('.')) {\n            const cleanedRule = rule.replace(/^\\*\\./, '.');\n            if (candidate.hostname.endsWith(cleanedRule)) {\n                return false;\n            }\n        }\n        // Basic string match\n        else if (rule === candidate.origin ||\n            rule === candidate.hostname ||\n            rule === candidate.href) {\n            return false;\n        }\n    }\n    return true;\n}, _Gaxios_applyRequestInterceptors = \n/**\n * Applies the request interceptors. The request interceptors are applied after the\n * call to prepareRequest is completed.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */\nasync function _Gaxios_applyRequestInterceptors(options) {\n    let promiseChain = Promise.resolve(options);\n    for (const interceptor of this.interceptors.request.values()) {\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_applyResponseInterceptors = \n/**\n * Applies the response interceptors. The response interceptors are applied after the\n * call to request is made.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */\nasync function _Gaxios_applyResponseInterceptors(response) {\n    let promiseChain = Promise.resolve(response);\n    for (const interceptor of this.interceptors.response.values()) {\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_prepareRequest = \n/**\n * Validates the options, merges them with defaults, and prepare request.\n *\n * @param options The original options passed from the client.\n * @returns Prepared options, ready to make a request\n */\nasync function _Gaxios_prepareRequest(options) {\n    var _b, _c, _d, _e;\n    const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n    if (!opts.url) {\n        throw new Error('URL is required.');\n    }\n    // baseUrl has been deprecated, remove in 2.0\n    const baseUrl = opts.baseUrl || opts.baseURL;\n    if (baseUrl) {\n        opts.url = baseUrl.toString() + opts.url;\n    }\n    opts.paramsSerializer = opts.paramsSerializer || this.paramsSerializer;\n    if (opts.params && Object.keys(opts.params).length > 0) {\n        let additionalQueryParams = opts.paramsSerializer(opts.params);\n        if (additionalQueryParams.startsWith('?')) {\n            additionalQueryParams = additionalQueryParams.slice(1);\n        }\n        const prefix = opts.url.toString().includes('?') ? '&' : '?';\n        opts.url = opts.url + prefix + additionalQueryParams;\n    }\n    if (typeof options.maxContentLength === 'number') {\n        opts.size = options.maxContentLength;\n    }\n    if (typeof options.maxRedirects === 'number') {\n        opts.follow = options.maxRedirects;\n    }\n    opts.headers = opts.headers || {};\n    if (opts.multipart === undefined && opts.data) {\n        const isFormData = typeof FormData === 'undefined'\n            ? false\n            : (opts === null || opts === void 0 ? void 0 : opts.data) instanceof FormData;\n        if (is_stream_1.default.readable(opts.data)) {\n            opts.body = opts.data;\n        }\n        else if (hasBuffer() && Buffer.isBuffer(opts.data)) {\n            // Do not attempt to JSON.stringify() a Buffer:\n            opts.body = opts.data;\n            if (!hasHeader(opts, 'Content-Type')) {\n                opts.headers['Content-Type'] = 'application/json';\n            }\n        }\n        else if (typeof opts.data === 'object') {\n            // If www-form-urlencoded content type has been set, but data is\n            // provided as an object, serialize the content using querystring:\n            if (!isFormData) {\n                if (getHeader(opts, 'content-type') ===\n                    'application/x-www-form-urlencoded') {\n                    opts.body = opts.paramsSerializer(opts.data);\n                }\n                else {\n                    // } else if (!(opts.data instanceof FormData)) {\n                    if (!hasHeader(opts, 'Content-Type')) {\n                        opts.headers['Content-Type'] = 'application/json';\n                    }\n                    opts.body = JSON.stringify(opts.data);\n                }\n            }\n        }\n        else {\n            opts.body = opts.data;\n        }\n    }\n    else if (opts.multipart && opts.multipart.length > 0) {\n        // note: once the minimum version reaches Node 16,\n        // this can be replaced with randomUUID() function from crypto\n        // and the dependency on UUID removed\n        const boundary = (0, uuid_1.v4)();\n        opts.headers['Content-Type'] = `multipart/related; boundary=${boundary}`;\n        const bodyStream = new stream_1.PassThrough();\n        opts.body = bodyStream;\n        (0, stream_1.pipeline)(this.getMultipartRequest(opts.multipart, boundary), bodyStream, () => { });\n    }\n    opts.validateStatus = opts.validateStatus || this.validateStatus;\n    opts.responseType = opts.responseType || 'unknown';\n    if (!opts.headers['Accept'] && opts.responseType === 'json') {\n        opts.headers['Accept'] = 'application/json';\n    }\n    opts.method = opts.method || 'GET';\n    const proxy = opts.proxy ||\n        ((_b = process === null || process === void 0 ? void 0 : process.env) === null || _b === void 0 ? void 0 : _b.HTTPS_PROXY) ||\n        ((_c = process === null || process === void 0 ? void 0 : process.env) === null || _c === void 0 ? void 0 : _c.https_proxy) ||\n        ((_d = process === null || process === void 0 ? void 0 : process.env) === null || _d === void 0 ? void 0 : _d.HTTP_PROXY) ||\n        ((_e = process === null || process === void 0 ? void 0 : process.env) === null || _e === void 0 ? void 0 : _e.http_proxy);\n    const urlMayUseProxy = __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_urlMayUseProxy).call(this, opts.url, opts.noProxy);\n    if (opts.agent) {\n        // don't do any of the following options - use the user-provided agent.\n    }\n    else if (proxy && urlMayUseProxy) {\n        const HttpsProxyAgent = await __classPrivateFieldGet(_a, _a, \"m\", _Gaxios_getProxyAgent).call(_a);\n        if (this.agentCache.has(proxy)) {\n            opts.agent = this.agentCache.get(proxy);\n        }\n        else {\n            opts.agent = new HttpsProxyAgent(proxy, {\n                cert: opts.cert,\n                key: opts.key,\n            });\n            this.agentCache.set(proxy, opts.agent);\n        }\n    }\n    else if (opts.cert && opts.key) {\n        // Configure client for mTLS\n        if (this.agentCache.has(opts.key)) {\n            opts.agent = this.agentCache.get(opts.key);\n        }\n        else {\n            opts.agent = new https_1.Agent({\n                cert: opts.cert,\n                key: opts.key,\n            });\n            this.agentCache.set(opts.key, opts.agent);\n        }\n    }\n    if (typeof opts.errorRedactor !== 'function' &&\n        opts.errorRedactor !== false) {\n        opts.errorRedactor = common_1.defaultErrorRedactor;\n    }\n    return opts;\n}, _Gaxios_getProxyAgent = async function _Gaxios_getProxyAgent() {\n    __classPrivateFieldSet(this, _a, __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent) || (await Promise.resolve().then(() => __importStar(__webpack_require__(/*! https-proxy-agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/index.js\")))).HttpsProxyAgent, \"f\", _Gaxios_proxyAgent);\n    return __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent);\n};\n/**\n * A cache for the lazily-loaded proxy agent.\n *\n * Should use {@link Gaxios[#getProxyAgent]} to retrieve.\n */\n// using `import` to dynamically import the types here\n_Gaxios_proxyAgent = { value: void 0 };\n//# sourceMappingURL=gaxios.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/index.js":
/*!************************************************!*\
  !*** ./node_modules/gaxios/build/src/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nexports.request = request;\nconst gaxios_1 = __webpack_require__(/*! ./gaxios */ \"(rsc)/./node_modules/gaxios/build/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_1.Gaxios; } }));\nvar common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return common_1.GaxiosError; } }));\n__exportStar(__webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/gaxios/build/src/interceptor.js\"), exports);\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */\nexports.instance = new gaxios_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */\nasync function request(opts) {\n    return exports.instance.request(opts);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/interceptor.js":
/*!******************************************************!*\
  !*** ./node_modules/gaxios/build/src/interceptor.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosInterceptorManager = void 0;\n/**\n * Class to manage collections of GaxiosInterceptors for both requests and responses.\n */\nclass GaxiosInterceptorManager extends Set {\n}\nexports.GaxiosInterceptorManager = GaxiosInterceptorManager;\n//# sourceMappingURL=interceptor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL3NyYy9pbnRlcmNlcHRvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL2dheGlvcy9idWlsZC9zcmMvaW50ZXJjZXB0b3IuanM/MjZiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIENvcHlyaWdodCAyMDI0IEdvb2dsZSBMTENcbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5HYXhpb3NJbnRlcmNlcHRvck1hbmFnZXIgPSB2b2lkIDA7XG4vKipcbiAqIENsYXNzIHRvIG1hbmFnZSBjb2xsZWN0aW9ucyBvZiBHYXhpb3NJbnRlcmNlcHRvcnMgZm9yIGJvdGggcmVxdWVzdHMgYW5kIHJlc3BvbnNlcy5cbiAqL1xuY2xhc3MgR2F4aW9zSW50ZXJjZXB0b3JNYW5hZ2VyIGV4dGVuZHMgU2V0IHtcbn1cbmV4cG9ydHMuR2F4aW9zSW50ZXJjZXB0b3JNYW5hZ2VyID0gR2F4aW9zSW50ZXJjZXB0b3JNYW5hZ2VyO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJjZXB0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/interceptor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/retry.js":
/*!************************************************!*\
  !*** ./node_modules/gaxios/build/src/retry.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRetryConfig = getRetryConfig;\nasync function getRetryConfig(err) {\n    let config = getConfig(err);\n    if (!err || !err.config || (!config && !err.config.retry)) {\n        return { shouldRetry: false };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry =\n        config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        'GET',\n        'HEAD',\n        'PUT',\n        'OPTIONS',\n        'DELETE',\n    ];\n    config.noResponseRetries =\n        config.noResponseRetries === undefined || config.noResponseRetries === null\n            ? 2\n            : config.noResponseRetries;\n    config.retryDelayMultiplier = config.retryDelayMultiplier\n        ? config.retryDelayMultiplier\n        : 2;\n    config.timeOfFirstRequest = config.timeOfFirstRequest\n        ? config.timeOfFirstRequest\n        : Date.now();\n    config.totalTimeout = config.totalTimeout\n        ? config.totalTimeout\n        : Number.MAX_SAFE_INTEGER;\n    config.maxRetryDelay = config.maxRetryDelay\n        ? config.maxRetryDelay\n        : Number.MAX_SAFE_INTEGER;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 408 - Retry (\"Request Timeout\")\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [100, 199],\n        [408, 408],\n        [429, 429],\n        [500, 599],\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!(await shouldRetryFn(err))) {\n        return { shouldRetry: false, config: err.config };\n    }\n    const delay = getNextRetryDelay(config);\n    // We're going to retry!  Incremenent the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff\n        ? config.retryBackoff(err, delay)\n        : new Promise(resolve => {\n            setTimeout(resolve, delay);\n        });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return { shouldRetry: true, config: err.config };\n}\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */\nfunction shouldRetryRequest(err) {\n    var _a;\n    const config = getConfig(err);\n    // node-fetch raises an AbortError if signaled:\n    // https://github.com/bitinn/node-fetch#request-cancellation-with-abortsignal\n    if (err.name === 'AbortError' || ((_a = err.error) === null || _a === void 0 ? void 0 : _a.name) === 'AbortError') {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response &&\n        (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!err.config.method ||\n        config.httpMethodsToRetry.indexOf(err.config.method.toUpperCase()) < 0) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry) {\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */\nfunction getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n/**\n * Gets the delay to wait before the next retry.\n *\n * @param {RetryConfig} config The current set of retry options\n * @returns {number} the amount of ms to wait before the next retry attempt.\n */\nfunction getNextRetryDelay(config) {\n    var _a;\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt ? 0 : (_a = config.retryDelay) !== null && _a !== void 0 ? _a : 100;\n    // Formula: retryDelay + ((retryDelayMultiplier^currentRetryAttempt - 1 / 2) * 1000)\n    const calculatedDelay = retryDelay +\n        ((Math.pow(config.retryDelayMultiplier, config.currentRetryAttempt) - 1) /\n            2) *\n            1000;\n    const maxAllowableDelay = config.totalTimeout - (Date.now() - config.timeOfFirstRequest);\n    return Math.min(calculatedDelay, maxAllowableDelay, config.maxRetryDelay);\n}\n//# sourceMappingURL=retry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/util.js":
/*!***********************************************!*\
  !*** ./node_modules/gaxios/build/src/util.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pkg = void 0;\nexports.pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/gaxios/package.json\");\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL3NyYy91dGlsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELFdBQVc7QUFDWCx1R0FBMkM7QUFDM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9nYXhpb3MvYnVpbGQvc3JjL3V0aWwuanM/Y2IxOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIENvcHlyaWdodCAyMDIzIEdvb2dsZSBMTENcbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5wa2cgPSB2b2lkIDA7XG5leHBvcnRzLnBrZyA9IHJlcXVpcmUoJy4uLy4uL3BhY2thZ2UuanNvbicpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/package.json":
/*!******************************************!*\
  !*** ./node_modules/gaxios/package.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"gaxios","version":"6.7.1","description":"A simple common HTTP client specifically for Google APIs and services.","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src"],"scripts":{"lint":"gts check","test":"c8 mocha build/test","presystem-test":"npm run compile","system-test":"mocha build/system-test --timeout 80000","compile":"tsc -p .","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","webpack":"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js","docs":"compodoc src/","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"repository":"googleapis/gaxios","keywords":["google"],"engines":{"node":">=14"},"author":"Google, LLC","license":"Apache-2.0","devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.19","@types/cors":"^2.8.6","@types/express":"^4.16.1","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/multiparty":"0.0.36","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^20.0.0","@types/node-fetch":"^2.5.7","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/uuid":"^10.0.0","abort-controller":"^3.0.0","assert":"^2.0.0","browserify":"^17.0.0","c8":"^8.0.0","cheerio":"1.0.0-rc.10","cors":"^2.8.5","execa":"^5.0.0","express":"^4.16.4","form-data":"^4.0.0","gts":"^5.0.0","is-docker":"^2.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"5.0.0","linkinator":"^3.0.0","mocha":"^8.0.0","multiparty":"^4.2.1","mv":"^2.1.1","ncp":"^2.0.0","nock":"^13.0.0","null-loader":"^4.0.0","puppeteer":"^19.0.0","sinon":"^18.0.0","stream-browserify":"^3.0.0","tmp":"0.2.3","ts-loader":"^8.0.0","typescript":"^5.1.6","webpack":"^5.35.0","webpack-cli":"^4.0.0"},"dependencies":{"extend":"^3.0.2","https-proxy-agent":"^7.0.1","is-stream":"^2.0.0","node-fetch":"^2.6.9","uuid":"^9.0.1"}}');

/***/ })

};
;