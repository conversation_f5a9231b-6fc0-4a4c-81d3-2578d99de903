/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/generic-pool";
exports.ids = ["vendor-chunks/generic-pool"];
exports.modules = {

/***/ "(rsc)/./node_modules/generic-pool/index.js":
/*!********************************************!*\
  !*** ./node_modules/generic-pool/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Pool = __webpack_require__(/*! ./lib/Pool */ \"(rsc)/./node_modules/generic-pool/lib/Pool.js\");\nconst Deque = __webpack_require__(/*! ./lib/Deque */ \"(rsc)/./node_modules/generic-pool/lib/Deque.js\");\nconst PriorityQueue = __webpack_require__(/*! ./lib/PriorityQueue */ \"(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js\");\nconst DefaultEvictor = __webpack_require__(/*! ./lib/DefaultEvictor */ \"(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js\");\nmodule.exports = {\n  Pool: Pool,\n  Deque: Deque,\n  PriorityQueue: PriorityQueue,\n  DefaultEvictor: DefaultEvictor,\n  createPool: function(factory, config) {\n    return new Pool(DefaultEvictor, Deque, PriorityQueue, factory, config);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxpRUFBWTtBQUNqQyxjQUFjLG1CQUFPLENBQUMsbUVBQWE7QUFDbkMsc0JBQXNCLG1CQUFPLENBQUMsbUZBQXFCO0FBQ25ELHVCQUF1QixtQkFBTyxDQUFDLHFGQUFzQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9nZW5lcmljLXBvb2wvaW5kZXguanM/NmI2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBQb29sID0gcmVxdWlyZShcIi4vbGliL1Bvb2xcIik7XG5jb25zdCBEZXF1ZSA9IHJlcXVpcmUoXCIuL2xpYi9EZXF1ZVwiKTtcbmNvbnN0IFByaW9yaXR5UXVldWUgPSByZXF1aXJlKFwiLi9saWIvUHJpb3JpdHlRdWV1ZVwiKTtcbmNvbnN0IERlZmF1bHRFdmljdG9yID0gcmVxdWlyZShcIi4vbGliL0RlZmF1bHRFdmljdG9yXCIpO1xubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFBvb2w6IFBvb2wsXG4gIERlcXVlOiBEZXF1ZSxcbiAgUHJpb3JpdHlRdWV1ZTogUHJpb3JpdHlRdWV1ZSxcbiAgRGVmYXVsdEV2aWN0b3I6IERlZmF1bHRFdmljdG9yLFxuICBjcmVhdGVQb29sOiBmdW5jdGlvbihmYWN0b3J5LCBjb25maWcpIHtcbiAgICByZXR1cm4gbmV3IFBvb2woRGVmYXVsdEV2aWN0b3IsIERlcXVlLCBQcmlvcml0eVF1ZXVlLCBmYWN0b3J5LCBjb25maWcpO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js":
/*!*********************************************************!*\
  !*** ./node_modules/generic-pool/lib/DefaultEvictor.js ***!
  \*********************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nclass DefaultEvictor {\n  evict(config, pooledResource, availableObjectsCount) {\n    const idleTime = Date.now() - pooledResource.lastIdleTime;\n\n    if (\n      config.softIdleTimeoutMillis > 0 &&\n      config.softIdleTimeoutMillis < idleTime &&\n      config.min < availableObjectsCount\n    ) {\n      return true;\n    }\n\n    if (config.idleTimeoutMillis < idleTime) {\n      return true;\n    }\n\n    return false;\n  }\n}\n\nmodule.exports = DefaultEvictor;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZWZhdWx0RXZpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9saWIvRGVmYXVsdEV2aWN0b3IuanM/NzQ2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuY2xhc3MgRGVmYXVsdEV2aWN0b3Ige1xuICBldmljdChjb25maWcsIHBvb2xlZFJlc291cmNlLCBhdmFpbGFibGVPYmplY3RzQ291bnQpIHtcbiAgICBjb25zdCBpZGxlVGltZSA9IERhdGUubm93KCkgLSBwb29sZWRSZXNvdXJjZS5sYXN0SWRsZVRpbWU7XG5cbiAgICBpZiAoXG4gICAgICBjb25maWcuc29mdElkbGVUaW1lb3V0TWlsbGlzID4gMCAmJlxuICAgICAgY29uZmlnLnNvZnRJZGxlVGltZW91dE1pbGxpcyA8IGlkbGVUaW1lICYmXG4gICAgICBjb25maWcubWluIDwgYXZhaWxhYmxlT2JqZWN0c0NvdW50XG4gICAgKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICBpZiAoY29uZmlnLmlkbGVUaW1lb3V0TWlsbGlzIDwgaWRsZVRpbWUpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IERlZmF1bHRFdmljdG9yO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Deferred.js":
/*!***************************************************!*\
  !*** ./node_modules/generic-pool/lib/Deferred.js ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * This is apparently a bit like a Jquery deferred, hence the name\n */\n\nclass Deferred {\n  constructor(Promise) {\n    this._state = Deferred.PENDING;\n    this._resolve = undefined;\n    this._reject = undefined;\n\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n    });\n  }\n\n  get state() {\n    return this._state;\n  }\n\n  get promise() {\n    return this._promise;\n  }\n\n  reject(reason) {\n    if (this._state !== Deferred.PENDING) {\n      return;\n    }\n    this._state = Deferred.REJECTED;\n    this._reject(reason);\n  }\n\n  resolve(value) {\n    if (this._state !== Deferred.PENDING) {\n      return;\n    }\n    this._state = Deferred.FULFILLED;\n    this._resolve(value);\n  }\n}\n\n// TODO: should these really live here? or be a seperate 'state' enum\nDeferred.PENDING = \"PENDING\";\nDeferred.FULFILLED = \"FULFILLED\";\nDeferred.REJECTED = \"REJECTED\";\n\nmodule.exports = Deferred;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZWZlcnJlZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZWZlcnJlZC5qcz9mMzMwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG4vKipcbiAqIFRoaXMgaXMgYXBwYXJlbnRseSBhIGJpdCBsaWtlIGEgSnF1ZXJ5IGRlZmVycmVkLCBoZW5jZSB0aGUgbmFtZVxuICovXG5cbmNsYXNzIERlZmVycmVkIHtcbiAgY29uc3RydWN0b3IoUHJvbWlzZSkge1xuICAgIHRoaXMuX3N0YXRlID0gRGVmZXJyZWQuUEVORElORztcbiAgICB0aGlzLl9yZXNvbHZlID0gdW5kZWZpbmVkO1xuICAgIHRoaXMuX3JlamVjdCA9IHVuZGVmaW5lZDtcblxuICAgIHRoaXMuX3Byb21pc2UgPSBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICB0aGlzLl9yZXNvbHZlID0gcmVzb2x2ZTtcbiAgICAgIHRoaXMuX3JlamVjdCA9IHJlamVjdDtcbiAgICB9KTtcbiAgfVxuXG4gIGdldCBzdGF0ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fc3RhdGU7XG4gIH1cblxuICBnZXQgcHJvbWlzZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fcHJvbWlzZTtcbiAgfVxuXG4gIHJlamVjdChyZWFzb24pIHtcbiAgICBpZiAodGhpcy5fc3RhdGUgIT09IERlZmVycmVkLlBFTkRJTkcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdGhpcy5fc3RhdGUgPSBEZWZlcnJlZC5SRUpFQ1RFRDtcbiAgICB0aGlzLl9yZWplY3QocmVhc29uKTtcbiAgfVxuXG4gIHJlc29sdmUodmFsdWUpIHtcbiAgICBpZiAodGhpcy5fc3RhdGUgIT09IERlZmVycmVkLlBFTkRJTkcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdGhpcy5fc3RhdGUgPSBEZWZlcnJlZC5GVUxGSUxMRUQ7XG4gICAgdGhpcy5fcmVzb2x2ZSh2YWx1ZSk7XG4gIH1cbn1cblxuLy8gVE9ETzogc2hvdWxkIHRoZXNlIHJlYWxseSBsaXZlIGhlcmU/IG9yIGJlIGEgc2VwZXJhdGUgJ3N0YXRlJyBlbnVtXG5EZWZlcnJlZC5QRU5ESU5HID0gXCJQRU5ESU5HXCI7XG5EZWZlcnJlZC5GVUxGSUxMRUQgPSBcIkZVTEZJTExFRFwiO1xuRGVmZXJyZWQuUkVKRUNURUQgPSBcIlJFSkVDVEVEXCI7XG5cbm1vZHVsZS5leHBvcnRzID0gRGVmZXJyZWQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Deferred.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Deque.js":
/*!************************************************!*\
  !*** ./node_modules/generic-pool/lib/Deque.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst DoublyLinkedList = __webpack_require__(/*! ./DoublyLinkedList */ \"(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js\");\nconst DequeIterator = __webpack_require__(/*! ./DequeIterator */ \"(rsc)/./node_modules/generic-pool/lib/DequeIterator.js\");\n/**\n * DoublyLinkedList backed double ended queue\n * implements just enough to keep the Pool\n */\nclass Deque {\n  constructor() {\n    this._list = new DoublyLinkedList();\n  }\n\n  /**\n   * removes and returns the first element from the queue\n   * @return {any} [description]\n   */\n  shift() {\n    if (this.length === 0) {\n      return undefined;\n    }\n\n    const node = this._list.head;\n    this._list.remove(node);\n\n    return node.data;\n  }\n\n  /**\n   * adds one elemts to the beginning of the queue\n   * @param  {any} element [description]\n   * @return {any}         [description]\n   */\n  unshift(element) {\n    const node = DoublyLinkedList.createNode(element);\n\n    this._list.insertBeginning(node);\n  }\n\n  /**\n   * adds one to the end of the queue\n   * @param  {any} element [description]\n   * @return {any}         [description]\n   */\n  push(element) {\n    const node = DoublyLinkedList.createNode(element);\n\n    this._list.insertEnd(node);\n  }\n\n  /**\n   * removes and returns the last element from the queue\n   */\n  pop() {\n    if (this.length === 0) {\n      return undefined;\n    }\n\n    const node = this._list.tail;\n    this._list.remove(node);\n\n    return node.data;\n  }\n\n  [Symbol.iterator]() {\n    return new DequeIterator(this._list);\n  }\n\n  iterator() {\n    return new DequeIterator(this._list);\n  }\n\n  reverseIterator() {\n    return new DequeIterator(this._list, true);\n  }\n\n  /**\n   * get a reference to the item at the head of the queue\n   * @return {any} [description]\n   */\n  get head() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const node = this._list.head;\n    return node.data;\n  }\n\n  /**\n   * get a reference to the item at the tail of the queue\n   * @return {any} [description]\n   */\n  get tail() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const node = this._list.tail;\n    return node.data;\n  }\n\n  get length() {\n    return this._list.length;\n  }\n}\n\nmodule.exports = Deque;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Deque.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DequeIterator.js":
/*!********************************************************!*\
  !*** ./node_modules/generic-pool/lib/DequeIterator.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst DoublyLinkedListIterator = __webpack_require__(/*! ./DoublyLinkedListIterator */ \"(rsc)/./node_modules/generic-pool/lib/DoublyLinkedListIterator.js\");\n/**\n * Thin wrapper around an underlying DDL iterator\n */\nclass DequeIterator extends DoublyLinkedListIterator {\n  next() {\n    const result = super.next();\n\n    // unwrap the node...\n    if (result.value) {\n      result.value = result.value.data;\n    }\n\n    return result;\n  }\n}\n\nmodule.exports = DequeIterator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZXF1ZUl0ZXJhdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlDQUFpQyxtQkFBTyxDQUFDLHFHQUE0QjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZXF1ZUl0ZXJhdG9yLmpzPzhkMzciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmNvbnN0IERvdWJseUxpbmtlZExpc3RJdGVyYXRvciA9IHJlcXVpcmUoXCIuL0RvdWJseUxpbmtlZExpc3RJdGVyYXRvclwiKTtcbi8qKlxuICogVGhpbiB3cmFwcGVyIGFyb3VuZCBhbiB1bmRlcmx5aW5nIERETCBpdGVyYXRvclxuICovXG5jbGFzcyBEZXF1ZUl0ZXJhdG9yIGV4dGVuZHMgRG91Ymx5TGlua2VkTGlzdEl0ZXJhdG9yIHtcbiAgbmV4dCgpIHtcbiAgICBjb25zdCByZXN1bHQgPSBzdXBlci5uZXh0KCk7XG5cbiAgICAvLyB1bndyYXAgdGhlIG5vZGUuLi5cbiAgICBpZiAocmVzdWx0LnZhbHVlKSB7XG4gICAgICByZXN1bHQudmFsdWUgPSByZXN1bHQudmFsdWUuZGF0YTtcbiAgICB9XG5cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gRGVxdWVJdGVyYXRvcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DequeIterator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js":
/*!***********************************************************!*\
  !*** ./node_modules/generic-pool/lib/DoublyLinkedList.js ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * A Doubly Linked List, because there aren't enough in the world...\n * this is pretty much a direct JS port of the one wikipedia\n * https://en.wikipedia.org/wiki/Doubly_linked_list\n *\n * For most usage 'insertBeginning' and 'insertEnd' should be enough\n *\n * nodes are expected to something like a POJSO like\n * {\n *   prev: null,\n *   next: null,\n *   something: 'whatever you like'\n * }\n */\nclass DoublyLinkedList {\n  constructor() {\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  insertBeginning(node) {\n    if (this.head === null) {\n      this.head = node;\n      this.tail = node;\n      node.prev = null;\n      node.next = null;\n      this.length++;\n    } else {\n      this.insertBefore(this.head, node);\n    }\n  }\n\n  insertEnd(node) {\n    if (this.tail === null) {\n      this.insertBeginning(node);\n    } else {\n      this.insertAfter(this.tail, node);\n    }\n  }\n\n  insertAfter(node, newNode) {\n    newNode.prev = node;\n    newNode.next = node.next;\n    if (node.next === null) {\n      this.tail = newNode;\n    } else {\n      node.next.prev = newNode;\n    }\n    node.next = newNode;\n    this.length++;\n  }\n\n  insertBefore(node, newNode) {\n    newNode.prev = node.prev;\n    newNode.next = node;\n    if (node.prev === null) {\n      this.head = newNode;\n    } else {\n      node.prev.next = newNode;\n    }\n    node.prev = newNode;\n    this.length++;\n  }\n\n  remove(node) {\n    if (node.prev === null) {\n      this.head = node.next;\n    } else {\n      node.prev.next = node.next;\n    }\n    if (node.next === null) {\n      this.tail = node.prev;\n    } else {\n      node.next.prev = node.prev;\n    }\n    node.prev = null;\n    node.next = null;\n    this.length--;\n  }\n\n  // FIXME: this should not live here and has become a dumping ground...\n  static createNode(data) {\n    return {\n      prev: null,\n      next: null,\n      data: data\n    };\n  }\n}\n\nmodule.exports = DoublyLinkedList;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DoublyLinkedListIterator.js":
/*!*******************************************************************!*\
  !*** ./node_modules/generic-pool/lib/DoublyLinkedListIterator.js ***!
  \*******************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Creates an interator for a DoublyLinkedList starting at the given node\n * It's internal cursor will remains relative to the last \"iterated\" node as that\n * node moves through the list until it either iterates to the end of the list,\n * or the the node it's tracking is removed from the list. Until the first 'next'\n * call it tracks the head/tail of the linked list. This means that one can create\n * an iterator on an empty list, then add nodes, and then the iterator will follow\n * those nodes. Because the DoublyLinkedList nodes don't track their owning \"list\" and\n * it's highly inefficient to walk the list for every iteration, the iterator won't know\n * if the node has been detached from one List and added to another list, or if the iterator\n *\n * The created object is an es6 compatible iterator\n */\nclass DoublyLinkedListIterator {\n  /**\n   * @param  {Object} doublyLinkedList     a node that is part of a doublyLinkedList\n   * @param  {Boolean} [reverse=false]     is this a reverse iterator? default: false\n   */\n  constructor(doublyLinkedList, reverse) {\n    this._list = doublyLinkedList;\n    // NOTE: these key names are tied to the DoublyLinkedListIterator\n    this._direction = reverse === true ? \"prev\" : \"next\";\n    this._startPosition = reverse === true ? \"tail\" : \"head\";\n    this._started = false;\n    this._cursor = null;\n    this._done = false;\n  }\n\n  _start() {\n    this._cursor = this._list[this._startPosition];\n    this._started = true;\n  }\n\n  _advanceCursor() {\n    if (this._started === false) {\n      this._started = true;\n      this._cursor = this._list[this._startPosition];\n      return;\n    }\n    this._cursor = this._cursor[this._direction];\n  }\n\n  reset() {\n    this._done = false;\n    this._started = false;\n    this._cursor = null;\n  }\n\n  remove() {\n    if (\n      this._started === false ||\n      this._done === true ||\n      this._isCursorDetached()\n    ) {\n      return false;\n    }\n    this._list.remove(this._cursor);\n  }\n\n  next() {\n    if (this._done === true) {\n      return { done: true };\n    }\n\n    this._advanceCursor();\n\n    // if there is no node at the cursor or the node at the cursor is no longer part of\n    // a doubly linked list then we are done/finished/kaput\n    if (this._cursor === null || this._isCursorDetached()) {\n      this._done = true;\n      return { done: true };\n    }\n\n    return {\n      value: this._cursor,\n      done: false\n    };\n  }\n\n  /**\n   * Is the node detached from a list?\n   * NOTE: you can trick/bypass/confuse this check by removing a node from one DoublyLinkedList\n   * and adding it to another.\n   * TODO: We can make this smarter by checking the direction of travel and only checking\n   * the required next/prev/head/tail rather than all of them\n   * @return {Boolean}      [description]\n   */\n  _isCursorDetached() {\n    return (\n      this._cursor.prev === null &&\n      this._cursor.next === null &&\n      this._list.tail !== this._cursor &&\n      this._list.head !== this._cursor\n    );\n  }\n}\n\nmodule.exports = DoublyLinkedListIterator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DoublyLinkedListIterator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Pool.js":
/*!***********************************************!*\
  !*** ./node_modules/generic-pool/lib/Pool.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter);\n\nconst factoryValidator = __webpack_require__(/*! ./factoryValidator */ \"(rsc)/./node_modules/generic-pool/lib/factoryValidator.js\");\nconst PoolOptions = __webpack_require__(/*! ./PoolOptions */ \"(rsc)/./node_modules/generic-pool/lib/PoolOptions.js\");\nconst ResourceRequest = __webpack_require__(/*! ./ResourceRequest */ \"(rsc)/./node_modules/generic-pool/lib/ResourceRequest.js\");\nconst ResourceLoan = __webpack_require__(/*! ./ResourceLoan */ \"(rsc)/./node_modules/generic-pool/lib/ResourceLoan.js\");\nconst PooledResource = __webpack_require__(/*! ./PooledResource */ \"(rsc)/./node_modules/generic-pool/lib/PooledResource.js\");\nconst DefaultEvictor = __webpack_require__(/*! ./DefaultEvictor */ \"(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js\");\nconst Deque = __webpack_require__(/*! ./Deque */ \"(rsc)/./node_modules/generic-pool/lib/Deque.js\");\nconst Deferred = __webpack_require__(/*! ./Deferred */ \"(rsc)/./node_modules/generic-pool/lib/Deferred.js\");\nconst PriorityQueue = __webpack_require__(/*! ./PriorityQueue */ \"(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js\");\nconst DequeIterator = __webpack_require__(/*! ./DequeIterator */ \"(rsc)/./node_modules/generic-pool/lib/DequeIterator.js\");\n\nconst reflector = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/generic-pool/lib/utils.js\").reflector);\n\n/**\n * TODO: move me\n */\nconst FACTORY_CREATE_ERROR = \"factoryCreateError\";\nconst FACTORY_DESTROY_ERROR = \"factoryDestroyError\";\n\nclass Pool extends EventEmitter {\n  /**\n   * Generate an Object pool with a specified `factory` and `config`.\n   *\n   * @param {typeof DefaultEvictor} Evictor\n   * @param {typeof Deque} Deque\n   * @param {typeof PriorityQueue} PriorityQueue\n   * @param {Object} factory\n   *   Factory to be used for generating and destroying the items.\n   * @param {Function} factory.create\n   *   Should create the item to be acquired,\n   *   and call it's first callback argument with the generated item as it's argument.\n   * @param {Function} factory.destroy\n   *   Should gently close any resources that the item is using.\n   *   Called before the items is destroyed.\n   * @param {Function} factory.validate\n   *   Test if a resource is still valid .Should return a promise that resolves to a boolean, true if resource is still valid and false\n   *   If it should be removed from pool.\n   * @param {Object} options\n   */\n  constructor(Evictor, Deque, PriorityQueue, factory, options) {\n    super();\n\n    factoryValidator(factory);\n\n    this._config = new PoolOptions(options);\n\n    // TODO: fix up this ugly glue-ing\n    this._Promise = this._config.Promise;\n\n    this._factory = factory;\n    this._draining = false;\n    this._started = false;\n    /**\n     * Holds waiting clients\n     * @type {PriorityQueue}\n     */\n    this._waitingClientsQueue = new PriorityQueue(this._config.priorityRange);\n\n    /**\n     * Collection of promises for resource creation calls made by the pool to factory.create\n     * @type {Set}\n     */\n    this._factoryCreateOperations = new Set();\n\n    /**\n     * Collection of promises for resource destruction calls made by the pool to factory.destroy\n     * @type {Set}\n     */\n    this._factoryDestroyOperations = new Set();\n\n    /**\n     * A queue/stack of pooledResources awaiting acquisition\n     * TODO: replace with LinkedList backed array\n     * @type {Deque}\n     */\n    this._availableObjects = new Deque();\n\n    /**\n     * Collection of references for any resource that are undergoing validation before being acquired\n     * @type {Set}\n     */\n    this._testOnBorrowResources = new Set();\n\n    /**\n     * Collection of references for any resource that are undergoing validation before being returned\n     * @type {Set}\n     */\n    this._testOnReturnResources = new Set();\n\n    /**\n     * Collection of promises for any validations currently in process\n     * @type {Set}\n     */\n    this._validationOperations = new Set();\n\n    /**\n     * All objects associated with this pool in any state (except destroyed)\n     * @type {Set}\n     */\n    this._allObjects = new Set();\n\n    /**\n     * Loans keyed by the borrowed resource\n     * @type {Map}\n     */\n    this._resourceLoans = new Map();\n\n    /**\n     * Infinitely looping iterator over available object\n     * @type {DequeIterator}\n     */\n    this._evictionIterator = this._availableObjects.iterator();\n\n    this._evictor = new Evictor();\n\n    /**\n     * handle for setTimeout for next eviction run\n     * @type {(number|null)}\n     */\n    this._scheduledEviction = null;\n\n    // create initial resources (if factory.min > 0)\n    if (this._config.autostart === true) {\n      this.start();\n    }\n  }\n\n  _destroy(pooledResource) {\n    // FIXME: do we need another state for \"in destruction\"?\n    pooledResource.invalidate();\n    this._allObjects.delete(pooledResource);\n    // NOTE: this maybe very bad promise usage?\n    const destroyPromise = this._factory.destroy(pooledResource.obj);\n    const wrappedDestroyPromise = this._config.destroyTimeoutMillis\n      ? this._Promise.resolve(this._applyDestroyTimeout(destroyPromise))\n      : this._Promise.resolve(destroyPromise);\n\n    this._trackOperation(\n      wrappedDestroyPromise,\n      this._factoryDestroyOperations\n    ).catch(reason => {\n      this.emit(FACTORY_DESTROY_ERROR, reason);\n    });\n\n    // TODO: maybe ensuring minimum pool size should live outside here\n    this._ensureMinimum();\n  }\n\n  _applyDestroyTimeout(promise) {\n    const timeoutPromise = new this._Promise((resolve, reject) => {\n      setTimeout(() => {\n        reject(new Error(\"destroy timed out\"));\n      }, this._config.destroyTimeoutMillis).unref();\n    });\n    return this._Promise.race([timeoutPromise, promise]);\n  }\n\n  /**\n   * Attempt to move an available resource into test and then onto a waiting client\n   * @return {Boolean} could we move an available resource into test\n   */\n  _testOnBorrow() {\n    if (this._availableObjects.length < 1) {\n      return false;\n    }\n\n    const pooledResource = this._availableObjects.shift();\n    // Mark the resource as in test\n    pooledResource.test();\n    this._testOnBorrowResources.add(pooledResource);\n    const validationPromise = this._factory.validate(pooledResource.obj);\n    const wrappedValidationPromise = this._Promise.resolve(validationPromise);\n\n    this._trackOperation(\n      wrappedValidationPromise,\n      this._validationOperations\n    ).then(isValid => {\n      this._testOnBorrowResources.delete(pooledResource);\n\n      if (isValid === false) {\n        pooledResource.invalidate();\n        this._destroy(pooledResource);\n        this._dispense();\n        return;\n      }\n      this._dispatchPooledResourceToNextWaitingClient(pooledResource);\n    });\n\n    return true;\n  }\n\n  /**\n   * Attempt to move an available resource to a waiting client\n   * @return {Boolean} [description]\n   */\n  _dispatchResource() {\n    if (this._availableObjects.length < 1) {\n      return false;\n    }\n\n    const pooledResource = this._availableObjects.shift();\n    this._dispatchPooledResourceToNextWaitingClient(pooledResource);\n    return false;\n  }\n\n  /**\n   * Attempt to resolve an outstanding resource request using an available resource from\n   * the pool, or creating new ones\n   *\n   * @private\n   */\n  _dispense() {\n    /**\n     * Local variables for ease of reading/writing\n     * these don't (shouldn't) change across the execution of this fn\n     */\n    const numWaitingClients = this._waitingClientsQueue.length;\n\n    // If there aren't any waiting requests then there is nothing to do\n    // so lets short-circuit\n    if (numWaitingClients < 1) {\n      return;\n    }\n\n    const resourceShortfall =\n      numWaitingClients - this._potentiallyAllocableResourceCount;\n\n    const actualNumberOfResourcesToCreate = Math.min(\n      this.spareResourceCapacity,\n      resourceShortfall\n    );\n    for (let i = 0; actualNumberOfResourcesToCreate > i; i++) {\n      this._createResource();\n    }\n\n    // If we are doing test-on-borrow see how many more resources need to be moved into test\n    // to help satisfy waitingClients\n    if (this._config.testOnBorrow === true) {\n      // how many available resources do we need to shift into test\n      const desiredNumberOfResourcesToMoveIntoTest =\n        numWaitingClients - this._testOnBorrowResources.size;\n      const actualNumberOfResourcesToMoveIntoTest = Math.min(\n        this._availableObjects.length,\n        desiredNumberOfResourcesToMoveIntoTest\n      );\n      for (let i = 0; actualNumberOfResourcesToMoveIntoTest > i; i++) {\n        this._testOnBorrow();\n      }\n    }\n\n    // if we aren't testing-on-borrow then lets try to allocate what we can\n    if (this._config.testOnBorrow === false) {\n      const actualNumberOfResourcesToDispatch = Math.min(\n        this._availableObjects.length,\n        numWaitingClients\n      );\n      for (let i = 0; actualNumberOfResourcesToDispatch > i; i++) {\n        this._dispatchResource();\n      }\n    }\n  }\n\n  /**\n   * Dispatches a pooledResource to the next waiting client (if any) else\n   * puts the PooledResource back on the available list\n   * @param  {PooledResource} pooledResource [description]\n   * @return {Boolean}                [description]\n   */\n  _dispatchPooledResourceToNextWaitingClient(pooledResource) {\n    const clientResourceRequest = this._waitingClientsQueue.dequeue();\n    if (\n      clientResourceRequest === undefined ||\n      clientResourceRequest.state !== Deferred.PENDING\n    ) {\n      // While we were away either all the waiting clients timed out\n      // or were somehow fulfilled. put our pooledResource back.\n      this._addPooledResourceToAvailableObjects(pooledResource);\n      // TODO: do need to trigger anything before we leave?\n      return false;\n    }\n    const loan = new ResourceLoan(pooledResource, this._Promise);\n    this._resourceLoans.set(pooledResource.obj, loan);\n    pooledResource.allocate();\n    clientResourceRequest.resolve(pooledResource.obj);\n    return true;\n  }\n\n  /**\n   * tracks on operation using given set\n   * handles adding/removing from the set and resolve/rejects the value/reason\n   * @param  {Promise} operation\n   * @param  {Set} set       Set holding operations\n   * @return {Promise}       Promise that resolves once operation has been removed from set\n   */\n  _trackOperation(operation, set) {\n    set.add(operation);\n\n    return operation.then(\n      v => {\n        set.delete(operation);\n        return this._Promise.resolve(v);\n      },\n      e => {\n        set.delete(operation);\n        return this._Promise.reject(e);\n      }\n    );\n  }\n\n  /**\n   * @private\n   */\n  _createResource() {\n    // An attempt to create a resource\n    const factoryPromise = this._factory.create();\n    const wrappedFactoryPromise = this._Promise\n      .resolve(factoryPromise)\n      .then(resource => {\n        const pooledResource = new PooledResource(resource);\n        this._allObjects.add(pooledResource);\n        this._addPooledResourceToAvailableObjects(pooledResource);\n      });\n\n    this._trackOperation(wrappedFactoryPromise, this._factoryCreateOperations)\n      .then(() => {\n        this._dispense();\n        // Stop bluebird complaining about this side-effect only handler\n        // - a promise was created in a handler but was not returned from it\n        // https://goo.gl/rRqMUw\n        return null;\n      })\n      .catch(reason => {\n        this.emit(FACTORY_CREATE_ERROR, reason);\n        this._dispense();\n      });\n  }\n\n  /**\n   * @private\n   */\n  _ensureMinimum() {\n    if (this._draining === true) {\n      return;\n    }\n    const minShortfall = this._config.min - this._count;\n    for (let i = 0; i < minShortfall; i++) {\n      this._createResource();\n    }\n  }\n\n  _evict() {\n    const testsToRun = Math.min(\n      this._config.numTestsPerEvictionRun,\n      this._availableObjects.length\n    );\n    const evictionConfig = {\n      softIdleTimeoutMillis: this._config.softIdleTimeoutMillis,\n      idleTimeoutMillis: this._config.idleTimeoutMillis,\n      min: this._config.min\n    };\n    for (let testsHaveRun = 0; testsHaveRun < testsToRun; ) {\n      const iterationResult = this._evictionIterator.next();\n\n      // Safety check incase we could get stuck in infinite loop because we\n      // somehow emptied the array after checking its length.\n      if (iterationResult.done === true && this._availableObjects.length < 1) {\n        this._evictionIterator.reset();\n        return;\n      }\n      // If this happens it should just mean we reached the end of the\n      // list and can reset the cursor.\n      if (iterationResult.done === true && this._availableObjects.length > 0) {\n        this._evictionIterator.reset();\n        continue;\n      }\n\n      const resource = iterationResult.value;\n\n      const shouldEvict = this._evictor.evict(\n        evictionConfig,\n        resource,\n        this._availableObjects.length\n      );\n      testsHaveRun++;\n\n      if (shouldEvict === true) {\n        // take it out of the _availableObjects list\n        this._evictionIterator.remove();\n        this._destroy(resource);\n      }\n    }\n  }\n\n  _scheduleEvictorRun() {\n    // Start eviction if set\n    if (this._config.evictionRunIntervalMillis > 0) {\n      // @ts-ignore\n      this._scheduledEviction = setTimeout(() => {\n        this._evict();\n        this._scheduleEvictorRun();\n      }, this._config.evictionRunIntervalMillis).unref();\n    }\n  }\n\n  _descheduleEvictorRun() {\n    if (this._scheduledEviction) {\n      clearTimeout(this._scheduledEviction);\n    }\n    this._scheduledEviction = null;\n  }\n\n  start() {\n    if (this._draining === true) {\n      return;\n    }\n    if (this._started === true) {\n      return;\n    }\n    this._started = true;\n    this._scheduleEvictorRun();\n    this._ensureMinimum();\n  }\n\n  /**\n   * Request a new resource. The callback will be called,\n   * when a new resource is available, passing the resource to the callback.\n   * TODO: should we add a seperate \"acquireWithPriority\" function\n   *\n   * @param {Number} [priority=0]\n   *   Optional.  Integer between 0 and (priorityRange - 1).  Specifies the priority\n   *   of the caller if there are no available resources.  Lower numbers mean higher\n   *   priority.\n   *\n   * @returns {Promise}\n   */\n  acquire(priority) {\n    if (this._started === false && this._config.autostart === false) {\n      this.start();\n    }\n\n    if (this._draining) {\n      return this._Promise.reject(\n        new Error(\"pool is draining and cannot accept work\")\n      );\n    }\n\n    // TODO: should we defer this check till after this event loop incase \"the situation\" changes in the meantime\n    if (\n      this.spareResourceCapacity < 1 &&\n      this._availableObjects.length < 1 &&\n      this._config.maxWaitingClients !== undefined &&\n      this._waitingClientsQueue.length >= this._config.maxWaitingClients\n    ) {\n      return this._Promise.reject(\n        new Error(\"max waitingClients count exceeded\")\n      );\n    }\n\n    const resourceRequest = new ResourceRequest(\n      this._config.acquireTimeoutMillis,\n      this._Promise\n    );\n    this._waitingClientsQueue.enqueue(resourceRequest, priority);\n    this._dispense();\n\n    return resourceRequest.promise;\n  }\n\n  /**\n   * [use method, aquires a resource, passes the resource to a user supplied function and releases it]\n   * @param  {Function} fn [a function that accepts a resource and returns a promise that resolves/rejects once it has finished using the resource]\n   * @return {Promise}      [resolves once the resource is released to the pool]\n   */\n  use(fn, priority) {\n    return this.acquire(priority).then(resource => {\n      return fn(resource).then(\n        result => {\n          this.release(resource);\n          return result;\n        },\n        err => {\n          this.destroy(resource);\n          throw err;\n        }\n      );\n    });\n  }\n\n  /**\n   * Check if resource is currently on loan from the pool\n   *\n   * @param {Function} resource\n   *    Resource for checking.\n   *\n   * @returns {Boolean}\n   *  True if resource belongs to this pool and false otherwise\n   */\n  isBorrowedResource(resource) {\n    return this._resourceLoans.has(resource);\n  }\n\n  /**\n   * Return the resource to the pool when it is no longer required.\n   *\n   * @param {Object} resource\n   *   The acquired object to be put back to the pool.\n   */\n  release(resource) {\n    // check for an outstanding loan\n    const loan = this._resourceLoans.get(resource);\n\n    if (loan === undefined) {\n      return this._Promise.reject(\n        new Error(\"Resource not currently part of this pool\")\n      );\n    }\n\n    this._resourceLoans.delete(resource);\n    loan.resolve();\n    const pooledResource = loan.pooledResource;\n\n    pooledResource.deallocate();\n    this._addPooledResourceToAvailableObjects(pooledResource);\n\n    this._dispense();\n    return this._Promise.resolve();\n  }\n\n  /**\n   * Request the resource to be destroyed. The factory's destroy handler\n   * will also be called.\n   *\n   * This should be called within an acquire() block as an alternative to release().\n   *\n   * @param {Object} resource\n   *   The acquired resource to be destoyed.\n   */\n  destroy(resource) {\n    // check for an outstanding loan\n    const loan = this._resourceLoans.get(resource);\n\n    if (loan === undefined) {\n      return this._Promise.reject(\n        new Error(\"Resource not currently part of this pool\")\n      );\n    }\n\n    this._resourceLoans.delete(resource);\n    loan.resolve();\n    const pooledResource = loan.pooledResource;\n\n    pooledResource.deallocate();\n    this._destroy(pooledResource);\n\n    this._dispense();\n    return this._Promise.resolve();\n  }\n\n  _addPooledResourceToAvailableObjects(pooledResource) {\n    pooledResource.idle();\n    if (this._config.fifo === true) {\n      this._availableObjects.push(pooledResource);\n    } else {\n      this._availableObjects.unshift(pooledResource);\n    }\n  }\n\n  /**\n   * Disallow any new acquire calls and let the request backlog dissapate.\n   * The Pool will no longer attempt to maintain a \"min\" number of resources\n   * and will only make new resources on demand.\n   * Resolves once all resource requests are fulfilled and all resources are returned to pool and available...\n   * Should probably be called \"drain work\"\n   * @returns {Promise}\n   */\n  drain() {\n    this._draining = true;\n    return this.__allResourceRequestsSettled()\n      .then(() => {\n        return this.__allResourcesReturned();\n      })\n      .then(() => {\n        this._descheduleEvictorRun();\n      });\n  }\n\n  __allResourceRequestsSettled() {\n    if (this._waitingClientsQueue.length > 0) {\n      // wait for last waiting client to be settled\n      // FIXME: what if they can \"resolve\" out of order....?\n      return reflector(this._waitingClientsQueue.tail.promise);\n    }\n    return this._Promise.resolve();\n  }\n\n  // FIXME: this is a horrific mess\n  __allResourcesReturned() {\n    const ps = Array.from(this._resourceLoans.values())\n      .map(loan => loan.promise)\n      .map(reflector);\n    return this._Promise.all(ps);\n  }\n\n  /**\n   * Forcibly destroys all available resources regardless of timeout.  Intended to be\n   * invoked as part of a drain.  Does not prevent the creation of new\n   * resources as a result of subsequent calls to acquire.\n   *\n   * Note that if factory.min > 0 and the pool isn't \"draining\", the pool will destroy all idle resources\n   * in the pool, but replace them with newly created resources up to the\n   * specified factory.min value.  If this is not desired, set factory.min\n   * to zero before calling clear()\n   *\n   */\n  clear() {\n    const reflectedCreatePromises = Array.from(\n      this._factoryCreateOperations\n    ).map(reflector);\n\n    // wait for outstanding factory.create to complete\n    return this._Promise.all(reflectedCreatePromises).then(() => {\n      // Destroy existing resources\n      // @ts-ignore\n      for (const resource of this._availableObjects) {\n        this._destroy(resource);\n      }\n      const reflectedDestroyPromises = Array.from(\n        this._factoryDestroyOperations\n      ).map(reflector);\n      return reflector(this._Promise.all(reflectedDestroyPromises));\n    });\n  }\n\n  /**\n   * Waits until the pool is ready.\n   * We define ready by checking if the current resource number is at least\n   * the minimum number defined.\n   * @returns {Promise} that resolves when the minimum number is ready.\n   */\n  ready() {\n    return new this._Promise(resolve => {\n      const isReady = () => {\n        if (this.available >= this.min) {\n          resolve();\n        } else {\n          setTimeout(isReady, 100);\n        }\n      };\n\n      isReady();\n    });\n  }\n\n  /**\n   * How many resources are available to allocated\n   * (includes resources that have not been tested and may faul validation)\n   * NOTE: internal for now as the name is awful and might not be useful to anyone\n   * @return {Number} number of resources the pool has to allocate\n   */\n  get _potentiallyAllocableResourceCount() {\n    return (\n      this._availableObjects.length +\n      this._testOnBorrowResources.size +\n      this._testOnReturnResources.size +\n      this._factoryCreateOperations.size\n    );\n  }\n\n  /**\n   * The combined count of the currently created objects and those in the\n   * process of being created\n   * Does NOT include resources in the process of being destroyed\n   * sort of legacy...\n   * @return {Number}\n   */\n  get _count() {\n    return this._allObjects.size + this._factoryCreateOperations.size;\n  }\n\n  /**\n   * How many more resources does the pool have room for\n   * @return {Number} number of resources the pool could create before hitting any limits\n   */\n  get spareResourceCapacity() {\n    return (\n      this._config.max -\n      (this._allObjects.size + this._factoryCreateOperations.size)\n    );\n  }\n\n  /**\n   * see _count above\n   * @return {Number} [description]\n   */\n  get size() {\n    return this._count;\n  }\n\n  /**\n   * number of available resources\n   * @return {Number} [description]\n   */\n  get available() {\n    return this._availableObjects.length;\n  }\n\n  /**\n   * number of resources that are currently acquired\n   * @return {Number} [description]\n   */\n  get borrowed() {\n    return this._resourceLoans.size;\n  }\n\n  /**\n   * number of waiting acquire calls\n   * @return {Number} [description]\n   */\n  get pending() {\n    return this._waitingClientsQueue.length;\n  }\n\n  /**\n   * maximum size of the pool\n   * @return {Number} [description]\n   */\n  get max() {\n    return this._config.max;\n  }\n\n  /**\n   * minimum size of the pool\n   * @return {Number} [description]\n   */\n  get min() {\n    return this._config.min;\n  }\n}\n\nmodule.exports = Pool;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Pool.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PoolDefaults.js":
/*!*******************************************************!*\
  !*** ./node_modules/generic-pool/lib/PoolDefaults.js ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
eval("\n/**\n * Create the default settings used by the pool\n *\n * @class\n */\nclass PoolDefaults {\n  constructor() {\n    this.fifo = true;\n    this.priorityRange = 1;\n\n    this.testOnBorrow = false;\n    this.testOnReturn = false;\n\n    this.autostart = true;\n\n    this.evictionRunIntervalMillis = 0;\n    this.numTestsPerEvictionRun = 3;\n    this.softIdleTimeoutMillis = -1;\n    this.idleTimeoutMillis = 30000;\n\n    // FIXME: no defaults!\n    this.acquireTimeoutMillis = null;\n    this.destroyTimeoutMillis = null;\n    this.maxWaitingClients = null;\n\n    this.min = null;\n    this.max = null;\n    // FIXME: this seems odd?\n    this.Promise = Promise;\n  }\n}\n\nmodule.exports = PoolDefaults;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Qb29sRGVmYXVsdHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9nZW5lcmljLXBvb2wvbGliL1Bvb2xEZWZhdWx0cy5qcz9iMTRhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLyoqXG4gKiBDcmVhdGUgdGhlIGRlZmF1bHQgc2V0dGluZ3MgdXNlZCBieSB0aGUgcG9vbFxuICpcbiAqIEBjbGFzc1xuICovXG5jbGFzcyBQb29sRGVmYXVsdHMge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmZpZm8gPSB0cnVlO1xuICAgIHRoaXMucHJpb3JpdHlSYW5nZSA9IDE7XG5cbiAgICB0aGlzLnRlc3RPbkJvcnJvdyA9IGZhbHNlO1xuICAgIHRoaXMudGVzdE9uUmV0dXJuID0gZmFsc2U7XG5cbiAgICB0aGlzLmF1dG9zdGFydCA9IHRydWU7XG5cbiAgICB0aGlzLmV2aWN0aW9uUnVuSW50ZXJ2YWxNaWxsaXMgPSAwO1xuICAgIHRoaXMubnVtVGVzdHNQZXJFdmljdGlvblJ1biA9IDM7XG4gICAgdGhpcy5zb2Z0SWRsZVRpbWVvdXRNaWxsaXMgPSAtMTtcbiAgICB0aGlzLmlkbGVUaW1lb3V0TWlsbGlzID0gMzAwMDA7XG5cbiAgICAvLyBGSVhNRTogbm8gZGVmYXVsdHMhXG4gICAgdGhpcy5hY3F1aXJlVGltZW91dE1pbGxpcyA9IG51bGw7XG4gICAgdGhpcy5kZXN0cm95VGltZW91dE1pbGxpcyA9IG51bGw7XG4gICAgdGhpcy5tYXhXYWl0aW5nQ2xpZW50cyA9IG51bGw7XG5cbiAgICB0aGlzLm1pbiA9IG51bGw7XG4gICAgdGhpcy5tYXggPSBudWxsO1xuICAgIC8vIEZJWE1FOiB0aGlzIHNlZW1zIG9kZD9cbiAgICB0aGlzLlByb21pc2UgPSBQcm9taXNlO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gUG9vbERlZmF1bHRzO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PoolDefaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PoolOptions.js":
/*!******************************************************!*\
  !*** ./node_modules/generic-pool/lib/PoolOptions.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst PoolDefaults = __webpack_require__(/*! ./PoolDefaults */ \"(rsc)/./node_modules/generic-pool/lib/PoolDefaults.js\");\n\nclass PoolOptions {\n  /**\n   * @param {Object} opts\n   *   configuration for the pool\n   * @param {Number} [opts.max=null]\n   *   Maximum number of items that can exist at the same time.  Default: 1.\n   *   Any further acquire requests will be pushed to the waiting list.\n   * @param {Number} [opts.min=null]\n   *   Minimum number of items in pool (including in-use). Default: 0.\n   *   When the pool is created, or a resource destroyed, this minimum will\n   *   be checked. If the pool resource count is below the minimum, a new\n   *   resource will be created and added to the pool.\n   * @param {Number} [opts.maxWaitingClients=null]\n   *   maximum number of queued requests allowed after which acquire calls will be rejected\n   * @param {Boolean} [opts.testOnBorrow=false]\n   *   should the pool validate resources before giving them to clients. Requires that\n   *   `factory.validate` is specified.\n   * @param {Boolean} [opts.testOnReturn=false]\n   *   should the pool validate resources before returning them to the pool.\n   * @param {Number} [opts.acquireTimeoutMillis=null]\n   *   Delay in milliseconds after which the an `acquire` call will fail. optional.\n   *   Default: undefined. Should be positive and non-zero\n   * @param {Number} [opts.destroyTimeoutMillis=null]\n   *   Delay in milliseconds after which the an `destroy` call will fail, causing it to emit a factoryDestroyError event. optional.\n   *   Default: undefined. Should be positive and non-zero\n   * @param {Number} [opts.priorityRange=1]\n   *   The range from 1 to be treated as a valid priority\n   * @param {Boolean} [opts.fifo=true]\n   *   Sets whether the pool has LIFO (last in, first out) behaviour with respect to idle objects.\n   *   if false then pool has FIFO behaviour\n   * @param {Boolean} [opts.autostart=true]\n   *   Should the pool start creating resources etc once the constructor is called\n   * @param {Number} [opts.evictionRunIntervalMillis=0]\n   *   How often to run eviction checks.  Default: 0 (does not run).\n   * @param {Number} [opts.numTestsPerEvictionRun=3]\n   *   Number of resources to check each eviction run.  Default: 3.\n   * @param {Number} [opts.softIdleTimeoutMillis=-1]\n   *   amount of time an object may sit idle in the pool before it is eligible\n   *   for eviction by the idle object evictor (if any), with the extra condition\n   *   that at least \"min idle\" object instances remain in the pool. Default -1 (nothing can get evicted)\n   * @param {Number} [opts.idleTimeoutMillis=30000]\n   *   the minimum amount of time that an object may sit idle in the pool before it is eligible for eviction\n   *   due to idle time. Supercedes \"softIdleTimeoutMillis\" Default: 30000\n   * @param {typeof Promise} [opts.Promise=Promise]\n   *   What promise implementation should the pool use, defaults to native promises.\n   */\n  constructor(opts) {\n    const poolDefaults = new PoolDefaults();\n\n    opts = opts || {};\n\n    this.fifo = typeof opts.fifo === \"boolean\" ? opts.fifo : poolDefaults.fifo;\n    this.priorityRange = opts.priorityRange || poolDefaults.priorityRange;\n\n    this.testOnBorrow =\n      typeof opts.testOnBorrow === \"boolean\"\n        ? opts.testOnBorrow\n        : poolDefaults.testOnBorrow;\n    this.testOnReturn =\n      typeof opts.testOnReturn === \"boolean\"\n        ? opts.testOnReturn\n        : poolDefaults.testOnReturn;\n\n    this.autostart =\n      typeof opts.autostart === \"boolean\"\n        ? opts.autostart\n        : poolDefaults.autostart;\n\n    if (opts.acquireTimeoutMillis) {\n      // @ts-ignore\n      this.acquireTimeoutMillis = parseInt(opts.acquireTimeoutMillis, 10);\n    }\n\n    if (opts.destroyTimeoutMillis) {\n      // @ts-ignore\n      this.destroyTimeoutMillis = parseInt(opts.destroyTimeoutMillis, 10);\n    }\n\n    if (opts.maxWaitingClients !== undefined) {\n      // @ts-ignore\n      this.maxWaitingClients = parseInt(opts.maxWaitingClients, 10);\n    }\n\n    // @ts-ignore\n    this.max = parseInt(opts.max, 10);\n    // @ts-ignore\n    this.min = parseInt(opts.min, 10);\n\n    this.max = Math.max(isNaN(this.max) ? 1 : this.max, 1);\n    this.min = Math.min(isNaN(this.min) ? 0 : this.min, this.max);\n\n    this.evictionRunIntervalMillis =\n      opts.evictionRunIntervalMillis || poolDefaults.evictionRunIntervalMillis;\n    this.numTestsPerEvictionRun =\n      opts.numTestsPerEvictionRun || poolDefaults.numTestsPerEvictionRun;\n    this.softIdleTimeoutMillis =\n      opts.softIdleTimeoutMillis || poolDefaults.softIdleTimeoutMillis;\n    this.idleTimeoutMillis =\n      opts.idleTimeoutMillis || poolDefaults.idleTimeoutMillis;\n\n    this.Promise = opts.Promise != null ? opts.Promise : poolDefaults.Promise;\n  }\n}\n\nmodule.exports = PoolOptions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PoolOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PooledResource.js":
/*!*********************************************************!*\
  !*** ./node_modules/generic-pool/lib/PooledResource.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst PooledResourceStateEnum = __webpack_require__(/*! ./PooledResourceStateEnum */ \"(rsc)/./node_modules/generic-pool/lib/PooledResourceStateEnum.js\");\n\n/**\n * @class\n * @private\n */\nclass PooledResource {\n  constructor(resource) {\n    this.creationTime = Date.now();\n    this.lastReturnTime = null;\n    this.lastBorrowTime = null;\n    this.lastIdleTime = null;\n    this.obj = resource;\n    this.state = PooledResourceStateEnum.IDLE;\n  }\n\n  // mark the resource as \"allocated\"\n  allocate() {\n    this.lastBorrowTime = Date.now();\n    this.state = PooledResourceStateEnum.ALLOCATED;\n  }\n\n  // mark the resource as \"deallocated\"\n  deallocate() {\n    this.lastReturnTime = Date.now();\n    this.state = PooledResourceStateEnum.IDLE;\n  }\n\n  invalidate() {\n    this.state = PooledResourceStateEnum.INVALID;\n  }\n\n  test() {\n    this.state = PooledResourceStateEnum.VALIDATION;\n  }\n\n  idle() {\n    this.lastIdleTime = Date.now();\n    this.state = PooledResourceStateEnum.IDLE;\n  }\n\n  returning() {\n    this.state = PooledResourceStateEnum.RETURNING;\n  }\n}\n\nmodule.exports = PooledResource;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PooledResource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PooledResourceStateEnum.js":
/*!******************************************************************!*\
  !*** ./node_modules/generic-pool/lib/PooledResourceStateEnum.js ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst PooledResourceStateEnum = {\n  ALLOCATED: \"ALLOCATED\", // In use\n  IDLE: \"IDLE\", // In the queue, not in use.\n  INVALID: \"INVALID\", // Failed validation\n  RETURNING: \"RETURNING\", // Resource is in process of returning\n  VALIDATION: \"VALIDATION\" // Currently being tested\n};\n\nmodule.exports = PooledResourceStateEnum;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Qb29sZWRSZXNvdXJjZVN0YXRlRW51bS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9saWIvUG9vbGVkUmVzb3VyY2VTdGF0ZUVudW0uanM/NjNkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuY29uc3QgUG9vbGVkUmVzb3VyY2VTdGF0ZUVudW0gPSB7XG4gIEFMTE9DQVRFRDogXCJBTExPQ0FURURcIiwgLy8gSW4gdXNlXG4gIElETEU6IFwiSURMRVwiLCAvLyBJbiB0aGUgcXVldWUsIG5vdCBpbiB1c2UuXG4gIElOVkFMSUQ6IFwiSU5WQUxJRFwiLCAvLyBGYWlsZWQgdmFsaWRhdGlvblxuICBSRVRVUk5JTkc6IFwiUkVUVVJOSU5HXCIsIC8vIFJlc291cmNlIGlzIGluIHByb2Nlc3Mgb2YgcmV0dXJuaW5nXG4gIFZBTElEQVRJT046IFwiVkFMSURBVElPTlwiIC8vIEN1cnJlbnRseSBiZWluZyB0ZXN0ZWRcbn07XG5cbm1vZHVsZS5leHBvcnRzID0gUG9vbGVkUmVzb3VyY2VTdGF0ZUVudW07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PooledResourceStateEnum.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js":
/*!********************************************************!*\
  !*** ./node_modules/generic-pool/lib/PriorityQueue.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst Queue = __webpack_require__(/*! ./Queue */ \"(rsc)/./node_modules/generic-pool/lib/Queue.js\");\n\n/**\n * @class\n * @private\n */\nclass PriorityQueue {\n  constructor(size) {\n    this._size = Math.max(+size | 0, 1);\n    /** @type {Queue[]} */\n    this._slots = [];\n    // initialize arrays to hold queue elements\n    for (let i = 0; i < this._size; i++) {\n      this._slots.push(new Queue());\n    }\n  }\n\n  get length() {\n    let _length = 0;\n    for (let i = 0, slots = this._slots.length; i < slots; i++) {\n      _length += this._slots[i].length;\n    }\n    return _length;\n  }\n\n  enqueue(obj, priority) {\n    // Convert to integer with a default value of 0.\n    priority = (priority && +priority | 0) || 0;\n\n    if (priority) {\n      if (priority < 0 || priority >= this._size) {\n        priority = this._size - 1;\n        // put obj at the end of the line\n      }\n    }\n    this._slots[priority].push(obj);\n  }\n\n  dequeue() {\n    for (let i = 0, sl = this._slots.length; i < sl; i += 1) {\n      if (this._slots[i].length) {\n        return this._slots[i].shift();\n      }\n    }\n    return;\n  }\n\n  get head() {\n    for (let i = 0, sl = this._slots.length; i < sl; i += 1) {\n      if (this._slots[i].length > 0) {\n        return this._slots[i].head;\n      }\n    }\n    return;\n  }\n\n  get tail() {\n    for (let i = this._slots.length - 1; i >= 0; i--) {\n      if (this._slots[i].length > 0) {\n        return this._slots[i].tail;\n      }\n    }\n    return;\n  }\n}\n\nmodule.exports = PriorityQueue;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Queue.js":
/*!************************************************!*\
  !*** ./node_modules/generic-pool/lib/Queue.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst DoublyLinkedList = __webpack_require__(/*! ./DoublyLinkedList */ \"(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js\");\nconst Deque = __webpack_require__(/*! ./Deque */ \"(rsc)/./node_modules/generic-pool/lib/Deque.js\");\n\n/**\n * Sort of a internal queue for holding the waiting\n * resource requets for a given \"priority\".\n * Also handles managing timeouts rejections on items (is this the best place for this?)\n * This is the last point where we know which queue a resourceRequest is in\n *\n */\nclass Queue extends Deque {\n  /**\n   * Adds the obj to the end of the list for this slot\n   * we completely override the parent method because we need access to the\n   * node for our rejection handler\n   * @param {any} resourceRequest [description]\n   */\n  push(resourceRequest) {\n    const node = DoublyLinkedList.createNode(resourceRequest);\n    resourceRequest.promise.catch(this._createTimeoutRejectionHandler(node));\n    this._list.insertEnd(node);\n  }\n\n  _createTimeoutRejectionHandler(node) {\n    return reason => {\n      if (reason.name === \"TimeoutError\") {\n        this._list.remove(node);\n      }\n    };\n  }\n}\n\nmodule.exports = Queue;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/ResourceLoan.js":
/*!*******************************************************!*\
  !*** ./node_modules/generic-pool/lib/ResourceLoan.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst Deferred = __webpack_require__(/*! ./Deferred */ \"(rsc)/./node_modules/generic-pool/lib/Deferred.js\");\n\n/**\n * Plan is to maybe add tracking via Error objects\n * and other fun stuff!\n */\n\nclass ResourceLoan extends Deferred {\n  /**\n   *\n   * @param  {any} pooledResource the PooledResource this loan belongs to\n   * @return {any}                [description]\n   */\n  constructor(pooledResource, Promise) {\n    super(Promise);\n    this._creationTimestamp = Date.now();\n    this.pooledResource = pooledResource;\n  }\n\n  reject() {\n    /**\n     * Loans can only be resolved at the moment\n     */\n  }\n}\n\nmodule.exports = ResourceLoan;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9SZXNvdXJjZUxvYW4uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsaUJBQWlCLG1CQUFPLENBQUMscUVBQVk7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsS0FBSztBQUNuQixjQUFjLG9CQUFvQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9SZXNvdXJjZUxvYW4uanM/MDM2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuY29uc3QgRGVmZXJyZWQgPSByZXF1aXJlKFwiLi9EZWZlcnJlZFwiKTtcblxuLyoqXG4gKiBQbGFuIGlzIHRvIG1heWJlIGFkZCB0cmFja2luZyB2aWEgRXJyb3Igb2JqZWN0c1xuICogYW5kIG90aGVyIGZ1biBzdHVmZiFcbiAqL1xuXG5jbGFzcyBSZXNvdXJjZUxvYW4gZXh0ZW5kcyBEZWZlcnJlZCB7XG4gIC8qKlxuICAgKlxuICAgKiBAcGFyYW0gIHthbnl9IHBvb2xlZFJlc291cmNlIHRoZSBQb29sZWRSZXNvdXJjZSB0aGlzIGxvYW4gYmVsb25ncyB0b1xuICAgKiBAcmV0dXJuIHthbnl9ICAgICAgICAgICAgICAgIFtkZXNjcmlwdGlvbl1cbiAgICovXG4gIGNvbnN0cnVjdG9yKHBvb2xlZFJlc291cmNlLCBQcm9taXNlKSB7XG4gICAgc3VwZXIoUHJvbWlzZSk7XG4gICAgdGhpcy5fY3JlYXRpb25UaW1lc3RhbXAgPSBEYXRlLm5vdygpO1xuICAgIHRoaXMucG9vbGVkUmVzb3VyY2UgPSBwb29sZWRSZXNvdXJjZTtcbiAgfVxuXG4gIHJlamVjdCgpIHtcbiAgICAvKipcbiAgICAgKiBMb2FucyBjYW4gb25seSBiZSByZXNvbHZlZCBhdCB0aGUgbW9tZW50XG4gICAgICovXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBSZXNvdXJjZUxvYW47XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/ResourceLoan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/ResourceRequest.js":
/*!**********************************************************!*\
  !*** ./node_modules/generic-pool/lib/ResourceRequest.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst Deferred = __webpack_require__(/*! ./Deferred */ \"(rsc)/./node_modules/generic-pool/lib/Deferred.js\");\nconst errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/generic-pool/lib/errors.js\");\n\nfunction fbind(fn, ctx) {\n  return function bound() {\n    return fn.apply(ctx, arguments);\n  };\n}\n\n/**\n * Wraps a users request for a resource\n * Basically a promise mashed in with a timeout\n * @private\n */\nclass ResourceRequest extends Deferred {\n  /**\n   * [constructor description]\n   * @param  {Number} ttl     timeout\n   */\n  constructor(ttl, Promise) {\n    super(Promise);\n    this._creationTimestamp = Date.now();\n    this._timeout = null;\n\n    if (ttl !== undefined) {\n      this.setTimeout(ttl);\n    }\n  }\n\n  setTimeout(delay) {\n    if (this._state !== ResourceRequest.PENDING) {\n      return;\n    }\n    const ttl = parseInt(delay, 10);\n\n    if (isNaN(ttl) || ttl <= 0) {\n      throw new Error(\"delay must be a positive int\");\n    }\n\n    const age = Date.now() - this._creationTimestamp;\n\n    if (this._timeout) {\n      this.removeTimeout();\n    }\n\n    this._timeout = setTimeout(\n      fbind(this._fireTimeout, this),\n      Math.max(ttl - age, 0)\n    );\n  }\n\n  removeTimeout() {\n    if (this._timeout) {\n      clearTimeout(this._timeout);\n    }\n    this._timeout = null;\n  }\n\n  _fireTimeout() {\n    this.reject(new errors.TimeoutError(\"ResourceRequest timed out\"));\n  }\n\n  reject(reason) {\n    this.removeTimeout();\n    super.reject(reason);\n  }\n\n  resolve(value) {\n    this.removeTimeout();\n    super.resolve(value);\n  }\n}\n\nmodule.exports = ResourceRequest;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/ResourceRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/errors.js":
/*!*************************************************!*\
  !*** ./node_modules/generic-pool/lib/errors.js ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nclass ExtendableError extends Error {\n  constructor(message) {\n    super(message);\n    // @ts-ignore\n    this.name = this.constructor.name;\n    this.message = message;\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, this.constructor);\n    } else {\n      this.stack = new Error(message).stack;\n    }\n  }\n}\n\n/* eslint-disable no-useless-constructor */\nclass TimeoutError extends ExtendableError {\n  constructor(m) {\n    super(m);\n  }\n}\n/* eslint-enable no-useless-constructor */\n\nmodule.exports = {\n  TimeoutError: TimeoutError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9lcnJvcnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9nZW5lcmljLXBvb2wvbGliL2Vycm9ycy5qcz9hN2UyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5jbGFzcyBFeHRlbmRhYmxlRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKG1lc3NhZ2UpIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAvLyBAdHMtaWdub3JlXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gICAgaWYgKHR5cGVvZiBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLCB0aGlzLmNvbnN0cnVjdG9yKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5zdGFjayA9IG5ldyBFcnJvcihtZXNzYWdlKS5zdGFjaztcbiAgICB9XG4gIH1cbn1cblxuLyogZXNsaW50LWRpc2FibGUgbm8tdXNlbGVzcy1jb25zdHJ1Y3RvciAqL1xuY2xhc3MgVGltZW91dEVycm9yIGV4dGVuZHMgRXh0ZW5kYWJsZUVycm9yIHtcbiAgY29uc3RydWN0b3IobSkge1xuICAgIHN1cGVyKG0pO1xuICB9XG59XG4vKiBlc2xpbnQtZW5hYmxlIG5vLXVzZWxlc3MtY29uc3RydWN0b3IgKi9cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFRpbWVvdXRFcnJvcjogVGltZW91dEVycm9yXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/factoryValidator.js":
/*!***********************************************************!*\
  !*** ./node_modules/generic-pool/lib/factoryValidator.js ***!
  \***********************************************************/
/***/ ((module) => {

eval("module.exports = function(factory) {\n  if (typeof factory.create !== \"function\") {\n    throw new TypeError(\"factory.create must be a function\");\n  }\n\n  if (typeof factory.destroy !== \"function\") {\n    throw new TypeError(\"factory.destroy must be a function\");\n  }\n\n  if (\n    typeof factory.validate !== \"undefined\" &&\n    typeof factory.validate !== \"function\"\n  ) {\n    throw new TypeError(\"factory.validate must be a function\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9mYWN0b3J5VmFsaWRhdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9saWIvZmFjdG9yeVZhbGlkYXRvci5qcz9mZjE3Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24oZmFjdG9yeSkge1xuICBpZiAodHlwZW9mIGZhY3RvcnkuY3JlYXRlICE9PSBcImZ1bmN0aW9uXCIpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiZmFjdG9yeS5jcmVhdGUgbXVzdCBiZSBhIGZ1bmN0aW9uXCIpO1xuICB9XG5cbiAgaWYgKHR5cGVvZiBmYWN0b3J5LmRlc3Ryb3kgIT09IFwiZnVuY3Rpb25cIikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJmYWN0b3J5LmRlc3Ryb3kgbXVzdCBiZSBhIGZ1bmN0aW9uXCIpO1xuICB9XG5cbiAgaWYgKFxuICAgIHR5cGVvZiBmYWN0b3J5LnZhbGlkYXRlICE9PSBcInVuZGVmaW5lZFwiICYmXG4gICAgdHlwZW9mIGZhY3RvcnkudmFsaWRhdGUgIT09IFwiZnVuY3Rpb25cIlxuICApIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiZmFjdG9yeS52YWxpZGF0ZSBtdXN0IGJlIGEgZnVuY3Rpb25cIik7XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/factoryValidator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/utils.js":
/*!************************************************!*\
  !*** ./node_modules/generic-pool/lib/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nfunction noop() {}\n\n/**\n * Reflects a promise but does not expose any\n * underlying value or rejection from that promise.\n * @param  {Promise} promise [description]\n * @return {Promise}         [description]\n */\nexports.reflector = function(promise) {\n  return promise.then(noop, noop);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFNBQVM7QUFDckIsWUFBWSxpQkFBaUI7QUFDN0I7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9saWIvdXRpbHMuanM/MzdlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZnVuY3Rpb24gbm9vcCgpIHt9XG5cbi8qKlxuICogUmVmbGVjdHMgYSBwcm9taXNlIGJ1dCBkb2VzIG5vdCBleHBvc2UgYW55XG4gKiB1bmRlcmx5aW5nIHZhbHVlIG9yIHJlamVjdGlvbiBmcm9tIHRoYXQgcHJvbWlzZS5cbiAqIEBwYXJhbSAge1Byb21pc2V9IHByb21pc2UgW2Rlc2NyaXB0aW9uXVxuICogQHJldHVybiB7UHJvbWlzZX0gICAgICAgICBbZGVzY3JpcHRpb25dXG4gKi9cbmV4cG9ydHMucmVmbGVjdG9yID0gZnVuY3Rpb24ocHJvbWlzZSkge1xuICByZXR1cm4gcHJvbWlzZS50aGVuKG5vb3AsIG5vb3ApO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/utils.js\n");

/***/ })

};
;