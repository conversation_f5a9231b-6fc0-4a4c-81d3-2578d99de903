"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-uri";
exports.ids = ["vendor-chunks/fast-uri"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-uri/index.js":
/*!****************************************!*\
  !*** ./node_modules/fast-uri/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { normalizeIPv6, normalizeIPv4, removeDotSegments, recomposeAuthority, normalizeComponentEncoding } = __webpack_require__(/*! ./lib/utils */ \"(rsc)/./node_modules/fast-uri/lib/utils.js\")\nconst SCHEMES = __webpack_require__(/*! ./lib/schemes */ \"(rsc)/./node_modules/fast-uri/lib/schemes.js\")\n\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = serialize(parse(uri, options), options)\n  } else if (typeof uri === 'object') {\n    uri = parse(serialize(uri, options), options)\n  }\n  return uri\n}\n\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = Object.assign({ scheme: 'null' }, options)\n  const resolved = resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  return serialize(resolved, { ...schemelessOptions, skipEscape: true })\n}\n\nfunction resolveComponents (base, relative, options, skipNormalization) {\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base components\n    relative = parse(serialize(relative, options), options) // normalize relative components\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path.charAt(0) === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\nfunction serialize (cmpts, opts) {\n  const components = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = SCHEMES[(options.scheme || components.scheme || '').toLowerCase()]\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options)\n\n  if (components.path !== undefined) {\n    if (!options.skipEscape) {\n      components.path = escape(components.path)\n\n      if (components.scheme !== undefined) {\n        components.path = components.path.split('%3A').join(':')\n      }\n    } else {\n      components.path = unescape(components.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && components.scheme) {\n    uriTokens.push(components.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(components)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (components.path && components.path.charAt(0) !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (components.path !== undefined) {\n    let s = components.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (authority === undefined) {\n      s = s.replace(/^\\/\\//u, '/%2F') // don't allow the path to start with \"//\"\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (components.query !== undefined) {\n    uriTokens.push('?', components.query)\n  }\n\n  if (components.fragment !== undefined) {\n    uriTokens.push('#', components.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst hexLookUp = Array.from({ length: 127 }, (_v, k) => /[^!\"$&'()*+,\\-.;=_`a-z{}~]/u.test(String.fromCharCode(k)))\n\nfunction nonSimpleDomain (value) {\n  let code = 0\n  for (let i = 0, len = value.length; i < len; ++i) {\n    code = value.charCodeAt(i)\n    if (code > 126 || hexLookUp[code]) {\n      return true\n    }\n  }\n  return false\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n  const gotEncoding = uri.indexOf('%') !== -1\n  let isIP = false\n  if (options.reference === 'suffix') uri = (options.scheme ? options.scheme + ':' : '') + '//' + uri\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = normalizeIPv4(parsed.host)\n      if (ipv4result.isIPV4 === false) {\n        const ipv6result = normalizeIPv6(ipv4result.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        parsed.host = ipv4result.host\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = SCHEMES[(options.scheme || parsed.scheme || '').toLowerCase()]\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (gotEncoding && parsed.scheme !== undefined) {\n        parsed.scheme = unescape(parsed.scheme)\n      }\n      if (gotEncoding && parsed.host !== undefined) {\n        parsed.host = unescape(parsed.host)\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponents,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports[\"default\"] = fastUri\nmodule.exports.fastUri = fastUri\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-uri/lib/schemes.js":
/*!**********************************************!*\
  !*** ./node_modules/fast-uri/lib/schemes.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nconst UUID_REG = /^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nfunction isSecure (wsComponents) {\n  return typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === 'wss'\n}\n\nfunction httpParse (components) {\n  if (!components.host) {\n    components.error = components.error || 'HTTP URIs must have a host.'\n  }\n\n  return components\n}\n\nfunction httpSerialize (components) {\n  const secure = String(components.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (components.port === (secure ? 443 : 80) || components.port === '') {\n    components.port = undefined\n  }\n\n  // normalize the empty path\n  if (!components.path) {\n    components.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return components\n}\n\nfunction wsParse (wsComponents) {\n// indicate if the secure flag is set\n  wsComponents.secure = isSecure(wsComponents)\n\n  // construct resouce name\n  wsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '')\n  wsComponents.path = undefined\n  wsComponents.query = undefined\n\n  return wsComponents\n}\n\nfunction wsSerialize (wsComponents) {\n// normalize the default port\n  if (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === '') {\n    wsComponents.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponents.secure === 'boolean') {\n    wsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws')\n    wsComponents.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponents.resourceName) {\n    const [path, query] = wsComponents.resourceName.split('?')\n    wsComponents.path = (path && path !== '/' ? path : undefined)\n    wsComponents.query = query\n    wsComponents.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponents.fragment = undefined\n\n  return wsComponents\n}\n\nfunction urnParse (urnComponents, options) {\n  if (!urnComponents.path) {\n    urnComponents.error = 'URN can not be parsed'\n    return urnComponents\n  }\n  const matches = urnComponents.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponents.scheme || 'urn'\n    urnComponents.nid = matches[1].toLowerCase()\n    urnComponents.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponents.nid}`\n    const schemeHandler = SCHEMES[urnScheme]\n    urnComponents.path = undefined\n\n    if (schemeHandler) {\n      urnComponents = schemeHandler.parse(urnComponents, options)\n    }\n  } else {\n    urnComponents.error = urnComponents.error || 'URN can not be parsed.'\n  }\n\n  return urnComponents\n}\n\nfunction urnSerialize (urnComponents, options) {\n  const scheme = options.scheme || urnComponents.scheme || 'urn'\n  const nid = urnComponents.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = SCHEMES[urnScheme]\n\n  if (schemeHandler) {\n    urnComponents = schemeHandler.serialize(urnComponents, options)\n  }\n\n  const uriComponents = urnComponents\n  const nss = urnComponents.nss\n  uriComponents.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponents\n}\n\nfunction urnuuidParse (urnComponents, options) {\n  const uuidComponents = urnComponents\n  uuidComponents.uuid = uuidComponents.nss\n  uuidComponents.nss = undefined\n\n  if (!options.tolerant && (!uuidComponents.uuid || !UUID_REG.test(uuidComponents.uuid))) {\n    uuidComponents.error = uuidComponents.error || 'UUID is not valid.'\n  }\n\n  return uuidComponents\n}\n\nfunction urnuuidSerialize (uuidComponents) {\n  const urnComponents = uuidComponents\n  // normalize UUID\n  urnComponents.nss = (uuidComponents.uuid || '').toLowerCase()\n  return urnComponents\n}\n\nconst http = {\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst https = {\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst ws = {\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n}\n\nconst wss = {\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n}\n\nconst urn = {\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n}\n\nconst urnuuid = {\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n}\n\nconst SCHEMES = {\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n}\n\nmodule.exports = SCHEMES\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/lib/schemes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-uri/lib/scopedChars.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-uri/lib/scopedChars.js ***!
  \**************************************************/
/***/ ((module) => {

eval("\n\nconst HEX = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  a: 10,\n  A: 10,\n  b: 11,\n  B: 11,\n  c: 12,\n  C: 12,\n  d: 13,\n  D: 13,\n  e: 14,\n  E: 14,\n  f: 15,\n  F: 15\n}\n\nmodule.exports = {\n  HEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmFzdC11cmkvbGliL3Njb3BlZENoYXJzLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jdXN0b21lci1hZ2VudC8uL25vZGVfbW9kdWxlcy9mYXN0LXVyaS9saWIvc2NvcGVkQ2hhcnMuanM/MWEwYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgSEVYID0ge1xuICAwOiAwLFxuICAxOiAxLFxuICAyOiAyLFxuICAzOiAzLFxuICA0OiA0LFxuICA1OiA1LFxuICA2OiA2LFxuICA3OiA3LFxuICA4OiA4LFxuICA5OiA5LFxuICBhOiAxMCxcbiAgQTogMTAsXG4gIGI6IDExLFxuICBCOiAxMSxcbiAgYzogMTIsXG4gIEM6IDEyLFxuICBkOiAxMyxcbiAgRDogMTMsXG4gIGU6IDE0LFxuICBFOiAxNCxcbiAgZjogMTUsXG4gIEY6IDE1XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBIRVhcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/lib/scopedChars.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-uri/lib/utils.js":
/*!********************************************!*\
  !*** ./node_modules/fast-uri/lib/utils.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { HEX } = __webpack_require__(/*! ./scopedChars */ \"(rsc)/./node_modules/fast-uri/lib/scopedChars.js\")\n\nconst IPV4_REG = /^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u\n\nfunction normalizeIPv4 (host) {\n  if (findToken(host, '.') < 3) { return { host, isIPV4: false } }\n  const matches = host.match(IPV4_REG) || []\n  const [address] = matches\n  if (address) {\n    return { host: stripLeadingZeros(address, '.'), isIPV4: true }\n  } else {\n    return { host, isIPV4: false }\n  }\n}\n\n/**\n * @param {string[]} input\n * @param {boolean} [keepZero=false]\n * @returns {string|undefined}\n */\nfunction stringArrayToHexStripped (input, keepZero = false) {\n  let acc = ''\n  let strip = true\n  for (const c of input) {\n    if (HEX[c] === undefined) return undefined\n    if (c !== '0' && strip === true) strip = false\n    if (!strip) acc += c\n  }\n  if (keepZero && acc.length === 0) acc = '0'\n  return acc\n}\n\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  const address = []\n  const buffer = []\n  let isZone = false\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  function consume () {\n    if (buffer.length) {\n      if (isZone === false) {\n        const hex = stringArrayToHexStripped(buffer)\n        if (hex !== undefined) {\n          address.push(hex)\n        } else {\n          output.error = true\n          return false\n        }\n      }\n      buffer.length = 0\n    }\n    return true\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume()) { break }\n      tokenCount++\n      address.push(':')\n      if (tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i - 1 >= 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      continue\n    } else if (cursor === '%') {\n      if (!consume()) { break }\n      // switch to zone detection\n      isZone = true\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (isZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, escapedHost, isIPV6: true }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\nfunction stripLeadingZeros (str, token) {\n  let out = ''\n  let skip = true\n  const l = str.length\n  for (let i = 0; i < l; i++) {\n    const c = str[i]\n    if (c === '0' && skip) {\n      if ((i + 1 <= l && str[i + 1] === token) || i + 1 === l) {\n        out += c\n        skip = false\n      }\n    } else {\n      if (c === token) {\n        skip = true\n      } else {\n        skip = false\n      }\n      out += c\n    }\n  }\n  return out\n}\n\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\nconst RDS1 = /^\\.\\.?\\//u\nconst RDS2 = /^\\/\\.(?:\\/|$)/u\nconst RDS3 = /^\\/\\.\\.(?:\\/|$)/u\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/u\n\nfunction removeDotSegments (input) {\n  const output = []\n\n  while (input.length) {\n    if (input.match(RDS1)) {\n      input = input.replace(RDS1, '')\n    } else if (input.match(RDS2)) {\n      input = input.replace(RDS2, '/')\n    } else if (input.match(RDS3)) {\n      input = input.replace(RDS3, '/')\n      output.pop()\n    } else if (input === '.' || input === '..') {\n      input = ''\n    } else {\n      const im = input.match(RDS5)\n      if (im) {\n        const s = im[0]\n        input = input.slice(s.length)\n        output.push(s)\n      } else {\n        throw new Error('Unexpected dot segment condition')\n      }\n    }\n  }\n  return output.join('')\n}\n\nfunction normalizeComponentEncoding (components, esc) {\n  const func = esc !== true ? escape : unescape\n  if (components.scheme !== undefined) {\n    components.scheme = func(components.scheme)\n  }\n  if (components.userinfo !== undefined) {\n    components.userinfo = func(components.userinfo)\n  }\n  if (components.host !== undefined) {\n    components.host = func(components.host)\n  }\n  if (components.path !== undefined) {\n    components.path = func(components.path)\n  }\n  if (components.query !== undefined) {\n    components.query = func(components.query)\n  }\n  if (components.fragment !== undefined) {\n    components.fragment = func(components.fragment)\n  }\n  return components\n}\n\nfunction recomposeAuthority (components) {\n  const uriTokens = []\n\n  if (components.userinfo !== undefined) {\n    uriTokens.push(components.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (components.host !== undefined) {\n    let host = unescape(components.host)\n    const ipV4res = normalizeIPv4(host)\n\n    if (ipV4res.isIPV4) {\n      host = ipV4res.host\n    } else {\n      const ipV6res = normalizeIPv6(ipV4res.host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = components.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof components.port === 'number' || typeof components.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(components.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  normalizeIPv4,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/lib/utils.js\n");

/***/ })

};
;