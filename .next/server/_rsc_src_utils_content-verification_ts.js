"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_content-verification_ts";
exports.ids = ["_rsc_src_utils_content-verification_ts"];
exports.modules = {

/***/ "(rsc)/./src/utils/content-verification.ts":
/*!*******************************************!*\
  !*** ./src/utils/content-verification.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runContentVerification: () => (/* binding */ runContentVerification),\n/* harmony export */   testHallucination: () => (/* binding */ testHallucination),\n/* harmony export */   verifyVectorContent: () => (/* binding */ verifyVectorContent)\n/* harmony export */ });\n/* harmony import */ var _lib_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n/* harmony import */ var _lib_chat__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/chat */ \"(rsc)/./src/lib/chat.ts\");\n\n\n// Test if specific Aven facts are in your vector database\nasync function verifyVectorContent() {\n    const testCases = [\n        {\n            name: \"Interest Rate Range\",\n            query: \"Aven interest rate 7.99 15.49\",\n            chatQuery: \"What are the interest rates for Aven HELOC card?\",\n            expectedContent: [\n                \"7.99\",\n                \"15.49\",\n                \"variable\"\n            ],\n            factCheck: \"Should mention 7.99% - 15.49% rate range\"\n        },\n        {\n            name: \"Credit Limit Maximum\",\n            query: \"Aven credit limit 250000 maximum\",\n            chatQuery: \"What is the maximum credit limit for Aven card?\",\n            expectedContent: [\n                \"250000\",\n                \"250,000\",\n                \"$250\"\n            ],\n            factCheck: \"Should mention $250,000 credit limit\"\n        },\n        {\n            name: \"Partner Bank\",\n            query: \"Aven Coastal Community Bank partner\",\n            chatQuery: \"Which bank does Aven partner with?\",\n            expectedContent: [\n                \"Coastal Community Bank\",\n                \"Coastal Community\"\n            ],\n            factCheck: \"Should mention Coastal Community Bank specifically\"\n        },\n        {\n            name: \"Cashback Rate\",\n            query: \"Aven cashback 2% rewards\",\n            chatQuery: \"What cashback does Aven offer?\",\n            expectedContent: [\n                \"2%\",\n                \"2 percent\",\n                \"two percent\"\n            ],\n            factCheck: \"Should mention 2% cashback on all purchases\"\n        },\n        {\n            name: \"Travel Rewards\",\n            query: \"Aven travel portal 7% cashback\",\n            chatQuery: \"What travel rewards does Aven offer?\",\n            expectedContent: [\n                \"7%\",\n                \"travel portal\",\n                \"seven percent\"\n            ],\n            factCheck: \"Should mention 7% on travel through portal\"\n        },\n        {\n            name: \"Annual Fee\",\n            query: \"Aven annual fee no fee\",\n            chatQuery: \"Does Aven have an annual fee?\",\n            expectedContent: [\n                \"no annual fee\",\n                \"no fee\",\n                \"free\"\n            ],\n            factCheck: \"Should mention no annual fee\"\n        },\n        {\n            name: \"Approval Time\",\n            query: \"Aven approval 5 minutes fast\",\n            chatQuery: \"How fast can I get approved for Aven?\",\n            expectedContent: [\n                \"5 minutes\",\n                \"five minutes\",\n                \"fast approval\"\n            ],\n            factCheck: \"Should mention approval as fast as 5 minutes\"\n        },\n        {\n            name: \"Autopay Discount\",\n            query: \"Aven autopay discount 0.25%\",\n            chatQuery: \"What autopay discount does Aven offer?\",\n            expectedContent: [\n                \"0.25%\",\n                \"0.25 percent\",\n                \"quarter percent\"\n            ],\n            factCheck: \"Should mention 0.25% autopay discount\"\n        },\n        {\n            name: \"Visa Network\",\n            query: \"Aven Visa network card\",\n            chatQuery: \"What payment network does Aven use?\",\n            expectedContent: [\n                \"Visa\",\n                \"Visa network\"\n            ],\n            factCheck: \"Should mention Visa network\"\n        },\n        {\n            name: \"Home Equity Requirement\",\n            query: \"Aven home equity requirement 250k\",\n            chatQuery: \"What home equity do I need for Aven?\",\n            expectedContent: [\n                \"$250\",\n                \"250k\",\n                \"home equity\"\n            ],\n            factCheck: \"Should mention home equity requirements\"\n        }\n    ];\n    const results = [];\n    for (const testCase of testCases){\n        try {\n            console.log(`\\nTesting: ${testCase.name}`);\n            // 1. Search vector database directly\n            const vectorResults = await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(testCase.query);\n            console.log(`Vector search found ${vectorResults.length} results`);\n            // 2. Get chat response\n            const chatResponse = await (0,_lib_chat__WEBPACK_IMPORTED_MODULE_1__.sendChatMessage)(testCase.chatQuery);\n            console.log(`Chat response: ${chatResponse.answer.substring(0, 100)}...`);\n            // 3. Analyze if RAG is being used\n            const isUsingRAG = analyzeRAGUsage(vectorResults, chatResponse.answer, chatResponse.sources || []);\n            // 4. Check accuracy\n            const isAccurate = checkAccuracy(chatResponse.answer, testCase.expectedContent);\n            // 5. Check citations\n            const hasCitations = (chatResponse.sources?.length || 0) > 0;\n            // 6. Generate analysis\n            const analysis = generateAnalysis(vectorResults, chatResponse, isUsingRAG, isAccurate, hasCitations, testCase.factCheck);\n            results.push({\n                testName: testCase.name,\n                vectorSearchResults: vectorResults.map((r)=>({\n                        title: r.title,\n                        contentSnippet: r.content.substring(0, 200),\n                        url: r.url,\n                        category: r.category\n                    })),\n                chatResponse: chatResponse.answer,\n                chatSources: chatResponse.sources || [],\n                isUsingRAG,\n                isAccurate,\n                hasCitations,\n                analysis\n            });\n        } catch (error) {\n            results.push({\n                testName: testCase.name,\n                vectorSearchResults: [],\n                chatResponse: \"\",\n                chatSources: [],\n                isUsingRAG: false,\n                isAccurate: false,\n                hasCitations: false,\n                analysis: `Error: ${error.message}`\n            });\n        }\n    }\n    // Calculate summary\n    const summary = {\n        totalTests: results.length,\n        usingRAG: results.filter((r)=>r.isUsingRAG).length,\n        accurate: results.filter((r)=>r.isAccurate).length,\n        cited: results.filter((r)=>r.hasCitations).length,\n        score: 0\n    };\n    summary.score = (summary.usingRAG + summary.accurate + summary.cited) / (summary.totalTests * 3);\n    return {\n        tests: results,\n        summary\n    };\n}\nfunction analyzeRAGUsage(vectorResults, chatAnswer, chatSources) {\n    // Check if vector search found relevant content\n    if (vectorResults.length === 0) return false;\n    // Check if chat response has sources (indicating RAG was used)\n    if (chatSources.length === 0) return false;\n    // Check if any vector content appears in the chat response\n    const answerLower = chatAnswer.toLowerCase();\n    const hasVectorContent = vectorResults.some((result)=>{\n        const contentWords = result.content.toLowerCase().split(/\\s+/);\n        return contentWords.some((word)=>word.length > 4 && answerLower.includes(word));\n    });\n    return hasVectorContent;\n}\nfunction checkAccuracy(answer, expectedContent) {\n    const answerLower = answer.toLowerCase();\n    return expectedContent.some((content)=>answerLower.includes(content.toLowerCase()));\n}\nfunction generateAnalysis(vectorResults, chatResponse, isUsingRAG, isAccurate, hasCitations, factCheck) {\n    const issues = [];\n    const positives = [];\n    if (vectorResults.length === 0) {\n        issues.push(\"No relevant content found in vector database\");\n    } else {\n        positives.push(`Found ${vectorResults.length} relevant documents`);\n    }\n    if (!hasCitations) {\n        issues.push(\"No source citations in response\");\n    } else {\n        positives.push(`${chatResponse.sources?.length || 0} sources cited`);\n    }\n    if (!isUsingRAG) {\n        issues.push(\"RAG pipeline not being used effectively\");\n    } else {\n        positives.push(\"RAG pipeline is retrieving and using content\");\n    }\n    if (!isAccurate) {\n        issues.push(`Response doesn't match expected facts: ${factCheck}`);\n    } else {\n        positives.push(\"Response contains accurate information\");\n    }\n    const analysis = [];\n    if (positives.length > 0) {\n        analysis.push(`✅ ${positives.join(\", \")}`);\n    }\n    if (issues.length > 0) {\n        analysis.push(`❌ ${issues.join(\", \")}`);\n    }\n    return analysis.join(\" | \");\n}\n// Test for hallucination by asking about non-existent Aven features\nasync function testHallucination() {\n    const nonExistentQueries = [\n        {\n            name: \"Cryptocurrency Program\",\n            query: \"Does Aven offer cryptocurrency investment options?\",\n            shouldNotExist: true\n        },\n        {\n            name: \"Student Loans\",\n            query: \"What are Aven's student loan rates?\",\n            shouldNotExist: true\n        },\n        {\n            name: \"Business Checking\",\n            query: \"Does Aven offer business checking accounts?\",\n            shouldNotExist: true\n        },\n        {\n            name: \"Mortgage Lending\",\n            query: \"What are Aven's mortgage lending rates?\",\n            shouldNotExist: true\n        },\n        {\n            name: \"Investment Advisory\",\n            query: \"Does Aven provide investment advisory services?\",\n            shouldNotExist: true\n        }\n    ];\n    const results = [];\n    for (const test of nonExistentQueries){\n        try {\n            const response = await (0,_lib_chat__WEBPACK_IMPORTED_MODULE_1__.sendChatMessage)(test.query);\n            const isHallucinated = !isProperRefusal(response.answer);\n            results.push({\n                testName: test.name,\n                vectorSearchResults: [],\n                chatResponse: response.answer,\n                chatSources: response.sources || [],\n                isUsingRAG: true,\n                isAccurate: !isHallucinated,\n                hasCitations: false,\n                analysis: isHallucinated ? \"HALLUCINATED: Made up information about non-existent service\" : \"CORRECT: Properly indicated Aven doesn't offer this service\"\n            });\n        } catch (error) {\n            results.push({\n                testName: test.name,\n                vectorSearchResults: [],\n                chatResponse: \"\",\n                chatSources: [],\n                isUsingRAG: false,\n                isAccurate: false,\n                hasCitations: false,\n                analysis: `Error: ${error.message}`\n            });\n        }\n    }\n    const hallucinationRate = results.filter((r)=>!r.isAccurate).length / results.length;\n    return {\n        tests: results,\n        hallucinationRate\n    };\n}\nfunction isProperRefusal(answer) {\n    const refusalPhrases = [\n        \"don't offer\",\n        \"doesn't offer\",\n        \"not available\",\n        \"don't provide\",\n        \"doesn't provide\",\n        \"don't have information\",\n        \"can't find information\",\n        \"not sure\",\n        \"don't know\",\n        \"unable to find\",\n        \"no information available\"\n    ];\n    const answerLower = answer.toLowerCase();\n    return refusalPhrases.some((phrase)=>answerLower.includes(phrase));\n}\n// Export a simple test runner\nasync function runContentVerification() {\n    console.log(\"\\uD83D\\uDD0D Starting Content Verification Tests...\");\n    const verification = await verifyVectorContent();\n    const hallucination = await testHallucination();\n    const report = `\n# RAG Content Verification Report\n\n## Vector Database & RAG Usage Analysis\n- **Total Tests**: ${verification.summary.totalTests}\n- **Using RAG Pipeline**: ${verification.summary.usingRAG}/${verification.summary.totalTests} (${(verification.summary.usingRAG / verification.summary.totalTests * 100).toFixed(1)}%)\n- **Accurate Responses**: ${verification.summary.accurate}/${verification.summary.totalTests} (${(verification.summary.accurate / verification.summary.totalTests * 100).toFixed(1)}%)\n- **Proper Citations**: ${verification.summary.cited}/${verification.summary.totalTests} (${(verification.summary.cited / verification.summary.totalTests * 100).toFixed(1)}%)\n- **Overall RAG Score**: ${(verification.summary.score * 100).toFixed(1)}%\n\n## Hallucination Detection\n- **Tests Run**: ${hallucination.tests.length}\n- **Hallucination Rate**: ${(hallucination.hallucinationRate * 100).toFixed(1)}%\n- **Proper Refusals**: ${hallucination.tests.filter((t)=>t.isAccurate).length}/${hallucination.tests.length}\n\n## Detailed Test Results\n\n### Content Verification Tests\n${verification.tests.map((test)=>`\n**${test.testName}**\n- RAG Usage: ${test.isUsingRAG ? \"✅\" : \"❌\"}\n- Accuracy: ${test.isAccurate ? \"✅\" : \"❌\"}  \n- Citations: ${test.hasCitations ? \"✅\" : \"❌\"}\n- Vector Results: ${test.vectorSearchResults.length} documents found\n- Analysis: ${test.analysis}\n`).join(\"\")}\n\n### Hallucination Tests\n${hallucination.tests.map((test)=>`\n**${test.testName}**\n- Status: ${test.isAccurate ? \"✅ Proper Refusal\" : \"\\uD83D\\uDEA8 Hallucinated\"}\n- Analysis: ${test.analysis}\n`).join(\"\")}\n\n## Recommendations\n\n${verification.summary.score < 0.7 ? \"\\uD83D\\uDEA8 **CRITICAL ISSUES**\\n- Your RAG pipeline has significant problems\\n- Vector database may not contain proper Aven content\\n- Run data scraping and re-index immediately\" : \"\"}\n\n${verification.summary.usingRAG < verification.summary.totalTests * 0.8 ? \"⚠️ **RAG NOT BEING USED**\\n- Responses may be coming from LLM training data\\n- Check if vector search is returning relevant results\\n- Verify prompt engineering emphasizes using retrieved context\" : \"\"}\n\n${hallucination.hallucinationRate > 0.2 ? \"\\uD83D\\uDEA8 **HIGH HALLUCINATION RATE**\\n- AI is making up information about non-existent services\\n- Implement stronger guardrails for unknown information\\n- Improve prompt to only answer based on retrieved context\" : \"\"}\n\n${verification.summary.cited < verification.summary.totalTests * 0.8 ? \"\\uD83D\\uDCDA **POOR CITATION QUALITY**\\n- Responses lack proper source attribution\\n- Users can't verify information\\n- Improve citation formatting in prompts\" : \"\"}\n\n${verification.summary.score >= 0.8 && hallucination.hallucinationRate <= 0.2 ? \"✅ **EXCELLENT PERFORMANCE**\\n- RAG pipeline is working correctly\\n- Low hallucination rate\\n- Good source attribution\\n- Ready for production deployment\" : \"\"}\n`;\n    return report;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/content-verification.ts\n");

/***/ })

};
;