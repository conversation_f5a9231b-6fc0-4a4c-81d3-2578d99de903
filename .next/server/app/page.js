/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxla3JhbWVsYWhlZWhyaWRveSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFpLWN1c3RvbWVyLWFnZW50JTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFtSCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Lz8zZjQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZWtyYW1lbGFoZWVocmlkb3kvRGVza3RvcC9wcm9qZWN0cy9haS1jdXN0b21lci1hZ2VudC9zcmMvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(ssr)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_EnhancedChatInterface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/EnhancedChatInterface */ \"(ssr)/./src/components/EnhancedChatInterface.tsx\");\n/* harmony import */ var _components_VoiceInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/VoiceInterface */ \"(ssr)/./src/components/VoiceInterface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"enhanced\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto px-6 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12 px-6 border-b border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-semibold text-gray-800 mb-3\",\n                            children: \"Aven Customer Support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Get instant answers about your HELOC Credit Card and financial services\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-8 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${activeTab === \"enhanced\" ? \"bg-red-400 text-white shadow-md hover:bg-red-500\" : \"bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300\"}`,\n                                    onClick: ()=>setActiveTab(\"enhanced\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"AI Agent\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${activeTab === \"chat\" ? \"bg-red-400 text-white shadow-md hover:bg-red-500\" : \"bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300\"}`,\n                                    onClick: ()=>setActiveTab(\"chat\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Simple Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${activeTab === \"voice\" ? \"bg-red-400 text-white shadow-md hover:bg-red-500\" : \"bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300\"}`,\n                                    onClick: ()=>setActiveTab(\"voice\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 0 01-3 3z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Voice Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white\",\n                    children: activeTab === \"enhanced\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedChatInterface__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 39\n                    }, this) : activeTab === \"chat\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 35\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VoiceInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 12\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AgentThinking.tsx":
/*!******************************************!*\
  !*** ./src/components/AgentThinking.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AgentThinking)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AgentThinking({ isThinking, steps, searchResults, isExpanded = false, onToggle }) {\n    const [localExpanded, setLocalExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(isExpanded);\n    const handleToggle = ()=>{\n        const newExpanded = !localExpanded;\n        setLocalExpanded(newExpanded);\n        onToggle?.();\n    };\n    const currentStep = steps.find((step)=>step.status === \"active\");\n    const completedSteps = steps.filter((step)=>step.status === \"complete\");\n    const progress = steps.length > 0 ? completedSteps.length / steps.length * 100 : 0;\n    const getStepIcon = (step)=>{\n        switch(step.step){\n            case \"analyzing\":\n                return \"\\uD83D\\uDD0D\";\n            case \"searching_memory\":\n                return \"\\uD83E\\uDDE0\";\n            case \"searching_knowledge\":\n                return \"\\uD83D\\uDCDA\";\n            case \"reranking\":\n                return \"\\uD83D\\uDCCA\";\n            case \"assembling\":\n                return \"\\uD83D\\uDD27\";\n            case \"generating\":\n                return \"✨\";\n            case \"complete\":\n                return \"✅\";\n            default:\n                return \"⚪\";\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case \"complete\":\n                return \"text-green-600 bg-green-50\";\n            case \"active\":\n                return \"text-blue-600 bg-blue-50\";\n            case \"error\":\n                return \"text-red-600 bg-red-50\";\n            default:\n                return \"text-gray-400 bg-gray-50\";\n        }\n    };\n    if (!isThinking && steps.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-200 rounded-xl bg-white shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors\",\n                onClick: handleToggle,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: isThinking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-green-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900\",\n                                        children: isThinking ? \"Agent is thinking...\" : \"Analysis complete\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: currentStep ? currentStep.description : steps.length > 0 ? `Completed ${completedSteps.length} of ${steps.length} steps` : \"Ready to analyze your query\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            isThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    Math.round(progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: `w-5 h-5 text-gray-400 transition-transform ${localExpanded ? \"rotate-180\" : \"\"}`,\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M19 9l-7 7-7-7\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            isThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-600 h-1 rounded-full transition-all duration-300\",\n                        style: {\n                            width: `${progress}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this),\n            localExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-100\",\n                children: [\n                    steps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900 mb-3\",\n                                children: \"Processing Steps\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${getStepColor(step.status)}`,\n                                                children: step.status === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 25\n                                                }, this) : getStepIcon(step)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: step.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: step.timestamp.toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: step.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    step.results && step.results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Found \",\n                                                            step.results.length,\n                                                            \" results\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, step.id, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, this),\n                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-100 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900 mb-3\",\n                                children: [\n                                    \"Documents Analyzed (\",\n                                    searchResults.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                children: searchResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-700\",\n                                                                children: result.type\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"Score: \",\n                                                                    (result.score * 100).toFixed(0),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-sm text-gray-900 truncate\",\n                                                        children: result.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 line-clamp-2\",\n                                                        children: result.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, result.id, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/AgentThinking.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AgentThinking.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/chat */ \"(ssr)/./src/lib/chat.ts\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _MemoryManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MemoryManager */ \"(ssr)/./src/components/MemoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatInterface() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hi! I'm here to help you with questions about your Aven HELOC Credit Card and financial services. How can I assist you today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            const response = await (0,_lib_chat__WEBPACK_IMPORTED_MODULE_2__.sendChatMessage)(input, sessionId);\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: response.answer,\n                timestamp: new Date(),\n                sources: response.sources?.map((s)=>s.url) || []\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again later.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    const formatMessageWithCitations = (content, sources)=>{\n        // This function will be enhanced later to add inline citations\n        // For now, just return the content\n        return content;\n    };\n    const getSourceTitle = (url)=>{\n        // Extract a meaningful title from the URL\n        if (url.includes(\"aven.com\")) {\n            if (url.includes(\"support\")) return \"Aven Customer Support\";\n            if (url.includes(\"eligibility\")) return \"Aven Eligibility Requirements\";\n            if (url.includes(\"apply\")) return \"Aven Application Process\";\n            if (url.includes(\"compare\")) return \"Aven vs Traditional Credit Cards\";\n            return \"Aven HELOC Credit Card Information\";\n        }\n        return url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-6\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${message.role === \"assistant\" ? \"bg-teal-400 text-white\" : \"bg-red-400 text-white\"}`,\n                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-800 leading-relaxed prose prose-sm max-w-none\",\n                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                                                        components: {\n                                                            h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-base font-semibold text-gray-800 mb-2 mt-3\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-800 mb-1 mt-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"list-disc list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                    className: \"list-decimal list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mb-2 last:mb-0\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 137,\n                                                                    columnNumber: 44\n                                                                }, void 0),\n                                                            strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 49\n                                                                }, void 0),\n                                                            code: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"bg-gray-200 px-1 py-0.5 rounded text-sm font-mono\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 47\n                                                                }, void 0),\n                                                            a: ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: href,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                        },\n                                                        children: formatMessageWithCitations(message.content, message.sources || [])\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-gray-600 mb-2\",\n                                                            children: \"\\uD83D\\uDCDA Sources:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid gap-2\",\n                                                            children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 mt-0.5\",\n                                                                            children: [\n                                                                                \"[\",\n                                                                                index + 1,\n                                                                                \"]\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: source,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: \"text-xs text-blue-600 hover:text-blue-800 hover:underline flex-1\",\n                                                                            children: getSourceTitle(source)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                            lineNumber: 166,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-2 ml-4\",\n                                            children: formatTime(message.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 w-10 h-10 bg-teal-400 rounded-full flex items-center justify-center text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-teal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-100 p-6 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 bg-gray-50 rounded-xl p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Ask me anything about Aven...\",\n                                className: \"flex-1 bg-transparent border-none outline-none text-gray-800 placeholder-gray-500\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSend,\n                                disabled: isLoading || !input.trim(),\n                                className: \"flex-shrink-0 w-10 h-10 bg-red-400 hover:bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"AI is ready with memory!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MemoryManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                sessionId: sessionId\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/EnhancedChatInterface.tsx":
/*!**************************************************!*\
  !*** ./src/components/EnhancedChatInterface.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EnhancedChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _AgentThinking__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AgentThinking */ \"(ssr)/./src/components/AgentThinking.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction EnhancedChatInterface() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hi! I'm here to help you with questions about your Aven HELOC Credit Card and financial services. How can I assist you today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);\n    const [agentSteps, setAgentSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAgentThinking, setShowAgentThinking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        agentSteps\n    ]);\n    const handleSend = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        setAgentSteps([]);\n        setSearchResults([]);\n        try {\n            const response = await fetch(\"/api/chat-stream\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: input,\n                    sessionId,\n                    userId: \"anonymous\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to send message\");\n            }\n            const reader = response.body?.getReader();\n            if (!reader) throw new Error(\"No response body\");\n            const decoder = new TextDecoder();\n            let finalAnswer = \"\";\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value);\n                const lines = chunk.split(\"\\n\");\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        try {\n                            const data = JSON.parse(line.slice(6));\n                            switch(data.type){\n                                case \"progress\":\n                                    setAgentSteps(data.data.steps || []);\n                                    setSearchResults(data.data.searchResults || []);\n                                    break;\n                                case \"response\":\n                                    finalAnswer = data.data.answer;\n                                    const aiMessage = {\n                                        id: (Date.now() + 1).toString(),\n                                        role: \"assistant\",\n                                        content: finalAnswer,\n                                        timestamp: new Date(),\n                                        sources: data.data.sources?.map((s)=>s.url) || []\n                                    };\n                                    setMessages((prev)=>[\n                                            ...prev,\n                                            aiMessage\n                                        ]);\n                                    break;\n                                case \"complete\":\n                                    setIsLoading(false);\n                                    break;\n                                case \"error\":\n                                    throw new Error(data.data.error);\n                            }\n                        } catch (parseError) {\n                            console.error(\"Error parsing SSE data:\", parseError);\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again later.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    const formatMessageWithCitations = (content, sources)=>{\n        return content;\n    };\n    const getSourceTitle = (url)=>{\n        if (url.includes(\"aven.com\")) {\n            if (url.includes(\"support\")) return \"Aven Customer Support\";\n            if (url.includes(\"eligibility\")) return \"Aven Eligibility Requirements\";\n            if (url.includes(\"apply\")) return \"Aven Application Process\";\n            if (url.includes(\"compare\")) return \"Aven vs Traditional Credit Cards\";\n            return \"Aven HELOC Credit Card Information\";\n        }\n        return url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[700px]\",\n        children: [\n            (isLoading || agentSteps.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AgentThinking__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    isThinking: isLoading,\n                    steps: agentSteps,\n                    searchResults: searchResults,\n                    isExpanded: showAgentThinking,\n                    onToggle: ()=>setShowAgentThinking(!showAgentThinking)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-6\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${message.role === \"assistant\" ? \"bg-teal-400 text-white\" : \"bg-red-400 text-white\"}`,\n                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-800 leading-relaxed prose prose-sm max-w-none\",\n                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                                        components: {\n                                                            h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-base font-semibold text-gray-800 mb-2 mt-3\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-800 mb-1 mt-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"list-disc list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                    className: \"list-decimal list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 45\n                                                                }, void 0),\n                                                            p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mb-2 last:mb-0\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 44\n                                                                }, void 0),\n                                                            strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 49\n                                                                }, void 0),\n                                                            code: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"bg-gray-200 px-1 py-0.5 rounded text-sm font-mono\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 47\n                                                                }, void 0),\n                                                            a: ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: href,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                        },\n                                                        children: formatMessageWithCitations(message.content, message.sources || [])\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-gray-600 mb-2\",\n                                                            children: \"\\uD83D\\uDCDA Sources:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid gap-2\",\n                                                            children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 mt-0.5\",\n                                                                            children: [\n                                                                                \"[\",\n                                                                                index + 1,\n                                                                                \"]\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: source,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: \"text-xs text-blue-600 hover:text-blue-800 hover:underline flex-1\",\n                                                                            children: getSourceTitle(source)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-2 ml-4\",\n                                            children: formatTime(message.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-100 p-6 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 bg-gray-50 rounded-xl p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Ask me anything about Aven...\",\n                                className: \"flex-1 bg-transparent border-none outline-none text-gray-800 placeholder-gray-500\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSend,\n                                disabled: isLoading || !input.trim(),\n                                className: \"flex-shrink-0 w-10 h-10 bg-red-400 hover:bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"Enhanced AI with agent thinking!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/EnhancedChatInterface.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9FbmhhbmNlZENoYXRJbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1EO0FBR1Q7QUFDQztBQUU1QixTQUFTSztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1AsK0NBQVFBLENBQWdCO1FBQ3REO1lBQ0VRLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxTQUFTO1lBQ1RDLFdBQVcsSUFBSUM7UUFDakI7S0FDRDtJQUNELE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHZCwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNlLFdBQVdDLGFBQWEsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2lCLFVBQVUsR0FBR2pCLCtDQUFRQSxDQUFDLElBQU0sQ0FBQyxRQUFRLEVBQUVZLEtBQUtNLEdBQUcsR0FBRyxDQUFDLEVBQUVDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLEdBQUcsQ0FBQztJQUNyRyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR3hCLCtDQUFRQSxDQUFjLEVBQUU7SUFDNUQsTUFBTSxDQUFDeUIsZUFBZUMsaUJBQWlCLEdBQUcxQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUMyQixtQkFBbUJDLHFCQUFxQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTTZCLGlCQUFpQjVCLDZDQUFNQSxDQUFpQjtJQUU5QyxNQUFNNkIsaUJBQWlCO1FBQ3JCRCxlQUFlRSxPQUFPLEVBQUVDLGVBQWU7WUFBRUMsVUFBVTtRQUFTO0lBQzlEO0lBRUEvQixnREFBU0EsQ0FBQztRQUNSNEI7SUFDRixHQUFHO1FBQUN4QjtRQUFVaUI7S0FBVztJQUV6QixNQUFNVyxhQUFhO1FBQ2pCLElBQUksQ0FBQ3JCLE1BQU1zQixJQUFJLE1BQU1wQixXQUFXO1FBRWhDLE1BQU1xQixjQUEyQjtZQUMvQjVCLElBQUlJLEtBQUtNLEdBQUcsR0FBR0csUUFBUTtZQUN2QlosTUFBTTtZQUNOQyxTQUFTRztZQUNURixXQUFXLElBQUlDO1FBQ2pCO1FBRUFMLFlBQVk4QixDQUFBQSxPQUFRO21CQUFJQTtnQkFBTUQ7YUFBWTtRQUMxQ3RCLFNBQVM7UUFDVEUsYUFBYTtRQUNiUSxjQUFjLEVBQUU7UUFDaEJFLGlCQUFpQixFQUFFO1FBRW5CLElBQUk7WUFDRixNQUFNWSxXQUFXLE1BQU1DLE1BQU0sb0JBQW9CO2dCQUMvQ0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxTQUFTaEM7b0JBQ1RJO29CQUNBNkIsUUFBUTtnQkFDVjtZQUNGO1lBRUEsSUFBSSxDQUFDUixTQUFTUyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU1DLFNBQVNYLFNBQVNJLElBQUksRUFBRVE7WUFDOUIsSUFBSSxDQUFDRCxRQUFRLE1BQU0sSUFBSUQsTUFBTTtZQUU3QixNQUFNRyxVQUFVLElBQUlDO1lBQ3BCLElBQUlDLGNBQWM7WUFFbEIsTUFBTyxLQUFNO2dCQUNYLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTixPQUFPTyxJQUFJO2dCQUN6QyxJQUFJRixNQUFNO2dCQUVWLE1BQU1HLFFBQVFOLFFBQVFPLE1BQU0sQ0FBQ0g7Z0JBQzdCLE1BQU1JLFFBQVFGLE1BQU1HLEtBQUssQ0FBQztnQkFFMUIsS0FBSyxNQUFNQyxRQUFRRixNQUFPO29CQUN4QixJQUFJRSxLQUFLQyxVQUFVLENBQUMsV0FBVzt3QkFDN0IsSUFBSTs0QkFDRixNQUFNQyxPQUFPcEIsS0FBS3FCLEtBQUssQ0FBQ0gsS0FBS0ksS0FBSyxDQUFDOzRCQUVuQyxPQUFRRixLQUFLRyxJQUFJO2dDQUNmLEtBQUs7b0NBQ0gxQyxjQUFjdUMsS0FBS0EsSUFBSSxDQUFDSSxLQUFLLElBQUksRUFBRTtvQ0FDbkN6QyxpQkFBaUJxQyxLQUFLQSxJQUFJLENBQUN0QyxhQUFhLElBQUksRUFBRTtvQ0FDOUM7Z0NBRUYsS0FBSztvQ0FDSDRCLGNBQWNVLEtBQUtBLElBQUksQ0FBQ0ssTUFBTTtvQ0FDOUIsTUFBTUMsWUFBeUI7d0NBQzdCN0QsSUFBSSxDQUFDSSxLQUFLTSxHQUFHLEtBQUssR0FBR0csUUFBUTt3Q0FDN0JaLE1BQU07d0NBQ05DLFNBQVMyQzt3Q0FDVDFDLFdBQVcsSUFBSUM7d0NBQ2YwRCxTQUFTUCxLQUFLQSxJQUFJLENBQUNPLE9BQU8sRUFBRUMsSUFBSSxDQUFDQyxJQUFXQSxFQUFFQyxHQUFHLEtBQUssRUFBRTtvQ0FDMUQ7b0NBQ0FsRSxZQUFZOEIsQ0FBQUEsT0FBUTsrQ0FBSUE7NENBQU1nQzt5Q0FBVTtvQ0FDeEM7Z0NBRUYsS0FBSztvQ0FDSHJELGFBQWE7b0NBQ2I7Z0NBRUYsS0FBSztvQ0FDSCxNQUFNLElBQUlnQyxNQUFNZSxLQUFLQSxJQUFJLENBQUNXLEtBQUs7NEJBQ25DO3dCQUNGLEVBQUUsT0FBT0MsWUFBWTs0QkFDbkJDLFFBQVFGLEtBQUssQ0FBQywyQkFBMkJDO3dCQUMzQztvQkFDRjtnQkFDRjtZQUNGO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RFLFFBQVFGLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDLE1BQU1HLGVBQTRCO2dCQUNoQ3JFLElBQUksQ0FBQ0ksS0FBS00sR0FBRyxLQUFLLEdBQUdHLFFBQVE7Z0JBQzdCWixNQUFNO2dCQUNOQyxTQUFTO2dCQUNUQyxXQUFXLElBQUlDO1lBQ2pCO1lBQ0FMLFlBQVk4QixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTXdDO2lCQUFhO1lBQzNDN0QsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNOEQsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtZQUNwQ0YsRUFBRUcsY0FBYztZQUNoQmhEO1FBQ0Y7SUFDRjtJQUVBLE1BQU1pRCxhQUFhLENBQUNDO1FBQ2xCLE9BQU9BLEtBQUtDLGtCQUFrQixDQUFDLFNBQVM7WUFDdENDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU1DLDZCQUE2QixDQUFDL0UsU0FBaUI0RDtRQUNuRCxPQUFPNUQ7SUFDVDtJQUVBLE1BQU1nRixpQkFBaUIsQ0FBQ2pCO1FBQ3RCLElBQUlBLElBQUlrQixRQUFRLENBQUMsYUFBYTtZQUM1QixJQUFJbEIsSUFBSWtCLFFBQVEsQ0FBQyxZQUFZLE9BQU87WUFDcEMsSUFBSWxCLElBQUlrQixRQUFRLENBQUMsZ0JBQWdCLE9BQU87WUFDeEMsSUFBSWxCLElBQUlrQixRQUFRLENBQUMsVUFBVSxPQUFPO1lBQ2xDLElBQUlsQixJQUFJa0IsUUFBUSxDQUFDLFlBQVksT0FBTztZQUNwQyxPQUFPO1FBQ1Q7UUFDQSxPQUFPbEI7SUFDVDtJQUVBLHFCQUNFLDhEQUFDbUI7UUFBSUMsV0FBVTs7WUFFWDlFLENBQUFBLGFBQWFRLFdBQVd1RSxNQUFNLEdBQUcsb0JBQ2pDLDhEQUFDRjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ3pGLHNEQUFhQTtvQkFDWjJGLFlBQVloRjtvQkFDWm9ELE9BQU81QztvQkFDUEUsZUFBZUE7b0JBQ2Z1RSxZQUFZckU7b0JBQ1pzRSxVQUFVLElBQU1yRSxxQkFBcUIsQ0FBQ0Q7Ozs7Ozs7Ozs7OzBCQU01Qyw4REFBQ2lFO2dCQUFJQyxXQUFVOztvQkFDWnZGLFNBQVNpRSxHQUFHLENBQUMsQ0FBQzFCLHdCQUNiLDhEQUFDK0M7NEJBQXFCQyxXQUFVOzs4Q0FFOUIsOERBQUNEO29DQUFJQyxXQUFXLENBQUMsc0VBQXNFLEVBQ3JGaEQsUUFBUXBDLElBQUksS0FBSyxjQUNiLDJCQUNBLHdCQUNMLENBQUM7OENBQ0NvQyxRQUFRcEMsSUFBSSxLQUFLLDRCQUNoQiw4REFBQ3lGO3dDQUFJTCxXQUFVO3dDQUFVTSxNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUNqRSw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7Ozs7OzZEQUd2RSw4REFBQ1I7d0NBQUlMLFdBQVU7d0NBQVVNLE1BQUs7d0NBQU9DLFFBQU87d0NBQWVDLFNBQVE7a0RBQ2pFLDRFQUFDQzs0Q0FBS0MsZUFBYzs0Q0FBUUMsZ0JBQWU7NENBQVFDLGFBQWE7NENBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTTNFLDhEQUFDZDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1poRCxRQUFRcEMsSUFBSSxLQUFLLDRCQUNoQiw4REFBQ04sb0RBQWFBO3dEQUNad0csWUFBWTs0REFDVkMsSUFBSSxDQUFDLEVBQUNDLFFBQVEsRUFBQyxpQkFBSyw4REFBQ0Q7b0VBQUdmLFdBQVU7OEVBQTRDZ0I7Ozs7Ozs0REFDOUVDLElBQUksQ0FBQyxFQUFDRCxRQUFRLEVBQUMsaUJBQUssOERBQUNDO29FQUFHakIsV0FBVTs4RUFBbURnQjs7Ozs7OzREQUNyRkUsSUFBSSxDQUFDLEVBQUNGLFFBQVEsRUFBQyxpQkFBSyw4REFBQ0U7b0VBQUdsQixXQUFVOzhFQUFpRGdCOzs7Ozs7NERBQ25GRyxJQUFJLENBQUMsRUFBQ0gsUUFBUSxFQUFDLGlCQUFLLDhEQUFDRztvRUFBR25CLFdBQVU7OEVBQXdDZ0I7Ozs7Ozs0REFDMUVJLElBQUksQ0FBQyxFQUFDSixRQUFRLEVBQUMsaUJBQUssOERBQUNJO29FQUFHcEIsV0FBVTs4RUFBMkNnQjs7Ozs7OzREQUM3RUssSUFBSSxDQUFDLEVBQUNMLFFBQVEsRUFBQyxpQkFBSyw4REFBQ0s7b0VBQUdyQixXQUFVOzhFQUFpQmdCOzs7Ozs7NERBQ25ETSxHQUFHLENBQUMsRUFBQ04sUUFBUSxFQUFDLGlCQUFLLDhEQUFDTTtvRUFBRXRCLFdBQVU7OEVBQWtCZ0I7Ozs7Ozs0REFDbERPLFFBQVEsQ0FBQyxFQUFDUCxRQUFRLEVBQUMsaUJBQUssOERBQUNPO29FQUFPdkIsV0FBVTs4RUFBK0JnQjs7Ozs7OzREQUN6RVEsTUFBTSxDQUFDLEVBQUNSLFFBQVEsRUFBQyxpQkFBSyw4REFBQ1E7b0VBQUt4QixXQUFVOzhFQUFxRGdCOzs7Ozs7NERBQzNGUyxHQUFHLENBQUMsRUFBQ0MsSUFBSSxFQUFFVixRQUFRLEVBQUMsaUJBQ2xCLDhEQUFDUztvRUFDQ0MsTUFBTUE7b0VBQ05DLFFBQU87b0VBQ1BDLEtBQUk7b0VBQ0o1QixXQUFVOzhFQUVUZ0I7Ozs7Ozt3REFHUDtrRUFFQ3BCLDJCQUEyQjVDLFFBQVFuQyxPQUFPLEVBQUVtQyxRQUFReUIsT0FBTyxJQUFJLEVBQUU7Ozs7OzZFQUdwRSw4REFBQzZDO2tFQUFHdEUsUUFBUW5DLE9BQU87Ozs7Ozs7Ozs7O2dEQUl0Qm1DLFFBQVF5QixPQUFPLElBQUl6QixRQUFReUIsT0FBTyxDQUFDd0IsTUFBTSxHQUFHLG1CQUMzQyw4REFBQ0Y7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDc0I7NERBQUV0QixXQUFVO3NFQUF5Qzs7Ozs7O3NFQUN0RCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ1poRCxRQUFReUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsQ0FBQ21ELFFBQVFDLHNCQUM1Qiw4REFBQy9CO29FQUFnQkMsV0FBVTs7c0ZBQ3pCLDhEQUFDK0I7NEVBQUsvQixXQUFVOztnRkFBK0I7Z0ZBQUU4QixRQUFRO2dGQUFFOzs7Ozs7O3NGQUMzRCw4REFBQ0w7NEVBQ0NDLE1BQU1HOzRFQUNORixRQUFPOzRFQUNQQyxLQUFJOzRFQUNKNUIsV0FBVTtzRkFFVEgsZUFBZWdDOzs7Ozs7O21FQVJWQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFrQnBCLDhEQUFDUjs0Q0FBRXRCLFdBQVU7c0RBQ1ZWLFdBQVd0QyxRQUFRbEMsU0FBUzs7Ozs7Ozs7Ozs7OzsyQkE3RXpCa0MsUUFBUXJDLEVBQUU7Ozs7O2tDQW1GdEIsOERBQUNvRjt3QkFBSWlDLEtBQUtoRzs7Ozs7Ozs7Ozs7OzBCQUlaLDhEQUFDK0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNoRjtnQ0FDQ3FELE1BQUs7Z0NBQ0xYLE9BQU8xQztnQ0FDUGlILFVBQVUsQ0FBQy9DLElBQU1qRSxTQUFTaUUsRUFBRXlDLE1BQU0sQ0FBQ2pFLEtBQUs7Z0NBQ3hDd0UsWUFBWWpEO2dDQUNaa0QsYUFBWTtnQ0FDWm5DLFdBQVU7Z0NBQ1ZvQyxVQUFVbEg7Ozs7OzswQ0FFWiw4REFBQ21IO2dDQUNDQyxTQUFTakc7Z0NBQ1QrRixVQUFVbEgsYUFBYSxDQUFDRixNQUFNc0IsSUFBSTtnQ0FDbEMwRCxXQUFVOzBDQUVWLDRFQUFDSztvQ0FBSUwsV0FBVTtvQ0FBVU0sTUFBSztvQ0FBT0MsUUFBTztvQ0FBZUMsU0FBUTs4Q0FDakUsNEVBQUNDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNM0UsOERBQUNkO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQytCO2dDQUFLL0IsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtsRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vc3JjL2NvbXBvbmVudHMvRW5oYW5jZWRDaGF0SW50ZXJmYWNlLnRzeD9hOGVhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENoYXRNZXNzYWdlIH0gZnJvbSAnQC90eXBlcydcbmltcG9ydCB7IEFnZW50U3RlcCwgU2VhcmNoUmVzdWx0IH0gZnJvbSAnQC90eXBlcy9hZ2VudCdcbmltcG9ydCBSZWFjdE1hcmtkb3duIGZyb20gJ3JlYWN0LW1hcmtkb3duJ1xuaW1wb3J0IEFnZW50VGhpbmtpbmcgZnJvbSAnLi9BZ2VudFRoaW5raW5nJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFbmhhbmNlZENoYXRJbnRlcmZhY2UoKSB7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8Q2hhdE1lc3NhZ2VbXT4oW1xuICAgIHtcbiAgICAgIGlkOiAnMScsXG4gICAgICByb2xlOiAnYXNzaXN0YW50JyxcbiAgICAgIGNvbnRlbnQ6ICdIaSEgSVxcJ20gaGVyZSB0byBoZWxwIHlvdSB3aXRoIHF1ZXN0aW9ucyBhYm91dCB5b3VyIEF2ZW4gSEVMT0MgQ3JlZGl0IENhcmQgYW5kIGZpbmFuY2lhbCBzZXJ2aWNlcy4gSG93IGNhbiBJIGFzc2lzdCB5b3UgdG9kYXk/JyxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICB9LFxuICBdKVxuICBjb25zdCBbaW5wdXQsIHNldElucHV0XSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZXNzaW9uSWRdID0gdXNlU3RhdGUoKCkgPT4gYHNlc3Npb25fJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gKVxuICBjb25zdCBbYWdlbnRTdGVwcywgc2V0QWdlbnRTdGVwc10gPSB1c2VTdGF0ZTxBZ2VudFN0ZXBbXT4oW10pXG4gIGNvbnN0IFtzZWFyY2hSZXN1bHRzLCBzZXRTZWFyY2hSZXN1bHRzXSA9IHVzZVN0YXRlPFNlYXJjaFJlc3VsdFtdPihbXSlcbiAgY29uc3QgW3Nob3dBZ2VudFRoaW5raW5nLCBzZXRTaG93QWdlbnRUaGlua2luZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBtZXNzYWdlc0VuZFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbClcblxuICBjb25zdCBzY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBtZXNzYWdlc0VuZFJlZi5jdXJyZW50Py5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KVxuICB9XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzY3JvbGxUb0JvdHRvbSgpXG4gIH0sIFttZXNzYWdlcywgYWdlbnRTdGVwc10pXG5cbiAgY29uc3QgaGFuZGxlU2VuZCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWlucHV0LnRyaW0oKSB8fCBpc0xvYWRpbmcpIHJldHVyblxuXG4gICAgY29uc3QgdXNlck1lc3NhZ2U6IENoYXRNZXNzYWdlID0ge1xuICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIHJvbGU6ICd1c2VyJyxcbiAgICAgIGNvbnRlbnQ6IGlucHV0LFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgIH1cblxuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIHVzZXJNZXNzYWdlXSlcbiAgICBzZXRJbnB1dCgnJylcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcbiAgICBzZXRBZ2VudFN0ZXBzKFtdKVxuICAgIHNldFNlYXJjaFJlc3VsdHMoW10pXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jaGF0LXN0cmVhbScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbWVzc2FnZTogaW5wdXQsXG4gICAgICAgICAgc2Vzc2lvbklkLFxuICAgICAgICAgIHVzZXJJZDogJ2Fub255bW91cydcbiAgICAgICAgfSksXG4gICAgICB9KVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIHNlbmQgbWVzc2FnZScpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHk/LmdldFJlYWRlcigpXG4gICAgICBpZiAoIXJlYWRlcikgdGhyb3cgbmV3IEVycm9yKCdObyByZXNwb25zZSBib2R5JylcblxuICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpXG4gICAgICBsZXQgZmluYWxBbnN3ZXIgPSAnJ1xuXG4gICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpXG4gICAgICAgIGlmIChkb25lKSBicmVha1xuXG4gICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpXG4gICAgICAgIGNvbnN0IGxpbmVzID0gY2h1bmsuc3BsaXQoJ1xcbicpXG5cbiAgICAgICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7XG4gICAgICAgICAgaWYgKGxpbmUuc3RhcnRzV2l0aCgnZGF0YTogJykpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGxpbmUuc2xpY2UoNikpXG5cbiAgICAgICAgICAgICAgc3dpdGNoIChkYXRhLnR5cGUpIHtcbiAgICAgICAgICAgICAgICBjYXNlICdwcm9ncmVzcyc6XG4gICAgICAgICAgICAgICAgICBzZXRBZ2VudFN0ZXBzKGRhdGEuZGF0YS5zdGVwcyB8fCBbXSlcbiAgICAgICAgICAgICAgICAgIHNldFNlYXJjaFJlc3VsdHMoZGF0YS5kYXRhLnNlYXJjaFJlc3VsdHMgfHwgW10pXG4gICAgICAgICAgICAgICAgICBicmVha1xuXG4gICAgICAgICAgICAgICAgY2FzZSAncmVzcG9uc2UnOlxuICAgICAgICAgICAgICAgICAgZmluYWxBbnN3ZXIgPSBkYXRhLmRhdGEuYW5zd2VyXG4gICAgICAgICAgICAgICAgICBjb25zdCBhaU1lc3NhZ2U6IENoYXRNZXNzYWdlID0ge1xuICAgICAgICAgICAgICAgICAgICBpZDogKERhdGUubm93KCkgKyAxKS50b1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICByb2xlOiAnYXNzaXN0YW50JyxcbiAgICAgICAgICAgICAgICAgICAgY29udGVudDogZmluYWxBbnN3ZXIsXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgICAgICAgICAgc291cmNlczogZGF0YS5kYXRhLnNvdXJjZXM/Lm1hcCgoczogYW55KSA9PiBzLnVybCkgfHwgW10sXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBhaU1lc3NhZ2VdKVxuICAgICAgICAgICAgICAgICAgYnJlYWtcblxuICAgICAgICAgICAgICAgIGNhc2UgJ2NvbXBsZXRlJzpcbiAgICAgICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgICAgICAgICAgICAgIGJyZWFrXG5cbiAgICAgICAgICAgICAgICBjYXNlICdlcnJvcic6XG4gICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5kYXRhLmVycm9yKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgU1NFIGRhdGE6JywgcGFyc2VFcnJvcilcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBtZXNzYWdlOicsIGVycm9yKVxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlOiBDaGF0TWVzc2FnZSA9IHtcbiAgICAgICAgaWQ6IChEYXRlLm5vdygpICsgMSkudG9TdHJpbmcoKSxcbiAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXG4gICAgICAgIGNvbnRlbnQ6ICdJIGFwb2xvZ2l6ZSwgYnV0IElcXCdtIGhhdmluZyB0cm91YmxlIGNvbm5lY3RpbmcgcmlnaHQgbm93LiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgIH1cbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGVycm9yTWVzc2FnZV0pXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICBoYW5kbGVTZW5kKClcbiAgICB9XG4gIH1cblxuICBjb25zdCBmb3JtYXRUaW1lID0gKGRhdGU6IERhdGUpID0+IHtcbiAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoJ2VuLVVTJywgeyBcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JywgXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgIGhvdXIxMjogdHJ1ZSBcbiAgICB9KVxuICB9XG5cbiAgY29uc3QgZm9ybWF0TWVzc2FnZVdpdGhDaXRhdGlvbnMgPSAoY29udGVudDogc3RyaW5nLCBzb3VyY2VzOiBzdHJpbmdbXSkgPT4ge1xuICAgIHJldHVybiBjb250ZW50XG4gIH1cblxuICBjb25zdCBnZXRTb3VyY2VUaXRsZSA9ICh1cmw6IHN0cmluZykgPT4ge1xuICAgIGlmICh1cmwuaW5jbHVkZXMoJ2F2ZW4uY29tJykpIHtcbiAgICAgIGlmICh1cmwuaW5jbHVkZXMoJ3N1cHBvcnQnKSkgcmV0dXJuICdBdmVuIEN1c3RvbWVyIFN1cHBvcnQnXG4gICAgICBpZiAodXJsLmluY2x1ZGVzKCdlbGlnaWJpbGl0eScpKSByZXR1cm4gJ0F2ZW4gRWxpZ2liaWxpdHkgUmVxdWlyZW1lbnRzJ1xuICAgICAgaWYgKHVybC5pbmNsdWRlcygnYXBwbHknKSkgcmV0dXJuICdBdmVuIEFwcGxpY2F0aW9uIFByb2Nlc3MnXG4gICAgICBpZiAodXJsLmluY2x1ZGVzKCdjb21wYXJlJykpIHJldHVybiAnQXZlbiB2cyBUcmFkaXRpb25hbCBDcmVkaXQgQ2FyZHMnXG4gICAgICByZXR1cm4gJ0F2ZW4gSEVMT0MgQ3JlZGl0IENhcmQgSW5mb3JtYXRpb24nXG4gICAgfVxuICAgIHJldHVybiB1cmxcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtWzcwMHB4XVwiPlxuICAgICAgey8qIEFnZW50IFRoaW5raW5nIFBhbmVsICovfVxuICAgICAgeyhpc0xvYWRpbmcgfHwgYWdlbnRTdGVwcy5sZW5ndGggPiAwKSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgIDxBZ2VudFRoaW5raW5nXG4gICAgICAgICAgICBpc1RoaW5raW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICBzdGVwcz17YWdlbnRTdGVwc31cbiAgICAgICAgICAgIHNlYXJjaFJlc3VsdHM9e3NlYXJjaFJlc3VsdHN9XG4gICAgICAgICAgICBpc0V4cGFuZGVkPXtzaG93QWdlbnRUaGlua2luZ31cbiAgICAgICAgICAgIG9uVG9nZ2xlPXsoKSA9PiBzZXRTaG93QWdlbnRUaGlua2luZyghc2hvd0FnZW50VGhpbmtpbmcpfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIE1lc3NhZ2VzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHAtNiBzcGFjZS15LTZcIj5cbiAgICAgICAge21lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXttZXNzYWdlLmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC00XCI+XG4gICAgICAgICAgICB7LyogQXZhdGFyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LXNocmluay0wIHctMTAgaC0xMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgJHtcbiAgICAgICAgICAgICAgbWVzc2FnZS5yb2xlID09PSAnYXNzaXN0YW50JyBcbiAgICAgICAgICAgICAgICA/ICdiZy10ZWFsLTQwMCB0ZXh0LXdoaXRlJyBcbiAgICAgICAgICAgICAgICA6ICdiZy1yZWQtNDAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgIHttZXNzYWdlLnJvbGUgPT09ICdhc3Npc3RhbnQnID8gKFxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEzIDEwVjNMNCAxNGg3djdsOS0xMWgtN3pcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE2IDdhNCA0IDAgMTEtOCAwIDQgNCAwIDAxOCAwek0xMiAxNGE3IDcgMCAwMC03IDdoMTRhNyA3IDAgMDAtNy03elwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1lc3NhZ2UgQ29udGVudCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQteGwgcC00IG1heC13LTN4bFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMCBsZWFkaW5nLXJlbGF4ZWQgcHJvc2UgcHJvc2Utc20gbWF4LXctbm9uZVwiPlxuICAgICAgICAgICAgICAgICAge21lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxSZWFjdE1hcmtkb3duXG4gICAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50cz17e1xuICAgICAgICAgICAgICAgICAgICAgICAgaDE6ICh7Y2hpbGRyZW59KSA9PiA8aDEgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItMlwiPntjaGlsZHJlbn08L2gxPixcbiAgICAgICAgICAgICAgICAgICAgICAgIGgyOiAoe2NoaWxkcmVufSkgPT4gPGgyIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItMiBtdC0zXCI+e2NoaWxkcmVufTwvaDI+LFxuICAgICAgICAgICAgICAgICAgICAgICAgaDM6ICh7Y2hpbGRyZW59KSA9PiA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItMSBtdC0yXCI+e2NoaWxkcmVufTwvaDM+LFxuICAgICAgICAgICAgICAgICAgICAgICAgdWw6ICh7Y2hpbGRyZW59KSA9PiA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMSBteS0yXCI+e2NoaWxkcmVufTwvdWw+LFxuICAgICAgICAgICAgICAgICAgICAgICAgb2w6ICh7Y2hpbGRyZW59KSA9PiA8b2wgY2xhc3NOYW1lPVwibGlzdC1kZWNpbWFsIGxpc3QtaW5zaWRlIHNwYWNlLXktMSBteS0yXCI+e2NoaWxkcmVufTwvb2w+LFxuICAgICAgICAgICAgICAgICAgICAgICAgbGk6ICh7Y2hpbGRyZW59KSA9PiA8bGkgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiPntjaGlsZHJlbn08L2xpPixcbiAgICAgICAgICAgICAgICAgICAgICAgIHA6ICh7Y2hpbGRyZW59KSA9PiA8cCBjbGFzc05hbWU9XCJtYi0yIGxhc3Q6bWItMFwiPntjaGlsZHJlbn08L3A+LFxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb25nOiAoe2NoaWxkcmVufSkgPT4gPHN0cm9uZyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj57Y2hpbGRyZW59PC9zdHJvbmc+LFxuICAgICAgICAgICAgICAgICAgICAgICAgY29kZTogKHtjaGlsZHJlbn0pID0+IDxjb2RlIGNsYXNzTmFtZT1cImJnLWdyYXktMjAwIHB4LTEgcHktMC41IHJvdW5kZWQgdGV4dC1zbSBmb250LW1vbm9cIj57Y2hpbGRyZW59PC9jb2RlPixcbiAgICAgICAgICAgICAgICAgICAgICAgIGE6ICh7aHJlZiwgY2hpbGRyZW59KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxhIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2hyZWZ9IFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMCB1bmRlcmxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgICApLFxuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TWVzc2FnZVdpdGhDaXRhdGlvbnMobWVzc2FnZS5jb250ZW50LCBtZXNzYWdlLnNvdXJjZXMgfHwgW10pfVxuICAgICAgICAgICAgICAgICAgICA8L1JlYWN0TWFya2Rvd24+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8cD57bWVzc2FnZS5jb250ZW50fTwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAge21lc3NhZ2Uuc291cmNlcyAmJiBtZXNzYWdlLnNvdXJjZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcHQtMyBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIG1iLTJcIj7wn5OaIFNvdXJjZXM6PC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bWVzc2FnZS5zb3VyY2VzLm1hcCgoc291cmNlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0wLjVcIj5be2luZGV4ICsgMX1dPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e3NvdXJjZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIGhvdmVyOnVuZGVybGluZSBmbGV4LTFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldFNvdXJjZVRpdGxlKHNvdXJjZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgey8qIFRpbWVzdGFtcCAqL31cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTIgbWwtNFwiPlxuICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lKG1lc3NhZ2UudGltZXN0YW1wKX1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgICBcbiAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIHsvKiBJbnB1dCBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0xMDAgcC02IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgYmctZ3JheS01MCByb3VuZGVkLXhsIHAtM1wiPlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2lucHV0fVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRJbnB1dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBvbktleVByZXNzPXtoYW5kbGVLZXlQcmVzc31cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQXNrIG1lIGFueXRoaW5nIGFib3V0IEF2ZW4uLi5cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLXRyYW5zcGFyZW50IGJvcmRlci1ub25lIG91dGxpbmUtbm9uZSB0ZXh0LWdyYXktODAwIHBsYWNlaG9sZGVyLWdyYXktNTAwXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZW5kfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZyB8fCAhaW5wdXQudHJpbSgpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LTEwIGgtMTAgYmctcmVkLTQwMCBob3ZlcjpiZy1yZWQtNTAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRleHQtd2hpdGUgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiAxOWw5IDItOS0xOC05IDE4IDktMnptMCAwdi04XCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHsvKiBTdGF0dXMgaW5kaWNhdG9yICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JlZW4tNDAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPkVuaGFuY2VkIEFJIHdpdGggYWdlbnQgdGhpbmtpbmchPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJSZWFjdE1hcmtkb3duIiwiQWdlbnRUaGlua2luZyIsIkVuaGFuY2VkQ2hhdEludGVyZmFjZSIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpZCIsInJvbGUiLCJjb250ZW50IiwidGltZXN0YW1wIiwiRGF0ZSIsImlucHV0Iiwic2V0SW5wdXQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzZXNzaW9uSWQiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJhZ2VudFN0ZXBzIiwic2V0QWdlbnRTdGVwcyIsInNlYXJjaFJlc3VsdHMiLCJzZXRTZWFyY2hSZXN1bHRzIiwic2hvd0FnZW50VGhpbmtpbmciLCJzZXRTaG93QWdlbnRUaGlua2luZyIsIm1lc3NhZ2VzRW5kUmVmIiwic2Nyb2xsVG9Cb3R0b20iLCJjdXJyZW50Iiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImhhbmRsZVNlbmQiLCJ0cmltIiwidXNlck1lc3NhZ2UiLCJwcmV2IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1lc3NhZ2UiLCJ1c2VySWQiLCJvayIsIkVycm9yIiwicmVhZGVyIiwiZ2V0UmVhZGVyIiwiZGVjb2RlciIsIlRleHREZWNvZGVyIiwiZmluYWxBbnN3ZXIiLCJkb25lIiwidmFsdWUiLCJyZWFkIiwiY2h1bmsiLCJkZWNvZGUiLCJsaW5lcyIsInNwbGl0IiwibGluZSIsInN0YXJ0c1dpdGgiLCJkYXRhIiwicGFyc2UiLCJzbGljZSIsInR5cGUiLCJzdGVwcyIsImFuc3dlciIsImFpTWVzc2FnZSIsInNvdXJjZXMiLCJtYXAiLCJzIiwidXJsIiwiZXJyb3IiLCJwYXJzZUVycm9yIiwiY29uc29sZSIsImVycm9yTWVzc2FnZSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJmb3JtYXRUaW1lIiwiZGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJob3VyMTIiLCJmb3JtYXRNZXNzYWdlV2l0aENpdGF0aW9ucyIsImdldFNvdXJjZVRpdGxlIiwiaW5jbHVkZXMiLCJkaXYiLCJjbGFzc05hbWUiLCJsZW5ndGgiLCJpc1RoaW5raW5nIiwiaXNFeHBhbmRlZCIsIm9uVG9nZ2xlIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiY29tcG9uZW50cyIsImgxIiwiY2hpbGRyZW4iLCJoMiIsImgzIiwidWwiLCJvbCIsImxpIiwicCIsInN0cm9uZyIsImNvZGUiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsInNvdXJjZSIsImluZGV4Iiwic3BhbiIsInJlZiIsIm9uQ2hhbmdlIiwib25LZXlQcmVzcyIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJidXR0b24iLCJvbkNsaWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/EnhancedChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MemoryManager.tsx":
/*!******************************************!*\
  !*** ./src/components/MemoryManager.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemoryManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MemoryManager({ sessionId }) {\n    const [showMemory, setShowMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memories, setMemories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadMemories = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch(`/api/memory?userId=${sessionId}`);\n            const data = await response.json();\n            setMemories(data.memories || []);\n        } catch (error) {\n            console.error(\"Error loading memories:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearMemories = async ()=>{\n        if (!confirm(\"Are you sure you want to clear all conversation memory? This cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/memory\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: sessionId\n                })\n            });\n            if (response.ok) {\n                setMemories([]);\n                alert(\"Conversation memory cleared successfully!\");\n            } else {\n                alert(\"Failed to clear memory\");\n            }\n        } catch (error) {\n            console.error(\"Error clearing memories:\", error);\n            alert(\"Error clearing memory\");\n        }\n    };\n    const toggleMemory = ()=>{\n        setShowMemory(!showMemory);\n        if (!showMemory && memories.length === 0) {\n            loadMemories();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t border-gray-100 bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-gray-500\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-600\",\n                                children: \"Memory: Enabled\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleMemory,\n                                className: \"text-xs text-blue-600 hover:text-blue-800 underline\",\n                                children: [\n                                    showMemory ? \"Hide\" : \"View\",\n                                    \" Memory\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearMemories,\n                                className: \"text-xs text-red-600 hover:text-red-800 underline\",\n                                children: \"Clear Memory\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            showMemory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-800\",\n                                children: \"Conversation Memory\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadMemories,\n                                disabled: isLoading,\n                                className: \"text-xs text-gray-600 hover:text-gray-800 underline disabled:opacity-50\",\n                                children: isLoading ? \"Loading...\" : \"Refresh\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-48 overflow-y-auto space-y-2\",\n                        children: memories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 italic\",\n                            children: isLoading ? \"Loading memories...\" : \"No conversation memories found.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, this) : memories.map((memory, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-700 line-clamp-2\",\n                                        children: memory.text || memory.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: new Date(memory.created_at || memory.timestamp).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\uD83D\\uDCA1 The AI remembers your preferences and past conversations to provide more personalized responses.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/MemoryManager.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MemoryManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VoiceInterface.tsx":
/*!*******************************************!*\
  !*** ./src/components/VoiceInterface.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VoiceInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction VoiceInterface() {\n    const [isCallActive, setIsCallActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transcript, setTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [vapi, setVapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize Vapi when component mounts\n        const initVapi = async ()=>{\n            try {\n                const { default: Vapi } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@vapi-ai\"), __webpack_require__.e(\"vendor-chunks/@daily-co\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @vapi-ai/web */ \"(ssr)/./node_modules/@vapi-ai/web/dist/vapi.js\", 23));\n                const vapiInstance = new Vapi(\"2f099101-7072-4a66-a24a-579648b720d0\" || 0);\n                // Set up event listeners\n                vapiInstance.on(\"call-start\", ()=>{\n                    console.log(\"Call started\");\n                    setIsCallActive(true);\n                    setIsConnecting(false);\n                });\n                vapiInstance.on(\"call-end\", ()=>{\n                    console.log(\"Call ended\");\n                    setIsCallActive(false);\n                    setIsConnecting(false);\n                });\n                vapiInstance.on(\"message\", (message)=>{\n                    if (message.type === \"transcript\") {\n                        setTranscript((prev)=>prev + `\\n${message.role}: ${message.transcript}`);\n                    }\n                });\n                vapiInstance.on(\"error\", (error)=>{\n                    console.error(\"Vapi error:\", error);\n                    setIsCallActive(false);\n                    setIsConnecting(false);\n                });\n                setVapi(vapiInstance);\n            } catch (error) {\n                console.error(\"Failed to initialize Vapi:\", error);\n            }\n        };\n        initVapi();\n    }, []);\n    const startCall = async ()=>{\n        if (!vapi) return;\n        setIsConnecting(true);\n        setTranscript(\"\");\n        try {\n            await vapi.start(\"cff2bad0-7e4e-413f-888f-5a4207cffbfa\" || 0);\n        } catch (error) {\n            console.error(\"Failed to start call:\", error);\n            setIsConnecting(false);\n        }\n    };\n    const endCall = ()=>{\n        if (!vapi) return;\n        vapi.stop();\n        setIsCallActive(false);\n        setIsConnecting(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center py-16 px-8 h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold text-gray-800 mb-3\",\n                        children: \"Voice Assistant\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg max-w-md\",\n                        children: \"Click the microphone to start a voice conversation with our AI assistant\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: isCallActive ? endCall : startCall,\n                        disabled: isConnecting,\n                        className: `w-24 h-24 rounded-full flex items-center justify-center text-3xl transition-all duration-300 transform hover:scale-105 ${isCallActive ? \"bg-red-500 hover:bg-red-600 text-white shadow-lg animate-pulse\" : isConnecting ? \"bg-yellow-500 text-white cursor-not-allowed shadow-lg\" : \"bg-green-500 hover:bg-green-600 text-white shadow-lg\"}`,\n                        children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-3 border-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this) : isCallActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            isConnecting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-600 font-medium\",\n                                        children: \"Connecting...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            isCallActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-600 font-medium\",\n                                        children: \"Call in progress\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            !isCallActive && !isConnecting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Ready to start voice chat\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            transcript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl mt-8 flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4 text-gray-800\",\n                        children: \"Live Conversation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 rounded-xl p-6 max-h-64 overflow-y-auto border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-sm whitespace-pre-wrap text-gray-700 leading-relaxed font-sans\",\n                            children: transcript\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm text-gray-500 max-w-lg mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Tips for best experience:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"leading-relaxed\",\n                        children: \"Allow microphone access when prompted. Speak clearly and naturally. The AI can help with all your Aven HELOC Credit Card questions.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VoiceInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/chat.ts":
/*!*************************!*\
  !*** ./src/lib/chat.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\nasync function sendChatMessage(message, sessionId, userId) {\n    try {\n        // Check guardrails first\n        const guardrailCheck = await checkGuardrails(message);\n        if (guardrailCheck.isBlocked) {\n            return {\n                answer: getGuardrailResponse(guardrailCheck),\n                sources: [],\n                confidence: 1.0\n            };\n        }\n        // Send to chat API\n        const response = await fetch(\"/api/chat\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                message,\n                sessionId,\n                userId: userId || \"anonymous\"\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to send message\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error in sendChatMessage:\", error);\n        throw error;\n    }\n}\nasync function checkGuardrails(message) {\n    try {\n        const response = await fetch(\"/api/guardrails\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                message\n            })\n        });\n        if (!response.ok) {\n            return {\n                isBlocked: false\n            };\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error checking guardrails:\", error);\n        return {\n            isBlocked: false\n        };\n    }\n}\nfunction getGuardrailResponse(check) {\n    switch(check.category){\n        case \"personal_data\":\n            return \"I can't help with personal data or account-specific information. For account questions, please log into your Aven account or contact customer service directly.\";\n        case \"legal_advice\":\n            return \"I can't provide legal advice. For legal questions about your Aven account or services, please consult with a qualified attorney or contact Aven's legal team.\";\n        case \"financial_advice\":\n            return \"I can provide general information about Aven's products but can't give personalized financial advice. Please consult with a financial advisor or contact Aven directly for specific financial guidance.\";\n        case \"toxicity\":\n            return \"I'm here to help with questions about Aven's services. Please keep our conversation respectful and on topic.\";\n        default:\n            return \"I can't help with that request. Please ask me about Aven's HELOC Credit Card, services, or general account information.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/chat.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d707a1c92634\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzVmYTYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNzA3YTFjOTI2MzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Aven AI Customer Support\",\n    description: \"AI-powered customer support for Aven homeowners\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white border-b border-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-800\",\n                                        children: \"Aven AI Support\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: \"text-gray-600 hover:text-gray-800 font-medium\",\n                                            children: \"Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/about\",\n                                            className: \"text-gray-600 hover:text-gray-800 font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();