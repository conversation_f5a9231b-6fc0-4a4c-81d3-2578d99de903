/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxla3JhbWVsYWhlZWhyaWRveSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFpLWN1c3RvbWVyLWFnZW50JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFsZWtyYW1lbGFoZWVocmlkb3klMkZEZXNrdG9wJTJGcHJvamVjdHMlMkZhaS1jdXN0b21lci1hZ2VudCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxla3JhbWVsYWhlZWhyaWRveSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFpLWN1c3RvbWVyLWFnZW50JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhbGVrcmFtZWxhaGVlaHJpZG95JTJGRGVza3RvcCUyRnByb2plY3RzJTJGYWktY3VzdG9tZXItYWdlbnQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxla3JhbWVsYWhlZWhyaWRveSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFpLWN1c3RvbWVyLWFnZW50JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxla3JhbWVsYWhlZWhyaWRveSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFpLWN1c3RvbWVyLWFnZW50JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXlKO0FBQ3pKO0FBQ0Esb09BQTBKO0FBQzFKO0FBQ0EsME9BQTZKO0FBQzdKO0FBQ0Esd09BQTRKO0FBQzVKO0FBQ0Esa1BBQWlLO0FBQ2pLO0FBQ0Esc1FBQTJLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvP2YwZmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxla3JhbWVsYWhlZWhyaWRveS9EZXNrdG9wL3Byb2plY3RzL2FpLWN1c3RvbWVyLWFnZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZWtyYW1lbGFoZWVocmlkb3kvRGVza3RvcC9wcm9qZWN0cy9haS1jdXN0b21lci1hZ2VudC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxla3JhbWVsYWhlZWhyaWRveS9EZXNrdG9wL3Byb2plY3RzL2FpLWN1c3RvbWVyLWFnZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGVrcmFtZWxhaGVlaHJpZG95L0Rlc2t0b3AvcHJvamVjdHMvYWktY3VzdG9tZXItYWdlbnQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxla3JhbWVsYWhlZWhyaWRveS9EZXNrdG9wL3Byb2plY3RzL2FpLWN1c3RvbWVyLWFnZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxla3JhbWVsYWhlZWhyaWRveS9EZXNrdG9wL3Byb2plY3RzL2FpLWN1c3RvbWVyLWFnZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxla3JhbWVsYWhlZWhyaWRveSUyRkRlc2t0b3AlMkZwcm9qZWN0cyUyRmFpLWN1c3RvbWVyLWFnZW50JTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFtSCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Lz8zZjQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZWtyYW1lbGFoZWVocmlkb3kvRGVza3RvcC9wcm9qZWN0cy9haS1jdXN0b21lci1hZ2VudC9zcmMvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SimpleChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SimpleChatInterface */ \"(ssr)/./src/components/SimpleChatInterface.tsx\");\n/* harmony import */ var _components_VoiceInterface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/VoiceInterface */ \"(ssr)/./src/components/VoiceInterface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [activeMode, setActiveMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900 opacity-50\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(120,119,198,0.1),transparent_50%)]\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 text-center pt-16 pb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-light text-white mb-4 tracking-wide\",\n                                children: \"aven\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-lg font-light max-w-md mx-auto\",\n                                children: \"AI-powered customer support for your HELOC journey\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col items-center justify-center px-4 pb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-4xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-full p-1.5 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 text-sm ${activeMode === \"chat\" ? \"bg-white text-black shadow-lg\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"}`,\n                                                onClick: ()=>setActiveMode(\"chat\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Chat\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 text-sm ${activeMode === \"voice\" ? \"bg-white text-black shadow-lg\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"}`,\n                                                onClick: ()=>setActiveMode(\"voice\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Voice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/30 backdrop-blur-md border border-gray-800/50 rounded-3xl shadow-2xl overflow-hidden\",\n                                    children: activeMode === \"chat\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 40\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VoiceInterface__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 66\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SimpleChatInterface.tsx":
/*!************************************************!*\
  !*** ./src/components/SimpleChatInterface.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/chat */ \"(ssr)/./src/lib/chat.ts\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SimpleChatInterface() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hi! I'm here to help you with questions about your Aven HELOC Credit Card. How can I assist you today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>`session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            const response = await (0,_lib_chat__WEBPACK_IMPORTED_MODULE_2__.sendChatMessage)(input, sessionId, \"anonymous\", true);\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: response.answer,\n                timestamp: new Date(),\n                sources: response.sources?.map((s)=>s.url) || []\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again later.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[600px] bg-transparent\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-8 space-y-6\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `max-w-3xl px-6 py-4 rounded-3xl ${message.role === \"assistant\" ? \"bg-gray-800/40 backdrop-blur-sm border border-gray-700/50 text-gray-100\" : \"bg-white/10 backdrop-blur-sm border border-white/20 text-white\"}`,\n                                children: [\n                                    message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-sm max-w-none prose-invert\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 pt-4 border-t border-gray-600/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs font-medium mb-2 text-gray-300\",\n                                                children: \"\\uD83D\\uDCDA Sources:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: source,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-xs text-blue-400 hover:text-blue-300 hover:underline block transition-colors\",\n                                                        children: [\n                                                            \"[\",\n                                                            index + 1,\n                                                            \"] \",\n                                                            source.includes(\"aven.com\") ? \"Aven Information\" : source\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/40 backdrop-blur-sm border border-gray-700/50 px-6 py-4 rounded-3xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: \"Thinking...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800/50 p-8 bg-gray-900/20 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyPress,\n                                    placeholder: \"Ask a question...\",\n                                    className: \"w-full px-6 py-4 bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-full text-white placeholder-gray-400 outline-none focus:border-white/30 focus:ring-2 focus:ring-white/10 transition-all\",\n                                    disabled: isLoading\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-gray-700/50 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSend,\n                            disabled: isLoading || !input.trim(),\n                            className: \"w-12 h-12 bg-white hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed text-black rounded-full flex items-center justify-center transition-all duration-200 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/SimpleChatInterface.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SimpleChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VoiceInterface.tsx":
/*!*******************************************!*\
  !*** ./src/components/VoiceInterface.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VoiceInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction VoiceInterface() {\n    const [isCallActive, setIsCallActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transcript, setTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [vapi, setVapi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentCallId, setCurrentCallId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lastUserMessage, setLastUserMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSchedulePrompt, setShowSchedulePrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize Vapi when component mounts\n        const initVapi = async ()=>{\n            try {\n                const { default: Vapi } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@vapi-ai\"), __webpack_require__.e(\"vendor-chunks/@daily-co\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @vapi-ai/web */ \"(ssr)/./node_modules/@vapi-ai/web/dist/vapi.js\", 23));\n                const vapiInstance = new Vapi(\"2f099101-7072-4a66-a24a-579648b720d0\" || 0);\n                // Set up event listeners\n                vapiInstance.on(\"call-start\", ()=>{\n                    console.log(\"Call started\");\n                    const callId = `voice_call_${Date.now()}`;\n                    setCurrentCallId(callId);\n                    setIsCallActive(true);\n                    setIsConnecting(false);\n                });\n                vapiInstance.on(\"call-end\", ()=>{\n                    console.log(\"Call ended\");\n                    setIsCallActive(false);\n                    setIsConnecting(false);\n                    // Show schedule prompt after call ends\n                    setShowSchedulePrompt(true);\n                    // Clean up any active voice traces\n                    if (currentCallId) {\n                        fetch(\"/api/voice/trace\", {\n                            method: \"DELETE\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                callId: currentCallId\n                            })\n                        }).catch(console.error);\n                    }\n                    setCurrentCallId(null);\n                });\n                vapiInstance.on(\"message\", (message)=>{\n                    console.log(\"Vapi message:\", message);\n                    console.log(\"Message type:\", message.type);\n                    console.log(\"Message role:\", message.role);\n                    console.log(\"Message content:\", message.transcript || message.content);\n                    if (message.type === \"transcript\" && message.transcript) {\n                        setTranscript((prev)=>prev + `\\n${message.role}: ${message.transcript}`);\n                        // Trace voice interactions\n                        if (message.role === \"user\" && message.transcript) {\n                            console.log(\"\\uD83C\\uDFA4 Tracing user message:\", message.transcript);\n                            setLastUserMessage(message.transcript);\n                            // Log user message to our tracing endpoint\n                            fetch(\"/api/voice/trace\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    type: \"user_message\",\n                                    transcript: message.transcript,\n                                    timestamp: new Date().toISOString(),\n                                    callId: currentCallId || message.call?.id || \"voice_call\"\n                                })\n                            }).catch(console.error);\n                            // Set a timeout to auto-complete the trace if no assistant response comes\n                            setTimeout(()=>{\n                                console.log(\"⏰ Auto-completing trace due to timeout...\");\n                                fetch(\"/api/voice/trace\", {\n                                    method: \"POST\",\n                                    headers: {\n                                        \"Content-Type\": \"application/json\"\n                                    },\n                                    body: JSON.stringify({\n                                        type: \"assistant_response\",\n                                        transcript: `This is an auto-generated response for the question: \"${message.transcript}\". The actual assistant response was not captured properly from Vapi messages.`,\n                                        timestamp: new Date().toISOString(),\n                                        callId: currentCallId || \"voice_call\"\n                                    })\n                                }).catch(console.error);\n                            }, 10000) // 10 second timeout\n                            ;\n                        } else if (message.role === \"assistant\" && message.transcript) {\n                            console.log(\"\\uD83E\\uDD16 Tracing assistant response:\", message.transcript);\n                            // Log assistant response to our tracing endpoint\n                            fetch(\"/api/voice/trace\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    type: \"assistant_response\",\n                                    transcript: message.transcript,\n                                    timestamp: new Date().toISOString(),\n                                    callId: currentCallId || message.call?.id || \"voice_call\"\n                                })\n                            }).catch(console.error);\n                        }\n                    } else if (message.type === \"conversation-update\") {\n                        console.log(\"\\uD83D\\uDCDD Conversation update:\", message);\n                        // Check if this has assistant message\n                        if (message.conversation && message.conversation.messages) {\n                            const lastMessage = message.conversation.messages[message.conversation.messages.length - 1];\n                            if (lastMessage && lastMessage.role === \"assistant\" && lastMessage.content) {\n                                console.log(\"\\uD83E\\uDD16 Tracing assistant from conversation update:\", lastMessage.content);\n                                // Log assistant response from conversation update\n                                fetch(\"/api/voice/trace\", {\n                                    method: \"POST\",\n                                    headers: {\n                                        \"Content-Type\": \"application/json\"\n                                    },\n                                    body: JSON.stringify({\n                                        type: \"assistant_response\",\n                                        transcript: lastMessage.content,\n                                        timestamp: new Date().toISOString(),\n                                        callId: currentCallId || message.call?.id || \"voice_call\"\n                                    })\n                                }).catch(console.error);\n                            }\n                        }\n                    } else if (message.type === \"function-call\") {\n                        console.log(\"Function call:\", message.functionCall);\n                    } else if (message.type === \"speech-update\") {\n                        console.log(\"Speech update:\", message);\n                        // Check if this contains assistant speech\n                        if (message.status === \"stopped\" && message.role === \"assistant\") {\n                            console.log(\"\\uD83E\\uDD16 Assistant speech detected, trying to capture response...\");\n                            // Try to get the response from the transcript so far\n                            const transcriptLines = transcript.split(\"\\n\");\n                            const lastAssistantLine = transcriptLines.reverse().find((line)=>line.startsWith(\"assistant:\"));\n                            if (lastAssistantLine) {\n                                const assistantResponse = lastAssistantLine.replace(\"assistant:\", \"\").trim();\n                                if (assistantResponse && assistantResponse.length > 0) {\n                                    console.log(\"\\uD83E\\uDD16 Captured assistant response from transcript:\", assistantResponse);\n                                    fetch(\"/api/voice/trace\", {\n                                        method: \"POST\",\n                                        headers: {\n                                            \"Content-Type\": \"application/json\"\n                                        },\n                                        body: JSON.stringify({\n                                            type: \"assistant_response\",\n                                            transcript: assistantResponse,\n                                            timestamp: new Date().toISOString(),\n                                            callId: currentCallId || \"voice_call\"\n                                        })\n                                    }).catch(console.error);\n                                }\n                            }\n                        }\n                    } else {\n                        console.log(\"\\uD83D\\uDD0D Unknown message type:\", message.type, message);\n                    }\n                });\n                vapiInstance.on(\"error\", (error)=>{\n                    console.error(\"Vapi error:\", error);\n                    setIsCallActive(false);\n                    setIsConnecting(false);\n                });\n                setVapi(vapiInstance);\n            } catch (error) {\n                console.error(\"Failed to initialize Vapi:\", error);\n            }\n        };\n        initVapi();\n    }, []);\n    const startCall = async ()=>{\n        if (!vapi) return;\n        setIsConnecting(true);\n        setTranscript(\"\");\n        setShowSchedulePrompt(false);\n        try {\n            await vapi.start(\"cff2bad0-7e4e-413f-888f-5a4207cffbfa\" || 0);\n        } catch (error) {\n            console.error(\"Failed to start call:\", error);\n            setIsConnecting(false);\n        }\n    };\n    const endCall = ()=>{\n        if (!vapi) return;\n        vapi.stop();\n        setIsCallActive(false);\n        setIsConnecting(false);\n    };\n    const handleScheduleMeeting = ()=>{\n        // Open cal.com link in new tab\n        window.open(\"https://cal.com/alhridoy/15min\", \"_blank\");\n        setShowSchedulePrompt(false);\n    };\n    const dismissSchedulePrompt = ()=>{\n        setShowSchedulePrompt(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center py-16 px-8 h-[600px] relative\",\n        children: [\n            showSchedulePrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-2xl p-8 max-w-md w-full mx-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-gray-500 rounded-full mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm mb-2\",\n                                    children: \"ended\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white text-xl font-semibold mb-2\",\n                                    children: \"Aven Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mb-6\",\n                                    children: \"Still not sure what plan is right for you? Speak with a member of our team\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleScheduleMeeting,\n                                    className: \"w-full bg-teal-500 hover:bg-teal-600 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n                                    children: \"Schedule\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: dismissSchedulePrompt,\n                                    className: \"w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n                                    children: \"Maybe Later\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold text-gray-800 mb-3\",\n                        children: \"Voice Assistant\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg max-w-md\",\n                        children: \"Click the microphone to start a voice conversation with our AI assistant\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: isCallActive ? endCall : startCall,\n                        disabled: isConnecting,\n                        className: `w-24 h-24 rounded-full flex items-center justify-center text-3xl transition-all duration-300 transform hover:scale-105 ${isCallActive ? \"bg-red-500 hover:bg-red-600 text-white shadow-lg animate-pulse\" : isConnecting ? \"bg-yellow-500 text-white cursor-not-allowed shadow-lg\" : \"bg-green-500 hover:bg-green-600 text-white shadow-lg\"}`,\n                        children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-3 border-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this) : isCallActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            isConnecting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-600 font-medium\",\n                                        children: \"Connecting...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            isCallActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-600 font-medium\",\n                                        children: \"Call in progress\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            !isCallActive && !isConnecting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Ready to start voice chat\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            transcript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl mt-8 flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4 text-gray-800\",\n                        children: \"Live Conversation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 rounded-xl p-6 max-h-64 overflow-y-auto border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-sm whitespace-pre-wrap text-gray-700 leading-relaxed font-sans\",\n                            children: transcript\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm text-gray-500 max-w-lg mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Tips for best experience:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"leading-relaxed\",\n                        children: \"Allow microphone access when prompted. Speak clearly and naturally. The AI can help with all your Aven HELOC Credit Card questions.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/VoiceInterface.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VoiceInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/chat.ts":
/*!*************************!*\
  !*** ./src/lib/chat.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\nasync function sendChatMessage(message, sessionId, userId, useFullPipeline) {\n    try {\n        const startTime = Date.now();\n        // Always use full pipeline now - removed instant responses bypass\n        // Full 6-step RAG pipeline provides better quality and observability\n        // Check guardrails for non-instant responses\n        const guardrailCheck = await checkGuardrails(message);\n        if (guardrailCheck.isBlocked) {\n            return {\n                answer: getGuardrailResponse(guardrailCheck),\n                sources: [],\n                confidence: 1.0\n            };\n        }\n        // Send to chat API\n        const response = await fetch(\"/api/chat\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                message,\n                sessionId,\n                userId: userId || \"anonymous\",\n                useFullPipeline\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to send message\");\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"Error in sendChatMessage:\", error);\n        throw error;\n    }\n}\nasync function checkGuardrails(message) {\n    try {\n        const response = await fetch(\"/api/guardrails\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                message\n            })\n        });\n        if (!response.ok) {\n            return {\n                isBlocked: false\n            };\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error checking guardrails:\", error);\n        return {\n            isBlocked: false\n        };\n    }\n}\nfunction getGuardrailResponse(check) {\n    switch(check.category){\n        case \"personal_data\":\n            return \"I can't help with personal data or account-specific information. For account questions, please log into your Aven account or contact customer service directly.\";\n        case \"legal_advice\":\n            return \"I can't provide legal advice. For legal questions about your Aven account or services, please consult with a qualified attorney or contact Aven's legal team.\";\n        case \"financial_advice\":\n            return \"I can provide general information about Aven's products but can't give personalized financial advice. Please consult with a financial advisor or contact Aven directly for specific financial guidance.\";\n        case \"toxicity\":\n            return \"I'm here to help with questions about Aven's services. Please keep our conversation respectful and on topic.\";\n        default:\n            return \"I can't help with that request. Please ask me about Aven's HELOC Credit Card, services, or general account information.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/chat.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d707a1c92634\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzVmYTYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNzA3YTFjOTI2MzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Aven AI Customer Support\",\n    description: \"AI-powered customer support for Aven homeowners\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFIZ0I7QUFLZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULCtKQUFlO3NCQUM5Qiw0RUFBQ1U7Z0JBQUtELFdBQVU7MEJBQTJCSjs7Ozs7Ozs7Ozs7Ozs7OztBQUluRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWN1c3RvbWVyLWFnZW50Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBdmVuIEFJIEN1c3RvbWVyIFN1cHBvcnQnLFxuICBkZXNjcmlwdGlvbjogJ0FJLXBvd2VyZWQgY3VzdG9tZXIgc3VwcG9ydCBmb3IgQXZlbiBob21lb3duZXJzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/@swc","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();