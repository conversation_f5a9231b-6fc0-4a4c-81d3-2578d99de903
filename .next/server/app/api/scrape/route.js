"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scrape/route";
exports.ids = ["app/api/scrape/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/scrape/route.ts */ \"(rsc)/./src/app/api/scrape/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scrape/route\",\n        pathname: \"/api/scrape\",\n        filename: \"route\",\n        bundlePath: \"app/api/scrape/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/api/scrape/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/scrape/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/scrape/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/scrape/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_scraper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/scraper */ \"(rsc)/./src/lib/scraper.ts\");\n\n\n// Track scraping progress\nlet scrapingInProgress = false;\nlet scrapingProgress = {\n    status: \"idle\",\n    startTime: null,\n    itemsProcessed: 0,\n    totalEstimated: 0,\n    currentTask: \"\",\n    errors: []\n};\nasync function POST(request) {\n    try {\n        const body = await request.json().catch(()=>({}));\n        const { comprehensive = false, mode = \"full\" } = body;\n        // Check if scraping is already in progress\n        if (scrapingInProgress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Scraping already in progress\",\n                progress: scrapingProgress\n            }, {\n                status: 409\n            });\n        }\n        // Initialize progress tracking\n        scrapingInProgress = true;\n        scrapingProgress = {\n            status: \"starting\",\n            startTime: new Date(),\n            itemsProcessed: 0,\n            totalEstimated: comprehensive ? 500 : 100,\n            currentTask: comprehensive ? \"Comprehensive site scraping\" : \"Targeted content scraping\",\n            errors: []\n        };\n        console.log(`🚀 Starting ${comprehensive ? \"comprehensive\" : \"targeted\"} knowledge base update...`);\n        let results;\n        let itemCount = 0;\n        try {\n            if (comprehensive) {\n                if (mode === \"comprehensive-only\") {\n                    // Only run comprehensive search\n                    scrapingProgress.currentTask = \"Running comprehensive site search\";\n                    results = await (0,_lib_scraper__WEBPACK_IMPORTED_MODULE_1__.comprehensiveSiteSearch)();\n                    itemCount = results.length;\n                } else {\n                    // Run both targeted and comprehensive\n                    scrapingProgress.currentTask = \"Running full comprehensive update\";\n                    await (0,_lib_scraper__WEBPACK_IMPORTED_MODULE_1__.updateKnowledgeBaseComprehensive)();\n                    itemCount = \"comprehensive\";\n                }\n            } else {\n                // Targeted scraping only\n                scrapingProgress.currentTask = \"Running targeted content scraping\";\n                results = await (0,_lib_scraper__WEBPACK_IMPORTED_MODULE_1__.scrapeAvenData)();\n                itemCount = results.length;\n            }\n            scrapingProgress.status = \"completed\";\n            scrapingProgress.itemsProcessed = typeof itemCount === \"number\" ? itemCount : 0;\n        } catch (error) {\n            scrapingProgress.status = \"error\";\n            scrapingProgress.errors.push(error instanceof Error ? error.message : \"Unknown error\");\n            throw error;\n        } finally{\n            scrapingInProgress = false;\n        }\n        const duration = scrapingProgress.startTime ? Date.now() - scrapingProgress.startTime.getTime() : 0;\n        console.log(`✅ Scraping completed in ${Math.round(duration / 1000)}s`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: comprehensive ? \"Successfully completed comprehensive site scraping\" : `Successfully scraped and added ${itemCount} items to knowledge base`,\n            itemCount,\n            type: comprehensive ? \"comprehensive\" : \"targeted\",\n            mode,\n            duration: Math.round(duration / 1000),\n            progress: scrapingProgress\n        });\n    } catch (error) {\n        console.error(\"❌ Error in scrape API:\", error);\n        scrapingInProgress = false;\n        scrapingProgress.status = \"error\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to scrape data\",\n            details: error instanceof Error ? error.message : \"Unknown error\",\n            progress: scrapingProgress\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Scraping API Status\",\n        endpoint: \"/api/scrape\",\n        inProgress: scrapingInProgress,\n        progress: scrapingProgress,\n        usage: {\n            \"POST /api/scrape\": \"Start targeted scraping\",\n            'POST /api/scrape {\"comprehensive\": true}': \"Start comprehensive scraping (targeted + comprehensive)\",\n            'POST /api/scrape {\"comprehensive\": true, \"mode\": \"comprehensive-only\"}': \"Start comprehensive-only scraping\",\n            \"GET /api/scrape\": \"Get current status and progress\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scrape/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/pinecone.ts":
/*!*****************************!*\
  !*** ./src/lib/pinecone.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToKnowledgeBase: () => (/* binding */ addToKnowledgeBase),\n/* harmony export */   initializePinecone: () => (/* binding */ initializePinecone),\n/* harmony export */   searchKnowledgeBase: () => (/* binding */ searchKnowledgeBase)\n/* harmony export */ });\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @pinecone-database/pinecone */ \"(rsc)/./node_modules/@pinecone-database/pinecone/dist/index.js\");\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__);\n\nlet pinecone = null;\nasync function initializePinecone() {\n    if (!pinecone) {\n        pinecone = new _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__.Pinecone({\n            apiKey: \"pcsk_77kxri_6eW7cu6TpWV5XKai7gd5RG9TjUYub3jYi6LLQ2Ppqg3FQvcNxeEXwcQntCcz1nt\" || 0\n        });\n    }\n    return {\n        pinecone\n    };\n}\nasync function searchKnowledgeBase(query) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        // Get the index\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the query using OpenAI\n        const { generateEmbedding } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/openai\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/web-streams-polyfill\"), __webpack_require__.e(\"vendor-chunks/event-target-shim\"), __webpack_require__.e(\"vendor-chunks/agentkeepalive\"), __webpack_require__.e(\"vendor-chunks/abort-controller\"), __webpack_require__.e(\"vendor-chunks/humanize-ms\"), __webpack_require__.e(\"_rsc_src_lib_openai_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const queryEmbedding = await generateEmbedding(query);\n        // Search for similar vectors\n        const searchResult = await index.query({\n            vector: queryEmbedding,\n            topK: 5,\n            includeMetadata: true\n        });\n        // Convert results to AvenKnowledgeItem format\n        const results = searchResult.matches?.map((match)=>({\n                id: match.id,\n                title: match.metadata?.title || \"\",\n                content: match.metadata?.content || \"\",\n                url: match.metadata?.url || \"\",\n                category: match.metadata?.category || \"general\",\n                embedding: match.values\n            })) || [];\n        return results;\n    } catch (error) {\n        console.error(\"Error searching knowledge base:\", error);\n        return [];\n    }\n}\nasync function addToKnowledgeBase(item) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the content using OpenAI\n        const { generateEmbedding } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/openai\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/web-streams-polyfill\"), __webpack_require__.e(\"vendor-chunks/event-target-shim\"), __webpack_require__.e(\"vendor-chunks/agentkeepalive\"), __webpack_require__.e(\"vendor-chunks/abort-controller\"), __webpack_require__.e(\"vendor-chunks/humanize-ms\"), __webpack_require__.e(\"_rsc_src_lib_openai_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const embedding = await generateEmbedding(item.content);\n        // Upsert the vector\n        await index.upsert([\n            {\n                id: item.id,\n                values: embedding,\n                metadata: {\n                    title: item.title,\n                    content: item.content,\n                    url: item.url,\n                    category: item.category\n                }\n            }\n        ]);\n        console.log(`Added item ${item.id} to knowledge base`);\n    } catch (error) {\n        console.error(\"Error adding to knowledge base:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/pinecone.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/scraper.ts":
/*!****************************!*\
  !*** ./src/lib/scraper.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comprehensiveSiteSearch: () => (/* binding */ comprehensiveSiteSearch),\n/* harmony export */   scrapeAvenData: () => (/* binding */ scrapeAvenData),\n/* harmony export */   updateKnowledgeBase: () => (/* binding */ updateKnowledgeBase),\n/* harmony export */   updateKnowledgeBaseComprehensive: () => (/* binding */ updateKnowledgeBaseComprehensive)\n/* harmony export */ });\n/* harmony import */ var exa_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exa-js */ \"(rsc)/./node_modules/exa-js/dist/index.mjs\");\n/* harmony import */ var _pinecone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n\n\nconst exa = new exa_js__WEBPACK_IMPORTED_MODULE_0__.Exa(\"876b8d37-ae90-4893-b725-eade4a31e359\" || 0);\n// Rate limiting and deduplication\nconst RATE_LIMIT_DELAY = 1000 // 1 second between requests\n;\nconst processedUrls = new Set();\nconst processedContent = new Set() // Track content hashes to avoid duplicates\n;\nfunction delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// Simple content hash function for deduplication\nfunction getContentHash(content) {\n    let hash = 0;\n    for(let i = 0; i < content.length; i++){\n        const char = content.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // Convert to 32-bit integer\n        ;\n    }\n    return hash.toString();\n}\n// Enhanced error handling with retry logic\nasync function safeExaRequest(requestFn, maxRetries = 3, context = \"request\") {\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            console.error(`❌ Attempt ${attempt}/${maxRetries} failed for ${context}:`, error.message);\n            if (attempt === maxRetries) {\n                console.error(`🚫 Max retries reached for ${context}`);\n                return null;\n            }\n            // Exponential backoff\n            const backoffDelay = RATE_LIMIT_DELAY * Math.pow(2, attempt - 1);\n            console.log(`⏳ Waiting ${backoffDelay}ms before retry...`);\n            await delay(backoffDelay);\n        }\n    }\n    return null;\n}\n// Content quality filter\nfunction isQualityContent(content, title) {\n    if (!content || content.length < 200) return false;\n    // Skip if content is mostly navigation or boilerplate\n    const lowQualityIndicators = [\n        \"javascript is disabled\",\n        \"enable javascript\",\n        \"cookie policy\",\n        \"loading...\",\n        \"please wait\",\n        \"error 404\",\n        \"page not found\",\n        \"access denied\"\n    ];\n    const lowerContent = content.toLowerCase();\n    if (lowQualityIndicators.some((indicator)=>lowerContent.includes(indicator))) {\n        return false;\n    }\n    // Check for reasonable content-to-navigation ratio\n    const words = content.split(/\\s+/).length;\n    if (words < 50) return false;\n    return true;\n}\nasync function scrapeAvenData() {\n    try {\n        console.log(\"Starting comprehensive Aven data scraping...\");\n        // Clear processed URLs for this session\n        processedUrls.clear();\n        // Comprehensive search queries covering all aspects of Aven\n        const searchQueries = [\n            // Core product information\n            \"Aven HELOC credit card features benefits\",\n            \"Aven home equity line of credit application process\",\n            \"Aven credit card interest rates fees\",\n            \"Aven credit card rewards cashback travel\",\n            \"Aven homeowner financial services\",\n            // Application and eligibility\n            \"Aven credit card eligibility requirements\",\n            \"Aven application approval process\",\n            \"Aven credit score requirements minimum\",\n            \"Aven income verification process\",\n            \"Aven home equity calculation requirements\",\n            // Customer support and service\n            \"Aven customer support FAQ help\",\n            \"Aven contact customer service phone\",\n            \"Aven customer reviews testimonials\",\n            \"Aven account management portal login\",\n            \"Aven mobile app features download\",\n            // Product comparisons and features\n            \"Aven vs traditional credit cards comparison\",\n            \"Aven balance transfer options fees\",\n            \"Aven autopay discount benefits\",\n            \"Aven travel rewards booking portal\",\n            \"Aven cashback rewards program\",\n            // Legal and policy information\n            \"Aven terms conditions privacy policy\",\n            \"Aven security fraud protection\",\n            \"Aven legal disclosures agreements\",\n            // Educational content\n            \"Aven education HELOC guide\",\n            \"Aven homeowner financial tips\",\n            \"Aven credit card best practices\",\n            // Company information\n            \"Aven about company team\",\n            \"Aven news updates announcements\",\n            \"Aven partnerships bank relationships\"\n        ];\n        const allResults = [];\n        let totalProcessed = 0;\n        console.log(`Processing ${searchQueries.length} search queries...`);\n        for(let i = 0; i < searchQueries.length; i++){\n            const query = searchQueries[i];\n            try {\n                console.log(`[${i + 1}/${searchQueries.length}] Searching: \"${query}\"`);\n                const searchResult = await exa.search(query, {\n                    numResults: 15,\n                    includeDomains: [\n                        \"aven.com\"\n                    ]\n                });\n                console.log(`Found ${searchResult.results.length} results for \"${query}\"`);\n                for (const result of searchResult.results){\n                    // Skip if we've already processed this URL\n                    if (processedUrls.has(result.url)) {\n                        continue;\n                    }\n                    try {\n                        // Add rate limiting\n                        await delay(RATE_LIMIT_DELAY);\n                        // Get the full content with retry logic\n                        const contentResult = await safeExaRequest(()=>exa.getContents([\n                                result.id\n                            ]), 3, `content for ${result.url}`);\n                        if (contentResult?.results && contentResult.results.length > 0) {\n                            const content = contentResult.results[0];\n                            // Quality check\n                            if (!isQualityContent(content.text || \"\", result.title || \"\")) {\n                                console.log(`⏭️  Skipping low-quality content: ${result.title}`);\n                                continue;\n                            }\n                            // Check for duplicate content\n                            const contentHash = getContentHash(content.text || \"\");\n                            if (processedContent.has(contentHash)) {\n                                console.log(`⏭️  Skipping duplicate content: ${result.title}`);\n                                continue;\n                            }\n                            const knowledgeItem = {\n                                id: `aven-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,\n                                title: result.title || \"Untitled\",\n                                content: content.text || result.title || \"No content available\",\n                                url: result.url || \"\",\n                                category: categorizeContent((result.title || \"\") + \" \" + (content.text || \"\"))\n                            };\n                            allResults.push(knowledgeItem);\n                            processedUrls.add(result.url);\n                            processedContent.add(contentHash);\n                            totalProcessed++;\n                            console.log(`✓ Processed: ${result.title} (${content.text?.length || 0} chars)`);\n                        }\n                    } catch (contentError) {\n                        console.error(`Error getting content for ${result.url}:`, contentError);\n                        continue;\n                    }\n                }\n                // Add delay between queries to respect rate limits\n                await delay(RATE_LIMIT_DELAY);\n            } catch (error) {\n                console.error(`Error searching for \"${query}\":`, error);\n                continue;\n            }\n        }\n        // Add additional manual knowledge items\n        const manualKnowledgeItems = getManualKnowledgeItems();\n        allResults.push(...manualKnowledgeItems);\n        // Add all items to knowledge base\n        for (const item of allResults){\n            try {\n                await (0,_pinecone__WEBPACK_IMPORTED_MODULE_1__.addToKnowledgeBase)(item);\n            } catch (error) {\n                console.error(`Error adding item ${item.id} to knowledge base:`, error);\n            }\n        }\n        console.log(`Successfully scraped and added ${allResults.length} items to knowledge base`);\n        return allResults;\n    } catch (error) {\n        console.error(\"Error scraping Aven data:\", error);\n        throw error;\n    }\n}\nfunction categorizeContent(content) {\n    const lowerContent = content.toLowerCase();\n    if (lowerContent.includes(\"application\") || lowerContent.includes(\"apply\")) {\n        return \"application\";\n    }\n    if (lowerContent.includes(\"interest rate\") || lowerContent.includes(\"apr\") || lowerContent.includes(\"fee\")) {\n        return \"rates-fees\";\n    }\n    if (lowerContent.includes(\"cashback\") || lowerContent.includes(\"reward\") || lowerContent.includes(\"travel\")) {\n        return \"rewards\";\n    }\n    if (lowerContent.includes(\"heloc\") || lowerContent.includes(\"home equity\") || lowerContent.includes(\"credit line\")) {\n        return \"heloc\";\n    }\n    if (lowerContent.includes(\"support\") || lowerContent.includes(\"help\") || lowerContent.includes(\"faq\")) {\n        return \"support\";\n    }\n    return \"general\";\n}\nfunction getManualKnowledgeItems() {\n    return [\n        {\n            id: \"aven-heloc-overview\",\n            title: \"Aven HELOC Credit Card Overview\",\n            content: `The Aven HELOC Credit Card allows homeowners to access their home equity through a convenient credit card. Key features include:\n      - Credit limits up to $250,000\n      - Interest rates from 7.99% - 15.49% (variable), maximum 18%\n      - 2% cashback on all purchases\n      - 7% cashback on travel booked through Aven's travel portal\n      - No annual fee and no notarization fee\n      - Approval as fast as 5 minutes\n      - Powered by Visa network\n      - Partnered with Coastal Community Bank\n      - 0.25% autopay discount available\n      - Balance transfer option with 2.5% fee`,\n            url: \"https://www.aven.com\",\n            category: \"heloc\"\n        },\n        {\n            id: \"aven-application-process\",\n            title: \"Aven Application Process\",\n            content: `To apply for the Aven HELOC Credit Card:\n      1. Must be a homeowner with sufficient home equity\n      2. Minimum credit score typically around 600\n      3. Stable income requirements (usually $50k+ annually)\n      4. Home value and equity verification\n      5. Quick online application process\n      6. Approval decisions as fast as 5 minutes\n      7. No notarization required\n      The application considers your home equity value, credit score, income, and debt-to-income ratio.`,\n            url: \"https://www.aven.com/apply\",\n            category: \"application\"\n        },\n        {\n            id: \"aven-eligibility\",\n            title: \"Aven Eligibility Requirements\",\n            content: `Eligibility requirements for Aven HELOC Credit Card:\n      - Must be a homeowner\n      - Minimum credit score around 600\n      - Sufficient home equity (typically $250k+ after mortgages and liens)\n      - Stable income (usually $50k+ annually)\n      - Home value requirements vary by state\n      - Subject to state usury limits\n      - Available in most U.S. states\n      The exact requirements may vary based on your location and specific financial situation.`,\n            url: \"https://www.aven.com/eligibility\",\n            category: \"application\"\n        },\n        {\n            id: \"aven-vs-traditional-credit\",\n            title: \"Aven vs Traditional Credit Cards\",\n            content: `Advantages of Aven HELOC Credit Card vs traditional credit cards:\n      - Lower interest rates (7.99%-15.49% vs 18%+ typical credit cards)\n      - Higher credit limits (up to $250k vs typical $5k-$25k)\n      - Secured by home equity (lower risk for lender)\n      - Same convenience as regular credit card\n      - Better rewards (2% cashback vs typical 1%)\n      - No annual fee\n      - Interest may be tax-deductible (consult tax advisor)\n      However, your home serves as collateral, which is an important consideration.`,\n            url: \"https://www.aven.com/compare\",\n            category: \"heloc\"\n        },\n        {\n            id: \"aven-customer-support\",\n            title: \"Aven Customer Support\",\n            content: `Aven customer support options:\n      - Online account management portal\n      - Customer service phone line\n      - Email support\n      - FAQ section on website\n      - Mobile app for account management\n      - 24/7 fraud monitoring\n      - Live chat support during business hours\n      For account-specific questions, customers should log into their account or contact customer service directly.`,\n            url: \"https://www.aven.com/support\",\n            category: \"support\"\n        }\n    ];\n}\nasync function comprehensiveSiteSearch() {\n    try {\n        console.log(\"Starting comprehensive Aven site search...\");\n        // Use multiple strategies to find all pages on aven.com\n        const searchStrategies = [\n            // Direct site search\n            {\n                query: \"site:aven.com\",\n                numResults: 100\n            },\n            // Content-based searches to find different page types\n            {\n                query: \"aven.com blog articles news\",\n                numResults: 50\n            },\n            {\n                query: \"aven.com support help FAQ\",\n                numResults: 50\n            },\n            {\n                query: \"aven.com legal terms privacy\",\n                numResults: 30\n            },\n            {\n                query: \"aven.com about company team\",\n                numResults: 30\n            },\n            {\n                query: \"aven.com contact information\",\n                numResults: 20\n            },\n            {\n                query: \"aven.com education guides tutorials\",\n                numResults: 40\n            },\n            {\n                query: \"aven.com reviews testimonials\",\n                numResults: 30\n            },\n            {\n                query: \"aven.com application process steps\",\n                numResults: 40\n            },\n            {\n                query: \"aven.com features benefits comparison\",\n                numResults: 40\n            }\n        ];\n        const allResults = [];\n        let totalFound = 0;\n        let totalProcessed = 0;\n        console.log(`Using ${searchStrategies.length} search strategies...`);\n        for(let i = 0; i < searchStrategies.length; i++){\n            const strategy = searchStrategies[i];\n            try {\n                console.log(`\\n[${i + 1}/${searchStrategies.length}] Strategy: \"${strategy.query}\" (max ${strategy.numResults} results)`);\n                const searchResult = await exa.search(strategy.query, {\n                    numResults: strategy.numResults,\n                    includeDomains: [\n                        \"aven.com\"\n                    ]\n                });\n                totalFound += searchResult.results.length;\n                console.log(`Found ${searchResult.results.length} results`);\n                for (const result of searchResult.results){\n                    // Skip if we already processed this URL\n                    if (processedUrls.has(result.url)) {\n                        console.log(`⏭️  Skipping duplicate: ${result.url}`);\n                        continue;\n                    }\n                    try {\n                        // Add rate limiting\n                        await delay(RATE_LIMIT_DELAY);\n                        const contentResult = await safeExaRequest(()=>exa.getContents([\n                                result.id\n                            ]), 3, `comprehensive content for ${result.url}`);\n                        if (contentResult?.results && contentResult.results.length > 0) {\n                            const content = contentResult.results[0];\n                            // Quality and duplication checks\n                            if (!isQualityContent(content.text || \"\", result.title || \"\")) {\n                                console.log(`⏭️  Skipping low-quality content: ${result.title} (${content.text?.length || 0} chars)`);\n                                continue;\n                            }\n                            const contentHash = getContentHash(content.text || \"\");\n                            if (processedContent.has(contentHash)) {\n                                console.log(`⏭️  Skipping duplicate content: ${result.title}`);\n                                continue;\n                            }\n                            const knowledgeItem = {\n                                id: `aven-comprehensive-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,\n                                title: result.title || \"Untitled\",\n                                content: content.text || \"No content available\",\n                                url: result.url || \"\",\n                                category: categorizeContent((result.title || \"\") + \" \" + (content.text || \"\"))\n                            };\n                            allResults.push(knowledgeItem);\n                            processedUrls.add(result.url);\n                            processedContent.add(contentHash);\n                            totalProcessed++;\n                            console.log(`✅ [${totalProcessed}] Processed: ${result.title} (${content.text?.length || 0} chars)`);\n                            // Add to knowledge base immediately with retry logic\n                            const kbResult = await safeExaRequest(()=>(0,_pinecone__WEBPACK_IMPORTED_MODULE_1__.addToKnowledgeBase)(knowledgeItem), 2, `adding ${knowledgeItem.id} to knowledge base`);\n                            if (kbResult !== null) {\n                                console.log(`📚 Added to knowledge base: ${knowledgeItem.id}`);\n                            } else {\n                                console.error(`❌ Failed to add to knowledge base: ${knowledgeItem.id}`);\n                            }\n                        }\n                    } catch (contentError) {\n                        console.error(`❌ Error getting content for ${result.url}:`, contentError);\n                        continue;\n                    }\n                }\n                // Add delay between strategies\n                await delay(RATE_LIMIT_DELAY * 2);\n            } catch (error) {\n                console.error(`❌ Error in search strategy \"${strategy.query}\":`, error);\n                continue;\n            }\n        }\n        console.log(`\\n🎉 Comprehensive search completed!`);\n        console.log(`📊 Total URLs found: ${totalFound}`);\n        console.log(`📚 Unique pages processed: ${totalProcessed}`);\n        console.log(`🔗 Unique URLs in memory: ${processedUrls.size}`);\n        return allResults;\n    } catch (error) {\n        console.error(\"❌ Error in comprehensive site search:\", error);\n        throw error;\n    }\n}\nasync function updateKnowledgeBase() {\n    try {\n        await scrapeAvenData();\n        console.log(\"Knowledge base updated successfully\");\n    } catch (error) {\n        console.error(\"Error updating knowledge base:\", error);\n        throw error;\n    }\n}\nasync function updateKnowledgeBaseComprehensive() {\n    try {\n        // First run the targeted search\n        await scrapeAvenData();\n        // Then run the comprehensive search\n        await comprehensiveSiteSearch();\n        console.log(\"Comprehensive knowledge base update completed successfully\");\n    } catch (error) {\n        console.error(\"Error updating knowledge base comprehensively:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/scraper.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@pinecone-database","vendor-chunks/tr46","vendor-chunks/ajv","vendor-chunks/zod-to-json-schema","vendor-chunks/@sinclair","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/fast-uri","vendor-chunks/webidl-conversions","vendor-chunks/json-schema-traverse","vendor-chunks/fast-deep-equal","vendor-chunks/cross-fetch","vendor-chunks/exa-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();