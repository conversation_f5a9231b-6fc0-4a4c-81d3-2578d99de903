"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/guardrails/route";
exports.ids = ["app/api/guardrails/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fguardrails%2Froute&page=%2Fapi%2Fguardrails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fguardrails%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fguardrails%2Froute&page=%2Fapi%2Fguardrails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fguardrails%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_guardrails_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/guardrails/route.ts */ \"(rsc)/./src/app/api/guardrails/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/guardrails/route\",\n        pathname: \"/api/guardrails\",\n        filename: \"route\",\n        bundlePath: \"app/api/guardrails/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/api/guardrails/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_guardrails_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/guardrails/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fguardrails%2Froute&page=%2Fapi%2Fguardrails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fguardrails%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/guardrails/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/guardrails/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const { message } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required\"\n            }, {\n                status: 400\n            });\n        }\n        const check = await checkMessageGuardrails(message);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(check);\n    } catch (error) {\n        console.error(\"Error in guardrails API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function checkMessageGuardrails(message) {\n    const lowerMessage = message.toLowerCase();\n    // Check for personal data requests\n    const personalDataPatterns = [\n        /social security|ssn|account number|password|pin|routing number/,\n        /credit score|income|balance|payment history|transaction/,\n        /personal information|address|phone number|email/\n    ];\n    for (const pattern of personalDataPatterns){\n        if (pattern.test(lowerMessage)) {\n            return {\n                isBlocked: true,\n                reason: \"Request contains personal data\",\n                category: \"personal_data\"\n            };\n        }\n    }\n    // Check for legal advice requests\n    const legalAdvicePatterns = [\n        /legal advice|lawsuit|sue|attorney|lawyer|legal rights/,\n        /breach of contract|violation|illegal|fraud|scam/\n    ];\n    for (const pattern of legalAdvicePatterns){\n        if (pattern.test(lowerMessage)) {\n            return {\n                isBlocked: true,\n                reason: \"Request asks for legal advice\",\n                category: \"legal_advice\"\n            };\n        }\n    }\n    // Check for financial advice requests\n    const financialAdvicePatterns = [\n        /should i invest|investment advice|financial planning|tax advice/,\n        /bankruptcy|debt consolidation|credit repair|loan modification/\n    ];\n    for (const pattern of financialAdvicePatterns){\n        if (pattern.test(lowerMessage)) {\n            return {\n                isBlocked: true,\n                reason: \"Request asks for financial advice\",\n                category: \"financial_advice\"\n            };\n        }\n    }\n    // Check for toxicity\n    const toxicityPatterns = [\n        /hate|stupid|idiot|terrible|worst|scam|fraud/,\n        /\\b(damn|hell|shit)\\b/\n    ];\n    for (const pattern of toxicityPatterns){\n        if (pattern.test(lowerMessage)) {\n            return {\n                isBlocked: true,\n                reason: \"Message contains inappropriate language\",\n                category: \"toxicity\"\n            };\n        }\n    }\n    return {\n        isBlocked: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/guardrails/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fguardrails%2Froute&page=%2Fapi%2Fguardrails%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fguardrails%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();