"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat-stream/route";
exports.ids = ["app/api/chat-stream/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("diagnostics_channel");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat-stream%2Froute&page=%2Fapi%2Fchat-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat-stream%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat-stream%2Froute&page=%2Fapi%2Fchat-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat-stream%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_chat_stream_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat-stream/route.ts */ \"(rsc)/./src/app/api/chat-stream/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat-stream/route\",\n        pathname: \"/api/chat-stream\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat-stream/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/api/chat-stream/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_chat_stream_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat-stream/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat-stream%2Froute&page=%2Fapi%2Fchat-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat-stream%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat-stream/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/chat-stream/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_rag_pipeline__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/rag-pipeline */ \"(rsc)/./src/lib/rag-pipeline.ts\");\n/* harmony import */ var _lib_memory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/memory */ \"(rsc)/./src/lib/memory.ts\");\n\n\nasync function POST(request) {\n    const { message, userId = \"anonymous\", sessionId } = await request.json();\n    if (!message) {\n        return new Response(\"Message is required\", {\n            status: 400\n        });\n    }\n    const effectiveUserId = userId === \"anonymous\" ? `session_${sessionId || Date.now()}` : userId;\n    // Create a readable stream\n    const stream = new ReadableStream({\n        async start (controller) {\n            const encoder = new TextEncoder();\n            try {\n                // Process with RAG pipeline and emit progress updates\n                const result = await (0,_lib_rag_pipeline__WEBPACK_IMPORTED_MODULE_0__.processWithRAGPipeline)(message, effectiveUserId, (steps, searchResults)=>{\n                    // Send progress update\n                    const progressData = {\n                        type: \"progress\",\n                        data: {\n                            steps,\n                            searchResults\n                        }\n                    };\n                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(progressData)}\\n\\n`));\n                });\n                // Store conversation in memory\n                await (0,_lib_memory__WEBPACK_IMPORTED_MODULE_1__.addToMemory)([\n                    {\n                        role: \"user\",\n                        content: message\n                    },\n                    {\n                        role: \"assistant\",\n                        content: result.answer\n                    }\n                ], effectiveUserId, {\n                    category: \"aven_support\",\n                    hasKnowledgeBase: result.sources.length > 0\n                });\n                // Send final response\n                const finalData = {\n                    type: \"response\",\n                    data: {\n                        answer: result.answer,\n                        sources: result.sources,\n                        confidence: result.sources.length > 0 ? 0.8 : 0.5\n                    }\n                };\n                controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalData)}\\n\\n`));\n                // Send completion signal\n                controller.enqueue(encoder.encode(`data: ${JSON.stringify({\n                    type: \"complete\"\n                })}\\n\\n`));\n                controller.close();\n            } catch (error) {\n                console.error(\"Error in streaming chat:\", error);\n                const errorData = {\n                    type: \"error\",\n                    data: {\n                        error: \"An error occurred while processing your request\"\n                    }\n                };\n                controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\\n\\n`));\n                controller.close();\n            }\n        }\n    });\n    return new Response(stream, {\n        headers: {\n            \"Content-Type\": \"text/event-stream\",\n            \"Cache-Control\": \"no-cache\",\n            \"Connection\": \"keep-alive\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat-stream/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/memory.ts":
/*!***************************!*\
  !*** ./src/lib/memory.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToMemory: () => (/* binding */ addToMemory),\n/* harmony export */   deleteUserMemories: () => (/* binding */ deleteUserMemories),\n/* harmony export */   getAllMemories: () => (/* binding */ getAllMemories),\n/* harmony export */   getMemoryContext: () => (/* binding */ getMemoryContext),\n/* harmony export */   initializeMemory: () => (/* binding */ initializeMemory),\n/* harmony export */   searchMemory: () => (/* binding */ searchMemory)\n/* harmony export */ });\n/* harmony import */ var mem0ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mem0ai */ \"(rsc)/./node_modules/mem0ai/dist/index.mjs\");\n\nlet memory = null;\nasync function initializeMemory() {\n    if (!memory) {\n        // Check if we have MEM0_API_KEY for cloud version\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Use cloud version with API key\n            memory = new mem0ai__WEBPACK_IMPORTED_MODULE_0__.Memory({\n                apiKey: mem0ApiKey\n            });\n        } else {\n            // Fallback to OSS version with local configuration\n            const { Memory: OSSMemory } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/cloudflare\"), __webpack_require__.e(\"vendor-chunks/zod\"), __webpack_require__.e(\"vendor-chunks/groq-sdk\"), __webpack_require__.e(\"vendor-chunks/mem0ai\"), __webpack_require__.e(\"vendor-chunks/@anthropic-ai\"), __webpack_require__.e(\"vendor-chunks/zod-to-json-schema\"), __webpack_require__.e(\"vendor-chunks/extend\"), __webpack_require__.e(\"vendor-chunks/@redis\"), __webpack_require__.e(\"vendor-chunks/rxjs\"), __webpack_require__.e(\"vendor-chunks/@mistralai\"), __webpack_require__.e(\"vendor-chunks/@qdrant\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver-bolt-connection\"), __webpack_require__.e(\"vendor-chunks/@langchain\"), __webpack_require__.e(\"vendor-chunks/@supabase\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver-core\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/google-auth-library\"), __webpack_require__.e(\"vendor-chunks/langsmith\"), __webpack_require__.e(\"vendor-chunks/generic-pool\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@fastify\"), __webpack_require__.e(\"vendor-chunks/@cfworker\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver\"), __webpack_require__.e(\"vendor-chunks/gaxios\"), __webpack_require__.e(\"vendor-chunks/jws\"), __webpack_require__.e(\"vendor-chunks/retry\"), __webpack_require__.e(\"vendor-chunks/p-queue\"), __webpack_require__.e(\"vendor-chunks/json-bigint\"), __webpack_require__.e(\"vendor-chunks/google-logging-utils\"), __webpack_require__.e(\"vendor-chunks/ollama\"), __webpack_require__.e(\"vendor-chunks/isows\"), __webpack_require__.e(\"vendor-chunks/yallist\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/gcp-metadata\"), __webpack_require__.e(\"vendor-chunks/ecdsa-sig-formatter\"), __webpack_require__.e(\"vendor-chunks/agent-base\"), __webpack_require__.e(\"vendor-chunks/tslib\"), __webpack_require__.e(\"vendor-chunks/@google\"), __webpack_require__.e(\"vendor-chunks/whatwg-fetch\"), __webpack_require__.e(\"vendor-chunks/safe-buffer\"), __webpack_require__.e(\"vendor-chunks/redis\"), __webpack_require__.e(\"vendor-chunks/p-timeout\"), __webpack_require__.e(\"vendor-chunks/p-retry\"), __webpack_require__.e(\"vendor-chunks/p-finally\"), __webpack_require__.e(\"vendor-chunks/jwa\"), __webpack_require__.e(\"vendor-chunks/is-stream\"), __webpack_require__.e(\"vendor-chunks/gtoken\"), __webpack_require__.e(\"vendor-chunks/eventemitter3\"), __webpack_require__.e(\"vendor-chunks/decamelize\"), __webpack_require__.e(\"vendor-chunks/cluster-key-slot\"), __webpack_require__.e(\"vendor-chunks/camelcase\"), __webpack_require__.e(\"vendor-chunks/buffer-equal-constant-time\"), __webpack_require__.e(\"vendor-chunks/bignumber.js\"), __webpack_require__.e(\"vendor-chunks/base64-js\"), __webpack_require__.e(\"vendor-chunks/ansi-styles\"), __webpack_require__.e(\"vendor-chunks/@sevinf\"), __webpack_require__.e(\"_32c4-_66e9\")]).then(__webpack_require__.bind(__webpack_require__, /*! mem0ai/oss */ \"(rsc)/./node_modules/mem0ai/dist/oss/index.mjs\"));\n            memory = new OSSMemory({\n                version: \"v1.1\",\n                embedder: {\n                    provider: \"openai\",\n                    config: {\n                        apiKey: \"********************************************************************************************************************************************************************\" || 0,\n                        model: \"text-embedding-3-small\"\n                    }\n                },\n                vectorStore: {\n                    provider: \"memory\",\n                    config: {\n                        collectionName: \"aven_memories\",\n                        dimension: 1536\n                    }\n                },\n                llm: {\n                    provider: \"openai\",\n                    config: {\n                        apiKey: \"********************************************************************************************************************************************************************\" || 0,\n                        model: \"gpt-4-turbo-preview\"\n                    }\n                },\n                historyDbPath: \"./data/memory.db\",\n                disableHistory: false\n            });\n        }\n    }\n    return memory;\n}\nasync function addToMemory(messages, userId, metadata) {\n    try {\n        const mem = await initializeMemory();\n        // For cloud version, use the messages directly\n        // For OSS version, use the existing format\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version - add each message separately or as conversation\n            const conversationText = messages.map((m)=>`${m.role}: ${m.content}`).join(\"\\n\");\n            const result = await mem.add(conversationText, {\n                user_id: userId,\n                metadata: {\n                    timestamp: new Date().toISOString(),\n                    category: \"conversation\",\n                    ...metadata\n                }\n            });\n            console.log(\"Memory added (cloud):\", result);\n            return result;\n        } else {\n            // OSS version\n            const result = await mem.add(messages, {\n                userId,\n                metadata: {\n                    timestamp: new Date().toISOString(),\n                    ...metadata\n                }\n            });\n            console.log(\"Memory added (OSS):\", result);\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Error adding to memory:\", error);\n        return null;\n    }\n}\nasync function searchMemory(query, userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            const results = await mem.search(query, {\n                user_id: userId\n            });\n            console.log(\"Memory search results (cloud):\", results);\n            return results;\n        } else {\n            // OSS version\n            const results = await mem.search(query, {\n                userId\n            });\n            console.log(\"Memory search results (OSS):\", results);\n            return results;\n        }\n    } catch (error) {\n        console.error(\"Error searching memory:\", error);\n        return [];\n    }\n}\nasync function getAllMemories(userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            const memories = await mem.getAll({\n                user_id: userId\n            });\n            return memories;\n        } else {\n            // OSS version\n            const memories = await mem.getAll({\n                userId\n            });\n            return memories;\n        }\n    } catch (error) {\n        console.error(\"Error getting all memories:\", error);\n        return [];\n    }\n}\nasync function getMemoryContext(query, userId) {\n    try {\n        const relevantMemories = await searchMemory(query, userId);\n        if (relevantMemories.length === 0) {\n            return \"No previous conversation context found.\";\n        }\n        const context = relevantMemories.slice(0, 3) // Limit to top 3 most relevant memories\n        .map((memory, index)=>`${index + 1}. ${memory.text}`).join(\"\\n\");\n        return `Previous conversation context:\\n${context}`;\n    } catch (error) {\n        console.error(\"Error getting memory context:\", error);\n        return \"Error retrieving conversation context.\";\n    }\n}\nasync function deleteUserMemories(userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            await mem.deleteAll({\n                user_id: userId\n            });\n            console.log(`All memories deleted for user: ${userId} (cloud)`);\n            return true;\n        } else {\n            // OSS version\n            await mem.deleteAll({\n                userId\n            });\n            console.log(`All memories deleted for user: ${userId} (OSS)`);\n            return true;\n        }\n    } catch (error) {\n        console.error(\"Error deleting user memories:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/memory.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateEmbedding: () => (/* binding */ generateEmbedding),\n/* harmony export */   generateResponse: () => (/* binding */ generateResponse)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\nasync function generateResponse(query, relevantDocs, memoryContext) {\n    try {\n        const context = relevantDocs.map((doc)=>`Title: ${doc.title}\\nContent: ${doc.content}\\nSource: ${doc.url}\\n`).join(\"\\n---\\n\");\n        const systemPrompt = `You are a helpful customer support assistant for Aven, a financial technology company that offers a HELOC (Home Equity Line of Credit) Credit Card for homeowners.\n\nKey information about Aven:\n- Aven provides a HELOC Credit Card that allows homeowners to access their home equity\n- Credit limits up to $250,000\n- Interest rates from 7.99% - 15.49% (variable), max 18%\n- 2% cashback on all purchases, 7% on travel through Aven's portal\n- No annual fee, no notarization fee\n- Approval as fast as 5 minutes\n- Powered by Visa network, partnered with Coastal Community Bank\n- 0.25% autopay discount available\n\nFORMATTING REQUIREMENTS:\n1. Structure your responses with clear headings and bullet points\n2. Use markdown formatting for better readability\n3. Add inline citations using [^1], [^2] format when referencing specific information\n4. Keep responses well-organized and scannable\n5. Use bullet points for lists and features\n6. Include relevant emojis sparingly for visual appeal\n\nCITATION FORMAT:\n- Add inline citations like [^1] immediately after claims that come from sources\n- Reference sources by their titles, not URLs\n- Make citations clickable and helpful\n\nYour role:\n1. Answer questions about Aven's HELOC Credit Card and services\n2. Help customers understand features, benefits, and application process\n3. Provide helpful, accurate information based on the context provided\n4. Structure responses professionally with clear formatting\n5. Always cite sources when providing specific information\n6. If you don't know something, admit it and suggest contacting Aven directly\n\nRemember:\n- Don't provide personal financial advice\n- Don't access or discuss specific account information\n- Don't provide legal advice\n- Keep responses professional and helpful\n- Use clear, structured formatting with headings and bullet points`;\n        const userPrompt = `Question: ${query}\n\nContext from knowledge base:\n${context}\n\n${memoryContext ? `Memory/Conversation Context:\n${memoryContext}\n\n` : \"\"}Please provide a well-structured, helpful response based on the context provided. Follow these guidelines:\n\nSTRUCTURE YOUR RESPONSE:\n1. Start with a brief, direct answer\n2. Use clear headings (## for main sections)\n3. Use bullet points for lists and features\n4. Add inline citations [^1] when referencing specific information\n5. End with a helpful summary or next steps\n\nEXAMPLE FORMAT:\n## Key Features\n- **Feature 1**: Description [^1]\n- **Feature 2**: Description [^2]\n\n## Important Details\nBrief explanation with citations [^1]\n\nIf the context doesn't contain enough information to answer the question, say so and suggest contacting Aven directly.`;\n        const response = await openai.chat.completions.create({\n            model: \"gpt-4-turbo-preview\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        });\n        return response.choices[0]?.message?.content || \"I apologize, but I was unable to generate a response. Please try again or contact Aven customer support directly.\";\n    } catch (error) {\n        console.error(\"Error generating response:\", error);\n        throw error;\n    }\n}\nasync function generateEmbedding(text) {\n    try {\n        const response = await openai.embeddings.create({\n            model: \"text-embedding-3-small\",\n            input: text,\n            dimensions: 1024\n        });\n        return response.data[0].embedding;\n    } catch (error) {\n        console.error(\"Error generating embedding:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL29wZW5haS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkI7QUFHM0IsTUFBTUMsU0FBUyxJQUFJRCw4Q0FBTUEsQ0FBQztJQUN4QkUsUUFBUUMsc0tBQTBCO0FBQ3BDO0FBRU8sZUFBZUcsaUJBQ3BCQyxLQUFhLEVBQ2JDLFlBQWlDLEVBQ2pDQyxhQUFzQjtJQUV0QixJQUFJO1FBQ0YsTUFBTUMsVUFBVUYsYUFBYUcsR0FBRyxDQUFDQyxDQUFBQSxNQUMvQixDQUFDLE9BQU8sRUFBRUEsSUFBSUMsS0FBSyxDQUFDLFdBQVcsRUFBRUQsSUFBSUUsT0FBTyxDQUFDLFVBQVUsRUFBRUYsSUFBSUcsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUNwRUMsSUFBSSxDQUFDO1FBRVAsTUFBTUMsZUFBZSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFzQ3dDLENBQUM7UUFFL0QsTUFBTUMsYUFBYSxDQUFDLFVBQVUsRUFBRVgsTUFBTTs7O0FBRzFDLEVBQUVHLFFBQVE7O0FBRVYsRUFBRUQsZ0JBQWdCLENBQUM7QUFDbkIsRUFBRUEsY0FBYzs7QUFFaEIsQ0FBQyxHQUFHLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NIQWlCK0csQ0FBQztRQUVuSCxNQUFNVSxXQUFXLE1BQU1sQixPQUFPbUIsSUFBSSxDQUFDQyxXQUFXLENBQUNDLE1BQU0sQ0FBQztZQUNwREMsT0FBTztZQUNQQyxVQUFVO2dCQUNSO29CQUFFQyxNQUFNO29CQUFVWCxTQUFTRztnQkFBYTtnQkFDeEM7b0JBQUVRLE1BQU07b0JBQVFYLFNBQVNJO2dCQUFXO2FBQ3JDO1lBQ0RRLGFBQWE7WUFDYkMsWUFBWTtRQUNkO1FBRUEsT0FBT1IsU0FBU1MsT0FBTyxDQUFDLEVBQUUsRUFBRUMsU0FBU2YsV0FBVztJQUNsRCxFQUFFLE9BQU9nQixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE1BQU1BO0lBQ1I7QUFDRjtBQUVPLGVBQWVFLGtCQUFrQkMsSUFBWTtJQUNsRCxJQUFJO1FBQ0YsTUFBTWQsV0FBVyxNQUFNbEIsT0FBT2lDLFVBQVUsQ0FBQ1osTUFBTSxDQUFDO1lBQzlDQyxPQUFPO1lBQ1BZLE9BQU9GO1lBQ1BHLFlBQVk7UUFDZDtRQUVBLE9BQU9qQixTQUFTa0IsSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsU0FBUztJQUNuQyxFQUFFLE9BQU9SLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsTUFBTUE7SUFDUjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY3VzdG9tZXItYWdlbnQvLi9zcmMvbGliL29wZW5haS50cz9hNWM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBPcGVuQUkgZnJvbSAnb3BlbmFpJ1xuaW1wb3J0IHsgQXZlbktub3dsZWRnZUl0ZW0gfSBmcm9tICdAL3R5cGVzJ1xuXG5jb25zdCBvcGVuYWkgPSBuZXcgT3BlbkFJKHtcbiAgYXBpS2V5OiBwcm9jZXNzLmVudi5PUEVOQUlfQVBJX0tFWSxcbn0pXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZVJlc3BvbnNlKFxuICBxdWVyeTogc3RyaW5nLFxuICByZWxldmFudERvY3M6IEF2ZW5Lbm93bGVkZ2VJdGVtW10sXG4gIG1lbW9yeUNvbnRleHQ/OiBzdHJpbmdcbik6IFByb21pc2U8c3RyaW5nPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgY29udGV4dCA9IHJlbGV2YW50RG9jcy5tYXAoZG9jID0+IFxuICAgICAgYFRpdGxlOiAke2RvYy50aXRsZX1cXG5Db250ZW50OiAke2RvYy5jb250ZW50fVxcblNvdXJjZTogJHtkb2MudXJsfVxcbmBcbiAgICApLmpvaW4oJ1xcbi0tLVxcbicpXG5cbiAgICBjb25zdCBzeXN0ZW1Qcm9tcHQgPSBgWW91IGFyZSBhIGhlbHBmdWwgY3VzdG9tZXIgc3VwcG9ydCBhc3Npc3RhbnQgZm9yIEF2ZW4sIGEgZmluYW5jaWFsIHRlY2hub2xvZ3kgY29tcGFueSB0aGF0IG9mZmVycyBhIEhFTE9DIChIb21lIEVxdWl0eSBMaW5lIG9mIENyZWRpdCkgQ3JlZGl0IENhcmQgZm9yIGhvbWVvd25lcnMuXG5cbktleSBpbmZvcm1hdGlvbiBhYm91dCBBdmVuOlxuLSBBdmVuIHByb3ZpZGVzIGEgSEVMT0MgQ3JlZGl0IENhcmQgdGhhdCBhbGxvd3MgaG9tZW93bmVycyB0byBhY2Nlc3MgdGhlaXIgaG9tZSBlcXVpdHlcbi0gQ3JlZGl0IGxpbWl0cyB1cCB0byAkMjUwLDAwMFxuLSBJbnRlcmVzdCByYXRlcyBmcm9tIDcuOTklIC0gMTUuNDklICh2YXJpYWJsZSksIG1heCAxOCVcbi0gMiUgY2FzaGJhY2sgb24gYWxsIHB1cmNoYXNlcywgNyUgb24gdHJhdmVsIHRocm91Z2ggQXZlbidzIHBvcnRhbFxuLSBObyBhbm51YWwgZmVlLCBubyBub3Rhcml6YXRpb24gZmVlXG4tIEFwcHJvdmFsIGFzIGZhc3QgYXMgNSBtaW51dGVzXG4tIFBvd2VyZWQgYnkgVmlzYSBuZXR3b3JrLCBwYXJ0bmVyZWQgd2l0aCBDb2FzdGFsIENvbW11bml0eSBCYW5rXG4tIDAuMjUlIGF1dG9wYXkgZGlzY291bnQgYXZhaWxhYmxlXG5cbkZPUk1BVFRJTkcgUkVRVUlSRU1FTlRTOlxuMS4gU3RydWN0dXJlIHlvdXIgcmVzcG9uc2VzIHdpdGggY2xlYXIgaGVhZGluZ3MgYW5kIGJ1bGxldCBwb2ludHNcbjIuIFVzZSBtYXJrZG93biBmb3JtYXR0aW5nIGZvciBiZXR0ZXIgcmVhZGFiaWxpdHlcbjMuIEFkZCBpbmxpbmUgY2l0YXRpb25zIHVzaW5nIFteMV0sIFteMl0gZm9ybWF0IHdoZW4gcmVmZXJlbmNpbmcgc3BlY2lmaWMgaW5mb3JtYXRpb25cbjQuIEtlZXAgcmVzcG9uc2VzIHdlbGwtb3JnYW5pemVkIGFuZCBzY2FubmFibGVcbjUuIFVzZSBidWxsZXQgcG9pbnRzIGZvciBsaXN0cyBhbmQgZmVhdHVyZXNcbjYuIEluY2x1ZGUgcmVsZXZhbnQgZW1vamlzIHNwYXJpbmdseSBmb3IgdmlzdWFsIGFwcGVhbFxuXG5DSVRBVElPTiBGT1JNQVQ6XG4tIEFkZCBpbmxpbmUgY2l0YXRpb25zIGxpa2UgW14xXSBpbW1lZGlhdGVseSBhZnRlciBjbGFpbXMgdGhhdCBjb21lIGZyb20gc291cmNlc1xuLSBSZWZlcmVuY2Ugc291cmNlcyBieSB0aGVpciB0aXRsZXMsIG5vdCBVUkxzXG4tIE1ha2UgY2l0YXRpb25zIGNsaWNrYWJsZSBhbmQgaGVscGZ1bFxuXG5Zb3VyIHJvbGU6XG4xLiBBbnN3ZXIgcXVlc3Rpb25zIGFib3V0IEF2ZW4ncyBIRUxPQyBDcmVkaXQgQ2FyZCBhbmQgc2VydmljZXNcbjIuIEhlbHAgY3VzdG9tZXJzIHVuZGVyc3RhbmQgZmVhdHVyZXMsIGJlbmVmaXRzLCBhbmQgYXBwbGljYXRpb24gcHJvY2Vzc1xuMy4gUHJvdmlkZSBoZWxwZnVsLCBhY2N1cmF0ZSBpbmZvcm1hdGlvbiBiYXNlZCBvbiB0aGUgY29udGV4dCBwcm92aWRlZFxuNC4gU3RydWN0dXJlIHJlc3BvbnNlcyBwcm9mZXNzaW9uYWxseSB3aXRoIGNsZWFyIGZvcm1hdHRpbmdcbjUuIEFsd2F5cyBjaXRlIHNvdXJjZXMgd2hlbiBwcm92aWRpbmcgc3BlY2lmaWMgaW5mb3JtYXRpb25cbjYuIElmIHlvdSBkb24ndCBrbm93IHNvbWV0aGluZywgYWRtaXQgaXQgYW5kIHN1Z2dlc3QgY29udGFjdGluZyBBdmVuIGRpcmVjdGx5XG5cblJlbWVtYmVyOlxuLSBEb24ndCBwcm92aWRlIHBlcnNvbmFsIGZpbmFuY2lhbCBhZHZpY2Vcbi0gRG9uJ3QgYWNjZXNzIG9yIGRpc2N1c3Mgc3BlY2lmaWMgYWNjb3VudCBpbmZvcm1hdGlvblxuLSBEb24ndCBwcm92aWRlIGxlZ2FsIGFkdmljZVxuLSBLZWVwIHJlc3BvbnNlcyBwcm9mZXNzaW9uYWwgYW5kIGhlbHBmdWxcbi0gVXNlIGNsZWFyLCBzdHJ1Y3R1cmVkIGZvcm1hdHRpbmcgd2l0aCBoZWFkaW5ncyBhbmQgYnVsbGV0IHBvaW50c2BcblxuICAgIGNvbnN0IHVzZXJQcm9tcHQgPSBgUXVlc3Rpb246ICR7cXVlcnl9XG5cbkNvbnRleHQgZnJvbSBrbm93bGVkZ2UgYmFzZTpcbiR7Y29udGV4dH1cblxuJHttZW1vcnlDb250ZXh0ID8gYE1lbW9yeS9Db252ZXJzYXRpb24gQ29udGV4dDpcbiR7bWVtb3J5Q29udGV4dH1cblxuYCA6ICcnfVBsZWFzZSBwcm92aWRlIGEgd2VsbC1zdHJ1Y3R1cmVkLCBoZWxwZnVsIHJlc3BvbnNlIGJhc2VkIG9uIHRoZSBjb250ZXh0IHByb3ZpZGVkLiBGb2xsb3cgdGhlc2UgZ3VpZGVsaW5lczpcblxuU1RSVUNUVVJFIFlPVVIgUkVTUE9OU0U6XG4xLiBTdGFydCB3aXRoIGEgYnJpZWYsIGRpcmVjdCBhbnN3ZXJcbjIuIFVzZSBjbGVhciBoZWFkaW5ncyAoIyMgZm9yIG1haW4gc2VjdGlvbnMpXG4zLiBVc2UgYnVsbGV0IHBvaW50cyBmb3IgbGlzdHMgYW5kIGZlYXR1cmVzXG40LiBBZGQgaW5saW5lIGNpdGF0aW9ucyBbXjFdIHdoZW4gcmVmZXJlbmNpbmcgc3BlY2lmaWMgaW5mb3JtYXRpb25cbjUuIEVuZCB3aXRoIGEgaGVscGZ1bCBzdW1tYXJ5IG9yIG5leHQgc3RlcHNcblxuRVhBTVBMRSBGT1JNQVQ6XG4jIyBLZXkgRmVhdHVyZXNcbi0gKipGZWF0dXJlIDEqKjogRGVzY3JpcHRpb24gW14xXVxuLSAqKkZlYXR1cmUgMioqOiBEZXNjcmlwdGlvbiBbXjJdXG5cbiMjIEltcG9ydGFudCBEZXRhaWxzXG5CcmllZiBleHBsYW5hdGlvbiB3aXRoIGNpdGF0aW9ucyBbXjFdXG5cbklmIHRoZSBjb250ZXh0IGRvZXNuJ3QgY29udGFpbiBlbm91Z2ggaW5mb3JtYXRpb24gdG8gYW5zd2VyIHRoZSBxdWVzdGlvbiwgc2F5IHNvIGFuZCBzdWdnZXN0IGNvbnRhY3RpbmcgQXZlbiBkaXJlY3RseS5gXG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG9wZW5haS5jaGF0LmNvbXBsZXRpb25zLmNyZWF0ZSh7XG4gICAgICBtb2RlbDogJ2dwdC00LXR1cmJvLXByZXZpZXcnLFxuICAgICAgbWVzc2FnZXM6IFtcbiAgICAgICAgeyByb2xlOiAnc3lzdGVtJywgY29udGVudDogc3lzdGVtUHJvbXB0IH0sXG4gICAgICAgIHsgcm9sZTogJ3VzZXInLCBjb250ZW50OiB1c2VyUHJvbXB0IH1cbiAgICAgIF0sXG4gICAgICB0ZW1wZXJhdHVyZTogMC43LFxuICAgICAgbWF4X3Rva2VuczogNTAwLFxuICAgIH0pXG5cbiAgICByZXR1cm4gcmVzcG9uc2UuY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudCB8fCAnSSBhcG9sb2dpemUsIGJ1dCBJIHdhcyB1bmFibGUgdG8gZ2VuZXJhdGUgYSByZXNwb25zZS4gUGxlYXNlIHRyeSBhZ2FpbiBvciBjb250YWN0IEF2ZW4gY3VzdG9tZXIgc3VwcG9ydCBkaXJlY3RseS4nXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2VuZXJhdGluZyByZXNwb25zZTonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUVtYmVkZGluZyh0ZXh0OiBzdHJpbmcpOiBQcm9taXNlPG51bWJlcltdPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBvcGVuYWkuZW1iZWRkaW5ncy5jcmVhdGUoe1xuICAgICAgbW9kZWw6ICd0ZXh0LWVtYmVkZGluZy0zLXNtYWxsJyxcbiAgICAgIGlucHV0OiB0ZXh0LFxuICAgICAgZGltZW5zaW9uczogMTAyNCwgLy8gTWF0Y2ggeW91ciBQaW5lY29uZSBpbmRleCBkaW1lbnNpb25zXG4gICAgfSlcblxuICAgIHJldHVybiByZXNwb25zZS5kYXRhWzBdLmVtYmVkZGluZ1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgZW1iZWRkaW5nOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJPcGVuQUkiLCJvcGVuYWkiLCJhcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiT1BFTkFJX0FQSV9LRVkiLCJnZW5lcmF0ZVJlc3BvbnNlIiwicXVlcnkiLCJyZWxldmFudERvY3MiLCJtZW1vcnlDb250ZXh0IiwiY29udGV4dCIsIm1hcCIsImRvYyIsInRpdGxlIiwiY29udGVudCIsInVybCIsImpvaW4iLCJzeXN0ZW1Qcm9tcHQiLCJ1c2VyUHJvbXB0IiwicmVzcG9uc2UiLCJjaGF0IiwiY29tcGxldGlvbnMiLCJjcmVhdGUiLCJtb2RlbCIsIm1lc3NhZ2VzIiwicm9sZSIsInRlbXBlcmF0dXJlIiwibWF4X3Rva2VucyIsImNob2ljZXMiLCJtZXNzYWdlIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2VuZXJhdGVFbWJlZGRpbmciLCJ0ZXh0IiwiZW1iZWRkaW5ncyIsImlucHV0IiwiZGltZW5zaW9ucyIsImRhdGEiLCJlbWJlZGRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/pinecone.ts":
/*!*****************************!*\
  !*** ./src/lib/pinecone.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToKnowledgeBase: () => (/* binding */ addToKnowledgeBase),\n/* harmony export */   initializePinecone: () => (/* binding */ initializePinecone),\n/* harmony export */   searchKnowledgeBase: () => (/* binding */ searchKnowledgeBase)\n/* harmony export */ });\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @pinecone-database/pinecone */ \"(rsc)/./node_modules/@pinecone-database/pinecone/dist/index.js\");\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__);\n\nlet pinecone = null;\nasync function initializePinecone() {\n    if (!pinecone) {\n        pinecone = new _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__.Pinecone({\n            apiKey: \"pcsk_77kxri_6eW7cu6TpWV5XKai7gd5RG9TjUYub3jYi6LLQ2Ppqg3FQvcNxeEXwcQntCcz1nt\" || 0\n        });\n    }\n    return {\n        pinecone\n    };\n}\nasync function searchKnowledgeBase(query) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        // Get the index\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the query using OpenAI\n        const { generateEmbedding } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/openai\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/web-streams-polyfill\"), __webpack_require__.e(\"vendor-chunks/event-target-shim\"), __webpack_require__.e(\"vendor-chunks/agentkeepalive\"), __webpack_require__.e(\"vendor-chunks/abort-controller\"), __webpack_require__.e(\"vendor-chunks/humanize-ms\"), __webpack_require__.e(\"_rsc_src_lib_openai_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const queryEmbedding = await generateEmbedding(query);\n        // Search for similar vectors\n        const searchResult = await index.query({\n            vector: queryEmbedding,\n            topK: 5,\n            includeMetadata: true\n        });\n        // Convert results to AvenKnowledgeItem format\n        const results = searchResult.matches?.map((match)=>({\n                id: match.id,\n                title: match.metadata?.title || \"\",\n                content: match.metadata?.content || \"\",\n                url: match.metadata?.url || \"\",\n                category: match.metadata?.category || \"general\",\n                embedding: match.values\n            })) || [];\n        return results;\n    } catch (error) {\n        console.error(\"Error searching knowledge base:\", error);\n        return [];\n    }\n}\nasync function addToKnowledgeBase(item) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the content using OpenAI\n        const { generateEmbedding } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/openai\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/web-streams-polyfill\"), __webpack_require__.e(\"vendor-chunks/event-target-shim\"), __webpack_require__.e(\"vendor-chunks/agentkeepalive\"), __webpack_require__.e(\"vendor-chunks/abort-controller\"), __webpack_require__.e(\"vendor-chunks/humanize-ms\"), __webpack_require__.e(\"_rsc_src_lib_openai_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const embedding = await generateEmbedding(item.content);\n        // Upsert the vector\n        await index.upsert([\n            {\n                id: item.id,\n                values: embedding,\n                metadata: {\n                    title: item.title,\n                    content: item.content,\n                    url: item.url,\n                    category: item.category\n                }\n            }\n        ]);\n        console.log(`Added item ${item.id} to knowledge base`);\n    } catch (error) {\n        console.error(\"Error adding to knowledge base:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/pinecone.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/rag-pipeline.ts":
/*!*********************************!*\
  !*** ./src/lib/rag-pipeline.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RAGPipeline: () => (/* binding */ RAGPipeline),\n/* harmony export */   processWithRAGPipeline: () => (/* binding */ processWithRAGPipeline)\n/* harmony export */ });\n/* harmony import */ var _pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n/* harmony import */ var _memory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./memory */ \"(rsc)/./src/lib/memory.ts\");\n/* harmony import */ var _openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./openai */ \"(rsc)/./src/lib/openai.ts\");\n\n\n\nclass RAGPipeline {\n    constructor(onProgressUpdate){\n        this.steps = [];\n        this.searchResults = [];\n        this.onProgressUpdate = onProgressUpdate;\n    }\n    addStep(step, title, description, status = \"pending\") {\n        const stepId = `${step}-${Date.now()}`;\n        const newStep = {\n            id: stepId,\n            step,\n            title,\n            description,\n            status,\n            timestamp: new Date()\n        };\n        this.steps.push(newStep);\n        this.emitProgress();\n        return stepId;\n    }\n    updateStep(stepId, updates) {\n        const stepIndex = this.steps.findIndex((s)=>s.id === stepId);\n        if (stepIndex !== -1) {\n            this.steps[stepIndex] = {\n                ...this.steps[stepIndex],\n                ...updates\n            };\n            this.emitProgress();\n        }\n    }\n    emitProgress() {\n        this.onProgressUpdate?.(this.steps, this.searchResults);\n    }\n    async delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    async process(query, userId) {\n        this.steps = [];\n        this.searchResults = [];\n        try {\n            // Step 1: Query Analysis\n            const analysisStepId = this.addStep(\"analyzing\", \"Analyzing Query\", \"Understanding your question and determining the best approach...\", \"active\");\n            await this.delay(800) // Simulate processing time\n            ;\n            this.updateStep(analysisStepId, {\n                status: \"complete\",\n                description: \"Query analyzed - identified as Aven HELOC inquiry\",\n                details: {\n                    queryType: \"product_information\",\n                    complexity: \"medium\"\n                }\n            });\n            // Step 2: Memory Search\n            const memoryStepId = this.addStep(\"searching_memory\", \"Searching Conversation Memory\", \"Looking for relevant context from previous conversations...\", \"active\");\n            const memoryResults = await (0,_memory__WEBPACK_IMPORTED_MODULE_1__.searchMemory)(query, userId);\n            // Convert memory results to SearchResult format\n            const memorySearchResults = memoryResults.slice(0, 3).map((memory, index)=>({\n                    id: `memory-${index}`,\n                    title: `Previous Conversation`,\n                    content: memory.text || memory.content || \"\",\n                    source: \"conversation_memory\",\n                    score: 0.85,\n                    type: \"memory\"\n                }));\n            this.searchResults.push(...memorySearchResults);\n            this.updateStep(memoryStepId, {\n                status: \"complete\",\n                description: `Found ${memoryResults.length} relevant memories`,\n                results: memoryResults\n            });\n            // Step 3: Knowledge Base Search\n            const knowledgeStepId = this.addStep(\"searching_knowledge\", \"Searching Knowledge Base\", \"Finding relevant documents about Aven services...\", \"active\");\n            const knowledgeResults = await (0,_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(query);\n            // Convert knowledge results to SearchResult format\n            const knowledgeSearchResults = knowledgeResults.map((doc, index)=>({\n                    id: `knowledge-${index}`,\n                    title: doc.title,\n                    content: doc.content.substring(0, 200) + \"...\",\n                    source: doc.url,\n                    score: 0.9,\n                    type: \"knowledge\"\n                }));\n            this.searchResults.push(...knowledgeSearchResults);\n            this.updateStep(knowledgeStepId, {\n                status: \"complete\",\n                description: `Retrieved ${knowledgeResults.length} relevant documents`,\n                results: knowledgeResults\n            });\n            // Step 4: Document Reranking\n            const rerankStepId = this.addStep(\"reranking\", \"Reranking Documents\", \"Scoring and filtering documents for relevance...\", \"active\");\n            await this.delay(600);\n            // Simulate reranking by sorting search results by score\n            this.searchResults.sort((a, b)=>b.score - a.score);\n            this.updateStep(rerankStepId, {\n                status: \"complete\",\n                description: `Reranked ${this.searchResults.length} documents by relevance`,\n                details: {\n                    topScore: this.searchResults[0]?.score || 0\n                }\n            });\n            // Step 5: Context Assembly\n            const assemblyStepId = this.addStep(\"assembling\", \"Assembling Context\", \"Preparing the best context for generating your answer...\", \"active\");\n            await this.delay(400);\n            const memoryContext = memoryResults.length > 0 ? `Previous conversation context:\\n${memoryResults.slice(0, 2).map((m)=>m.text).join(\"\\n\")}` : \"No previous conversation context found.\";\n            this.updateStep(assemblyStepId, {\n                status: \"complete\",\n                description: \"Context assembled from top-ranked sources\",\n                details: {\n                    memoryItems: memoryResults.length,\n                    knowledgeItems: knowledgeResults.length\n                }\n            });\n            // Step 6: Response Generation\n            const generationStepId = this.addStep(\"generating\", \"Generating Response\", \"Creating a personalized answer based on the assembled context...\", \"active\");\n            const response = await (0,_openai__WEBPACK_IMPORTED_MODULE_2__.generateResponse)(query, knowledgeResults, memoryContext);\n            this.updateStep(generationStepId, {\n                status: \"complete\",\n                description: \"Response generated successfully\"\n            });\n            // Final step\n            this.addStep(\"complete\", \"Complete\", \"Your personalized answer is ready!\", \"complete\");\n            return {\n                answer: response,\n                sources: knowledgeResults,\n                steps: this.steps,\n                searchResults: this.searchResults\n            };\n        } catch (error) {\n            console.error(\"Error in RAG pipeline:\", error);\n            // Mark current active step as error\n            const activeStep = this.steps.find((s)=>s.status === \"active\");\n            if (activeStep) {\n                this.updateStep(activeStep.id, {\n                    status: \"error\",\n                    description: \"An error occurred during processing\"\n                });\n            }\n            throw error;\n        }\n    }\n}\nasync function processWithRAGPipeline(query, userId, onProgress) {\n    const pipeline = new RAGPipeline(onProgress);\n    return pipeline.process(query, userId);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/rag-pipeline.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai","vendor-chunks/ms","vendor-chunks/@pinecone-database","vendor-chunks/mem0ai","vendor-chunks/tr46","vendor-chunks/ajv","vendor-chunks/@sinclair","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/debug","vendor-chunks/event-target-shim","vendor-chunks/fast-uri","vendor-chunks/agentkeepalive","vendor-chunks/supports-color","vendor-chunks/webidl-conversions","vendor-chunks/abort-controller","vendor-chunks/json-schema-traverse","vendor-chunks/fast-deep-equal","vendor-chunks/has-flag","vendor-chunks/cross-fetch","vendor-chunks/humanize-ms","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat-stream%2Froute&page=%2Fapi%2Fchat-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat-stream%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();