"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("diagnostics_channel");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_pinecone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n/* harmony import */ var _lib_memory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/memory */ \"(rsc)/./src/lib/memory.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const { message, userId = \"anonymous\", sessionId } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate a unique user ID if not provided\n        const effectiveUserId = userId === \"anonymous\" ? `session_${sessionId || Date.now()}` : userId;\n        // Check if we have real API keys\n        const hasRealKeys =  true && !\"********************************************************************************************************************************************************************\".includes(\"placeholder\");\n        if (!hasRealKeys) {\n            // Return a demo response when API keys are placeholders\n            const demoResponse = getDemoResponse(message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(demoResponse);\n        }\n        // Get memory context for this user\n        const memoryContext = await (0,_lib_memory__WEBPACK_IMPORTED_MODULE_3__.getMemoryContext)(message, effectiveUserId);\n        // Search knowledge base for relevant information\n        const relevantDocs = await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_1__.searchKnowledgeBase)(message);\n        // Generate response using OpenAI with memory context\n        const response = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_2__.generateResponse)(message, relevantDocs, memoryContext);\n        // Store the conversation in memory\n        await (0,_lib_memory__WEBPACK_IMPORTED_MODULE_3__.addToMemory)([\n            {\n                role: \"user\",\n                content: message\n            },\n            {\n                role: \"assistant\",\n                content: response\n            }\n        ], effectiveUserId, {\n            category: \"aven_support\",\n            hasKnowledgeBase: relevantDocs.length > 0\n        });\n        const result = {\n            answer: response,\n            sources: relevantDocs,\n            confidence: relevantDocs.length > 0 ? 0.8 : 0.5\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error(\"Error in chat API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getDemoResponse(message) {\n    const lowerMessage = message.toLowerCase();\n    let answer = \"I'm a demo version of the Aven AI Customer Support Agent. \";\n    if (lowerMessage.includes(\"heloc\") || lowerMessage.includes(\"credit card\")) {\n        answer = \"The Aven HELOC Credit Card allows homeowners to access their home equity with credit limits up to $250,000. It offers 7.99% - 15.49% variable interest rates, 2% cashback on all purchases, and 7% cashback on travel booked through Aven's portal. There's no annual fee and approval can be as fast as 5 minutes.\";\n    } else if (lowerMessage.includes(\"interest rate\") || lowerMessage.includes(\"apr\")) {\n        answer = \"The Aven HELOC Credit Card offers variable interest rates from 7.99% to 15.49%, with a maximum of 18% during the life of the account. There's also a 0.25% autopay discount available.\";\n    } else if (lowerMessage.includes(\"cashback\") || lowerMessage.includes(\"rewards\")) {\n        answer = \"Aven offers 2% cashback on all purchases and 7% cashback on travel bookings made through Aven's travel portal. There are no annual fees.\";\n    } else if (lowerMessage.includes(\"apply\") || lowerMessage.includes(\"eligibility\")) {\n        answer = \"To be eligible for the Aven HELOC Credit Card, you must be a homeowner with sufficient home equity, have a credit score typically around 600 or higher, and meet stable income requirements (usually $50k+ annually). The application process is quick, with approval decisions as fast as 5 minutes.\";\n    } else {\n        answer += \"I can help you with questions about Aven's HELOC Credit Card, including features, interest rates, cashback rewards, eligibility requirements, and application process. To enable full functionality with real-time data, please set up your API keys in the environment variables.\";\n    }\n    return {\n        answer,\n        sources: [\n            {\n                id: \"demo-source\",\n                title: \"Aven HELOC Credit Card Information\",\n                content: \"Demo information about Aven services\",\n                url: \"https://www.aven.com\",\n                category: \"demo\"\n            }\n        ],\n        confidence: 0.8\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/memory.ts":
/*!***************************!*\
  !*** ./src/lib/memory.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToMemory: () => (/* binding */ addToMemory),\n/* harmony export */   deleteUserMemories: () => (/* binding */ deleteUserMemories),\n/* harmony export */   getAllMemories: () => (/* binding */ getAllMemories),\n/* harmony export */   getMemoryContext: () => (/* binding */ getMemoryContext),\n/* harmony export */   initializeMemory: () => (/* binding */ initializeMemory),\n/* harmony export */   searchMemory: () => (/* binding */ searchMemory)\n/* harmony export */ });\n/* harmony import */ var mem0ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mem0ai */ \"(rsc)/./node_modules/mem0ai/dist/index.mjs\");\n\nlet memory = null;\nasync function initializeMemory() {\n    if (!memory) {\n        // Check if we have MEM0_API_KEY for cloud version\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Use cloud version with API key\n            memory = new mem0ai__WEBPACK_IMPORTED_MODULE_0__.Memory({\n                apiKey: mem0ApiKey\n            });\n        } else {\n            // Fallback to OSS version with local configuration\n            const { Memory: OSSMemory } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/cloudflare\"), __webpack_require__.e(\"vendor-chunks/groq-sdk\"), __webpack_require__.e(\"vendor-chunks/mem0ai\"), __webpack_require__.e(\"vendor-chunks/@anthropic-ai\"), __webpack_require__.e(\"vendor-chunks/extend\"), __webpack_require__.e(\"vendor-chunks/@redis\"), __webpack_require__.e(\"vendor-chunks/rxjs\"), __webpack_require__.e(\"vendor-chunks/@mistralai\"), __webpack_require__.e(\"vendor-chunks/@qdrant\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver-bolt-connection\"), __webpack_require__.e(\"vendor-chunks/zod-to-json-schema\"), __webpack_require__.e(\"vendor-chunks/@langchain\"), __webpack_require__.e(\"vendor-chunks/@supabase\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver-core\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/google-auth-library\"), __webpack_require__.e(\"vendor-chunks/langsmith\"), __webpack_require__.e(\"vendor-chunks/zod\"), __webpack_require__.e(\"vendor-chunks/generic-pool\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@fastify\"), __webpack_require__.e(\"vendor-chunks/@cfworker\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver\"), __webpack_require__.e(\"vendor-chunks/gaxios\"), __webpack_require__.e(\"vendor-chunks/jws\"), __webpack_require__.e(\"vendor-chunks/retry\"), __webpack_require__.e(\"vendor-chunks/p-queue\"), __webpack_require__.e(\"vendor-chunks/json-bigint\"), __webpack_require__.e(\"vendor-chunks/google-logging-utils\"), __webpack_require__.e(\"vendor-chunks/ollama\"), __webpack_require__.e(\"vendor-chunks/isows\"), __webpack_require__.e(\"vendor-chunks/yallist\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/gcp-metadata\"), __webpack_require__.e(\"vendor-chunks/ecdsa-sig-formatter\"), __webpack_require__.e(\"vendor-chunks/agent-base\"), __webpack_require__.e(\"vendor-chunks/tslib\"), __webpack_require__.e(\"vendor-chunks/@google\"), __webpack_require__.e(\"vendor-chunks/whatwg-fetch\"), __webpack_require__.e(\"vendor-chunks/safe-buffer\"), __webpack_require__.e(\"vendor-chunks/redis\"), __webpack_require__.e(\"vendor-chunks/p-timeout\"), __webpack_require__.e(\"vendor-chunks/p-retry\"), __webpack_require__.e(\"vendor-chunks/p-finally\"), __webpack_require__.e(\"vendor-chunks/jwa\"), __webpack_require__.e(\"vendor-chunks/is-stream\"), __webpack_require__.e(\"vendor-chunks/gtoken\"), __webpack_require__.e(\"vendor-chunks/eventemitter3\"), __webpack_require__.e(\"vendor-chunks/decamelize\"), __webpack_require__.e(\"vendor-chunks/cluster-key-slot\"), __webpack_require__.e(\"vendor-chunks/camelcase\"), __webpack_require__.e(\"vendor-chunks/buffer-equal-constant-time\"), __webpack_require__.e(\"vendor-chunks/bignumber.js\"), __webpack_require__.e(\"vendor-chunks/base64-js\"), __webpack_require__.e(\"vendor-chunks/ansi-styles\"), __webpack_require__.e(\"vendor-chunks/@sevinf\"), __webpack_require__.e(\"_32c4-_66e9\")]).then(__webpack_require__.bind(__webpack_require__, /*! mem0ai/oss */ \"(rsc)/./node_modules/mem0ai/dist/oss/index.mjs\"));\n            memory = new OSSMemory({\n                version: \"v1.1\",\n                embedder: {\n                    provider: \"openai\",\n                    config: {\n                        apiKey: \"********************************************************************************************************************************************************************\" || 0,\n                        model: \"text-embedding-3-small\"\n                    }\n                },\n                vectorStore: {\n                    provider: \"memory\",\n                    config: {\n                        collectionName: \"aven_memories\",\n                        dimension: 1536\n                    }\n                },\n                llm: {\n                    provider: \"openai\",\n                    config: {\n                        apiKey: \"********************************************************************************************************************************************************************\" || 0,\n                        model: \"gpt-4-turbo-preview\"\n                    }\n                },\n                historyDbPath: \"./data/memory.db\",\n                disableHistory: false\n            });\n        }\n    }\n    return memory;\n}\nasync function addToMemory(messages, userId, metadata) {\n    try {\n        const mem = await initializeMemory();\n        // For cloud version, use the messages directly\n        // For OSS version, use the existing format\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version - add each message separately or as conversation\n            const conversationText = messages.map((m)=>`${m.role}: ${m.content}`).join(\"\\n\");\n            const result = await mem.add(conversationText, {\n                user_id: userId,\n                metadata: {\n                    timestamp: new Date().toISOString(),\n                    category: \"conversation\",\n                    ...metadata\n                }\n            });\n            console.log(\"Memory added (cloud):\", result);\n            return result;\n        } else {\n            // OSS version\n            const result = await mem.add(messages, {\n                userId,\n                metadata: {\n                    timestamp: new Date().toISOString(),\n                    ...metadata\n                }\n            });\n            console.log(\"Memory added (OSS):\", result);\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Error adding to memory:\", error);\n        return null;\n    }\n}\nasync function searchMemory(query, userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            const results = await mem.search(query, {\n                user_id: userId\n            });\n            console.log(\"Memory search results (cloud):\", results);\n            return results;\n        } else {\n            // OSS version\n            const results = await mem.search(query, {\n                userId\n            });\n            console.log(\"Memory search results (OSS):\", results);\n            return results;\n        }\n    } catch (error) {\n        console.error(\"Error searching memory:\", error);\n        return [];\n    }\n}\nasync function getAllMemories(userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            const memories = await mem.getAll({\n                user_id: userId\n            });\n            return memories;\n        } else {\n            // OSS version\n            const memories = await mem.getAll({\n                userId\n            });\n            return memories;\n        }\n    } catch (error) {\n        console.error(\"Error getting all memories:\", error);\n        return [];\n    }\n}\nasync function getMemoryContext(query, userId) {\n    try {\n        const relevantMemories = await searchMemory(query, userId);\n        if (relevantMemories.length === 0) {\n            return \"No previous conversation context found.\";\n        }\n        const context = relevantMemories.slice(0, 3) // Limit to top 3 most relevant memories\n        .map((memory, index)=>`${index + 1}. ${memory.text}`).join(\"\\n\");\n        return `Previous conversation context:\\n${context}`;\n    } catch (error) {\n        console.error(\"Error getting memory context:\", error);\n        return \"Error retrieving conversation context.\";\n    }\n}\nasync function deleteUserMemories(userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            await mem.deleteAll({\n                user_id: userId\n            });\n            console.log(`All memories deleted for user: ${userId} (cloud)`);\n            return true;\n        } else {\n            // OSS version\n            await mem.deleteAll({\n                userId\n            });\n            console.log(`All memories deleted for user: ${userId} (OSS)`);\n            return true;\n        }\n    } catch (error) {\n        console.error(\"Error deleting user memories:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/memory.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateEmbedding: () => (/* binding */ generateEmbedding),\n/* harmony export */   generateResponse: () => (/* binding */ generateResponse)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\nasync function generateResponse(query, relevantDocs, memoryContext) {\n    try {\n        const context = relevantDocs.map((doc)=>`Title: ${doc.title}\\nContent: ${doc.content}\\nSource: ${doc.url}\\n`).join(\"\\n---\\n\");\n        const systemPrompt = `You are a helpful customer support assistant for Aven, a financial technology company that offers a HELOC (Home Equity Line of Credit) Credit Card for homeowners.\n\nKey information about Aven:\n- Aven provides a HELOC Credit Card that allows homeowners to access their home equity\n- Credit limits up to $250,000\n- Interest rates from 7.99% - 15.49% (variable), max 18%\n- 2% cashback on all purchases, 7% on travel through Aven's portal\n- No annual fee, no notarization fee\n- Approval as fast as 5 minutes\n- Powered by Visa network, partnered with Coastal Community Bank\n- 0.25% autopay discount available\n\nFORMATTING REQUIREMENTS:\n1. Structure your responses with clear headings and bullet points\n2. Use markdown formatting for better readability\n3. Add inline citations using [^1], [^2] format when referencing specific information\n4. Keep responses well-organized and scannable\n5. Use bullet points for lists and features\n6. Include relevant emojis sparingly for visual appeal\n\nCITATION FORMAT:\n- Add inline citations like [^1] immediately after claims that come from sources\n- Reference sources by their titles, not URLs\n- Make citations clickable and helpful\n\nYour role:\n1. Answer questions about Aven's HELOC Credit Card and services\n2. Help customers understand features, benefits, and application process\n3. Provide helpful, accurate information based on the context provided\n4. Structure responses professionally with clear formatting\n5. Always cite sources when providing specific information\n6. If you don't know something, admit it and suggest contacting Aven directly\n\nRemember:\n- Don't provide personal financial advice\n- Don't access or discuss specific account information\n- Don't provide legal advice\n- Keep responses professional and helpful\n- Use clear, structured formatting with headings and bullet points`;\n        const userPrompt = `Question: ${query}\n\nContext from knowledge base:\n${context}\n\n${memoryContext ? `Memory/Conversation Context:\n${memoryContext}\n\n` : \"\"}Please provide a well-structured, helpful response based on the context provided. Follow these guidelines:\n\nSTRUCTURE YOUR RESPONSE:\n1. Start with a brief, direct answer\n2. Use clear headings (## for main sections)\n3. Use bullet points for lists and features\n4. Add inline citations [^1] when referencing specific information\n5. End with a helpful summary or next steps\n\nEXAMPLE FORMAT:\n## Key Features\n- **Feature 1**: Description [^1]\n- **Feature 2**: Description [^2]\n\n## Important Details\nBrief explanation with citations [^1]\n\nIf the context doesn't contain enough information to answer the question, say so and suggest contacting Aven directly.`;\n        const response = await openai.chat.completions.create({\n            model: \"gpt-4-turbo-preview\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        });\n        return response.choices[0]?.message?.content || \"I apologize, but I was unable to generate a response. Please try again or contact Aven customer support directly.\";\n    } catch (error) {\n        console.error(\"Error generating response:\", error);\n        throw error;\n    }\n}\nasync function generateEmbedding(text) {\n    try {\n        const response = await openai.embeddings.create({\n            model: \"text-embedding-3-small\",\n            input: text,\n            dimensions: 1024\n        });\n        return response.data[0].embedding;\n    } catch (error) {\n        console.error(\"Error generating embedding:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/pinecone.ts":
/*!*****************************!*\
  !*** ./src/lib/pinecone.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToKnowledgeBase: () => (/* binding */ addToKnowledgeBase),\n/* harmony export */   initializePinecone: () => (/* binding */ initializePinecone),\n/* harmony export */   searchKnowledgeBase: () => (/* binding */ searchKnowledgeBase)\n/* harmony export */ });\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @pinecone-database/pinecone */ \"(rsc)/./node_modules/@pinecone-database/pinecone/dist/index.js\");\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__);\n\nlet pinecone = null;\nasync function initializePinecone() {\n    if (!pinecone) {\n        pinecone = new _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__.Pinecone({\n            apiKey: \"pcsk_77kxri_6eW7cu6TpWV5XKai7gd5RG9TjUYub3jYi6LLQ2Ppqg3FQvcNxeEXwcQntCcz1nt\" || 0\n        });\n    }\n    return {\n        pinecone\n    };\n}\nasync function searchKnowledgeBase(query) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        // Get the index\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the query using OpenAI\n        const { generateEmbedding } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const queryEmbedding = await generateEmbedding(query);\n        // Search for similar vectors\n        const searchResult = await index.query({\n            vector: queryEmbedding,\n            topK: 5,\n            includeMetadata: true\n        });\n        // Convert results to AvenKnowledgeItem format\n        const results = searchResult.matches?.map((match)=>({\n                id: match.id,\n                title: match.metadata?.title || \"\",\n                content: match.metadata?.content || \"\",\n                url: match.metadata?.url || \"\",\n                category: match.metadata?.category || \"general\",\n                embedding: match.values\n            })) || [];\n        return results;\n    } catch (error) {\n        console.error(\"Error searching knowledge base:\", error);\n        return [];\n    }\n}\nasync function addToKnowledgeBase(item) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the content using OpenAI\n        const { generateEmbedding } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const embedding = await generateEmbedding(item.content);\n        // Upsert the vector\n        await index.upsert([\n            {\n                id: item.id,\n                values: embedding,\n                metadata: {\n                    title: item.title,\n                    content: item.content,\n                    url: item.url,\n                    category: item.category\n                }\n            }\n        ]);\n        console.log(`Added item ${item.id} to knowledge base`);\n    } catch (error) {\n        console.error(\"Error adding to knowledge base:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/pinecone.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai","vendor-chunks/mem0ai","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/@pinecone-database","vendor-chunks/ajv","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/whatwg-url","vendor-chunks/call-bind-apply-helpers","vendor-chunks/fast-uri","vendor-chunks/agentkeepalive","vendor-chunks/get-proto","vendor-chunks/tr46","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/json-schema-traverse","vendor-chunks/humanize-ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/fast-deep-equal","vendor-chunks/event-target-shim","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/cross-fetch","vendor-chunks/combined-stream","vendor-chunks/abort-controller","vendor-chunks/@sinclair"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();