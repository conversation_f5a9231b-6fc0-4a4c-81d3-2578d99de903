"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("diagnostics_channel");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alekramelaheehridoy_Desktop_projects_ai_customer_agent_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_pinecone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n/* harmony import */ var _lib_memory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/memory */ \"(rsc)/./src/lib/memory.ts\");\n/* harmony import */ var _utils_agentic_retrieval__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/agentic-retrieval */ \"(rsc)/./src/utils/agentic-retrieval.ts\");\n/* harmony import */ var _utils_agent_memory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/agent-memory */ \"(rsc)/./src/utils/agent-memory.ts\");\n/* harmony import */ var _utils_meeting_scheduler__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/meeting-scheduler */ \"(rsc)/./src/utils/meeting-scheduler.ts\");\n/* harmony import */ var _lib_langfuse__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/langfuse */ \"(rsc)/./src/lib/langfuse.ts\");\n/* harmony import */ var _lib_fast_responses__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/fast-responses */ \"(rsc)/./src/lib/fast-responses.ts\");\n/* harmony import */ var _lib_rag_pipeline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/rag-pipeline */ \"(rsc)/./src/lib/rag-pipeline.ts\");\n\n\n\n\n\n\n\n\n\n\nasync function POST(request) {\n    try {\n        console.log(\"\\uD83D\\uDE80 Chat API called\");\n        const startTime1 = Date.now();\n        const { message, userId = \"anonymous\", sessionId, useFullPipeline = true } = await request.json();\n        console.log(\"\\uD83D\\uDCDD Message:\", message);\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check for instant responses first while keeping full pipeline for complex queries\n        const instantResponse = (0,_lib_fast_responses__WEBPACK_IMPORTED_MODULE_8__.getInstantResponse)(message);\n        if (instantResponse) {\n            console.log(`⚡ Instant response in ${Date.now() - startTime1}ms`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(instantResponse);\n        }\n        // Generate a unique user ID if not provided\n        const effectiveUserId = userId === \"anonymous\" ? `session_${sessionId || Date.now()}` : userId;\n        // Initialize LangFuse tracing for text chat\n        const chatSessionId = sessionId || `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const tracer1 = (0,_lib_langfuse__WEBPACK_IMPORTED_MODULE_7__.createRAGTrace)(chatSessionId, effectiveUserId, message);\n        // Check if we have real API keys\n        const hasRealKeys =  true && !\"********************************************************************************************************************************************************************\".includes(\"placeholder\");\n        console.log(\"\\uD83D\\uDD11 Has real API keys:\", hasRealKeys);\n        if (!hasRealKeys) {\n            // Return a demo response when API keys are placeholders\n            console.log(\"\\uD83D\\uDCCB Using demo response\");\n            const demoResponse = getDemoResponse(message);\n            await tracer1.endTrace({\n                response: demoResponse,\n                mode: \"demo\"\n            }, {\n                total_duration_ms: Date.now() - startTime1,\n                demo_mode: true\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(demoResponse);\n        }\n        // CHECK FOR FULL RAG PIPELINE REQUEST\n        if (useFullPipeline) {\n            console.log(\"\\uD83D\\uDD04 Using full 6-step RAG pipeline...\");\n            try {\n                const ragResult = await (0,_lib_rag_pipeline__WEBPACK_IMPORTED_MODULE_9__.processWithRAGPipeline)(message, effectiveUserId);\n                // Store the conversation in memory async\n                (0,_lib_memory__WEBPACK_IMPORTED_MODULE_3__.addToMemory)([\n                    {\n                        role: \"user\",\n                        content: message\n                    },\n                    {\n                        role: \"assistant\",\n                        content: ragResult.answer\n                    }\n                ], effectiveUserId, {\n                    category: \"aven_support_rag\",\n                    hasKnowledgeBase: ragResult.sources.length > 0,\n                    pipeline_type: \"multi_step_rag\"\n                }).catch(console.error);\n                const result = {\n                    answer: ragResult.answer,\n                    sources: ragResult.sources,\n                    confidence: ragResult.sources.length > 0 ? 0.9 : 0.6,\n                    steps: ragResult.steps,\n                    searchResults: ragResult.searchResults,\n                    traceId: ragResult.traceId\n                };\n                console.log(`🎯 Full RAG pipeline completed in ${Date.now() - startTime1}ms`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n            } catch (ragError) {\n                console.error(\"Error in full RAG pipeline:\", ragError);\n            // Fall through to optimized pipeline\n            }\n        }\n        // PARALLEL OPTIMIZATION: Run memory context and meeting check simultaneously\n        console.log(\"\\uD83E\\uDDE0 Getting memory context and checking for meeting requests...\");\n        const [memoryContext, isSchedulingRequest] = await Promise.all([\n            (0,_lib_memory__WEBPACK_IMPORTED_MODULE_3__.getMemoryContext)(message, effectiveUserId).catch(()=>\"\"),\n            Promise.resolve([\n                \"schedule\",\n                \"appointment\",\n                \"meeting\",\n                \"book\",\n                \"reserve\",\n                \"consultation\"\n            ].some((keyword)=>message.toLowerCase().includes(keyword)))\n        ]);\n        // Meeting scheduling handled above in parallel\n        if (isSchedulingRequest) {\n            console.log(\"\\uD83D\\uDCC5 Processing meeting scheduling request...\");\n            try {\n                const schedulingResult = await _utils_meeting_scheduler__WEBPACK_IMPORTED_MODULE_6__.meetingScheduler.parseAndSchedule(message, effectiveUserId);\n                if (schedulingResult.success) {\n                    const meeting = schedulingResult.meeting;\n                    const result = {\n                        answer: `${schedulingResult.message} Your meeting details:\\n\\n📅 Date: ${meeting.date}\\n🕐 Time: ${meeting.time}\\n📋 Type: ${meeting.meetingType}\\n🆔 Meeting ID: ${meeting.id}\\n\\nYou can reschedule or cancel this meeting by referencing the Meeting ID.`,\n                        sources: [\n                            {\n                                id: \"meeting-scheduler\",\n                                title: \"Meeting Scheduled Successfully\",\n                                content: `Meeting scheduled for ${meeting.date} at ${meeting.time}`,\n                                url: \"#\",\n                                category: \"scheduling\"\n                            }\n                        ],\n                        confidence: 1.0\n                    };\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n                } else {\n                    // If scheduling failed, provide scheduling help\n                    const availableSlots = _utils_meeting_scheduler__WEBPACK_IMPORTED_MODULE_6__.meetingScheduler.getAvailableSlots().slice(0, 5);\n                    const slotsText = availableSlots.map((slot)=>`${slot.date} at ${slot.time}`).join(\", \");\n                    const result = {\n                        answer: `${schedulingResult.message}\\n\\nHere are some available time slots: ${slotsText}\\n\\nTo schedule a meeting, please specify a date and time, for example: \"Schedule a consultation for December 15th at 2 PM\"`,\n                        sources: [\n                            {\n                                id: \"meeting-scheduler-help\",\n                                title: \"Meeting Scheduling Help\",\n                                content: \"Available meeting slots and scheduling instructions\",\n                                url: \"#\",\n                                category: \"scheduling\"\n                            }\n                        ],\n                        confidence: 0.8\n                    };\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n                }\n            } catch (schedulingError) {\n                console.error(\"Error in meeting scheduling:\", schedulingError);\n            // Continue to normal chat flow if scheduling fails\n            }\n        }\n        // PARALLEL OPTIMIZATION: Check cache and start agentic retrieval simultaneously\n        console.log(\"⚡ Checking semantic cache and preparing agentic retrieval...\");\n        const [cachedResults, agenticBackupResults] = await Promise.allSettled([\n            _utils_agent_memory__WEBPACK_IMPORTED_MODULE_5__.agentMemory.checkSemanticCache(message).catch(()=>null),\n            _utils_agentic_retrieval__WEBPACK_IMPORTED_MODULE_4__.agenticRetrieval.search(message).catch(async ()=>{\n                // Fallback to basic search\n                try {\n                    const basicResults = await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_1__.searchKnowledgeBase)(message);\n                    return {\n                        results: basicResults,\n                        confidence: 0.5,\n                        searchPath: [\n                            \"basic_search\"\n                        ]\n                    };\n                } catch  {\n                    return {\n                        results: [],\n                        confidence: 0,\n                        searchPath: [\n                            \"fallback\"\n                        ]\n                    };\n                }\n            })\n        ]);\n        let relevantDocs;\n        const cacheResult = cachedResults.status === \"fulfilled\" ? cachedResults.value : null;\n        if (cacheResult) {\n            console.log(\"⚡ Using cached results\");\n            relevantDocs = cacheResult;\n            // Cache hit, but still update cache async with new agentic results if available\n            if (agenticBackupResults.status === \"fulfilled\") {\n                _utils_agent_memory__WEBPACK_IMPORTED_MODULE_5__.agentMemory.addToSemanticCache(message, agenticBackupResults.value.results).catch(console.error);\n            }\n        } else {\n            // Use agentic results\n            const agenticResult = agenticBackupResults.status === \"fulfilled\" ? agenticBackupResults.value : {\n                results: [],\n                confidence: 0,\n                searchPath: [\n                    \"error\"\n                ]\n            };\n            relevantDocs = agenticResult.results;\n            console.log(`🔍 Agentic search found ${relevantDocs.length} results with confidence ${agenticResult.confidence}`);\n            console.log(`📊 Search path: ${agenticResult.searchPath.join(\" → \")}`);\n            // Add to cache async (don't wait)\n            _utils_agent_memory__WEBPACK_IMPORTED_MODULE_5__.agentMemory.addToSemanticCache(message, relevantDocs).catch(console.error);\n        }\n        // Generate response using OpenAI with memory context\n        console.log(\"\\uD83E\\uDD16 Generating response...\");\n        const response = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_2__.generateResponse)(message, relevantDocs, memoryContext);\n        // ASYNC OPTIMIZATION: Run memory storage, learning, and tracing in parallel (don't block response)\n        const asyncOperations = Promise.allSettled([\n            // Store conversation in memory\n            (0,_lib_memory__WEBPACK_IMPORTED_MODULE_3__.addToMemory)([\n                {\n                    role: \"user\",\n                    content: message\n                },\n                {\n                    role: \"assistant\",\n                    content: response\n                }\n            ], effectiveUserId, {\n                category: \"aven_support\",\n                hasKnowledgeBase: relevantDocs.length > 0\n            }).catch(console.error),\n            // Learn from interaction for future improvements\n            _utils_agent_memory__WEBPACK_IMPORTED_MODULE_5__.agentMemory.learnFromInteraction(message, response, relevantDocs, effectiveUserId).catch(console.error),\n            // End LangFuse trace\n            tracer1.endTrace({\n                answer: response,\n                sources_count: relevantDocs.length,\n                confidence: relevantDocs.length > 0 ? 0.8 : 0.5\n            }, {\n                total_duration_ms: Date.now() - startTime1,\n                success: true,\n                pipeline_type: \"text_chat\"\n            }).catch(console.error)\n        ]);\n        // Don't await - let these run in background\n        asyncOperations.catch(console.error);\n        console.log(`⚡ Response generated in ${Date.now() - startTime1}ms (async operations continuing in background)`);\n        const result = {\n            answer: response,\n            sources: relevantDocs,\n            confidence: relevantDocs.length > 0 ? 0.8 : 0.5\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error(\"Error in chat API:\", error);\n        // Log error in trace\n        await tracer.endTrace({\n            error: error instanceof Error ? error.message : \"Unknown error\",\n            partial_response: true\n        }, {\n            total_duration_ms: Date.now() - startTime,\n            success: false,\n            error: true\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getDemoResponse(message) {\n    const lowerMessage = message.toLowerCase();\n    let answer = \"I'm a demo version of the Aven AI Customer Support Agent. \";\n    // Check for meeting scheduling requests in demo mode\n    const meetingKeywords = [\n        \"schedule\",\n        \"appointment\",\n        \"meeting\",\n        \"book\",\n        \"reserve\",\n        \"consultation\"\n    ];\n    const isSchedulingRequest = meetingKeywords.some((keyword)=>lowerMessage.includes(keyword));\n    if (isSchedulingRequest) {\n        answer = \"I can help you schedule a meeting with an Aven representative! In demo mode, I can show you how the scheduling works. Try asking: 'Schedule a consultation for tomorrow at 2 PM' or 'Book an appointment for Friday at 10 AM'. Available meeting types include: consultation, application help, account review, and general inquiries.\";\n    } else if (lowerMessage.includes(\"heloc\") || lowerMessage.includes(\"credit card\")) {\n        answer = \"The Aven HELOC Credit Card allows homeowners to access their home equity with credit limits up to $250,000. It offers 7.99% - 15.49% variable interest rates, 2% cashback on all purchases, and 7% cashback on travel booked through Aven's portal. There's no annual fee and approval can be as fast as 5 minutes.\";\n    } else if (lowerMessage.includes(\"interest rate\") || lowerMessage.includes(\"apr\")) {\n        answer = \"The Aven HELOC Credit Card offers variable interest rates from 7.99% to 15.49%, with a maximum of 18% during the life of the account. There's also a 0.25% autopay discount available.\";\n    } else if (lowerMessage.includes(\"cashback\") || lowerMessage.includes(\"rewards\")) {\n        answer = \"Aven offers 2% cashback on all purchases and 7% cashback on travel bookings made through Aven's travel portal. There are no annual fees.\";\n    } else if (lowerMessage.includes(\"apply\") || lowerMessage.includes(\"eligibility\")) {\n        answer = \"To be eligible for the Aven HELOC Credit Card, you must be a homeowner with sufficient home equity, have a credit score typically around 600 or higher, and meet stable income requirements (usually $50k+ annually). The application process is quick, with approval decisions as fast as 5 minutes.\";\n    } else {\n        answer += \"I can help you with questions about Aven's HELOC Credit Card, including features, interest rates, cashback rewards, eligibility requirements, application process, and meeting scheduling. To enable full functionality with real-time data, please set up your API keys in the environment variables.\";\n    }\n    return {\n        answer,\n        sources: [\n            {\n                id: \"demo-source\",\n                title: \"Aven HELOC Credit Card Information\",\n                content: \"Demo information about Aven services\",\n                url: \"https://www.aven.com\",\n                category: \"demo\"\n            }\n        ],\n        confidence: 0.8\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/fast-responses.ts":
/*!***********************************!*\
  !*** ./src/lib/fast-responses.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INSTANT_RESPONSES: () => (/* binding */ INSTANT_RESPONSES),\n/* harmony export */   getInstantResponse: () => (/* binding */ getInstantResponse)\n/* harmony export */ });\n// Pre-computed responses for common questions to achieve sub-second response times\nconst INSTANT_RESPONSES = {\n    // Interest rate questions\n    \"what are the interest rates\": {\n        answer: \"Aven offers variable interest rates from 7.99% to 15.49% APR on the HELOC Credit Card, with a maximum rate of 18% during the life of the account. You can also get a 0.25% autopay discount.\",\n        sources: [\n            {\n                id: \"rates\",\n                title: \"Interest Rates\",\n                content: \"Current HELOC Credit Card rates\",\n                url: \"https://www.aven.com/rates\",\n                category: \"rates\"\n            }\n        ],\n        confidence: 0.95\n    },\n    \"interest rate\": {\n        answer: \"Our HELOC Credit Card has variable rates from 7.99% to 15.49% APR with a 0.25% autopay discount available.\",\n        sources: [\n            {\n                id: \"rates\",\n                title: \"Interest Rates\",\n                content: \"HELOC Credit Card interest rates\",\n                url: \"https://www.aven.com/rates\",\n                category: \"rates\"\n            }\n        ],\n        confidence: 0.95\n    },\n    // Credit limit questions\n    \"credit limit\": {\n        answer: \"The Aven HELOC Credit Card offers credit limits up to $250,000, based on your home equity and creditworthiness.\",\n        sources: [\n            {\n                id: \"limits\",\n                title: \"Credit Limits\",\n                content: \"HELOC Credit Card limits\",\n                url: \"https://www.aven.com/limits\",\n                category: \"limits\"\n            }\n        ],\n        confidence: 0.95\n    },\n    \"how much can i borrow\": {\n        answer: \"You can borrow up to $250,000 with the Aven HELOC Credit Card, depending on your available home equity and qualification criteria.\",\n        sources: [\n            {\n                id: \"limits\",\n                title: \"Borrowing Limits\",\n                content: \"Maximum borrowing amounts\",\n                url: \"https://www.aven.com/limits\",\n                category: \"limits\"\n            }\n        ],\n        confidence: 0.95\n    },\n    // Cashback questions\n    \"cashback\": {\n        answer: \"Earn 2% cashback on all purchases and 7% cashback on travel bookings made through Aven's travel portal. No annual fee.\",\n        sources: [\n            {\n                id: \"rewards\",\n                title: \"Cashback Rewards\",\n                content: \"HELOC Credit Card rewards program\",\n                url: \"https://www.aven.com/rewards\",\n                category: \"rewards\"\n            }\n        ],\n        confidence: 0.95\n    },\n    \"rewards\": {\n        answer: \"Get 2% cashback on all purchases and 7% cashback on travel with the Aven HELOC Credit Card. No annual fees or caps.\",\n        sources: [\n            {\n                id: \"rewards\",\n                title: \"Rewards Program\",\n                content: \"Cashback and travel rewards\",\n                url: \"https://www.aven.com/rewards\",\n                category: \"rewards\"\n            }\n        ],\n        confidence: 0.95\n    },\n    // Application questions\n    \"how to apply\": {\n        answer: \"Apply online for the Aven HELOC Credit Card in minutes. You'll need proof of homeownership, income verification, and basic personal information. Approval decisions in as fast as 5 minutes.\",\n        sources: [\n            {\n                id: \"apply\",\n                title: \"Application Process\",\n                content: \"How to apply for HELOC Credit Card\",\n                url: \"https://www.aven.com/apply\",\n                category: \"application\"\n            }\n        ],\n        confidence: 0.95\n    },\n    \"eligibility\": {\n        answer: \"To qualify: be a homeowner with available equity, credit score typically 600+, stable income usually $50k+ annually, and meet debt-to-income requirements.\",\n        sources: [\n            {\n                id: \"eligibility\",\n                title: \"Eligibility Requirements\",\n                content: \"HELOC Credit Card qualification criteria\",\n                url: \"https://www.aven.com/eligibility\",\n                category: \"eligibility\"\n            }\n        ],\n        confidence: 0.95\n    },\n    // General product questions\n    \"what is aven\": {\n        answer: \"Aven is a fintech company offering the HELOC Credit Card, which lets homeowners access their home equity through a convenient credit card with competitive rates and cashback rewards.\",\n        sources: [\n            {\n                id: \"about\",\n                title: \"About Aven\",\n                content: \"Company overview and products\",\n                url: \"https://www.aven.com/about\",\n                category: \"general\"\n            }\n        ],\n        confidence: 0.95\n    },\n    \"heloc credit card\": {\n        answer: \"The Aven HELOC Credit Card combines home equity access with credit card convenience. Get up to $250,000 credit limit, 7.99-15.49% variable rates, 2% cashback on purchases, 7% on travel, no annual fee.\",\n        sources: [\n            {\n                id: \"product\",\n                title: \"HELOC Credit Card\",\n                content: \"Product overview and features\",\n                url: \"https://www.aven.com/heloc-credit-card\",\n                category: \"product\"\n            }\n        ],\n        confidence: 0.95\n    }\n};\nfunction getInstantResponse(message) {\n    const cleanMessage = message.toLowerCase().trim();\n    // Exact match first\n    if (INSTANT_RESPONSES[cleanMessage]) {\n        return INSTANT_RESPONSES[cleanMessage];\n    }\n    // Fuzzy matching for common variations\n    for (const [key, response] of Object.entries(INSTANT_RESPONSES)){\n        if (cleanMessage.includes(key) || key.includes(cleanMessage)) {\n            return response;\n        }\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/fast-responses.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/langfuse.ts":
/*!*****************************!*\
  !*** ./src/lib/langfuse.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RAGTracer: () => (/* binding */ RAGTracer),\n/* harmony export */   VoiceTracer: () => (/* binding */ VoiceTracer),\n/* harmony export */   createRAGTrace: () => (/* binding */ createRAGTrace),\n/* harmony export */   createVoiceTrace: () => (/* binding */ createVoiceTrace),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   flushTraces: () => (/* binding */ flushTraces),\n/* harmony export */   logCustomEvaluation: () => (/* binding */ logCustomEvaluation)\n/* harmony export */ });\n/* harmony import */ var langfuse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! langfuse */ \"(rsc)/./node_modules/langfuse/lib/index.mjs\");\n\n// Initialize LangFuse client\nconst langfuse = new langfuse__WEBPACK_IMPORTED_MODULE_0__.Langfuse({\n    publicKey: process.env.LANGFUSE_PUBLIC_KEY,\n    secretKey: process.env.LANGFUSE_SECRET_KEY,\n    baseUrl: process.env.LANGFUSE_HOST || \"https://cloud.langfuse.com\",\n    flushAt: 1,\n    requestTimeout: 10000 // 10 second timeout\n});\n// Custom trace utility for RAG pipeline\nclass RAGTracer {\n    constructor(sessionId, userId, input){\n        this.trace = langfuse.trace({\n            sessionId,\n            userId,\n            name: \"rag_pipeline\",\n            input: {\n                query: input\n            },\n            tags: [\n                \"rag\",\n                \"aven_support\"\n            ],\n            metadata: {\n                pipeline: \"multi_step_rag\",\n                timestamp: new Date().toISOString()\n            }\n        });\n    }\n    // Start a new step in the RAG pipeline\n    startStep(stepName, input, metadata) {\n        this.currentSpan = this.trace.span({\n            name: stepName,\n            input,\n            metadata: {\n                step: stepName,\n                ...metadata\n            }\n        });\n        return this.currentSpan;\n    }\n    // End current step with output and metrics\n    endStep(output, metadata) {\n        if (this.currentSpan) {\n            this.currentSpan.end({\n                output,\n                metadata\n            });\n        }\n    }\n    // Log LLM calls within steps\n    logLLMCall(stepName, model, input, output, usage, cost) {\n        const generation = this.trace.generation({\n            name: `${stepName}_llm_call`,\n            model,\n            input,\n            output,\n            usage,\n            metadata: {\n                step: stepName,\n                cost: cost || 0\n            }\n        });\n        return generation;\n    }\n    // Log retrieval operations\n    logRetrieval(stepName, query, results, metadata) {\n        const span = this.trace.span({\n            name: `${stepName}_retrieval`,\n            input: {\n                query\n            },\n            output: {\n                results_count: results.length,\n                results: results.slice(0, 3) // Log first 3 results to avoid too much data\n            },\n            metadata: {\n                step: stepName,\n                retrieval_type: metadata?.type || \"vector_search\",\n                ...metadata\n            }\n        });\n        span.end();\n        return span;\n    }\n    // Log evaluation scores\n    logEvaluation(stepName, metrics) {\n        this.trace.score({\n            name: `${stepName}_evaluation`,\n            value: metrics.score || 0,\n            comment: JSON.stringify(metrics)\n        });\n    }\n    // End the entire trace\n    async endTrace(output, metadata) {\n        this.trace.update({\n            output,\n            metadata: {\n                completed_at: new Date().toISOString(),\n                ...metadata\n            }\n        });\n        // Flush immediately to ensure trace is sent\n        try {\n            await langfuse.flushAsync();\n        } catch (error) {\n            console.error(\"Failed to flush LangFuse trace:\", error);\n        }\n    }\n    // Get trace ID for linking\n    getTraceId() {\n        return this.trace.id;\n    }\n}\n// Voice pipeline tracer\nclass VoiceTracer {\n    constructor(callId, userId, transcript){\n        this.trace = langfuse.trace({\n            sessionId: callId,\n            userId,\n            name: \"voice_pipeline\",\n            input: {\n                transcript\n            },\n            tags: [\n                \"voice\",\n                \"vapi\",\n                \"aven_support\"\n            ],\n            metadata: {\n                pipeline: \"voice_to_rag_to_voice\",\n                call_id: callId,\n                timestamp: new Date().toISOString()\n            }\n        });\n    }\n    // Log voice processing steps\n    logVoiceStep(stepName, input, output, metadata) {\n        const span = this.trace.span({\n            name: stepName,\n            input,\n            output,\n            metadata: {\n                voice_step: stepName,\n                ...metadata\n            }\n        });\n        span.end();\n        return span;\n    }\n    // Log external service calls (11Labs, Deepgram, etc.)\n    logExternalService(serviceName, input, output, cost, duration) {\n        const span = this.trace.span({\n            name: `${serviceName}_call`,\n            input,\n            output,\n            metadata: {\n                service: serviceName,\n                cost: cost || 0,\n                duration_ms: duration || 0\n            }\n        });\n        span.end();\n        return span;\n    }\n    // End voice trace\n    async endTrace(output, metadata) {\n        this.trace.update({\n            output,\n            metadata: {\n                completed_at: new Date().toISOString(),\n                ...metadata\n            }\n        });\n        // Flush immediately to ensure trace is sent\n        try {\n            await langfuse.flushAsync();\n        } catch (error) {\n            console.error(\"Failed to flush LangFuse voice trace:\", error);\n        }\n    }\n    getTraceId() {\n        return this.trace.id;\n    }\n}\n// Utility functions\nconst createRAGTrace = (sessionId, userId, input)=>{\n    return new RAGTracer(sessionId, userId, input);\n};\nconst createVoiceTrace = (callId, userId, transcript)=>{\n    return new VoiceTracer(callId, userId, transcript);\n};\n// Evaluation utilities\nconst logCustomEvaluation = (traceId, evaluationName, score, comment)=>{\n    langfuse.score({\n        traceId,\n        name: evaluationName,\n        value: score,\n        comment\n    });\n};\n// Flush traces (call before app shutdown)\nconst flushTraces = async ()=>{\n    await langfuse.flushAsync();\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (langfuse);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/langfuse.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/memory.ts":
/*!***************************!*\
  !*** ./src/lib/memory.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToMemory: () => (/* binding */ addToMemory),\n/* harmony export */   deleteUserMemories: () => (/* binding */ deleteUserMemories),\n/* harmony export */   getAllMemories: () => (/* binding */ getAllMemories),\n/* harmony export */   getMemoryContext: () => (/* binding */ getMemoryContext),\n/* harmony export */   initializeMemory: () => (/* binding */ initializeMemory),\n/* harmony export */   searchMemory: () => (/* binding */ searchMemory)\n/* harmony export */ });\n// Import mem0ai properly\nlet Memory;\ntry {\n    Memory = (__webpack_require__(/*! mem0ai */ \"(rsc)/./node_modules/mem0ai/dist/index.js\").Memory);\n} catch (error) {\n    console.warn(\"mem0ai cloud version not available, falling back to OSS\");\n}\nlet memory = null;\nasync function initializeMemory() {\n    if (!memory) {\n        // Check if we have MEM0_API_KEY for cloud version\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Use cloud version with API key\n            memory = new Memory({\n                apiKey: mem0ApiKey\n            });\n        } else {\n            // Fallback to OSS version with local configuration\n            const { Memory: OSSMemory } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/cloudflare\"), __webpack_require__.e(\"vendor-chunks/groq-sdk\"), __webpack_require__.e(\"vendor-chunks/mem0ai\"), __webpack_require__.e(\"vendor-chunks/@anthropic-ai\"), __webpack_require__.e(\"vendor-chunks/extend\"), __webpack_require__.e(\"vendor-chunks/@redis\"), __webpack_require__.e(\"vendor-chunks/rxjs\"), __webpack_require__.e(\"vendor-chunks/@mistralai\"), __webpack_require__.e(\"vendor-chunks/@qdrant\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver-bolt-connection\"), __webpack_require__.e(\"vendor-chunks/zod-to-json-schema\"), __webpack_require__.e(\"vendor-chunks/@langchain\"), __webpack_require__.e(\"vendor-chunks/@supabase\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver-core\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/google-auth-library\"), __webpack_require__.e(\"vendor-chunks/langsmith\"), __webpack_require__.e(\"vendor-chunks/zod\"), __webpack_require__.e(\"vendor-chunks/generic-pool\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@fastify\"), __webpack_require__.e(\"vendor-chunks/@cfworker\"), __webpack_require__.e(\"vendor-chunks/neo4j-driver\"), __webpack_require__.e(\"vendor-chunks/gaxios\"), __webpack_require__.e(\"vendor-chunks/jws\"), __webpack_require__.e(\"vendor-chunks/retry\"), __webpack_require__.e(\"vendor-chunks/p-queue\"), __webpack_require__.e(\"vendor-chunks/json-bigint\"), __webpack_require__.e(\"vendor-chunks/google-logging-utils\"), __webpack_require__.e(\"vendor-chunks/ollama\"), __webpack_require__.e(\"vendor-chunks/isows\"), __webpack_require__.e(\"vendor-chunks/yallist\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/gcp-metadata\"), __webpack_require__.e(\"vendor-chunks/ecdsa-sig-formatter\"), __webpack_require__.e(\"vendor-chunks/agent-base\"), __webpack_require__.e(\"vendor-chunks/tslib\"), __webpack_require__.e(\"vendor-chunks/@google\"), __webpack_require__.e(\"vendor-chunks/whatwg-fetch\"), __webpack_require__.e(\"vendor-chunks/safe-buffer\"), __webpack_require__.e(\"vendor-chunks/redis\"), __webpack_require__.e(\"vendor-chunks/p-timeout\"), __webpack_require__.e(\"vendor-chunks/p-retry\"), __webpack_require__.e(\"vendor-chunks/p-finally\"), __webpack_require__.e(\"vendor-chunks/jwa\"), __webpack_require__.e(\"vendor-chunks/is-stream\"), __webpack_require__.e(\"vendor-chunks/gtoken\"), __webpack_require__.e(\"vendor-chunks/eventemitter3\"), __webpack_require__.e(\"vendor-chunks/decamelize\"), __webpack_require__.e(\"vendor-chunks/cluster-key-slot\"), __webpack_require__.e(\"vendor-chunks/camelcase\"), __webpack_require__.e(\"vendor-chunks/buffer-equal-constant-time\"), __webpack_require__.e(\"vendor-chunks/bignumber.js\"), __webpack_require__.e(\"vendor-chunks/base64-js\"), __webpack_require__.e(\"vendor-chunks/ansi-styles\"), __webpack_require__.e(\"vendor-chunks/@sevinf\"), __webpack_require__.e(\"_32c4-_66e9\")]).then(__webpack_require__.bind(__webpack_require__, /*! mem0ai/oss */ \"(rsc)/./node_modules/mem0ai/dist/oss/index.mjs\"));\n            memory = new OSSMemory({\n                version: \"v1.1\",\n                embedder: {\n                    provider: \"openai\",\n                    config: {\n                        apiKey: \"********************************************************************************************************************************************************************\" || 0,\n                        model: \"text-embedding-3-small\"\n                    }\n                },\n                vectorStore: {\n                    provider: \"memory\",\n                    config: {\n                        collectionName: \"aven_memories\",\n                        dimension: 1024\n                    }\n                },\n                llm: {\n                    provider: \"openai\",\n                    config: {\n                        apiKey: \"********************************************************************************************************************************************************************\" || 0,\n                        model: \"gpt-4o-mini\"\n                    }\n                },\n                historyDbPath: \"./data/memory.db\",\n                disableHistory: false\n            });\n        }\n    }\n    return memory;\n}\nasync function addToMemory(messages, userId, metadata) {\n    try {\n        const mem = await initializeMemory();\n        // For cloud version, use the messages directly\n        // For OSS version, use the existing format\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version - add each message separately or as conversation\n            const conversationText = messages.map((m)=>`${m.role}: ${m.content}`).join(\"\\n\");\n            const result = await mem.add(conversationText, {\n                user_id: userId,\n                metadata: {\n                    timestamp: new Date().toISOString(),\n                    category: \"conversation\",\n                    ...metadata\n                }\n            });\n            console.log(\"Memory added (cloud):\", result);\n            return result;\n        } else {\n            // OSS version\n            const result = await mem.add(messages, {\n                userId,\n                metadata: {\n                    timestamp: new Date().toISOString(),\n                    ...metadata\n                }\n            });\n            console.log(\"Memory added (OSS):\", result);\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Error adding to memory:\", error);\n        return null;\n    }\n}\nasync function searchMemory(query, userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            const results = await mem.search(query, {\n                user_id: userId\n            });\n            console.log(\"Memory search results (cloud):\", results);\n            return results;\n        } else {\n            // OSS version\n            const results = await mem.search(query, {\n                userId\n            });\n            console.log(\"Memory search results (OSS):\", results);\n            return results;\n        }\n    } catch (error) {\n        console.error(\"Error searching memory:\", error);\n        return [];\n    }\n}\nasync function getAllMemories(userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            const memories = await mem.getAll({\n                user_id: userId\n            });\n            return memories;\n        } else {\n            // OSS version\n            const memories = await mem.getAll({\n                userId\n            });\n            return memories;\n        }\n    } catch (error) {\n        console.error(\"Error getting all memories:\", error);\n        return [];\n    }\n}\nasync function getMemoryContext(query, userId) {\n    try {\n        const relevantMemories = await searchMemory(query, userId);\n        if (relevantMemories.length === 0) {\n            return \"No previous conversation context found.\";\n        }\n        const context = relevantMemories.slice(0, 3) // Limit to top 3 most relevant memories\n        .map((memory, index)=>`${index + 1}. ${memory.text}`).join(\"\\n\");\n        return `Previous conversation context:\\n${context}`;\n    } catch (error) {\n        console.error(\"Error getting memory context:\", error);\n        return \"Error retrieving conversation context.\";\n    }\n}\nasync function deleteUserMemories(userId) {\n    try {\n        const mem = await initializeMemory();\n        const mem0ApiKey = process.env.MEM0_API_KEY;\n        if (mem0ApiKey) {\n            // Cloud version\n            await mem.deleteAll({\n                user_id: userId\n            });\n            console.log(`All memories deleted for user: ${userId} (cloud)`);\n            return true;\n        } else {\n            // OSS version\n            await mem.deleteAll({\n                userId\n            });\n            console.log(`All memories deleted for user: ${userId} (OSS)`);\n            return true;\n        }\n    } catch (error) {\n        console.error(\"Error deleting user memories:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/memory.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateEmbedding: () => (/* binding */ generateEmbedding),\n/* harmony export */   generateResponse: () => (/* binding */ generateResponse)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\nasync function generateResponse(query, relevantDocs, memoryContext, customPrompt) {\n    try {\n        const context = relevantDocs.map((doc)=>`Title: ${doc.title}\\nContent: ${doc.content}\\nSource: ${doc.url}\\n`).join(\"\\n---\\n\");\n        const systemPrompt = `You are a helpful customer support assistant for Aven, a financial technology company that offers a HELOC (Home Equity Line of Credit) Credit Card for homeowners.\n\nKey information about Aven:\n- Aven provides a HELOC Credit Card that allows homeowners to access their home equity\n- Credit limits up to $250,000\n- Interest rates from 7.99% - 15.49% (variable), max 18%\n- 2% cashback on all purchases, 7% on travel through Aven's portal\n- No annual fee, no notarization fee\n- Approval as fast as 5 minutes\n- Powered by Visa network, partnered with Coastal Community Bank\n- 0.25% autopay discount available\n\nFORMATTING REQUIREMENTS:\n1. Structure your responses with clear headings and bullet points\n2. Use markdown formatting for better readability\n3. Add inline citations using [^1], [^2] format when referencing specific information\n4. Keep responses well-organized and scannable\n5. Use bullet points for lists and features\n6. Include relevant emojis sparingly for visual appeal\n\nCITATION FORMAT:\n- Add inline citations like [^1] immediately after claims that come from sources\n- Reference sources by their titles, not URLs\n- Make citations clickable and helpful\n\nYour role:\n1. Answer questions about Aven's HELOC Credit Card and services\n2. Help customers understand features, benefits, and application process\n3. Provide helpful, accurate information based on the context provided\n4. Structure responses professionally with clear formatting\n5. Always cite sources when providing specific information\n6. If you don't know something, admit it and suggest contacting Aven directly\n\nRemember:\n- Don't provide personal financial advice\n- Don't access or discuss specific account information\n- Don't provide legal advice\n- Keep responses professional and helpful\n- Use clear, structured formatting with headings and bullet points`;\n        const userPrompt = customPrompt || `Question: ${query}\n\nContext from knowledge base:\n${context}\n\n${memoryContext ? `Memory/Conversation Context:\n${memoryContext}\n\n` : \"\"}Please provide a well-structured, helpful response based on the context provided. Follow these guidelines:\n\nSTRUCTURE YOUR RESPONSE:\n1. Start with a brief, direct answer\n2. Use clear headings (## for main sections)\n3. Use bullet points for lists and features\n4. Add inline citations [^1] when referencing specific information\n5. End with a helpful summary or next steps\n\nEXAMPLE FORMAT:\n## Key Features\n- **Feature 1**: Description [^1]\n- **Feature 2**: Description [^2]\n\n## Important Details\nBrief explanation with citations [^1]\n\nIf the context doesn't contain enough information to answer the question, say so and suggest contacting Aven directly.`;\n        const response = await openai.chat.completions.create({\n            model: \"gpt-4.1-mini\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 1000\n        });\n        return response.choices[0]?.message?.content || \"I apologize, but I was unable to generate a response. Please try again or contact Aven customer support directly.\";\n    } catch (error) {\n        console.error(\"Error generating response:\", error);\n        throw error;\n    }\n}\nasync function generateEmbedding(text) {\n    try {\n        const response = await openai.embeddings.create({\n            model: \"text-embedding-3-small\",\n            input: text,\n            dimensions: 1024\n        });\n        return response.data[0].embedding;\n    } catch (error) {\n        console.error(\"Error generating embedding:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/pinecone.ts":
/*!*****************************!*\
  !*** ./src/lib/pinecone.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToKnowledgeBase: () => (/* binding */ addToKnowledgeBase),\n/* harmony export */   initializePinecone: () => (/* binding */ initializePinecone),\n/* harmony export */   searchKnowledgeBase: () => (/* binding */ searchKnowledgeBase)\n/* harmony export */ });\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @pinecone-database/pinecone */ \"(rsc)/./node_modules/@pinecone-database/pinecone/dist/index.js\");\n/* harmony import */ var _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__);\n\nlet pinecone = null;\nasync function initializePinecone() {\n    if (!pinecone) {\n        pinecone = new _pinecone_database_pinecone__WEBPACK_IMPORTED_MODULE_0__.Pinecone({\n            apiKey: \"pcsk_5QHMhf_FdxztBL4x9UpkQNpzZGWEqgqbnLEFc9vLF3qYuVo5k9BFr5s7Yv8kdYkiV7po4J\" || 0\n        });\n    }\n    return {\n        pinecone\n    };\n}\nasync function searchKnowledgeBase(query) {\n    try {\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        // Get the index\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        // Create embedding for the query using OpenAI\n        const { generateEmbedding } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        const queryEmbedding = await generateEmbedding(query);\n        // Search for similar vectors\n        const searchResult = await index.query({\n            vector: queryEmbedding,\n            topK: 5,\n            includeMetadata: true\n        });\n        // Convert results to AvenKnowledgeItem format\n        const results = searchResult.matches?.map((match)=>({\n                id: match.id,\n                title: match.metadata?.title || \"\",\n                content: match.metadata?.content || \"\",\n                url: match.metadata?.url || \"\",\n                category: match.metadata?.category || \"general\",\n                embedding: match.values\n            })) || [];\n        return results;\n    } catch (error) {\n        console.error(\"Error searching knowledge base:\", error);\n        return [];\n    }\n}\n// Function to chunk large content\nfunction chunkContent(content, maxChunkSize = 6000) {\n    if (content.length <= maxChunkSize) {\n        return [\n            content\n        ];\n    }\n    const chunks = [];\n    const sentences = content.split(/[.!?]+/);\n    let currentChunk = \"\";\n    for (const sentence of sentences){\n        if ((currentChunk + sentence).length > maxChunkSize && currentChunk.length > 0) {\n            chunks.push(currentChunk.trim());\n            currentChunk = sentence;\n        } else {\n            currentChunk += sentence + \". \";\n        }\n    }\n    if (currentChunk.trim().length > 0) {\n        chunks.push(currentChunk.trim());\n    }\n    return chunks.filter((chunk)=>chunk.length > 100) // Filter out very small chunks\n    ;\n}\nasync function addToKnowledgeBase(item) {\n    try {\n        console.log(`Adding item ${item.id} to knowledge base (${item.content.length} chars)`);\n        const { pinecone: pc } = await initializePinecone();\n        if (!pc) {\n            throw new Error(\"Failed to initialize Pinecone\");\n        }\n        const index = pc.Index(process.env.PINECONE_INDEX_NAME || \"aven-support-index\");\n        const { generateEmbedding } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./openai */ \"(rsc)/./src/lib/openai.ts\"));\n        // Chunk large content\n        const chunks = chunkContent(item.content);\n        console.log(`Split into ${chunks.length} chunks`);\n        const upsertData = [];\n        for(let i = 0; i < chunks.length; i++){\n            const chunk = chunks[i];\n            const chunkId = chunks.length > 1 ? `${item.id}-chunk-${i + 1}` : item.id;\n            try {\n                // Generate embedding for the chunk\n                const embedding = await generateEmbedding(chunk);\n                upsertData.push({\n                    id: chunkId,\n                    values: embedding,\n                    metadata: {\n                        title: item.title,\n                        content: chunk,\n                        url: item.url,\n                        category: item.category,\n                        chunkIndex: i + 1,\n                        totalChunks: chunks.length,\n                        contentLength: chunk.length\n                    }\n                });\n                console.log(`✓ Prepared chunk ${i + 1}/${chunks.length} (${chunk.length} chars)`);\n            } catch (embeddingError) {\n                console.error(`Error generating embedding for chunk ${i + 1}:`, embeddingError);\n                continue; // Skip this chunk but continue with others\n            }\n        }\n        if (upsertData.length > 0) {\n            // Upsert to Pinecone in batches\n            const batchSize = 10;\n            for(let i = 0; i < upsertData.length; i += batchSize){\n                const batch = upsertData.slice(i, i + batchSize);\n                await index.upsert(batch);\n                console.log(`✓ Upserted batch ${Math.floor(i / batchSize) + 1}`);\n            }\n            console.log(`Successfully added item ${item.id} to knowledge base (${upsertData.length} chunks)`);\n        } else {\n            console.warn(`No valid chunks created for item ${item.id}`);\n        }\n    } catch (error) {\n        console.error(\"Error adding to knowledge base:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/pinecone.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/rag-pipeline.ts":
/*!*********************************!*\
  !*** ./src/lib/rag-pipeline.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RAGPipeline: () => (/* binding */ RAGPipeline),\n/* harmony export */   processWithRAGPipeline: () => (/* binding */ processWithRAGPipeline)\n/* harmony export */ });\n/* harmony import */ var _pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n/* harmony import */ var _memory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./memory */ \"(rsc)/./src/lib/memory.ts\");\n/* harmony import */ var _openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./openai */ \"(rsc)/./src/lib/openai.ts\");\n/* harmony import */ var _langfuse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./langfuse */ \"(rsc)/./src/lib/langfuse.ts\");\n\n\n\n\nclass RAGPipeline {\n    constructor(onProgressUpdate){\n        this.steps = [];\n        this.searchResults = [];\n        this.onProgressUpdate = onProgressUpdate;\n    }\n    addStep(step, title, description, status = \"pending\") {\n        const stepId = `${step}-${Date.now()}`;\n        const newStep = {\n            id: stepId,\n            step,\n            title,\n            description,\n            status,\n            timestamp: new Date()\n        };\n        this.steps.push(newStep);\n        this.emitProgress();\n        return stepId;\n    }\n    updateStep(stepId, updates) {\n        const stepIndex = this.steps.findIndex((s)=>s.id === stepId);\n        if (stepIndex !== -1) {\n            this.steps[stepIndex] = {\n                ...this.steps[stepIndex],\n                ...updates\n            };\n            this.emitProgress();\n        }\n    }\n    emitProgress() {\n        this.onProgressUpdate?.(this.steps, this.searchResults);\n    }\n    async delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    async process(query, userId) {\n        this.steps = [];\n        this.searchResults = [];\n        // Initialize LangFuse tracing\n        const sessionId = `rag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        this.tracer = (0,_langfuse__WEBPACK_IMPORTED_MODULE_3__.createRAGTrace)(sessionId, userId, query);\n        const startTime = Date.now();\n        try {\n            // Step 1: Query Analysis\n            const analysisStepId = this.addStep(\"analyzing\", \"Analyzing Query\", \"Understanding your question and determining the best approach...\", \"active\");\n            this.tracer?.startStep(\"query_analysis\", {\n                query\n            }, {\n                step: \"analyzing\"\n            });\n            await this.delay(800) // Simulate processing time\n            ;\n            const analysisResult = {\n                queryType: \"product_information\",\n                complexity: \"medium\"\n            };\n            this.tracer?.endStep(analysisResult, {\n                duration_ms: 800\n            });\n            this.updateStep(analysisStepId, {\n                status: \"complete\",\n                description: \"Query analyzed - identified as Aven HELOC inquiry\",\n                details: analysisResult\n            });\n            // Step 2: Memory Search\n            const memoryStepId = this.addStep(\"searching_memory\", \"Searching Conversation Memory\", \"Looking for relevant context from previous conversations...\", \"active\");\n            this.tracer?.startStep(\"memory_search\", {\n                query,\n                userId\n            }, {\n                step: \"searching_memory\"\n            });\n            const memoryStartTime = Date.now();\n            const memoryResults = await (0,_memory__WEBPACK_IMPORTED_MODULE_1__.searchMemory)(query, userId);\n            this.tracer?.logRetrieval(\"memory_search\", query, memoryResults, {\n                type: \"conversation_memory\",\n                results_count: memoryResults.length,\n                duration_ms: Date.now() - memoryStartTime\n            });\n            this.tracer?.endStep({\n                results_count: memoryResults.length\n            }, {\n                duration_ms: Date.now() - memoryStartTime\n            });\n            // Convert memory results to SearchResult format\n            const memorySearchResults = memoryResults.slice(0, 3).map((memory, index)=>({\n                    id: `memory-${index}`,\n                    title: `Previous Conversation`,\n                    content: memory.text || memory.content || \"\",\n                    source: \"conversation_memory\",\n                    score: 0.85,\n                    type: \"memory\"\n                }));\n            this.searchResults.push(...memorySearchResults);\n            this.updateStep(memoryStepId, {\n                status: \"complete\",\n                description: `Found ${memoryResults.length} relevant memories`,\n                results: memoryResults\n            });\n            // Step 3: Knowledge Base Search\n            const knowledgeStepId = this.addStep(\"searching_knowledge\", \"Searching Knowledge Base\", \"Finding relevant documents about Aven services...\", \"active\");\n            this.tracer?.startStep(\"knowledge_search\", {\n                query\n            }, {\n                step: \"searching_knowledge\"\n            });\n            const knowledgeStartTime = Date.now();\n            const knowledgeResults = await (0,_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(query);\n            this.tracer?.logRetrieval(\"knowledge_search\", query, knowledgeResults, {\n                type: \"vector_search\",\n                results_count: knowledgeResults.length,\n                duration_ms: Date.now() - knowledgeStartTime,\n                index: \"pinecone\"\n            });\n            this.tracer?.endStep({\n                results_count: knowledgeResults.length\n            }, {\n                duration_ms: Date.now() - knowledgeStartTime\n            });\n            // Convert knowledge results to SearchResult format\n            const knowledgeSearchResults = knowledgeResults.map((doc, index)=>({\n                    id: `knowledge-${index}`,\n                    title: doc.title,\n                    content: doc.content.substring(0, 200) + \"...\",\n                    source: doc.url,\n                    score: 0.9,\n                    type: \"knowledge\"\n                }));\n            this.searchResults.push(...knowledgeSearchResults);\n            this.updateStep(knowledgeStepId, {\n                status: \"complete\",\n                description: `Retrieved ${knowledgeResults.length} relevant documents`,\n                results: knowledgeResults\n            });\n            // Step 4: Document Reranking\n            const rerankStepId = this.addStep(\"reranking\", \"Reranking Documents\", \"Scoring and filtering documents for relevance...\", \"active\");\n            this.tracer?.startStep(\"document_reranking\", {\n                total_documents: this.searchResults.length\n            }, {\n                step: \"reranking\"\n            });\n            await this.delay(600);\n            // Simulate reranking by sorting search results by score\n            this.searchResults.sort((a, b)=>b.score - a.score);\n            const rerankResult = {\n                reranked_count: this.searchResults.length,\n                top_score: this.searchResults[0]?.score || 0,\n                score_distribution: this.searchResults.slice(0, 5).map((r)=>r.score)\n            };\n            this.tracer?.endStep(rerankResult, {\n                duration_ms: 600\n            });\n            this.updateStep(rerankStepId, {\n                status: \"complete\",\n                description: `Reranked ${this.searchResults.length} documents by relevance`,\n                details: {\n                    topScore: this.searchResults[0]?.score || 0\n                }\n            });\n            // Step 5: Context Assembly\n            const assemblyStepId = this.addStep(\"assembling\", \"Assembling Context\", \"Preparing the best context for generating your answer...\", \"active\");\n            this.tracer?.startStep(\"context_assembly\", {\n                memory_items: memoryResults.length,\n                knowledge_items: knowledgeResults.length\n            }, {\n                step: \"assembling\"\n            });\n            await this.delay(400);\n            const memoryContext = memoryResults.length > 0 ? `Previous conversation context:\\n${memoryResults.slice(0, 2).map((m)=>m.text).join(\"\\n\")}` : \"No previous conversation context found.\";\n            const assemblyResult = {\n                memoryItems: memoryResults.length,\n                knowledgeItems: knowledgeResults.length,\n                context_length: memoryContext.length + knowledgeResults.reduce((acc, doc)=>acc + doc.content.length, 0)\n            };\n            this.tracer?.endStep(assemblyResult, {\n                duration_ms: 400\n            });\n            this.updateStep(assemblyStepId, {\n                status: \"complete\",\n                description: \"Context assembled from top-ranked sources\",\n                details: {\n                    memoryItems: memoryResults.length,\n                    knowledgeItems: knowledgeResults.length\n                }\n            });\n            // Step 6: Response Generation\n            const generationStepId = this.addStep(\"generating\", \"Generating Response\", \"Creating a personalized answer based on the assembled context...\", \"active\");\n            this.tracer?.startStep(\"response_generation\", {\n                query,\n                context_items: knowledgeResults.length + memoryResults.length\n            }, {\n                step: \"generating\"\n            });\n            const generationStartTime = Date.now();\n            const response = await (0,_openai__WEBPACK_IMPORTED_MODULE_2__.generateResponse)(query, knowledgeResults, memoryContext);\n            // Log the LLM call for response generation\n            this.tracer?.logLLMCall(\"response_generation\", \"gpt-4.1-mini\", {\n                query,\n                context: memoryContext,\n                documents: knowledgeResults.length\n            }, {\n                response,\n                length: response.length\n            }, {\n                prompt_tokens: 1000,\n                completion_tokens: 500,\n                total_tokens: 1500\n            }, 0.05 // Estimated cost\n            );\n            const generationResult = {\n                response_length: response.length,\n                generation_time_ms: Date.now() - generationStartTime\n            };\n            this.tracer?.endStep(generationResult, {\n                duration_ms: Date.now() - generationStartTime\n            });\n            this.updateStep(generationStepId, {\n                status: \"complete\",\n                description: \"Response generated successfully\"\n            });\n            // Final step\n            this.addStep(\"complete\", \"Complete\", \"Your personalized answer is ready!\", \"complete\");\n            // End the entire trace\n            const totalDuration = Date.now() - startTime;\n            await this.tracer?.endTrace({\n                answer: response,\n                sources_count: knowledgeResults.length,\n                memory_items: memoryResults.length,\n                total_steps: this.steps.length\n            }, {\n                total_duration_ms: totalDuration,\n                pipeline_type: \"multi_step_rag\",\n                success: true\n            });\n            return {\n                answer: response,\n                sources: knowledgeResults,\n                steps: this.steps,\n                searchResults: this.searchResults,\n                traceId: this.tracer?.getTraceId()\n            };\n        } catch (error) {\n            console.error(\"Error in RAG pipeline:\", error);\n            // Log error in trace\n            await this.tracer?.endTrace({\n                error: error instanceof Error ? error.message : \"Unknown error\",\n                partial_results: {\n                    steps_completed: this.steps.filter((s)=>s.status === \"complete\").length,\n                    total_steps: this.steps.length\n                }\n            }, {\n                total_duration_ms: Date.now() - startTime,\n                pipeline_type: \"multi_step_rag\",\n                success: false,\n                error: true\n            });\n            // Mark current active step as error\n            const activeStep = this.steps.find((s)=>s.status === \"active\");\n            if (activeStep) {\n                this.updateStep(activeStep.id, {\n                    status: \"error\",\n                    description: \"An error occurred during processing\"\n                });\n            }\n            throw error;\n        }\n    }\n}\nasync function processWithRAGPipeline(query, userId, onProgress) {\n    const pipeline = new RAGPipeline(onProgress);\n    return pipeline.process(query, userId);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/rag-pipeline.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/agent-memory.ts":
/*!***********************************!*\
  !*** ./src/utils/agent-memory.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentMemory: () => (/* binding */ AgentMemory),\n/* harmony export */   agentMemory: () => (/* binding */ agentMemory)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\nclass AgentMemory {\n    /**\n   * MEMORY TOOLS - as suggested: \"You add a tool that saves memories. Then you add a tool that reads memories\"\n   */ async saveMemory(content, type, metadata) {\n        const memoryId = `memory-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\n        const memory = {\n            id: memoryId,\n            type,\n            content,\n            metadata: {\n                category: metadata.category || \"general\",\n                topics: metadata.topics || [],\n                confidence: metadata.confidence || 0.8,\n                source: metadata.source || \"system\",\n                ...metadata\n            },\n            createdAt: new Date(),\n            lastAccessed: new Date(),\n            accessCount: 1\n        };\n        // Generate embedding for semantic search\n        try {\n            const embeddingResponse = await openai.embeddings.create({\n                model: \"text-embedding-3-small\",\n                input: content,\n                dimensions: 1024\n            });\n            memory.embedding = embeddingResponse.data[0].embedding;\n        } catch (error) {\n            console.error(\"Error generating memory embedding:\", error);\n        }\n        this.memories.set(memoryId, memory);\n        // Cleanup old memories if we exceed limit\n        if (this.memories.size > this.maxMemories) {\n            this.cleanupOldMemories();\n        }\n        console.log(`💾 Saved memory: ${type} - ${content.substring(0, 50)}...`);\n        return memoryId;\n    }\n    async readMemories(query, type, limit = 5) {\n        let relevantMemories = Array.from(this.memories.values());\n        // Filter by type if specified\n        if (type) {\n            relevantMemories = relevantMemories.filter((m)=>m.type === type);\n        }\n        // Generate query embedding for semantic search\n        let queryEmbedding = [];\n        try {\n            const embeddingResponse = await openai.embeddings.create({\n                model: \"text-embedding-3-small\",\n                input: query,\n                dimensions: 1024\n            });\n            queryEmbedding = embeddingResponse.data[0].embedding;\n        } catch (error) {\n            console.error(\"Error generating query embedding for memory search:\", error);\n            // Fallback to keyword search\n            return this.keywordSearchMemories(query, relevantMemories, limit);\n        }\n        // Calculate similarity scores\n        const memoryScores = relevantMemories.filter((memory)=>memory.embedding).map((memory)=>({\n                memory,\n                similarity: this.cosineSimilarity(queryEmbedding, memory.embedding)\n            })).sort((a, b)=>b.similarity - a.similarity).slice(0, limit);\n        // Update access tracking\n        memoryScores.forEach(({ memory })=>{\n            memory.lastAccessed = new Date();\n            memory.accessCount++;\n        });\n        console.log(`🔍 Found ${memoryScores.length} relevant memories for: \"${query}\"`);\n        return memoryScores.map(({ memory })=>memory);\n    }\n    /**\n   * SEMANTIC CACHING - Speed up repeated searches\n   */ async checkSemanticCache(query) {\n        // Generate query embedding\n        let queryEmbedding;\n        try {\n            const embeddingResponse = await openai.embeddings.create({\n                model: \"text-embedding-3-small\",\n                input: query,\n                dimensions: 1024\n            });\n            queryEmbedding = embeddingResponse.data[0].embedding;\n        } catch (error) {\n            console.error(\"Error generating embedding for cache check:\", error);\n            return null;\n        }\n        // Check for similar cached queries\n        for (const [cacheKey, cacheEntry] of this.semanticCache.entries()){\n            const similarity = this.cosineSimilarity(queryEmbedding, cacheEntry.queryEmbedding);\n            if (similarity > this.cacheHitThreshold) {\n                cacheEntry.hitCount++;\n                console.log(`⚡ Cache hit for \"${query}\" (similarity: ${similarity.toFixed(3)})`);\n                return cacheEntry.results;\n            }\n        }\n        return null;\n    }\n    async addToSemanticCache(query, results) {\n        try {\n            const embeddingResponse = await openai.embeddings.create({\n                model: \"text-embedding-3-small\",\n                input: query,\n                dimensions: 1024\n            });\n            const cacheKey = `cache-${Date.now()}`;\n            const cacheEntry = {\n                query,\n                results,\n                timestamp: new Date(),\n                hitCount: 0,\n                queryEmbedding: embeddingResponse.data[0].embedding\n            };\n            this.semanticCache.set(cacheKey, cacheEntry);\n            // Cleanup old cache entries (keep 100 most recent)\n            if (this.semanticCache.size > 100) {\n                const entries = Array.from(this.semanticCache.entries());\n                entries.sort((a, b)=>b[1].timestamp.getTime() - a[1].timestamp.getTime());\n                // Keep top 100, remove rest\n                for(let i = 100; i < entries.length; i++){\n                    this.semanticCache.delete(entries[i][0]);\n                }\n            }\n            console.log(`💾 Added to semantic cache: \"${query}\"`);\n        } catch (error) {\n            console.error(\"Error adding to semantic cache:\", error);\n        }\n    }\n    /**\n   * CONTEXTUAL MEMORY - Learn from user interactions\n   */ async learnFromInteraction(userQuery, agentResponse, searchResults, userId) {\n        // Save search pattern\n        if (searchResults.length > 0) {\n            const searchPattern = `Query: \"${userQuery}\" found ${searchResults.length} results about ${searchResults[0].category}`;\n            await this.saveMemory(searchPattern, \"search_pattern\", {\n                userId,\n                category: searchResults[0].category,\n                topics: searchResults.map((r)=>r.category),\n                confidence: 0.9,\n                source: \"user_interaction\"\n            });\n        }\n        // Extract domain knowledge from successful interactions\n        if (searchResults.length > 0) {\n            const domainKnowledge = `Customers asking about \"${userQuery}\" are typically interested in: ${searchResults.map((r)=>r.title).join(\", \")}`;\n            await this.saveMemory(domainKnowledge, \"domain_knowledge\", {\n                userId,\n                category: \"customer_patterns\",\n                topics: [\n                    userQuery.toLowerCase()\n                ],\n                confidence: 0.8,\n                source: \"interaction_analysis\"\n            });\n        }\n    }\n    /**\n   * RETRIEVAL ENHANCEMENT - Use memories to improve search\n   */ async enhanceQuery(originalQuery, userId) {\n        // Look for relevant memories\n        const relevantMemories = await this.readMemories(originalQuery, \"search_pattern\", 3);\n        if (relevantMemories.length === 0) {\n            return originalQuery;\n        }\n        // Use LLM to enhance the query based on memory\n        const memoryContext = relevantMemories.map((m)=>m.content).join(\"\\n\");\n        const prompt = `Based on previous successful searches, enhance this query for better results.\n\nOriginal Query: \"${originalQuery}\"\n\nSimilar Past Searches:\n${memoryContext}\n\nGenerate an enhanced query that incorporates learnings from past successful searches. Keep it concise and focused.`;\n        try {\n            const response = await openai.chat.completions.create({\n                model: \"gpt-4o-mini\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 100\n            });\n            const enhancedQuery = response.choices[0]?.message?.content?.trim() || originalQuery;\n            console.log(`🧠 Enhanced query: \"${originalQuery}\" → \"${enhancedQuery}\"`);\n            return enhancedQuery;\n        } catch (error) {\n            console.error(\"Error enhancing query with memory:\", error);\n            return originalQuery;\n        }\n    }\n    /**\n   * UTILITY METHODS\n   */ cosineSimilarity(a, b) {\n        if (a.length !== b.length) return 0;\n        let dotProduct = 0;\n        let normA = 0;\n        let normB = 0;\n        for(let i = 0; i < a.length; i++){\n            dotProduct += a[i] * b[i];\n            normA += a[i] * a[i];\n            normB += b[i] * b[i];\n        }\n        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));\n    }\n    keywordSearchMemories(query, memories, limit) {\n        const queryLower = query.toLowerCase();\n        const queryWords = queryLower.split(/\\s+/);\n        return memories.map((memory)=>{\n            const contentLower = memory.content.toLowerCase();\n            const matchScore = queryWords.reduce((score, word)=>{\n                return contentLower.includes(word) ? score + 1 : score;\n            }, 0) / queryWords.length;\n            return {\n                memory,\n                score: matchScore\n            };\n        }).filter(({ score })=>score > 0).sort((a, b)=>b.score - a.score).slice(0, limit).map(({ memory })=>memory);\n    }\n    cleanupOldMemories() {\n        const memories = Array.from(this.memories.entries());\n        // Sort by last accessed (oldest first)\n        memories.sort((a, b)=>a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime());\n        // Remove oldest 10%\n        const toRemove = Math.floor(memories.length * 0.1);\n        for(let i = 0; i < toRemove; i++){\n            this.memories.delete(memories[i][0]);\n        }\n        console.log(`🧹 Cleaned up ${toRemove} old memories`);\n    }\n    /**\n   * MEMORY ANALYTICS\n   */ getMemoryStats() {\n        const memories = Array.from(this.memories.values());\n        const categoryCount = new Map();\n        memories.forEach((memory)=>{\n            const count = categoryCount.get(memory.metadata.category) || 0;\n            categoryCount.set(memory.metadata.category, count + 1);\n        });\n        const topCategories = Array.from(categoryCount.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([category])=>category);\n        const totalCacheHits = Array.from(this.semanticCache.values()).reduce((sum, cache)=>sum + cache.hitCount, 0);\n        const cacheHitRate = this.semanticCache.size > 0 ? totalCacheHits / this.semanticCache.size : 0;\n        return {\n            totalMemories: this.memories.size,\n            cacheSize: this.semanticCache.size,\n            topCategories,\n            cacheHitRate\n        };\n    }\n    constructor(){\n        this.memories = new Map();\n        this.semanticCache = new Map();\n        this.maxMemories = 1000;\n        this.cacheHitThreshold = 0.85 // Cosine similarity threshold\n        ;\n    }\n}\nconst agentMemory = new AgentMemory();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/agent-memory.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/agentic-retrieval.ts":
/*!****************************************!*\
  !*** ./src/utils/agentic-retrieval.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgenticRetrieval: () => (/* binding */ AgenticRetrieval),\n/* harmony export */   agenticRetrieval: () => (/* binding */ agenticRetrieval)\n/* harmony export */ });\n/* harmony import */ var _lib_pinecone__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/pinecone */ \"(rsc)/./src/lib/pinecone.ts\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\n/**\n * Agentic Retrieval System\n * Inspired by Augment's SWE-bench approach - agents with persistent search tools\n */ class AgenticRetrieval {\n    constructor(){\n        this.maxIterations = 3;\n        this.searchHistory = [];\n        this.tools = [\n            {\n                name: \"vector_search\",\n                description: \"Search using semantic similarity in vector database\",\n                execute: this.vectorSearch.bind(this)\n            },\n            {\n                name: \"keyword_search\",\n                description: \"Search using keyword matching for specific terms\",\n                execute: this.keywordSearch.bind(this)\n            },\n            {\n                name: \"category_search\",\n                description: \"Search within specific content categories (heloc, rates, rewards, etc)\",\n                execute: this.categorySearch.bind(this)\n            },\n            {\n                name: \"related_search\",\n                description: \"Find related content based on previous search results\",\n                execute: this.relatedSearch.bind(this)\n            }\n        ];\n    }\n    async search(userQuery) {\n        this.searchHistory = [];\n        let allResults = [];\n        let currentQuery = userQuery;\n        let iteration = 0;\n        console.log(`🤖 Starting agentic retrieval for: \"${userQuery}\"`);\n        while(iteration < this.maxIterations){\n            iteration++;\n            // Agent decides which tool to use\n            const selectedTool = await this.selectTool(currentQuery, allResults, iteration);\n            console.log(`🔧 Iteration ${iteration}: Using ${selectedTool.name}`);\n            this.searchHistory.push(`${iteration}. ${selectedTool.name}: ${currentQuery}`);\n            // Execute the selected tool\n            const results = await selectedTool.execute(currentQuery, this.getContext(allResults));\n            // Merge and deduplicate results\n            allResults = this.mergeResults(allResults, results);\n            // Agent decides if it should continue searching\n            const shouldContinue = await this.shouldContinueSearch(userQuery, allResults, iteration);\n            if (!shouldContinue || allResults.length >= 8) {\n                console.log(`✅ Stopping search at iteration ${iteration}: sufficient results found`);\n                break;\n            }\n            // Generate next search query based on results\n            currentQuery = await this.generateNextQuery(userQuery, allResults, iteration);\n        }\n        const confidence = this.calculateConfidence(userQuery, allResults);\n        return {\n            results: allResults.slice(0, 5),\n            searchPath: this.searchHistory,\n            confidence\n        };\n    }\n    async selectTool(query, currentResults, iteration) {\n        // First iteration: always start with vector search\n        if (iteration === 1) {\n            return this.tools[0] // vector_search\n            ;\n        }\n        // Use LLM to select the best tool based on context\n        const prompt = `You are a search strategist. Given the user query and current search results, select the best search tool.\n\nUser Query: \"${query}\"\nCurrent Results: ${currentResults.length} documents found\nIteration: ${iteration}\n\nAvailable Tools:\n${this.tools.map((tool, i)=>`${i + 1}. ${tool.name}: ${tool.description}`).join(\"\\n\")}\n\nSearch History: ${this.searchHistory.join(\"; \")}\n\nSelect the tool number (1-${this.tools.length}) that would most likely find additional relevant information.`;\n        try {\n            const response = await openai.chat.completions.create({\n                model: \"gpt-4o-mini\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                temperature: 0.1,\n                max_tokens: 50\n            });\n            const toolNumber = parseInt(response.choices[0]?.message?.content?.trim() || \"1\");\n            const selectedIndex = Math.max(0, Math.min(toolNumber - 1, this.tools.length - 1));\n            return this.tools[selectedIndex];\n        } catch (error) {\n            console.error(\"Error selecting tool:\", error);\n            return this.tools[1] // Fallback to keyword search\n            ;\n        }\n    }\n    async shouldContinueSearch(originalQuery, currentResults, iteration) {\n        if (iteration >= this.maxIterations || currentResults.length === 0) {\n            return false;\n        }\n        const prompt = `Evaluate if more search is needed to answer this question comprehensively.\n\nQuestion: \"${originalQuery}\"\nCurrent Results: ${currentResults.length} documents\nDocuments Cover: ${currentResults.map((r)=>r.title).join(\", \")}\nIteration: ${iteration}/${this.maxIterations}\n\nDo we have sufficient information to answer the question? Respond with only \"YES\" or \"NO\".`;\n        try {\n            const response = await openai.chat.completions.create({\n                model: \"gpt-4o-mini\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                temperature: 0.1,\n                max_tokens: 10\n            });\n            const answer = response.choices[0]?.message?.content?.trim().toLowerCase();\n            return answer === \"no\";\n        } catch (error) {\n            console.error(\"Error evaluating search completion:\", error);\n            return false;\n        }\n    }\n    async generateNextQuery(originalQuery, currentResults, iteration) {\n        const prompt = `Generate a focused search query to find missing information.\n\nOriginal Question: \"${originalQuery}\"\nFound Documents: ${currentResults.map((r)=>r.title).join(\", \")}\nIteration: ${iteration}\n\nWhat specific aspect of the question still needs more information? Generate a concise search query (max 10 words).`;\n        try {\n            const response = await openai.chat.completions.create({\n                model: \"gpt-4o-mini\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 50\n            });\n            return response.choices[0]?.message?.content?.trim() || originalQuery;\n        } catch (error) {\n            console.error(\"Error generating next query:\", error);\n            return originalQuery;\n        }\n    }\n    // Search tool implementations\n    async vectorSearch(query) {\n        return await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(query);\n    }\n    async keywordSearch(query) {\n        // Extract key terms and search for exact matches\n        const keywords = query.toLowerCase().match(/\\b\\w{3,}\\b/g) || [];\n        const keywordQuery = keywords.join(\" \");\n        const results = await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(keywordQuery);\n        // Filter for results that contain the actual keywords\n        return results.filter((result)=>{\n            const content = (result.title + \" \" + result.content).toLowerCase();\n            return keywords.some((keyword)=>content.includes(keyword));\n        });\n    }\n    async categorySearch(query, context) {\n        // Determine relevant categories\n        const categories = [\n            \"heloc\",\n            \"rates-fees\",\n            \"rewards\",\n            \"application\",\n            \"support\",\n            \"eligibility\"\n        ];\n        const prompt = `What Aven product categories are most relevant to this query?\nQuery: \"${query}\"\nAvailable categories: ${categories.join(\", \")}\n\nSelect 1-2 most relevant categories (comma-separated).`;\n        try {\n            const response = await openai.chat.completions.create({\n                model: \"gpt-4o-mini\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                temperature: 0.1,\n                max_tokens: 30\n            });\n            const selectedCategories = response.choices[0]?.message?.content?.trim().split(\",\").map((c)=>c.trim()) || [\n                \"heloc\"\n            ];\n            const allResults = await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(query);\n            return allResults.filter((result)=>selectedCategories.some((category)=>result.category === category));\n        } catch (error) {\n            console.error(\"Error in category search:\", error);\n            return await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(query);\n        }\n    }\n    async relatedSearch(query, context) {\n        if (!context) {\n            return [];\n        }\n        // Generate related search terms based on context\n        const prompt = `Based on these documents, what related terms should we search for?\n\nOriginal Query: \"${query}\"\nDocument Context: ${context.substring(0, 500)}...\n\nGenerate 2-3 related search terms that might find additional relevant information.`;\n        try {\n            const response = await openai.chat.completions.create({\n                model: \"gpt-4o-mini\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 50\n            });\n            const relatedTerms = response.choices[0]?.message?.content?.trim() || query;\n            return await (0,_lib_pinecone__WEBPACK_IMPORTED_MODULE_0__.searchKnowledgeBase)(relatedTerms);\n        } catch (error) {\n            console.error(\"Error in related search:\", error);\n            return [];\n        }\n    }\n    mergeResults(existing, newResults) {\n        const seen = new Set(existing.map((r)=>r.id));\n        const uniqueNew = newResults.filter((r)=>!seen.has(r.id));\n        return [\n            ...existing,\n            ...uniqueNew\n        ];\n    }\n    getContext(results) {\n        return results.slice(0, 3).map((r)=>`${r.title}: ${r.content.substring(0, 200)}...`).join(\"\\n\");\n    }\n    calculateConfidence(query, results) {\n        if (results.length === 0) return 0;\n        if (results.length >= 3) return 0.9;\n        if (results.length >= 2) return 0.7;\n        return 0.5;\n    }\n}\nconst agenticRetrieval = new AgenticRetrieval();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/agentic-retrieval.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/meeting-scheduler.ts":
/*!****************************************!*\
  !*** ./src/utils/meeting-scheduler.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeetingScheduler: () => (/* binding */ MeetingScheduler),\n/* harmony export */   meetingScheduler: () => (/* binding */ meetingScheduler)\n/* harmony export */ });\nclass MeetingScheduler {\n    constructor(){\n        this.availableSlots = new Map();\n        this.scheduledMeetings = new Map();\n        this.initializeAvailableSlots();\n    }\n    /**\n   * Initialize available meeting slots\n   */ initializeAvailableSlots() {\n        const today = new Date();\n        const businessDays = [];\n        // Generate next 30 business days\n        for(let i = 1; i <= 45; i++){\n            const date = new Date(today);\n            date.setDate(today.getDate() + i);\n            // Skip weekends\n            if (date.getDay() !== 0 && date.getDay() !== 6) {\n                businessDays.push(date);\n            }\n            if (businessDays.length >= 30) break;\n        }\n        // Create time slots for each business day\n        businessDays.forEach((date)=>{\n            const dateStr = date.toISOString().split(\"T\")[0];\n            // Available times: 9 AM to 5 PM, every hour\n            for(let hour = 9; hour <= 17; hour++){\n                const timeStr = `${hour.toString().padStart(2, \"0\")}:00`;\n                const slotId = `${dateStr}-${timeStr}`;\n                this.availableSlots.set(slotId, {\n                    id: slotId,\n                    date: dateStr,\n                    time: timeStr,\n                    duration: 60,\n                    available: true,\n                    meetingType: [\n                        \"consultation\",\n                        \"application_help\",\n                        \"account_review\",\n                        \"general\"\n                    ]\n                });\n            }\n        });\n    }\n    /**\n   * Get available meeting slots\n   */ getAvailableSlots(meetingType, startDate, endDate) {\n        const slots = Array.from(this.availableSlots.values());\n        return slots.filter((slot)=>{\n            if (!slot.available) return false;\n            if (meetingType && !slot.meetingType.includes(meetingType)) {\n                return false;\n            }\n            if (startDate && slot.date < startDate) return false;\n            if (endDate && slot.date > endDate) return false;\n            return true;\n        }).sort((a, b)=>{\n            if (a.date !== b.date) return a.date.localeCompare(b.date);\n            return a.time.localeCompare(b.time);\n        });\n    }\n    /**\n   * Schedule a meeting\n   */ async scheduleMeeting(request) {\n        const { userId, preferredDate, preferredTime, meetingType, customerNotes } = request;\n        // Validate date format\n        if (!this.isValidDate(preferredDate)) {\n            return {\n                success: false,\n                message: \"Please provide a valid date in YYYY-MM-DD format.\"\n            };\n        }\n        // Validate time format\n        if (!this.isValidTime(preferredTime)) {\n            return {\n                success: false,\n                message: \"Please provide a valid time in HH:MM format (24-hour).\"\n            };\n        }\n        // Check if the requested slot is available\n        const slotId = `${preferredDate}-${preferredTime}`;\n        const slot = this.availableSlots.get(slotId);\n        if (!slot || !slot.available) {\n            const alternatives = this.getAvailableSlots(meetingType, preferredDate).slice(0, 3).map((s)=>`${s.date} at ${s.time}`).join(\", \");\n            return {\n                success: false,\n                message: `The requested time slot is not available. Alternative times: ${alternatives || \"Please check our available slots.\"}`\n            };\n        }\n        if (!slot.meetingType.includes(meetingType)) {\n            return {\n                success: false,\n                message: `The requested meeting type \"${meetingType}\" is not available for this time slot.`\n            };\n        }\n        // Create the meeting\n        const meetingId = `meeting-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\n        const meeting = {\n            id: meetingId,\n            userId,\n            date: preferredDate,\n            time: preferredTime,\n            duration: slot.duration,\n            meetingType,\n            status: \"scheduled\",\n            customerNotes,\n            reminderSent: false,\n            createdAt: new Date()\n        };\n        // Book the slot\n        slot.available = false;\n        this.scheduledMeetings.set(meetingId, meeting);\n        console.log(`📅 Meeting scheduled: ${meetingId} for ${userId} on ${preferredDate} at ${preferredTime}`);\n        return {\n            success: true,\n            meeting,\n            message: `Your meeting has been scheduled for ${preferredDate} at ${preferredTime}. You will receive a confirmation email shortly.`\n        };\n    }\n    /**\n   * Cancel a meeting\n   */ async cancelMeeting(meetingId, userId) {\n        const meeting = this.scheduledMeetings.get(meetingId);\n        if (!meeting) {\n            return {\n                success: false,\n                message: \"Meeting not found.\"\n            };\n        }\n        if (meeting.userId !== userId) {\n            return {\n                success: false,\n                message: \"You can only cancel your own meetings.\"\n            };\n        }\n        if (meeting.status === \"cancelled\") {\n            return {\n                success: false,\n                message: \"This meeting is already cancelled.\"\n            };\n        }\n        // Cancel the meeting and free up the slot\n        meeting.status = \"cancelled\";\n        const slotId = `${meeting.date}-${meeting.time}`;\n        const slot = this.availableSlots.get(slotId);\n        if (slot) {\n            slot.available = true;\n        }\n        console.log(`❌ Meeting cancelled: ${meetingId}`);\n        return {\n            success: true,\n            message: `Your meeting on ${meeting.date} at ${meeting.time} has been cancelled.`\n        };\n    }\n    /**\n   * Get user's scheduled meetings\n   */ getUserMeetings(userId) {\n        return Array.from(this.scheduledMeetings.values()).filter((meeting)=>meeting.userId === userId && meeting.status !== \"cancelled\").sort((a, b)=>{\n            if (a.date !== b.date) return a.date.localeCompare(b.date);\n            return a.time.localeCompare(b.time);\n        });\n    }\n    /**\n   * Reschedule a meeting\n   */ async rescheduleMeeting(meetingId, userId, newDate, newTime) {\n        const meeting = this.scheduledMeetings.get(meetingId);\n        if (!meeting || meeting.userId !== userId) {\n            return {\n                success: false,\n                message: \"Meeting not found or access denied.\"\n            };\n        }\n        // Free up the old slot\n        const oldSlotId = `${meeting.date}-${meeting.time}`;\n        const oldSlot = this.availableSlots.get(oldSlotId);\n        if (oldSlot) {\n            oldSlot.available = true;\n        }\n        // Try to schedule in the new slot\n        const rescheduleRequest = {\n            userId,\n            preferredDate: newDate,\n            preferredTime: newTime,\n            meetingType: meeting.meetingType,\n            customerNotes: meeting.customerNotes\n        };\n        const result = await this.scheduleMeeting(rescheduleRequest);\n        if (result.success) {\n            // Remove old meeting and update with new one\n            this.scheduledMeetings.delete(meetingId);\n            console.log(`🔄 Meeting rescheduled: ${meetingId} from ${meeting.date} ${meeting.time} to ${newDate} ${newTime}`);\n        } else {\n            // Restore the old slot if rescheduling failed\n            if (oldSlot) {\n                oldSlot.available = false;\n            }\n        }\n        return result;\n    }\n    /**\n   * Natural language meeting scheduling\n   */ async parseAndSchedule(naturalLanguageRequest, userId) {\n        const parsed = this.parseNaturalLanguageRequest(naturalLanguageRequest);\n        if (!parsed) {\n            return {\n                success: false,\n                message: 'I couldn\\'t understand your scheduling request. Please specify a date and time, like \"Schedule a meeting for tomorrow at 2 PM\" or \"Book an appointment on December 15th at 10:00 AM\".'\n            };\n        }\n        const meetingRequest = {\n            userId,\n            preferredDate: parsed.date,\n            preferredTime: parsed.time,\n            meetingType: parsed.meetingType || \"general\",\n            customerNotes: parsed.notes\n        };\n        const result = await this.scheduleMeeting(meetingRequest);\n        return {\n            ...result,\n            parsedRequest: meetingRequest\n        };\n    }\n    /**\n   * Parse natural language scheduling requests\n   */ parseNaturalLanguageRequest(request) {\n        const lowerRequest = request.toLowerCase();\n        // Extract meeting type\n        let meetingType = \"general\";\n        if (lowerRequest.includes(\"consultation\") || lowerRequest.includes(\"consult\")) {\n            meetingType = \"consultation\";\n        } else if (lowerRequest.includes(\"application\") || lowerRequest.includes(\"apply\")) {\n            meetingType = \"application_help\";\n        } else if (lowerRequest.includes(\"review\") || lowerRequest.includes(\"account\")) {\n            meetingType = \"account_review\";\n        }\n        // Extract date\n        const today = new Date();\n        let targetDate = new Date();\n        // Handle relative dates\n        if (lowerRequest.includes(\"tomorrow\")) {\n            targetDate.setDate(today.getDate() + 1);\n        } else if (lowerRequest.includes(\"next week\")) {\n            targetDate.setDate(today.getDate() + 7);\n        } else if (lowerRequest.includes(\"monday\")) {\n            targetDate = this.getNextWeekday(1) // Monday = 1\n            ;\n        } else if (lowerRequest.includes(\"tuesday\")) {\n            targetDate = this.getNextWeekday(2);\n        } else if (lowerRequest.includes(\"wednesday\")) {\n            targetDate = this.getNextWeekday(3);\n        } else if (lowerRequest.includes(\"thursday\")) {\n            targetDate = this.getNextWeekday(4);\n        } else if (lowerRequest.includes(\"friday\")) {\n            targetDate = this.getNextWeekday(5);\n        } else {\n            // Try to extract explicit dates\n            const dateMatch = request.match(/(\\d{4}-\\d{2}-\\d{2})|(\\d{1,2}\\/\\d{1,2}\\/\\d{4})|(\\d{1,2}-\\d{1,2}-\\d{4})/);\n            if (dateMatch) {\n                const dateStr = dateMatch[0];\n                if (dateStr.includes(\"/\")) {\n                    const [month, day, year] = dateStr.split(\"/\");\n                    targetDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));\n                } else if (dateStr.includes(\"-\") && dateStr.length === 10) {\n                    targetDate = new Date(dateStr);\n                }\n            }\n        }\n        // Extract time\n        let time = \"10:00\" // default\n        ;\n        const timeMatch = request.match(/(\\d{1,2}):?(\\d{2})?\\s*(am|pm|AM|PM)?|\\b(\\d{1,2})\\s*(am|pm|AM|PM)\\b/);\n        if (timeMatch) {\n            let hour = parseInt(timeMatch[1] || timeMatch[4]);\n            const minute = timeMatch[2] ? parseInt(timeMatch[2]) : 0;\n            const period = timeMatch[3] || timeMatch[5];\n            if (period && period.toLowerCase() === \"pm\" && hour !== 12) {\n                hour += 12;\n            } else if (period && period.toLowerCase() === \"am\" && hour === 12) {\n                hour = 0;\n            }\n            time = `${hour.toString().padStart(2, \"0\")}:${minute.toString().padStart(2, \"0\")}`;\n        }\n        // Validate business hours (9 AM - 5 PM)\n        const hourNum = parseInt(time.split(\":\")[0]);\n        if (hourNum < 9 || hourNum > 17) {\n            time = \"10:00\" // Default to 10 AM if outside business hours\n            ;\n        }\n        const dateStr = targetDate.toISOString().split(\"T\")[0];\n        return {\n            date: dateStr,\n            time,\n            meetingType,\n            notes: request\n        };\n    }\n    getNextWeekday(targetDay) {\n        const today = new Date();\n        const currentDay = today.getDay();\n        const daysUntilTarget = targetDay === 0 ? 7 : targetDay // Sunday = 0, but we want next Sunday to be 7 days away\n        ;\n        let daysToAdd = (daysUntilTarget - currentDay + 7) % 7;\n        if (daysToAdd === 0) daysToAdd = 7 // If it's the same day, get next week's occurrence\n        ;\n        const targetDate = new Date(today);\n        targetDate.setDate(today.getDate() + daysToAdd);\n        return targetDate;\n    }\n    isValidDate(dateStr) {\n        const date = new Date(dateStr);\n        return date instanceof Date && !isNaN(date.getTime()) && dateStr.match(/^\\d{4}-\\d{2}-\\d{2}$/);\n    }\n    isValidTime(timeStr) {\n        return /^([01]?\\d|2[0-3]):([0-5]\\d)$/.test(timeStr);\n    }\n    /**\n   * Get meeting statistics\n   */ getMeetingStats() {\n        const meetings = Array.from(this.scheduledMeetings.values());\n        const today = new Date().toISOString().split(\"T\")[0];\n        const upcomingMeetings = meetings.filter((m)=>m.status === \"scheduled\" && m.date >= today).length;\n        const meetingsByType = meetings.reduce((acc, meeting)=>{\n            acc[meeting.meetingType] = (acc[meeting.meetingType] || 0) + 1;\n            return acc;\n        }, {});\n        return {\n            totalScheduled: meetings.length,\n            upcomingMeetings,\n            availableSlots: Array.from(this.availableSlots.values()).filter((s)=>s.available).length,\n            meetingsByType\n        };\n    }\n}\nconst meetingScheduler = new MeetingScheduler();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/meeting-scheduler.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai","vendor-chunks/mem0ai","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/@pinecone-database","vendor-chunks/ajv","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/whatwg-url","vendor-chunks/call-bind-apply-helpers","vendor-chunks/fast-uri","vendor-chunks/agentkeepalive","vendor-chunks/get-proto","vendor-chunks/tr46","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/cross-fetch","vendor-chunks/web-streams-polyfill","vendor-chunks/mustache","vendor-chunks/langfuse","vendor-chunks/langfuse-core","vendor-chunks/webidl-conversions","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/json-schema-traverse","vendor-chunks/humanize-ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/fast-deep-equal","vendor-chunks/event-target-shim","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/abort-controller","vendor-chunks/@sinclair"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falekramelaheehridoy%2FDesktop%2Fprojects%2Fai-customer-agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();