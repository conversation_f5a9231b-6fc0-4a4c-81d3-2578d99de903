"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/chat */ \"(app-pages-browser)/./src/lib/chat.ts\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _MemoryManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MemoryManager */ \"(app-pages-browser)/./src/components/MemoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hi! I'm here to help you with questions about your Aven HELOC Credit Card and financial services. How can I assist you today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11)));\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            var _response_sources;\n            const response = await (0,_lib_chat__WEBPACK_IMPORTED_MODULE_2__.sendChatMessage)(input, sessionId);\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: response.answer,\n                timestamp: new Date(),\n                sources: ((_response_sources = response.sources) === null || _response_sources === void 0 ? void 0 : _response_sources.map((s)=>s.url)) || []\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again later.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    const formatMessageWithCitations = (content, sources)=>{\n        // This function will be enhanced later to add inline citations\n        // For now, just return the content\n        return content;\n    };\n    const getSourceTitle = (url)=>{\n        // Extract a meaningful title from the URL\n        if (url.includes(\"aven.com\")) {\n            if (url.includes(\"support\")) return \"Aven Customer Support\";\n            if (url.includes(\"eligibility\")) return \"Aven Eligibility Requirements\";\n            if (url.includes(\"apply\")) return \"Aven Application Process\";\n            if (url.includes(\"compare\")) return \"Aven vs Traditional Credit Cards\";\n            return \"Aven HELOC Credit Card Information\";\n        }\n        return url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[700px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center \".concat(message.role === \"assistant\" ? \"bg-teal-400 text-white\" : \"bg-red-400 text-white\"),\n                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-800 leading-relaxed prose prose-sm max-w-none\",\n                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                                                        components: {\n                                                            h1: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            h2: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-base font-semibold text-gray-800 mb-2 mt-3\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            h3: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-800 mb-1 mt-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            ul: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"list-disc list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            ol: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                    className: \"list-decimal list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            li: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            p: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mb-2 last:mb-0\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 137,\n                                                                    columnNumber: 44\n                                                                }, void 0);\n                                                            },\n                                                            strong: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 49\n                                                                }, void 0);\n                                                            },\n                                                            code: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"bg-gray-200 px-1 py-0.5 rounded text-sm font-mono\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 47\n                                                                }, void 0);\n                                                            },\n                                                            a: (param)=>{\n                                                                let { href, children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: href,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        },\n                                                        children: formatMessageWithCitations(message.content, message.sources || [])\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-gray-600 mb-2\",\n                                                            children: \"\\uD83D\\uDCDA Sources:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid gap-2\",\n                                                            children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 mt-0.5\",\n                                                                            children: [\n                                                                                \"[\",\n                                                                                index + 1,\n                                                                                \"]\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: source,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: \"text-xs text-blue-600 hover:text-blue-800 hover:underline flex-1\",\n                                                                            children: getSourceTitle(source)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                            lineNumber: 166,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-2 ml-4\",\n                                            children: formatTime(message.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 w-10 h-10 bg-teal-400 rounded-full flex items-center justify-center text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-teal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-100 p-6 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 bg-gray-50 rounded-xl p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyDown: handleKeyPress,\n                                placeholder: \"Ask me anything about Aven...\",\n                                className: \"flex-1 bg-transparent border-none outline-none text-gray-800 placeholder-gray-500\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSend,\n                                disabled: isLoading || !input.trim(),\n                                className: \"flex-shrink-0 w-10 h-10 bg-red-400 hover:bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"AI is ready with memory!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MemoryManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                sessionId: sessionId\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"NPAh/rW6mc9RDStCgxuZiqmY5PM=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NoYXRJbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVtRDtBQUVQO0FBQ0Y7QUFDQztBQUU1QixTQUFTTTs7SUFDdEIsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdSLCtDQUFRQSxDQUFnQjtRQUN0RDtZQUNFUyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsU0FBUztZQUNUQyxXQUFXLElBQUlDO1FBQ2pCO0tBQ0Q7SUFDRCxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2YsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDZ0IsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDa0IsVUFBVSxHQUFHbEIsK0NBQVFBLENBQUMsSUFBTSxXQUF5Qm1CLE9BQWROLEtBQUtPLEdBQUcsSUFBRyxLQUErQyxPQUE1Q0QsS0FBS0UsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7SUFDcEcsTUFBTUMsaUJBQWlCdkIsNkNBQU1BLENBQWlCO0lBRTlDLE1BQU13QixpQkFBaUI7WUFDckJEO1NBQUFBLDBCQUFBQSxlQUFlRSxPQUFPLGNBQXRCRiw4Q0FBQUEsd0JBQXdCRyxjQUFjLENBQUM7WUFBRUMsVUFBVTtRQUFTO0lBQzlEO0lBRUExQixnREFBU0EsQ0FBQztRQUNSdUI7SUFDRixHQUFHO1FBQUNsQjtLQUFTO0lBRWIsTUFBTXNCLGFBQWE7UUFDakIsSUFBSSxDQUFDZixNQUFNZ0IsSUFBSSxNQUFNZCxXQUFXO1FBRWhDLE1BQU1lLGNBQTJCO1lBQy9CdEIsSUFBSUksS0FBS08sR0FBRyxHQUFHRSxRQUFRO1lBQ3ZCWixNQUFNO1lBQ05DLFNBQVNHO1lBQ1RGLFdBQVcsSUFBSUM7UUFDakI7UUFFQUwsWUFBWXdCLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNRDthQUFZO1FBQzFDaEIsU0FBUztRQUNURSxhQUFhO1FBRWIsSUFBSTtnQkFPU2dCO1lBTlgsTUFBTUEsV0FBVyxNQUFNOUIsMERBQWVBLENBQUNXLE9BQU9JO1lBQzlDLE1BQU1nQixZQUF5QjtnQkFDN0J6QixJQUFJLENBQUNJLEtBQUtPLEdBQUcsS0FBSyxHQUFHRSxRQUFRO2dCQUM3QlosTUFBTTtnQkFDTkMsU0FBU3NCLFNBQVNFLE1BQU07Z0JBQ3hCdkIsV0FBVyxJQUFJQztnQkFDZnVCLFNBQVNILEVBQUFBLG9CQUFBQSxTQUFTRyxPQUFPLGNBQWhCSCx3Q0FBQUEsa0JBQWtCSSxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEdBQUcsTUFBSyxFQUFFO1lBQ2xEO1lBQ0EvQixZQUFZd0IsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1FO2lCQUFVO1FBQzFDLEVBQUUsT0FBT00sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4QyxNQUFNRSxlQUE0QjtnQkFDaENqQyxJQUFJLENBQUNJLEtBQUtPLEdBQUcsS0FBSyxHQUFHRSxRQUFRO2dCQUM3QlosTUFBTTtnQkFDTkMsU0FBUztnQkFDVEMsV0FBVyxJQUFJQztZQUNqQjtZQUNBTCxZQUFZd0IsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1VO2lCQUFhO1FBQzdDLFNBQVU7WUFDUnpCLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTTBCLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7WUFDcENGLEVBQUVHLGNBQWM7WUFDaEJsQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNbUIsYUFBYSxDQUFDQztRQUNsQixPQUFPQSxLQUFLQyxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3RDQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNQyw2QkFBNkIsQ0FBQzNDLFNBQWlCeUI7UUFDbkQsK0RBQStEO1FBQy9ELG1DQUFtQztRQUNuQyxPQUFPekI7SUFDVDtJQUVBLE1BQU00QyxpQkFBaUIsQ0FBQ2hCO1FBQ3RCLDBDQUEwQztRQUMxQyxJQUFJQSxJQUFJaUIsUUFBUSxDQUFDLGFBQWE7WUFDNUIsSUFBSWpCLElBQUlpQixRQUFRLENBQUMsWUFBWSxPQUFPO1lBQ3BDLElBQUlqQixJQUFJaUIsUUFBUSxDQUFDLGdCQUFnQixPQUFPO1lBQ3hDLElBQUlqQixJQUFJaUIsUUFBUSxDQUFDLFVBQVUsT0FBTztZQUNsQyxJQUFJakIsSUFBSWlCLFFBQVEsQ0FBQyxZQUFZLE9BQU87WUFDcEMsT0FBTztRQUNUO1FBQ0EsT0FBT2pCO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ2tCO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7b0JBQ1puRCxTQUFTOEIsR0FBRyxDQUFDLENBQUNzQix3QkFDYiw4REFBQ0Y7NEJBQXFCQyxXQUFVOzs4Q0FFOUIsOERBQUNEO29DQUFJQyxXQUFXLHlFQUlmLE9BSENDLFFBQVFqRCxJQUFJLEtBQUssY0FDYiwyQkFDQTs4Q0FFSGlELFFBQVFqRCxJQUFJLEtBQUssNEJBQ2hCLDhEQUFDa0Q7d0NBQUlGLFdBQVU7d0NBQVVHLE1BQUs7d0NBQU9DLFFBQU87d0NBQWVDLFNBQVE7a0RBQ2pFLDRFQUFDQzs0Q0FBS0MsZUFBYzs0Q0FBUUMsZ0JBQWU7NENBQVFDLGFBQWE7NENBQUdDLEdBQUU7Ozs7Ozs7Ozs7NkRBR3ZFLDhEQUFDUjt3Q0FBSUYsV0FBVTt3Q0FBVUcsTUFBSzt3Q0FBT0MsUUFBTzt3Q0FBZUMsU0FBUTtrREFDakUsNEVBQUNDOzRDQUFLQyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBYTs0Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNM0UsOERBQUNYO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWkMsUUFBUWpELElBQUksS0FBSyw0QkFDaEIsOERBQUNOLG9EQUFhQTt3REFDWmlFLFlBQVk7NERBQ1ZDLElBQUk7b0VBQUMsRUFBQ0MsUUFBUSxFQUFDO3FGQUFLLDhEQUFDRDtvRUFBR1osV0FBVTs4RUFBNENhOzs7Ozs7OzREQUM5RUMsSUFBSTtvRUFBQyxFQUFDRCxRQUFRLEVBQUM7cUZBQUssOERBQUNDO29FQUFHZCxXQUFVOzhFQUFtRGE7Ozs7Ozs7NERBQ3JGRSxJQUFJO29FQUFDLEVBQUNGLFFBQVEsRUFBQztxRkFBSyw4REFBQ0U7b0VBQUdmLFdBQVU7OEVBQWlEYTs7Ozs7Ozs0REFDbkZHLElBQUk7b0VBQUMsRUFBQ0gsUUFBUSxFQUFDO3FGQUFLLDhEQUFDRztvRUFBR2hCLFdBQVU7OEVBQXdDYTs7Ozs7Ozs0REFDMUVJLElBQUk7b0VBQUMsRUFBQ0osUUFBUSxFQUFDO3FGQUFLLDhEQUFDSTtvRUFBR2pCLFdBQVU7OEVBQTJDYTs7Ozs7Ozs0REFDN0VLLElBQUk7b0VBQUMsRUFBQ0wsUUFBUSxFQUFDO3FGQUFLLDhEQUFDSztvRUFBR2xCLFdBQVU7OEVBQWlCYTs7Ozs7Ozs0REFDbkRNLEdBQUc7b0VBQUMsRUFBQ04sUUFBUSxFQUFDO3FGQUFLLDhEQUFDTTtvRUFBRW5CLFdBQVU7OEVBQWtCYTs7Ozs7Ozs0REFDbERPLFFBQVE7b0VBQUMsRUFBQ1AsUUFBUSxFQUFDO3FGQUFLLDhEQUFDTztvRUFBT3BCLFdBQVU7OEVBQStCYTs7Ozs7Ozs0REFDekVRLE1BQU07b0VBQUMsRUFBQ1IsUUFBUSxFQUFDO3FGQUFLLDhEQUFDUTtvRUFBS3JCLFdBQVU7OEVBQXFEYTs7Ozs7Ozs0REFDM0ZTLEdBQUc7b0VBQUMsRUFBQ0MsSUFBSSxFQUFFVixRQUFRLEVBQUM7cUZBQ2xCLDhEQUFDUztvRUFDQ0MsTUFBTUE7b0VBQ05DLFFBQU87b0VBQ1BDLEtBQUk7b0VBQ0p6QixXQUFVOzhFQUVUYTs7Ozs7Ozt3REFHUDtrRUFFQ2pCLDJCQUEyQkssUUFBUWhELE9BQU8sRUFBRWdELFFBQVF2QixPQUFPLElBQUksRUFBRTs7Ozs7NkVBR3BFLDhEQUFDeUM7a0VBQUdsQixRQUFRaEQsT0FBTzs7Ozs7Ozs7Ozs7Z0RBSXRCZ0QsUUFBUXZCLE9BQU8sSUFBSXVCLFFBQVF2QixPQUFPLENBQUNnRCxNQUFNLEdBQUcsbUJBQzNDLDhEQUFDM0I7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDbUI7NERBQUVuQixXQUFVO3NFQUF5Qzs7Ozs7O3NFQUN0RCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ1pDLFFBQVF2QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxDQUFDZ0QsUUFBUUMsc0JBQzVCLDhEQUFDN0I7b0VBQWdCQyxXQUFVOztzRkFDekIsOERBQUM2Qjs0RUFBSzdCLFdBQVU7O2dGQUErQjtnRkFBRTRCLFFBQVE7Z0ZBQUU7Ozs7Ozs7c0ZBQzNELDhEQUFDTjs0RUFDQ0MsTUFBTUk7NEVBQ05ILFFBQU87NEVBQ1BDLEtBQUk7NEVBQ0p6QixXQUFVO3NGQUVUSCxlQUFlOEI7Ozs7Ozs7bUVBUlZDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQWtCcEIsOERBQUNUOzRDQUFFbkIsV0FBVTtzREFDVlYsV0FBV1csUUFBUS9DLFNBQVM7Ozs7Ozs7Ozs7Ozs7MkJBN0V6QitDLFFBQVFsRCxFQUFFOzs7OztvQkFtRnJCTywyQkFDQyw4REFBQ3lDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNFO29DQUFJRixXQUFVO29DQUFVRyxNQUFLO29DQUFPQyxRQUFPO29DQUFlQyxTQUFROzhDQUNqRSw0RUFBQ0M7d0NBQUtDLGVBQWM7d0NBQVFDLGdCQUFlO3dDQUFRQyxhQUFhO3dDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBDQUd6RSw4REFBQ1g7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUM2QjtnREFBSzdCLFdBQVU7MERBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU8xQyw4REFBQ0Q7d0JBQUkrQixLQUFLaEU7Ozs7Ozs7Ozs7OzswQkFJWiw4REFBQ2lDO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDNUM7Z0NBQ0MyRSxNQUFLO2dDQUNMQyxPQUFPNUU7Z0NBQ1A2RSxVQUFVLENBQUMvQyxJQUFNN0IsU0FBUzZCLEVBQUVzQyxNQUFNLENBQUNRLEtBQUs7Z0NBQ3hDRSxXQUFXakQ7Z0NBQ1hrRCxhQUFZO2dDQUNabkMsV0FBVTtnQ0FDVm9DLFVBQVU5RTs7Ozs7OzBDQUVaLDhEQUFDK0U7Z0NBQ0NDLFNBQVNuRTtnQ0FDVGlFLFVBQVU5RSxhQUFhLENBQUNGLE1BQU1nQixJQUFJO2dDQUNsQzRCLFdBQVU7MENBRVYsNEVBQUNFO29DQUFJRixXQUFVO29DQUFVRyxNQUFLO29DQUFPQyxRQUFPO29DQUFlQyxTQUFROzhDQUNqRSw0RUFBQ0M7d0NBQUtDLGVBQWM7d0NBQVFDLGdCQUFlO3dDQUFRQyxhQUFhO3dDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU0zRSw4REFBQ1g7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDNkI7Z0NBQUs3QixXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUs1Qyw4REFBQ3JELHNEQUFhQTtnQkFBQ2EsV0FBV0E7Ozs7Ozs7Ozs7OztBQUdoQztHQTNPd0JaO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0NoYXRJbnRlcmZhY2UudHN4P2NlNWYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ2hhdE1lc3NhZ2UgfSBmcm9tICdAL3R5cGVzJ1xuaW1wb3J0IHsgc2VuZENoYXRNZXNzYWdlIH0gZnJvbSAnQC9saWIvY2hhdCdcbmltcG9ydCBSZWFjdE1hcmtkb3duIGZyb20gJ3JlYWN0LW1hcmtkb3duJ1xuaW1wb3J0IE1lbW9yeU1hbmFnZXIgZnJvbSAnLi9NZW1vcnlNYW5hZ2VyJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGF0SW50ZXJmYWNlKCkge1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPENoYXRNZXNzYWdlW10+KFtcbiAgICB7XG4gICAgICBpZDogJzEnLFxuICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXG4gICAgICBjb250ZW50OiAnSGkhIElcXCdtIGhlcmUgdG8gaGVscCB5b3Ugd2l0aCBxdWVzdGlvbnMgYWJvdXQgeW91ciBBdmVuIEhFTE9DIENyZWRpdCBDYXJkIGFuZCBmaW5hbmNpYWwgc2VydmljZXMuIEhvdyBjYW4gSSBhc3Npc3QgeW91IHRvZGF5PycsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgfSxcbiAgXSlcbiAgY29uc3QgW2lucHV0LCBzZXRJbnB1dF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2Vzc2lvbklkXSA9IHVzZVN0YXRlKCgpID0+IGBzZXNzaW9uXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTEpfWApXG4gIGNvbnN0IG1lc3NhZ2VzRW5kUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxuXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tID0gKCkgPT4ge1xuICAgIG1lc3NhZ2VzRW5kUmVmLmN1cnJlbnQ/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6ICdzbW9vdGgnIH0pXG4gIH1cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKClcbiAgfSwgW21lc3NhZ2VzXSlcblxuICBjb25zdCBoYW5kbGVTZW5kID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaW5wdXQudHJpbSgpIHx8IGlzTG9hZGluZykgcmV0dXJuXG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxuICAgICAgcm9sZTogJ3VzZXInLFxuICAgICAgY29udGVudDogaW5wdXQsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgfVxuXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgdXNlck1lc3NhZ2VdKVxuICAgIHNldElucHV0KCcnKVxuICAgIHNldElzTG9hZGluZyh0cnVlKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc2VuZENoYXRNZXNzYWdlKGlucHV0LCBzZXNzaW9uSWQpXG4gICAgICBjb25zdCBhaU1lc3NhZ2U6IENoYXRNZXNzYWdlID0ge1xuICAgICAgICBpZDogKERhdGUubm93KCkgKyAxKS50b1N0cmluZygpLFxuICAgICAgICByb2xlOiAnYXNzaXN0YW50JyxcbiAgICAgICAgY29udGVudDogcmVzcG9uc2UuYW5zd2VyLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgIHNvdXJjZXM6IHJlc3BvbnNlLnNvdXJjZXM/Lm1hcChzID0+IHMudXJsKSB8fCBbXSxcbiAgICAgIH1cbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGFpTWVzc2FnZV0pXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgbWVzc2FnZTonLCBlcnJvcilcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICAgIGlkOiAoRGF0ZS5ub3coKSArIDEpLnRvU3RyaW5nKCksXG4gICAgICAgIHJvbGU6ICdhc3Npc3RhbnQnLFxuICAgICAgICBjb250ZW50OiAnSSBhcG9sb2dpemUsIGJ1dCBJXFwnbSBoYXZpbmcgdHJvdWJsZSBjb25uZWN0aW5nIHJpZ2h0IG5vdy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICB9XG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBlcnJvck1lc3NhZ2VdKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICBoYW5kbGVTZW5kKClcbiAgICB9XG4gIH1cblxuICBjb25zdCBmb3JtYXRUaW1lID0gKGRhdGU6IERhdGUpID0+IHtcbiAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoJ2VuLVVTJywgeyBcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JywgXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgIGhvdXIxMjogdHJ1ZSBcbiAgICB9KVxuICB9XG5cbiAgY29uc3QgZm9ybWF0TWVzc2FnZVdpdGhDaXRhdGlvbnMgPSAoY29udGVudDogc3RyaW5nLCBzb3VyY2VzOiBzdHJpbmdbXSkgPT4ge1xuICAgIC8vIFRoaXMgZnVuY3Rpb24gd2lsbCBiZSBlbmhhbmNlZCBsYXRlciB0byBhZGQgaW5saW5lIGNpdGF0aW9uc1xuICAgIC8vIEZvciBub3csIGp1c3QgcmV0dXJuIHRoZSBjb250ZW50XG4gICAgcmV0dXJuIGNvbnRlbnRcbiAgfVxuXG4gIGNvbnN0IGdldFNvdXJjZVRpdGxlID0gKHVybDogc3RyaW5nKSA9PiB7XG4gICAgLy8gRXh0cmFjdCBhIG1lYW5pbmdmdWwgdGl0bGUgZnJvbSB0aGUgVVJMXG4gICAgaWYgKHVybC5pbmNsdWRlcygnYXZlbi5jb20nKSkge1xuICAgICAgaWYgKHVybC5pbmNsdWRlcygnc3VwcG9ydCcpKSByZXR1cm4gJ0F2ZW4gQ3VzdG9tZXIgU3VwcG9ydCdcbiAgICAgIGlmICh1cmwuaW5jbHVkZXMoJ2VsaWdpYmlsaXR5JykpIHJldHVybiAnQXZlbiBFbGlnaWJpbGl0eSBSZXF1aXJlbWVudHMnXG4gICAgICBpZiAodXJsLmluY2x1ZGVzKCdhcHBseScpKSByZXR1cm4gJ0F2ZW4gQXBwbGljYXRpb24gUHJvY2VzcydcbiAgICAgIGlmICh1cmwuaW5jbHVkZXMoJ2NvbXBhcmUnKSkgcmV0dXJuICdBdmVuIHZzIFRyYWRpdGlvbmFsIENyZWRpdCBDYXJkcydcbiAgICAgIHJldHVybiAnQXZlbiBIRUxPQyBDcmVkaXQgQ2FyZCBJbmZvcm1hdGlvbidcbiAgICB9XG4gICAgcmV0dXJuIHVybFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1bNzAwcHhdXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC02IHNwYWNlLXktNFwiPlxuICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e21lc3NhZ2UuaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTRcIj5cbiAgICAgICAgICAgIHsvKiBBdmF0YXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtc2hyaW5rLTAgdy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke1xuICAgICAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09ICdhc3Npc3RhbnQnIFxuICAgICAgICAgICAgICAgID8gJ2JnLXRlYWwtNDAwIHRleHQtd2hpdGUnIFxuICAgICAgICAgICAgICAgIDogJ2JnLXJlZC00MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAge21lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcgPyAoXG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTMgMTBWM0w0IDE0aDd2N2w5LTExaC03elwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTYgN2E0IDQgMCAxMS04IDAgNCA0IDAgMDE4IDB6TTEyIDE0YTcgNyAwIDAwLTcgN2gxNGE3IDcgMCAwMC03LTd6XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTWVzc2FnZSBDb250ZW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcm91bmRlZC14bCBwLTQgbWF4LXctM3hsXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwIGxlYWRpbmctcmVsYXhlZCBwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICB7bWVzc2FnZS5yb2xlID09PSAnYXNzaXN0YW50JyA/IChcbiAgICAgICAgICAgICAgICAgICAgPFJlYWN0TWFya2Rvd25cbiAgICAgICAgICAgICAgICAgICAgICBjb21wb25lbnRzPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBoMTogKHtjaGlsZHJlbn0pID0+IDxoMSBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0yXCI+e2NoaWxkcmVufTwvaDE+LFxuICAgICAgICAgICAgICAgICAgICAgICAgaDI6ICh7Y2hpbGRyZW59KSA9PiA8aDIgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0yIG10LTNcIj57Y2hpbGRyZW59PC9oMj4sXG4gICAgICAgICAgICAgICAgICAgICAgICBoMzogKHtjaGlsZHJlbn0pID0+IDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0xIG10LTJcIj57Y2hpbGRyZW59PC9oMz4sXG4gICAgICAgICAgICAgICAgICAgICAgICB1bDogKHtjaGlsZHJlbn0pID0+IDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgc3BhY2UteS0xIG15LTJcIj57Y2hpbGRyZW59PC91bD4sXG4gICAgICAgICAgICAgICAgICAgICAgICBvbDogKHtjaGlsZHJlbn0pID0+IDxvbCBjbGFzc05hbWU9XCJsaXN0LWRlY2ltYWwgbGlzdC1pbnNpZGUgc3BhY2UteS0xIG15LTJcIj57Y2hpbGRyZW59PC9vbD4sXG4gICAgICAgICAgICAgICAgICAgICAgICBsaTogKHtjaGlsZHJlbn0pID0+IDxsaSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+e2NoaWxkcmVufTwvbGk+LFxuICAgICAgICAgICAgICAgICAgICAgICAgcDogKHtjaGlsZHJlbn0pID0+IDxwIGNsYXNzTmFtZT1cIm1iLTIgbGFzdDptYi0wXCI+e2NoaWxkcmVufTwvcD4sXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJvbmc6ICh7Y2hpbGRyZW59KSA9PiA8c3Ryb25nIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPntjaGlsZHJlbn08L3N0cm9uZz4sXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiAoe2NoaWxkcmVufSkgPT4gPGNvZGUgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgcHgtMSBweS0wLjUgcm91bmRlZCB0ZXh0LXNtIGZvbnQtbW9ub1wiPntjaGlsZHJlbn08L2NvZGU+LFxuICAgICAgICAgICAgICAgICAgICAgICAgYTogKHtocmVmLCBjaGlsZHJlbn0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17aHJlZn0gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCIgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIHVuZGVybGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgICksXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRNZXNzYWdlV2l0aENpdGF0aW9ucyhtZXNzYWdlLmNvbnRlbnQsIG1lc3NhZ2Uuc291cmNlcyB8fCBbXSl9XG4gICAgICAgICAgICAgICAgICAgIDwvUmVhY3RNYXJrZG93bj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxwPnttZXNzYWdlLmNvbnRlbnR9PC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB7bWVzc2FnZS5zb3VyY2VzICYmIG1lc3NhZ2Uuc291cmNlcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwdC0zIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgbWItMlwiPvCfk5ogU291cmNlczo8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHttZXNzYWdlLnNvdXJjZXMubWFwKChzb3VyY2UsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTAuNVwiPlt7aW5kZXggKyAxfV08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17c291cmNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDAgaG92ZXI6dW5kZXJsaW5lIGZsZXgtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U291cmNlVGl0bGUoc291cmNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7LyogVGltZXN0YW1wICovfVxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMiBtbC00XCI+XG4gICAgICAgICAgICAgICAge2Zvcm1hdFRpbWUobWVzc2FnZS50aW1lc3RhbXApfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICAgIFxuICAgICAgICB7aXNMb2FkaW5nICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LTEwIGgtMTAgYmctdGVhbC00MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEzIDEwVjNMNCAxNGg3djdsOS0xMWgtN3pcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQteGwgcC00IG1heC13LTN4bFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci10ZWFsLTQwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlRoaW5raW5nLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICBcbiAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIHsvKiBJbnB1dCBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0xMDAgcC02IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgYmctZ3JheS01MCByb3VuZGVkLXhsIHAtM1wiPlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2lucHV0fVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRJbnB1dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBvbktleURvd249e2hhbmRsZUtleVByZXNzfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBc2sgbWUgYW55dGhpbmcgYWJvdXQgQXZlbi4uLlwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctdHJhbnNwYXJlbnQgYm9yZGVyLW5vbmUgb3V0bGluZS1ub25lIHRleHQtZ3JheS04MDAgcGxhY2Vob2xkZXItZ3JheS01MDBcIlxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmR9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nIHx8ICFpbnB1dC50cmltKCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIHctMTAgaC0xMCBiZy1yZWQtNDAwIGhvdmVyOmJnLXJlZC01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDE5bDkgMi05LTE4LTkgMTggOS0yem0wIDB2LThcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIFN0YXR1cyBpbmRpY2F0b3IgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbXQtM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+QUkgaXMgcmVhZHkgd2l0aCBtZW1vcnkhPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICB7LyogTWVtb3J5IE1hbmFnZXIgKi99XG4gICAgICA8TWVtb3J5TWFuYWdlciBzZXNzaW9uSWQ9e3Nlc3Npb25JZH0gLz5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0Iiwic2VuZENoYXRNZXNzYWdlIiwiUmVhY3RNYXJrZG93biIsIk1lbW9yeU1hbmFnZXIiLCJDaGF0SW50ZXJmYWNlIiwibWVzc2FnZXMiLCJzZXRNZXNzYWdlcyIsImlkIiwicm9sZSIsImNvbnRlbnQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwiaW5wdXQiLCJzZXRJbnB1dCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInNlc3Npb25JZCIsIk1hdGgiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsIm1lc3NhZ2VzRW5kUmVmIiwic2Nyb2xsVG9Cb3R0b20iLCJjdXJyZW50Iiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImhhbmRsZVNlbmQiLCJ0cmltIiwidXNlck1lc3NhZ2UiLCJwcmV2IiwicmVzcG9uc2UiLCJhaU1lc3NhZ2UiLCJhbnN3ZXIiLCJzb3VyY2VzIiwibWFwIiwicyIsInVybCIsImVycm9yIiwiY29uc29sZSIsImVycm9yTWVzc2FnZSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJmb3JtYXRUaW1lIiwiZGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJob3VyMTIiLCJmb3JtYXRNZXNzYWdlV2l0aENpdGF0aW9ucyIsImdldFNvdXJjZVRpdGxlIiwiaW5jbHVkZXMiLCJkaXYiLCJjbGFzc05hbWUiLCJtZXNzYWdlIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiY29tcG9uZW50cyIsImgxIiwiY2hpbGRyZW4iLCJoMiIsImgzIiwidWwiLCJvbCIsImxpIiwicCIsInN0cm9uZyIsImNvZGUiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsImxlbmd0aCIsInNvdXJjZSIsImluZGV4Iiwic3BhbiIsInJlZiIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwib25LZXlEb3duIiwicGxhY2Vob2xkZXIiLCJkaXNhYmxlZCIsImJ1dHRvbiIsIm9uQ2xpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.tsx\n"));

/***/ })

});