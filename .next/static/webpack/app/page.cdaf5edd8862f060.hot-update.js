"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/chat */ \"(app-pages-browser)/./src/lib/chat.ts\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _MemoryManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MemoryManager */ \"(app-pages-browser)/./src/components/MemoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hi! I'm here to help you with questions about your Aven HELOC Credit Card and financial services. How can I assist you today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>\"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)));\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        try {\n            var _response_sources;\n            const response = await (0,_lib_chat__WEBPACK_IMPORTED_MODULE_2__.sendChatMessage)(input, sessionId);\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: response.answer,\n                timestamp: new Date(),\n                sources: ((_response_sources = response.sources) === null || _response_sources === void 0 ? void 0 : _response_sources.map((s)=>s.url)) || []\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again later.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    const formatMessageWithCitations = (content, sources)=>{\n        // This function will be enhanced later to add inline citations\n        // For now, just return the content\n        return content;\n    };\n    const getSourceTitle = (url)=>{\n        // Extract a meaningful title from the URL\n        if (url.includes(\"aven.com\")) {\n            if (url.includes(\"support\")) return \"Aven Customer Support\";\n            if (url.includes(\"eligibility\")) return \"Aven Eligibility Requirements\";\n            if (url.includes(\"apply\")) return \"Aven Application Process\";\n            if (url.includes(\"compare\")) return \"Aven vs Traditional Credit Cards\";\n            return \"Aven HELOC Credit Card Information\";\n        }\n        return url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-[700px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center \".concat(message.role === \"assistant\" ? \"bg-teal-400 text-white\" : \"bg-red-400 text-white\"),\n                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-800 leading-relaxed prose prose-sm max-w-none\",\n                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                                                        components: {\n                                                            h1: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            h2: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-base font-semibold text-gray-800 mb-2 mt-3\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            h3: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-800 mb-1 mt-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            ul: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"list-disc list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            ol: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                    className: \"list-decimal list-inside space-y-1 my-2\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            li: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 45\n                                                                }, void 0);\n                                                            },\n                                                            p: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mb-2 last:mb-0\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 137,\n                                                                    columnNumber: 44\n                                                                }, void 0);\n                                                            },\n                                                            strong: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 49\n                                                                }, void 0);\n                                                            },\n                                                            code: (param)=>{\n                                                                let { children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"bg-gray-200 px-1 py-0.5 rounded text-sm font-mono\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 47\n                                                                }, void 0);\n                                                            },\n                                                            a: (param)=>{\n                                                                let { href, children } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: href,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                                                                    children: children\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        },\n                                                        children: formatMessageWithCitations(message.content, message.sources || [])\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-gray-600 mb-2\",\n                                                            children: \"\\uD83D\\uDCDA Sources:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid gap-2\",\n                                                            children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 mt-0.5\",\n                                                                            children: [\n                                                                                \"[\",\n                                                                                index + 1,\n                                                                                \"]\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: source,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: \"text-xs text-blue-600 hover:text-blue-800 hover:underline flex-1\",\n                                                                            children: getSourceTitle(source)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                            lineNumber: 166,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-2 ml-4\",\n                                            children: formatTime(message.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 w-10 h-10 bg-teal-400 rounded-full flex items-center justify-center text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-4 max-w-3xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-teal-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-100 p-6 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 bg-gray-50 rounded-xl p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Ask me anything about Aven...\",\n                                className: \"flex-1 bg-transparent border-none outline-none text-gray-800 placeholder-gray-500\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSend,\n                                disabled: isLoading || !input.trim(),\n                                className: \"flex-shrink-0 w-10 h-10 bg-red-400 hover:bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg flex items-center justify-center transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"AI is ready with memory!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MemoryManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                sessionId: sessionId\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/ai-customer-agent/src/components/ChatInterface.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"E+k/VSbd/SXbJToQxrSfnWBdmLI=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.tsx\n"));

/***/ })

});