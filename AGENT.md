# Aven AI Customer Support Agent - Development Guidelines

## Commands
- **Build**: `npm run build`
- **Dev server**: `npm run dev`
- **Lint**: `npm run lint`
- **Type check**: `npm run type-check`
- **Update knowledge base**: `curl -X POST http://localhost:3000/api/scrape`
- **Run evaluation**: `curl -X POST http://localhost:3000/api/evaluate`
- **Test memory**: `curl -X GET "http://localhost:3000/api/memory?userId=test_user"`

## Architecture
- **Frontend**: Next.js 14 + React + TypeScript + Tailwind CSS
- **AI/LLM**: OpenAI GPT-4 for text, Vapi for voice
- **Memory**: Mem0 for conversation memory and personalization
- **Vector DB**: Pinecone for RAG knowledge storage
- **Web scraping**: Exa.ai for automated data collection
- **API Routes**: `/api/chat`, `/api/memory`, `/api/guardrails`, `/api/schedule-meeting`, `/api/scrape`, `/api/evaluate`
- **Key directories**: `src/app/`, `src/components/`, `src/lib/`, `src/types/`, `src/utils/`

## New Features
- **Conversation Memory**: AI remembers past interactions using Mem0
- **User Sessions**: Persistent memory across chat sessions
- **Memory Management**: View and clear conversation history
- **Structured Responses**: Markdown formatting with inline citations
- **Personalization**: Responses adapt to user preferences and history

## Code Style
- **Language**: TypeScript with strict mode
- **Imports**: Absolute imports with `@/*` path aliases
- **Naming**: camelCase for variables/functions, PascalCase for components
- **Error handling**: Try-catch blocks, structured error responses
- **Types**: Interfaces in `src/types/index.ts` for all data structures
- **Comments**: Only for complex business logic, not obvious code

## Environment Variables
- `OPENAI_API_KEY`, `PINECONE_API_KEY`, `VAPI_PUBLIC_KEY`, `EXA_API_KEY`
- Memory data stored locally in `./data/memory.db`
- See `.env.local.example` for full list
