{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "Bash(npm run type-check:*)", "Bash(node run-eval.js:*)", "Bash(EVAL_BASE_URL=http://localhost:3002 node run-eval.js --rag-health)", "Bash(npm run scrape:*)", "Bash(npm run:*)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "Bash(EVAL_BASE_URL=http://localhost:3002 node run-eval.js --basic)", "<PERSON><PERSON>(pkill:*)", "Bash(EVAL_BASE_URL=http://localhost:3000 node run-eval.js --rag-health)", "Bash(EVAL_BASE_URL=http://localhost:3000 node run-eval.js --basic)", "Bash(EVAL_BASE_URL=http://localhost:3000 node run-eval.js --content-verify)", "Bash(find:*)", "Bash(grep:*)", "Bash(lsof:*)", "Bash(npm install:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "WebFetch(domain:docs.vapi.ai)", "WebFetch(domain:docs.smith.langchain.com)", "WebFetch(domain:langfuse.com)", "Bash(ls:*)", "Bash(git remote set-url:*)", "Bash(git add:*)", "Bash(git push:*)", "WebFetch(domain:platform.openai.com)", "WebFetch(domain:openai.com)"], "deny": []}}