// <PERSON><PERSON>t to update existing VAPI assistant with webhook integration
// Run with: node update-vapi-assistant.js

const VAPI_PRIVATE_KEY = '58f7b065-554b-4499-85e4-91679da51c80'
const ASSISTANT_ID = 'cff2bad0-7e4e-413f-888f-5a4207cffbfa'
const WEBHOOK_URL = process.argv[2] || 'https://your-domain.com/api/voice/webhook'

async function updateVapiAssistant() {
  const assistantConfig = {
    name: "Aven AI Customer Support Assistant",
    model: {
      provider: "openai",
      model: "gpt-4o-mini",
      temperature: 0.7
    },
    systemMessage: "You are Aven's AI voice assistant. For ANY question about Aven services, products, rates, features, or policies, you MUST use the searchKnowledge function to get accurate information from the knowledge base. Do not answer from general knowledge - always search first.",
    voice: {
      provider: "11labs",
      voiceId: "21m00Tcm4TlvDq8ikWAM"
    },
    transcriber: {
      provider: "deepgram",
      model: "nova-2",
      language: "en-US"
    },
    functions: [
      {
        name: "searchKnowledge",
        description: "Search Aven's knowledge base for information about products, services, rates, features, or any company-specific details. ALWAYS use this for Aven-related questions.",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "Search query for the knowledge base"
            }
          },
          required: ["query"]
        }
      }
    ],
    serverUrl: WEBHOOK_URL,
    endCallMessage: "Thank you for contacting Aven!",
    maxDurationSeconds: 1800
  };

  console.log('Updating VAPI assistant...');
  console.log('Assistant ID:', ASSISTANT_ID);
  console.log('Webhook URL:', WEBHOOK_URL);
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP ${response.status}: ${error}`);
    }

    const assistant = await response.json();
    console.log('✅ Assistant updated successfully!');
    console.log('Assistant now has webhook integration and searchKnowledge function');
    
    return assistant;
  } catch (error) {
    console.error('❌ Error updating assistant:', error);
    throw error;
  }
}

// Check if running directly
if (require.main === module) {
  if (!process.argv[2]) {
    console.log('Usage: node update-vapi-assistant.js https://your-domain.com');
    console.log('Example: node update-vapi-assistant.js https://myapp.vercel.app');
    process.exit(1);
  }
  
  updateVapiAssistant().catch(console.error);
}

module.exports = { updateVapiAssistant };