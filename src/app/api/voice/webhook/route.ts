import { NextRequest, NextResponse } from 'next/server'
import { searchKnowledgeBase } from '@/lib/pinecone'
import { generateResponse } from '@/lib/openai'
import { addToMemory, getMemoryContext } from '@/lib/memory'
import { agenticRetrieval } from '@/utils/agentic-retrieval'
import { agentMemory } from '@/utils/agent-memory'
import { meetingScheduler } from '@/utils/meeting-scheduler'

async function processVoiceQuery(userMessage: string, userId: string): Promise<string> {
  console.log('📝 Processing voice query:', userMessage)
  console.log('👤 User ID:', userId)

  // Check if we have real API keys
  const hasRealKeys = process.env.OPENAI_API_KEY && !process.env.OPENAI_API_KEY.includes('placeholder')
  console.log('🔑 Has real API keys:', hasRealKeys)

  if (!hasRealKeys) {
    return getVoiceDemoResponse(userMessage)
  }

  try {
    // Get memory context for this voice user
    console.log('🧠 Getting memory context...')
    const memoryContext = await getMemoryContext(userMessage, userId)

    // Check if this is a meeting scheduling request
    const meetingKeywords = ['schedule', 'appointment', 'meeting', 'book', 'reserve', 'consultation']
    const isSchedulingRequest = meetingKeywords.some(keyword => 
      userMessage.toLowerCase().includes(keyword)
    )

    if (isSchedulingRequest) {
      console.log('📅 Processing voice meeting scheduling request...')
      try {
        const schedulingResult = await meetingScheduler.parseAndSchedule(userMessage, userId)
        
        if (schedulingResult.success) {
          const meeting = schedulingResult.meeting!
          return `Great! ${schedulingResult.message} I've scheduled your meeting for ${meeting.date} at ${meeting.time}. Your meeting type is ${meeting.meetingType} and your meeting ID is ${meeting.id}. You can reschedule or cancel this meeting by referencing the Meeting ID.`
        } else {
          const availableSlots = meetingScheduler.getAvailableSlots().slice(0, 3)
          const slotsText = availableSlots.map(slot => `${slot.date} at ${slot.time}`).join(', ')
          return `${schedulingResult.message} Here are some available time slots: ${slotsText}. To schedule a meeting, please specify a date and time, for example: "Schedule a consultation for December 15th at 2 PM"`
        }
      } catch (schedulingError) {
        console.error('Error in voice meeting scheduling:', schedulingError)
        // Continue to normal voice flow if scheduling fails
      }
    }

    // Check semantic cache first
    console.log('⚡ Checking semantic cache...')
    let cachedResults
    try {
      cachedResults = await agentMemory.checkSemanticCache(userMessage)
    } catch (cacheError) {
      console.error('Cache error:', cacheError)
      cachedResults = null
    }
    
    let relevantDocs
    if (cachedResults) {
      console.log('⚡ Using cached results for voice')
      relevantDocs = cachedResults
    } else {
      // Use agentic retrieval for better results
      console.log('🤖 Using agentic retrieval for voice')
      try {
        const agenticResults = await agenticRetrieval.search(userMessage)
        relevantDocs = agenticResults.results
        
        // Add successful search to cache
        await agentMemory.addToSemanticCache(userMessage, relevantDocs)
        
        console.log(`🔍 Voice agentic search found ${relevantDocs.length} results with confidence ${agenticResults.confidence}`)
        console.log(`📊 Voice search path: ${agenticResults.searchPath.join(' → ')}`)
      } catch (retrievalError) {
        console.error('Error in voice agentic retrieval:', retrievalError)
        // Fallback to basic search
        try {
          relevantDocs = await searchKnowledgeBase(userMessage)
          console.log(`🔍 Voice fallback search found ${relevantDocs.length} results`)
        } catch (searchError) {
          console.error('Error in voice basic search:', searchError)
          relevantDocs = []
        }
      }
    }

    // Generate response optimized for voice
    console.log('🤖 Generating voice response...')
    const voicePrompt = `You are Aven's AI voice assistant. Provide a clear, concise, and conversational response suitable for voice delivery. Keep responses under 200 words and speak naturally as if talking to a customer over the phone. Focus on being helpful and friendly.

User question: ${userMessage}

Context from previous conversation: ${memoryContext}

Use the following knowledge base information to answer the user's question:`

    const response = await generateResponse(userMessage, relevantDocs, memoryContext, voicePrompt)

    // Make response more voice-friendly
    const voiceResponse = optimizeForVoice(response)

    // Store the voice conversation in memory
    console.log('💾 Storing voice conversation in memory...')
    try {
      await addToMemory([
        { role: 'user', content: userMessage },
        { role: 'assistant', content: voiceResponse }
      ], userId, {
        category: 'aven_voice_support',
        hasKnowledgeBase: relevantDocs.length > 0,
        channel: 'voice'
      })
    } catch (memoryError) {
      console.error('Error storing voice memory:', memoryError)
    }

    // Learn from voice interaction
    console.log('📚 Learning from voice interaction...')
    try {
      await agentMemory.learnFromInteraction(userMessage, voiceResponse, relevantDocs, userId)
    } catch (learningError) {
      console.error('Error in voice learning:', learningError)
    }

    return voiceResponse

  } catch (error) {
    console.error('Error in processVoiceQuery:', error)
    return "I apologize, but I'm experiencing some technical difficulties. Please try asking your question again, or you can always contact our support team directly."
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🎤 Voice webhook called')
    const body = await request.json()
    console.log('📞 Voice webhook payload:', JSON.stringify(body, null, 2))

    // Handle different VAPI message types
    const { message, call } = body

    if (!message) {
      return NextResponse.json({ error: 'No message provided' }, { status: 400 })
    }

    // Extract the user's transcribed message
    let userMessage = ''
    let userId = call?.customer?.number || call?.id || 'voice-user'
    
    if (message.type === 'function-call') {
      // Handle function calls from VAPI
      const { functionCall } = message
      if (functionCall?.name === 'searchKnowledge') {
        userMessage = functionCall.parameters?.query || ''
        console.log('🔍 Function call - searchKnowledge:', userMessage)
        
        // Process the search and return result for function call
        const searchResult = await processVoiceQuery(userMessage, userId)
        return NextResponse.json({
          result: searchResult
        })
      }
    } else if (message.transcript) {
      // Handle direct transcript - but we want VAPI to call searchKnowledge function instead
      userMessage = message.transcript
      console.log('📝 Direct transcript (should trigger function call):', userMessage)
      return NextResponse.json({
        result: "Let me search for information about that for you.",
        // This should trigger the assistant to call searchKnowledge function
      })
    } else {
      console.log('❓ Unknown message type:', message.type)
      return NextResponse.json({ 
        error: 'Unsupported message type',
        type: message.type 
      }, { status: 400 })
    }

    // This should not be reached since we handle function calls above
    return NextResponse.json({
      result: "I'm here to help with questions about Aven. Please ask me anything about our HELOC Credit Card!"
    })

  } catch (error) {
    console.error('Error in voice webhook:', error)
    return NextResponse.json({
      result: "I apologize, but I'm experiencing some technical difficulties. Please try asking your question again, or you can always contact our support team directly."
    }, { status: 200 }) // Return 200 to avoid VAPI retries
  }
}

function optimizeForVoice(text: string): string {
  return text
    // Remove markdown formatting
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/`(.*?)`/g, '$1')
    .replace(/#{1,6}\s/g, '')
    
    // Replace bullet points with verbal connectors
    .replace(/^- /gm, '')
    .replace(/^\* /gm, '')
    .replace(/^\d+\. /gm, '')
    
    // Replace line breaks with natural pauses
    .replace(/\n\n/g, '. ')
    .replace(/\n/g, ', ')
    
    // Clean up extra spaces
    .replace(/\s+/g, ' ')
    .trim()
    
    // Ensure it ends with proper punctuation
    .replace(/[.!?]*$/, '.')
}

function getVoiceDemoResponse(message: string): string {
  const lowerMessage = message.toLowerCase()
  
  let response = "Hello! I'm Aven's AI voice assistant in demo mode. "
  
  // Check for meeting scheduling requests
  const meetingKeywords = ['schedule', 'appointment', 'meeting', 'book', 'reserve', 'consultation']
  const isSchedulingRequest = meetingKeywords.some(keyword => lowerMessage.includes(keyword))
  
  if (isSchedulingRequest) {
    response = "I can help you schedule a meeting with an Aven representative! In demo mode, I can show you how the scheduling works. Try saying: 'Schedule a consultation for tomorrow at 2 PM' or 'Book an appointment for Friday at 10 AM'. Available meeting types include consultation, application help, account review, and general inquiries."
  } else if (lowerMessage.includes('heloc') || lowerMessage.includes('credit card')) {
    response = "The Aven HELOC Credit Card allows homeowners to access their home equity with credit limits up to $250,000. It offers variable interest rates from 7.99% to 15.49%, plus 2% cashback on all purchases and 7% cashback on travel booked through Aven's portal. There's no annual fee and approval can be as fast as 5 minutes."
  } else if (lowerMessage.includes('interest rate') || lowerMessage.includes('apr')) {
    response = "The Aven HELOC Credit Card offers variable interest rates from 7.99% to 15.49%, with a maximum of 18% during the life of the account. There's also a 0.25% autopay discount available to help you save even more."
  } else if (lowerMessage.includes('cashback') || lowerMessage.includes('rewards')) {
    response = "Aven offers 2% cashback on all purchases and 7% cashback on travel bookings made through Aven's travel portal. There are no annual fees, so you keep more of what you earn."
  } else if (lowerMessage.includes('apply') || lowerMessage.includes('eligibility')) {
    response = "To be eligible for the Aven HELOC Credit Card, you must be a homeowner with sufficient home equity, have a credit score typically around 600 or higher, and meet stable income requirements of usually $50,000 or more annually. The application process is quick, with approval decisions as fast as 5 minutes."
  } else {
    response += "I can help you with questions about Aven's HELOC Credit Card, including features, interest rates, cashback rewards, eligibility requirements, application process, and meeting scheduling. To enable full functionality with real-time data, please set up your API keys in the environment variables."
  }
  
  return response
}