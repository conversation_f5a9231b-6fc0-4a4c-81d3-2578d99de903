import { NextRequest, NextResponse } from 'next/server'
import { scrapeAvenData, updateKnowledgeBaseComprehensive } from '@/lib/scraper'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}))
    const { comprehensive = false } = body

    console.log(`Starting ${comprehensive ? 'comprehensive' : 'targeted'} knowledge base update...`)

    let results
    if (comprehensive) {
      await updateKnowledgeBaseComprehensive()
      results = { length: 'comprehensive' }
    } else {
      results = await scrapeAvenData()
    }

    return NextResponse.json({
      success: true,
      message: comprehensive
        ? 'Successfully completed comprehensive site scraping'
        : `Successfully scraped and added ${results.length} items to knowledge base`,
      itemCount: comprehensive ? 'comprehensive' : results.length,
      type: comprehensive ? 'comprehensive' : 'targeted'
    })
  } catch (error) {
    console.error('Error in scrape API:', error)
    return NextResponse.json(
      { error: 'Failed to scrape data' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST to trigger scraping',
    endpoint: '/api/scrape'
  })
}
