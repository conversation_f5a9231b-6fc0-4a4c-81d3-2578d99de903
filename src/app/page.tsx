'use client'

import { useState } from 'react'
import ChatInterface from '@/components/ChatInterface'
import EnhancedChatInterface from '@/components/EnhancedChatInterface'
import VoiceInterface from '@/components/VoiceInterface'
import ScrapingMonitor from '@/components/ScrapingMonitor'

export default function Home() {
  const [activeTab, setActiveTab] = useState<'chat' | 'voice' | 'enhanced' | 'scraping'>('enhanced')

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        {/* Header Section */}
        <div className="text-center py-12 px-6 border-b border-gray-100">
          <h1 className="text-3xl font-semibold text-gray-800 mb-3">Aven Customer Support</h1>
          <p className="text-gray-600 text-lg">
            Get instant answers about your HELOC Credit Card and financial services
          </p>
          
          {/* Tab <PERSON><PERSON> */}
          <div className="flex justify-center mt-8 gap-3">
            <button
              className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                activeTab === 'enhanced'
                  ? 'bg-red-400 text-white shadow-md hover:bg-red-500'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300'
              }`}
              onClick={() => setActiveTab('enhanced')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              AI Agent
            </button>
            <button
              className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                activeTab === 'chat'
                  ? 'bg-red-400 text-white shadow-md hover:bg-red-500'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300'
              }`}
              onClick={() => setActiveTab('chat')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
              </svg>
              Simple Chat
            </button>
            <button
              className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                activeTab === 'voice'
                  ? 'bg-red-400 text-white shadow-md hover:bg-red-500'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300'
              }`}
              onClick={() => setActiveTab('voice')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 0 01-3 3z" />
              </svg>
              Voice Chat
            </button>
            <button
              className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                activeTab === 'scraping'
                  ? 'bg-red-400 text-white shadow-md hover:bg-red-500'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300'
              }`}
              onClick={() => setActiveTab('scraping')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              Scraping
            </button>
          </div>
        </div>

        {/* Interface Content */}
        <div className="bg-white">
          {activeTab === 'enhanced' ? <EnhancedChatInterface /> :
           activeTab === 'chat' ? <ChatInterface /> :
           activeTab === 'voice' ? <VoiceInterface /> :
           <div className="p-6"><ScrapingMonitor /></div>}
        </div>
      </div>
    </div>
  )
}
