import { Exa } from 'exa-js'
import { AvenKnowledgeItem } from '@/types'
import { addToKnowledgeBase } from './pinecone'

const exa = new Exa(process.env.EXA_API_KEY || '')

export async function scrapeAvenData() {
  try {
    console.log('Starting Aven data scraping...')

    // Search for Aven-related content - comprehensive queries
    const searchQueries = [
      'Aven HELOC credit card features benefits',
      'Aven home equity line of credit application process',
      'Aven credit card interest rates fees',
      'Aven customer support FAQ',
      'Aven credit card rewards cashback',
      'Aven homeowner financial services',
      // Additional comprehensive queries
      'Aven credit card eligibility requirements',
      'Aven application approval process',
      'Aven customer reviews testimonials',
      'Aven vs traditional credit cards comparison',
      'Aven terms conditions privacy policy',
      'Aven contact customer service phone',
      'Aven mobile app features',
      'Aven account management portal',
      'Aven balance transfer options',
      'Aven travel rewards booking',
      'Aven autopay discount benefits',
      'Aven home equity calculation',
      'Aven credit score requirements',
      'Aven income verification process',
    ]

    const allResults: AvenKnowledgeItem[] = []

    for (const query of searchQueries) {
      try {
        const searchResult = await exa.search(query, {
          numResults: 10, // Increased from 5 to get more comprehensive coverage
          includeDomains: ['aven.com'],
        })

        for (const result of searchResult.results) {
          // Get the full content
          const contentResult = await exa.getContents([result.id])
          
          if (contentResult.results && contentResult.results.length > 0) {
            const content = contentResult.results[0]
            
            const knowledgeItem: AvenKnowledgeItem = {
              id: `aven-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
              title: result.title || 'Untitled',
              content: content.text || result.title || 'No content available',
              url: result.url || '',
              category: categorizeContent((result.title || '') + ' ' + (content.text || '')),
            }

            allResults.push(knowledgeItem)
          }
        }
      } catch (error) {
        console.error(`Error searching for "${query}":`, error)
        continue
      }
    }

    // Add additional manual knowledge items
    const manualKnowledgeItems = getManualKnowledgeItems()
    allResults.push(...manualKnowledgeItems)

    // Add all items to knowledge base
    for (const item of allResults) {
      try {
        await addToKnowledgeBase(item)
      } catch (error) {
        console.error(`Error adding item ${item.id} to knowledge base:`, error)
      }
    }

    console.log(`Successfully scraped and added ${allResults.length} items to knowledge base`)
    return allResults
  } catch (error) {
    console.error('Error scraping Aven data:', error)
    throw error
  }
}

function categorizeContent(content: string): string {
  const lowerContent = content.toLowerCase()
  
  if (lowerContent.includes('application') || lowerContent.includes('apply')) {
    return 'application'
  }
  if (lowerContent.includes('interest rate') || lowerContent.includes('apr') || lowerContent.includes('fee')) {
    return 'rates-fees'
  }
  if (lowerContent.includes('cashback') || lowerContent.includes('reward') || lowerContent.includes('travel')) {
    return 'rewards'
  }
  if (lowerContent.includes('heloc') || lowerContent.includes('home equity') || lowerContent.includes('credit line')) {
    return 'heloc'
  }
  if (lowerContent.includes('support') || lowerContent.includes('help') || lowerContent.includes('faq')) {
    return 'support'
  }
  
  return 'general'
}

function getManualKnowledgeItems(): AvenKnowledgeItem[] {
  return [
    {
      id: 'aven-heloc-overview',
      title: 'Aven HELOC Credit Card Overview',
      content: `The Aven HELOC Credit Card allows homeowners to access their home equity through a convenient credit card. Key features include:
      - Credit limits up to $250,000
      - Interest rates from 7.99% - 15.49% (variable), maximum 18%
      - 2% cashback on all purchases
      - 7% cashback on travel booked through Aven's travel portal
      - No annual fee and no notarization fee
      - Approval as fast as 5 minutes
      - Powered by Visa network
      - Partnered with Coastal Community Bank
      - 0.25% autopay discount available
      - Balance transfer option with 2.5% fee`,
      url: 'https://www.aven.com',
      category: 'heloc',
    },
    {
      id: 'aven-application-process',
      title: 'Aven Application Process',
      content: `To apply for the Aven HELOC Credit Card:
      1. Must be a homeowner with sufficient home equity
      2. Minimum credit score typically around 600
      3. Stable income requirements (usually $50k+ annually)
      4. Home value and equity verification
      5. Quick online application process
      6. Approval decisions as fast as 5 minutes
      7. No notarization required
      The application considers your home equity value, credit score, income, and debt-to-income ratio.`,
      url: 'https://www.aven.com/apply',
      category: 'application',
    },
    {
      id: 'aven-eligibility',
      title: 'Aven Eligibility Requirements',
      content: `Eligibility requirements for Aven HELOC Credit Card:
      - Must be a homeowner
      - Minimum credit score around 600
      - Sufficient home equity (typically $250k+ after mortgages and liens)
      - Stable income (usually $50k+ annually)
      - Home value requirements vary by state
      - Subject to state usury limits
      - Available in most U.S. states
      The exact requirements may vary based on your location and specific financial situation.`,
      url: 'https://www.aven.com/eligibility',
      category: 'application',
    },
    {
      id: 'aven-vs-traditional-credit',
      title: 'Aven vs Traditional Credit Cards',
      content: `Advantages of Aven HELOC Credit Card vs traditional credit cards:
      - Lower interest rates (7.99%-15.49% vs 18%+ typical credit cards)
      - Higher credit limits (up to $250k vs typical $5k-$25k)
      - Secured by home equity (lower risk for lender)
      - Same convenience as regular credit card
      - Better rewards (2% cashback vs typical 1%)
      - No annual fee
      - Interest may be tax-deductible (consult tax advisor)
      However, your home serves as collateral, which is an important consideration.`,
      url: 'https://www.aven.com/compare',
      category: 'heloc',
    },
    {
      id: 'aven-customer-support',
      title: 'Aven Customer Support',
      content: `Aven customer support options:
      - Online account management portal
      - Customer service phone line
      - Email support
      - FAQ section on website
      - Mobile app for account management
      - 24/7 fraud monitoring
      - Live chat support during business hours
      For account-specific questions, customers should log into their account or contact customer service directly.`,
      url: 'https://www.aven.com/support',
      category: 'support',
    },
  ]
}

export async function comprehensiveSiteSearch() {
  try {
    console.log('Starting comprehensive Aven site search...')

    // Use broader search terms to find more pages
    const broadSearchQueries = [
      'site:aven.com',
      'aven.com pages',
      'aven.com content',
    ]

    const allResults: AvenKnowledgeItem[] = []

    for (const query of broadSearchQueries) {
      try {
        const searchResult = await exa.search(query, {
          numResults: 50, // Much higher number for comprehensive coverage
          includeDomains: ['aven.com'],
        })

        console.log(`Found ${searchResult.results.length} results for "${query}"`)

        for (const result of searchResult.results) {
          // Skip if we already have this URL
          if (allResults.some(item => item.url === result.url)) {
            continue
          }

          try {
            const contentResult = await exa.getContents([result.id])

            if (contentResult.results && contentResult.results.length > 0) {
              const content = contentResult.results[0]

              const knowledgeItem: AvenKnowledgeItem = {
                id: `aven-comprehensive-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                title: result.title || 'Untitled',
                content: content.text || result.title || 'No content available',
                url: result.url || '',
                category: categorizeContent((result.title || '') + ' ' + (content.text || '')),
              }

              allResults.push(knowledgeItem)

              // Add to knowledge base immediately
              await addToKnowledgeBase(knowledgeItem)
            }
          } catch (contentError) {
            console.error(`Error getting content for ${result.url}:`, contentError)
            continue
          }
        }
      } catch (error) {
        console.error(`Error in comprehensive search for "${query}":`, error)
        continue
      }
    }

    console.log(`Comprehensive search completed. Added ${allResults.length} unique pages to knowledge base`)
    return allResults
  } catch (error) {
    console.error('Error in comprehensive site search:', error)
    throw error
  }
}

export async function updateKnowledgeBase() {
  try {
    await scrapeAvenData()
    console.log('Knowledge base updated successfully')
  } catch (error) {
    console.error('Error updating knowledge base:', error)
    throw error
  }
}

export async function updateKnowledgeBaseComprehensive() {
  try {
    // First run the targeted search
    await scrapeAvenData()

    // Then run the comprehensive search
    await comprehensiveSiteSearch()

    console.log('Comprehensive knowledge base update completed successfully')
  } catch (error) {
    console.error('Error updating knowledge base comprehensively:', error)
    throw error
  }
}
