import { Memory } from 'mem0ai/oss'

let memory: Memory | null = null

export async function initializeMemory() {
  if (!memory) {
    memory = new Memory({
      version: 'v1.1',
      embedder: {
        provider: 'openai',
        config: {
          apiKey: process.env.OPENAI_API_KEY || '',
          model: 'text-embedding-3-small',
        },
      },
      vectorStore: {
        provider: 'memory',
        config: {
          collectionName: 'aven_memories',
          dimension: 1536,
        },
      },
      llm: {
        provider: 'openai',
        config: {
          apiKey: process.env.OPENAI_API_KEY || '',
          model: 'gpt-4-turbo-preview',
        },
      },
      historyDbPath: './data/memory.db',
      disableHistory: false,
    })
  }
  return memory
}

export async function addToMemory(
  messages: Array<{ role: string; content: string }>,
  userId: string,
  metadata?: Record<string, any>
) {
  try {
    const mem = await initializeMemory()
    const result = await mem.add(messages, { 
      userId, 
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata 
      }
    })
    console.log('Memory added:', result)
    return result
  } catch (error) {
    console.error('Error adding to memory:', error)
    return null
  }
}

export async function searchMemory(query: string, userId: string) {
  try {
    const mem = await initializeMemory()
    const results = await mem.search(query, { userId })
    console.log('Memory search results:', results)
    return results
  } catch (error) {
    console.error('Error searching memory:', error)
    return []
  }
}

export async function getAllMemories(userId: string) {
  try {
    const mem = await initializeMemory()
    const memories = await mem.getAll({ userId })
    return memories
  } catch (error) {
    console.error('Error getting all memories:', error)
    return []
  }
}

export async function getMemoryContext(query: string, userId: string): Promise<string> {
  try {
    const relevantMemories = await searchMemory(query, userId)
    
    if (relevantMemories.length === 0) {
      return "No previous conversation context found."
    }

    const context = relevantMemories
      .slice(0, 3) // Limit to top 3 most relevant memories
      .map((memory: any, index: number) => `${index + 1}. ${memory.text}`)
      .join('\n')

    return `Previous conversation context:\n${context}`
  } catch (error) {
    console.error('Error getting memory context:', error)
    return "Error retrieving conversation context."
  }
}

export async function deleteUserMemories(userId: string) {
  try {
    const mem = await initializeMemory()
    await mem.deleteAll({ userId })
    console.log(`All memories deleted for user: ${userId}`)
    return true
  } catch (error) {
    console.error('Error deleting user memories:', error)
    return false
  }
}
